<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIconFile</key>
	<string></string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSMinimumSystemVersion</key>
	<string>$(MACOSX_DEPLOYMENT_TARGET)</string>
	<key>NSHumanReadableCopyright</key>
	<string>$(PRODUCT_COPYRIGHT)</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	
	<!-- 权限描述 -->
	<!-- 相机权限 -->
	<key>NSCameraUsageDescription</key>
	<string>需要访问相机以便拍摄照片和扫描二维码</string>
	
	<!-- 麦克风权限 -->
	<key>NSMicrophoneUsageDescription</key>
	<string>需要访问麦克风以进行语音识别和录音</string>
	
	<!-- 蓝牙权限 -->
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>需要蓝牙功能以连接外部设备</string>
	
	<!-- 位置权限 -->
	<key>NSLocationUsageDescription</key>
	<string>需要访问您的位置以提供基于位置的功能</string>
</dict>
</plist>
