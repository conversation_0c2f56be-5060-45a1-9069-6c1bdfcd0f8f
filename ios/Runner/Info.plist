<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>ContentPal</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>contentpal</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	
	<!-- 权限描述 -->
	<!-- 麦克风权限 -->
	<key>NSMicrophoneUsageDescription</key>
	<string>ContentPal需要麦克风权限来录制您的声音，用于语音记录和转文字功能。没有此权限将无法使用语音功能。</string>
	
	<!-- 语音识别权限 -->
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>ContentPal需要语音识别权限将您的语音转换为文本。没有此权限将无法使用语音转文字功能。</string>
	
	<!-- 相机权限 -->
	<key>NSCameraUsageDescription</key>
	<string>需要访问相机以便拍摄照片和扫描二维码</string>
	
	<!-- 相册权限 -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>ContentPal需要访问相册以选择和保存图片</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>ContentPal需要访问相册以保存生成的卡片图片</string>

	<!-- iOS 14+ 限制相册访问权限 -->
	<key>PHPhotoLibraryPreventAutomaticLimitedAccessAlert</key>
	<true/>
	
	<!-- 位置权限 (如果需要) -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>需要访问您的位置以提供基于位置的功能</string>
	
	<!-- 本地网络权限 -->
	<key>NSLocalNetworkUsageDescription</key>
	<string>需要访问本地网络以与附近设备通信</string>
	
	<!-- 蓝牙权限 (如果需要) -->
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>需要蓝牙功能以连接外部设备</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>需要蓝牙功能以连接外部设备</string>
	
	<!-- 文档处理配置 -->
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	
	<!-- 支持的文档类型 -->
	<key>CFBundleDocumentTypes</key>
	<array>
		<!-- Markdown文件 -->
		<dict>
			<key>CFBundleTypeName</key>
			<string>Markdown Document</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>net.daringfireball.markdown</string>
				<string>public.plain-text</string>
			</array>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>md</string>
				<string>markdown</string>
			</array>
		</dict>
		
		<!-- 文本文件 -->
		<dict>
			<key>CFBundleTypeName</key>
			<string>Text Document</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.plain-text</string>
				<string>public.text</string>
			</array>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>txt</string>
				<string>text</string>
			</array>
		</dict>
		
		<!-- SVG文件 -->
		<dict>
			<key>CFBundleTypeName</key>
			<string>SVG Document</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.svg-image</string>
			</array>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>svg</string>
			</array>
		</dict>
		
		<!-- HTML文件 -->
		<dict>
			<key>CFBundleTypeName</key>
			<string>HTML Document</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.html</string>
			</array>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>html</string>
				<string>htm</string>
			</array>
		</dict>
	</array>
	
	<!-- UTI导出定义 -->
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeIdentifier</key>
			<string>net.daringfireball.markdown</string>
			<key>UTTypeDescription</key>
			<string>Markdown Document</string>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.plain-text</string>
			</array>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>md</string>
					<string>markdown</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>text/markdown</string>
					<string>text/x-markdown</string>
				</array>
			</dict>
		</dict>
	</array>
</dict>
</plist>
