import Flutter
import UIKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  private let CHANNEL = "com.example.contentpal/file_intent"
  private var initialIntent: [String: Any]?
  
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
    let channel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: controller.binaryMessenger)
    
    channel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
      switch call.method {
      case "getInitialIntent":
        result(self?.initialIntent)
      default:
        result(FlutterMethodNotImplemented)
      }
    }
    
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  // 处理从其他应用打开文件
  override func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> <PERSON><PERSON> {
    handleFileURL(url)
    return true
  }
  
  // 处理文档打开（iOS 9+）
  override func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bool {
    handleFileURL(url)
    return true
  }
  
  // 处理继续用户活动（Handoff等）
  override func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
    
    if userActivity.activityType == NSUserActivityTypeBrowsingWeb {
      if let url = userActivity.webpageURL {
        handleFileURL(url)
      }
    }
    
    return true
  }
  
  private func handleFileURL(_ url: URL) {
    guard url.isFileURL else {
      return
    }
    
    let filePath = url.path
    let intentData: [String: Any] = [
      "type": "file",
      "filePath": filePath
    ]
    
    // 如果Flutter引擎已经准备好，直接发送
    if let controller = window?.rootViewController as? FlutterViewController {
      let channel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: controller.binaryMessenger)
      channel.invokeMethod("handleFileIntent", arguments: ["filePath": filePath])
    } else {
      // 否则保存以供后续使用
      initialIntent = intentData
    }
  }
  
  private func handleTextIntent(_ text: String) {
    let intentData: [String: Any] = [
      "type": "text",
      "text": text
    ]
    
    // 如果Flutter引擎已经准备好，直接发送
    if let controller = window?.rootViewController as? FlutterViewController {
      let channel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: controller.binaryMessenger)
      channel.invokeMethod("handleTextIntent", arguments: ["text": text])
    } else {
      // 否则保存以供后续使用
      initialIntent = intentData
    }
  }
}
