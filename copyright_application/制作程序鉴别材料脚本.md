# 程序鉴别材料制作脚本

## 概述
本脚本用于自动生成ContentPal软件著作权申请所需的程序鉴别材料（前后各30页源代码）。

## 第一步：选择代表性源代码文件

### 前30页推荐文件列表（按重要性排序）

#### 核心程序文件
1. `lib/main.dart` - 程序入口文件
2. `lib/home.dart` - 主界面文件
3. `lib/config/app_config.dart` - 应用配置
4. `lib/models/document.dart` - 核心数据模型

#### 主要功能模块
5. `lib/markdown/markdown_editor.dart` - Markdown编辑器
6. `lib/markdown/markdown_preview.dart` - Markdown预览
7. `lib/pdf/pdf_viewer.dart` - PDF查看器
8. `lib/pdf/pdf_processor.dart` - PDF处理
9. `lib/svg/svg_editor.dart` - SVG编辑器
10. `lib/html/html_editor.dart` - HTML编辑器

#### 服务和工具类
11. `lib/services/file_service.dart` - 文件服务
12. `lib/services/storage_service.dart` - 存储服务
13. `lib/common/widgets/custom_editor.dart` - 自定义编辑器组件

### 后30页推荐文件列表

#### 高级功能模块
1. `lib/voice/speech_service.dart` - 语音服务
2. `lib/voice/tts_service.dart` - 文字转语音
3. `lib/subscription/subscription_manager.dart` - 订阅管理
4. `lib/content/content_processor.dart` - 内容处理器

#### UI组件和工具
5. `lib/common/widgets/` 目录下的重要组件文件
6. `lib/settings/settings_page.dart` - 设置页面
7. `lib/text_cards/` 目录下的功能文件

## 第二步：生成程序鉴别材料

### 方法一：手动制作
1. 将选择的源代码文件按顺序复制到一个新文档中
2. 确保每页包含行号
3. 在每页页眉添加：`ContentPal内容处理工具 V1.0.0`
4. 在每页页脚添加权利人信息
5. 转换为PDF格式

### 方法二：使用脚本生成

```bash
#!/bin/bash
# 创建程序鉴别材料生成脚本

# 前30页文件列表
front_files=(
    "lib/main.dart"
    "lib/home.dart"
    "lib/config/app_config.dart"
    "lib/models/document.dart"
    "lib/markdown/markdown_editor.dart"
    "lib/markdown/markdown_preview.dart"
    "lib/pdf/pdf_viewer.dart"
    "lib/pdf/pdf_processor.dart"
    "lib/svg/svg_editor.dart"
    "lib/html/html_editor.dart"
    "lib/services/file_service.dart"
    "lib/services/storage_service.dart"
    "lib/common/widgets/custom_editor.dart"
)

# 后30页文件列表
back_files=(
    "lib/voice/speech_service.dart"
    "lib/voice/tts_service.dart"
    "lib/subscription/subscription_manager.dart"
    "lib/content/content_processor.dart"
    "lib/settings/settings_page.dart"
)

# 创建临时目录
mkdir -p copyright_temp

# 生成前30页
echo "生成前30页源代码..."
for file in "${front_files[@]}"; do
    if [ -f "$file" ]; then
        echo "添加文件: $file"
        echo "// 文件路径: $file" >> copyright_temp/front_30_pages.dart
        echo "// ContentPal内容处理工具 V1.0.0" >> copyright_temp/front_30_pages.dart
        echo "// 版权所有 [著作权人]" >> copyright_temp/front_30_pages.dart
        echo "" >> copyright_temp/front_30_pages.dart
        cat "$file" >> copyright_temp/front_30_pages.dart
        echo "" >> copyright_temp/front_30_pages.dart
        echo "// ============================================" >> copyright_temp/front_30_pages.dart
        echo "" >> copyright_temp/front_30_pages.dart
    fi
done

# 生成后30页
echo "生成后30页源代码..."
for file in "${back_files[@]}"; do
    if [ -f "$file" ]; then
        echo "添加文件: $file"
        echo "// 文件路径: $file" >> copyright_temp/back_30_pages.dart
        echo "// ContentPal内容处理工具 V1.0.0" >> copyright_temp/back_30_pages.dart
        echo "// 版权所有 [著作权人]" >> copyright_temp/back_30_pages.dart
        echo "" >> copyright_temp/back_30_pages.dart
        cat "$file" >> copyright_temp/back_30_pages.dart
        echo "" >> copyright_temp/back_30_pages.dart
        echo "// ============================================" >> copyright_temp/back_30_pages.dart
        echo "" >> copyright_temp/back_30_pages.dart
    fi
done

echo "源代码合并完成，请检查 copyright_temp/ 目录"
```

## 第三步：格式化和PDF生成

### 使用VS Code或其他编辑器
1. 打开生成的源代码文件
2. 确保代码格式化正确
3. 添加行号显示
4. 设置页眉页脚
5. 导出为PDF

### 页面格式要求
- **页面大小**: A4 (210mm × 297mm)
- **边距**: 上下左右各25mm
- **字体**: 等宽字体，如Consolas或Monaco
- **字号**: 10-12pt
- **行间距**: 1.2倍
- **每页行数**: 约50-60行

### 页眉格式
```
ContentPal内容处理工具 V1.0.0                    第 X 页，共 60 页
```

### 页脚格式
```
版权所有 © [年份] [著作权人姓名/公司名称]
```

## 第四步：质量检查

### 检查清单
- [ ] 总页数为60页（前30页 + 后30页）
- [ ] 每页都有正确的页眉和页脚
- [ ] 软件名称和版本号一致
- [ ] 权利人信息一致
- [ ] 代码清晰可读
- [ ] 无乱码或格式错误
- [ ] PDF文件可正常打开和打印

### 版权信息一致性检查
确保以下信息在所有页面都保持一致：
- 软件名称：ContentPal内容处理工具
- 版本号：V1.0.0
- 权利人信息：[需要统一填写]

## 第五步：文件输出

### 输出文件
- `程序鉴别材料.pdf` - 最终的程序鉴别材料
- `程序鉴别材料_源文件/` - 源代码文件备份

### 文件验证
1. 确认PDF文件大小合理（通常5-20MB）
2. 检查PDF可搜索性
3. 验证所有页面正确显示
4. 确认版权信息完整

## 注意事项

### 重要提醒
1. **原创性**: 确保所有代码都是原创，不包含第三方版权代码
2. **完整性**: 程序必须是完整的，能体现软件的主要功能
3. **一致性**: 所有版权信息必须保持一致
4. **可读性**: 代码格式良好，便于审查人员阅读

### 常见问题
1. **文件选择**: 选择最能代表软件核心功能的文件
2. **页数控制**: 确保正好60页，不多不少
3. **格式规范**: 严格按照要求的格式制作
4. **版权标识**: 每页都必须有明确的版权标识

## 自动化脚本完整版

```bash
#!/bin/bash
# ContentPal程序鉴别材料自动生成脚本
# 使用方法: ./generate_copyright_materials.sh

echo "ContentPal软件著作权程序鉴别材料生成脚本"
echo "============================================"

# 检查lib目录是否存在
if [ ! -d "lib" ]; then
    echo "错误: 未找到lib目录，请在项目根目录运行此脚本"
    exit 1
fi

# 创建输出目录
output_dir="copyright_application/program_materials"
mkdir -p "$output_dir"

# 定义版权信息
SOFTWARE_NAME="ContentPal内容处理工具"
VERSION="V1.0.0"
COPYRIGHT_HOLDER="[请填写著作权人信息]"

# 生成版权声明
generate_copyright_header() {
    local file_path="$1"
    echo "/*"
    echo " * 软件名称: $SOFTWARE_NAME"
    echo " * 版本号: $VERSION"
    echo " * 文件路径: $file_path"
    echo " * 版权所有: $COPYRIGHT_HOLDER"
    echo " * 生成时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo " */"
    echo ""
}

# 合并源代码文件
merge_source_files() {
    local files_array=("$@")
    local output_file="$1"
    shift
    local files=("$@")
    
    > "$output_file"  # 清空输出文件
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            echo "正在处理: $file"
            generate_copyright_header "$file" >> "$output_file"
            cat "$file" >> "$output_file"
            echo "" >> "$output_file"
            echo "// ===== 文件结束: $file =====" >> "$output_file"
            echo "" >> "$output_file"
        else
            echo "警告: 文件不存在 - $file"
        fi
    done
}

# 前30页核心文件
front_files=(
    "lib/main.dart"
    "lib/home.dart"
)

# 后30页文件
back_files=(
    "lib/settings/settings_page.dart"
)

# 自动查找更多文件来填充页数
echo "自动查找源代码文件..."
additional_files=($(find lib -name "*.dart" -not -path "*/test/*" | head -20))

# 合并文件列表
all_front_files=("${front_files[@]}" "${additional_files[@]:0:10}")
all_back_files=("${back_files[@]}" "${additional_files[@]:10:10}")

echo "生成前30页材料..."
merge_source_files "$output_dir/front_30_pages.dart" "${all_front_files[@]}"

echo "生成后30页材料..."
merge_source_files "$output_dir/back_30_pages.dart" "${all_back_files[@]}"

echo ""
echo "程序鉴别材料生成完成！"
echo "输出目录: $output_dir"
echo ""
echo "下一步:"
echo "1. 检查生成的源代码文件"
echo "2. 使用编辑器打开并格式化"
echo "3. 添加页眉页脚并转换为PDF"
echo "4. 确保总页数为60页"
```

保存为 `generate_copyright_materials.sh` 并运行：
```bash
chmod +x generate_copyright_materials.sh
./generate_copyright_materials.sh
``` 