// // ======================================================
// // ContentPal内容处理工具 V1.0.0 - 程序鉴别材料（后30页）
// // 版权所有: [请填写著作权人信息]
// // 生成时间: 2025-06-05 12:39:16
// // ======================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/home/<USER>/background_painter.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:math' as math;
// import 'package:flutter/material.dart';
// import '../../config/app_theme.dart';
//
// /// 背景动画绘制器
// class BackgroundPainter extends CustomPainter {
//   final double animationValue;
//
//   BackgroundPainter(this.animationValue);
//
//   @override
//   void paint(Canvas canvas, Size size) {
//     final paint =
//         Paint()
//           ..shader = LinearGradient(
//             colors: [
//               AppTheme.primaryColor.withValues(alpha: 0.2),
//               AppTheme.secondaryColor.withValues(alpha: 0.1),
//             ],
//             begin: Alignment.topLeft,
//             end: Alignment.bottomRight,
//           ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
//
//     final path = Path();
//
//     // 绘制波浪背景
//     for (int i = 0; i < 5; i++) {
//       final wavePhase = i * 0.5 + animationValue * math.pi * 2;
//       final waveAmplitude = 25.0 - i * 4.0;
//       final waveFrequency = 0.015 + i * 0.01;
//
//       path.reset();
//       path.moveTo(0, size.height * 0.3 + math.sin(wavePhase) * waveAmplitude);
//
//       for (double x = 0; x <= size.width; x++) {
//         double y =
//             size.height * 0.3 +
//             math.sin(wavePhase + x * waveFrequency) * waveAmplitude;
//         path.lineTo(x, y);
//       }
//
//       path.lineTo(size.width, size.height);
//       path.lineTo(0, size.height);
//       path.close();
//
//       canvas.drawPath(
//         path,
//         paint..color = AppTheme.primaryColor.withValues(alpha: 0.05 - i * 0.01),
//       );
//     }
//   }
//
//   @override
//   bool shouldRepaint(BackgroundPainter oldDelegate) =>
//       oldDelegate.animationValue != animationValue;
// }
//
// // ===================================================================
// // 文件结束: lib/home/<USER>/background_painter.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/home/<USER>/create_content_bottom_sheet.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
// import '../../config/app_theme.dart';
// import '../../content/content_home_page.dart';
// import '../../html/html_manager_screen.dart';
// import '../../markdown/markdown_render_screen.dart';
// import '../../pdf/pdf_manager_screen.dart';
// import '../../svg/svg_manager_screen.dart';
// import '../../voice/voice_home_page.dart';
// import 'create_content_option.dart';
//
// /// 创建内容底部菜单组件
// class CreateContentBottomSheet extends StatelessWidget {
//   const CreateContentBottomSheet({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 24),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           const Text(
//             '创建新内容',
//             style: TextStyle(
//               fontSize: 22,
//               fontWeight: FontWeight.bold,
//               color: AppTheme.textDarkColor,
//             ),
//           ),
//           const SizedBox(height: 10),
//           const Text(
//             '选择您要创建的内容类型',
//             style: TextStyle(fontSize: 14, color: AppTheme.textMediumColor),
//           ),
//           const SizedBox(height: 30),
//           SizedBox(
//             height: 100,
//             child: ListView(
//               scrollDirection: Axis.horizontal,
//               children: [
//                 CreateContentOption(
//                   icon: Icons.dashboard_rounded,
//                   label: '内容库',
//                   gradient: AppTheme.chineseGradient,
//                   onTap: () {
//                     Navigator.pop(context);
//                     Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                         builder: (context) => const ContentHomePage(),
//                       ),
//                     );
//                   },
//                 ),
//                 CreateContentOption(
//                   icon: Icons.text_fields,
//                   label: 'Markdown',
//                   gradient: AppTheme.blueGradient,
//                   onTap: () {
//                     Navigator.pop(context);
//                     Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                         builder: (context) => const MarkdownRenderScreen(),
//                       ),
//                     );
//                   },
//                 ),
//                 CreateContentOption(
//                   icon: Icons.image,
//                   label: 'SVG',
//                   gradient: AppTheme.purpleGradient,
//                   onTap: () {
//                     Navigator.pop(context);
//                     Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                         builder: (context) => const SvgManagerScreen(),
//                       ),
//                     );
//                   },
//                 ),
//                 CreateContentOption(
//                   icon: Icons.code,
//                   label: 'HTML',
//                   gradient: AppTheme.greenGradient,
//                   onTap: () {
//                     Navigator.pop(context);
//                     Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                         builder: (context) => const HtmlManagerScreen(),
//                       ),
//                     );
//                   },
//                 ),
//                 CreateContentOption(
//                   icon: Icons.picture_as_pdf,
//                   label: 'PDF',
//                   gradient: AppTheme.orangeGradient,
//                   onTap: () {
//                     Navigator.pop(context);
//                     Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                         builder: (context) => const PdfManagerScreen(),
//                       ),
//                     );
//                   },
//                 ),
//                 CreateContentOption(
//                   icon: Icons.mic,
//                   label: '语音',
//                   gradient: AppTheme.purpleGradient,
//                   onTap: () {
//                     Navigator.pop(context);
//                     Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                         builder: (context) => const VoiceHomePage(),
//                       ),
//                     );
//                   },
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/home/<USER>/create_content_bottom_sheet.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/home/<USER>/create_content_option.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
// import '../../config/app_theme.dart';
//
// /// 创建内容选项组件
// class CreateContentOption extends StatelessWidget {
//   final IconData icon;
//   final String label;
//   final LinearGradient gradient;
//   final VoidCallback onTap;
//
//   const CreateContentOption({
//     super.key,
//     required this.icon,
//     required this.label,
//     required this.gradient,
//     required this.onTap,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.only(right: 18),
//       child: InkWell(
//         onTap: onTap,
//         borderRadius: BorderRadius.circular(14),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Container(
//               height: 64,
//               width: 64,
//               decoration: BoxDecoration(
//                 gradient: gradient,
//                 borderRadius: BorderRadius.circular(14),
//                 boxShadow: [
//                   BoxShadow(
//                     color: gradient.colors.first.withValues(alpha: 0.3),
//                     blurRadius: 8,
//                     offset: const Offset(0, 3),
//                   ),
//                 ],
//               ),
//               child: Icon(icon, color: Colors.white, size: 30),
//             ),
//             const SizedBox(height: 10),
//             Text(
//               label,
//               style: const TextStyle(
//                 fontSize: 14,
//                 fontWeight: FontWeight.w500,
//                 color: AppTheme.textDarkColor,
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/home/<USER>/create_content_option.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/home/<USER>/home_tool_card.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
// import '../../config/app_theme.dart';
//
// /// 主页工具卡片组件
// class HomeToolCard extends StatelessWidget {
//   final String title;
//   final String description;
//   final IconData icon;
//   final LinearGradient gradient;
//   final VoidCallback onTap;
//
//   const HomeToolCard({
//     super.key,
//     required this.title,
//     required this.description,
//     required this.icon,
//     required this.gradient,
//     required this.onTap,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: onTap,
//       borderRadius: BorderRadius.circular(16),
//       child: Container(
//         padding: const EdgeInsets.all(14),
//         decoration: BoxDecoration(
//           color: Colors.white,
//           borderRadius: BorderRadius.circular(16),
//           boxShadow: [
//             BoxShadow(
//               color: Colors.black.withValues(alpha: 0.03),
//               blurRadius: 10,
//               offset: const Offset(0, 2),
//             ),
//           ],
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.center,
//               children: [
//                 Container(
//                   padding: const EdgeInsets.all(8),
//                   decoration: BoxDecoration(
//                     gradient: gradient,
//                     borderRadius: BorderRadius.circular(10),
//                     boxShadow: [
//                       BoxShadow(
//                         color: gradient.colors.first.withValues(alpha: 0.2),
//                         blurRadius: 5,
//                         offset: const Offset(0, 2),
//                       ),
//                     ],
//                   ),
//                   child: Icon(icon, color: Colors.white, size: 20),
//                 ),
//                 const SizedBox(width: 12),
//                 Expanded(
//                   child: Text(
//                     title,
//                     style: const TextStyle(
//                       fontSize: 16,
//                       fontWeight: FontWeight.bold,
//                       color: AppTheme.textDarkColor,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             const SizedBox(height: 10),
//             Text(
//               description,
//               style: const TextStyle(
//                 fontSize: 12,
//                 color: AppTheme.textMediumColor,
//               ),
//               maxLines: 2,
//               overflow: TextOverflow.ellipsis,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/home/<USER>/home_tool_card.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/html/html_document.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:io';
//
// import 'package:path_provider/path_provider.dart';
// import 'package:share_plus/share_plus.dart';
// import 'package:uuid/uuid.dart';
//
// /// HTML文档模型
// class HtmlDocument {
//   /// 文档ID
//   final String id;
//
//   /// HTML内容
//   String content;
//
//   /// 文档标题
//   String title;
//
//   /// 创建时间
//   final DateTime createdAt;
//
//   /// 最后修改时间
//   DateTime updatedAt;
//
//   /// 构造函数
//   HtmlDocument({
//     String? id,
//     required this.content,
//     String? title,
//     DateTime? createdAt,
//     DateTime? updatedAt,
//   }) : id = id ?? const Uuid().v4(),
//        title = title ?? '未命名HTML',
//        createdAt = createdAt ?? DateTime.now(),
//        updatedAt = updatedAt ?? DateTime.now();
//
//   /// 从JSON创建
//   factory HtmlDocument.fromJson(Map<String, dynamic> json) {
//     return HtmlDocument(
//       id: json['id'] as String,
//       content: json['content'] as String,
//       title: json['title'] as String,
//       createdAt: DateTime.parse(json['createdAt'] as String),
//       updatedAt: DateTime.parse(json['updatedAt'] as String),
//     );
//   }
//
//   /// 转换为JSON
//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'content': content,
//       'title': title,
//       'createdAt': createdAt.toIso8601String(),
//       'updatedAt': updatedAt.toIso8601String(),
//     };
//   }
//
//   /// 更新内容
//   void updateContent(String newContent) {
//     content = newContent;
//     updatedAt = DateTime.now();
//   }
//
//   /// 更新标题
//   void updateTitle(String newTitle) {
//     title = newTitle;
//     updatedAt = DateTime.now();
//   }
//
//   /// 从文件加载HTML
//   static Future<HtmlDocument> fromFile(File file) async {
//     final content = await file.readAsString();
//     final fileName = file.path.split('/').last;
//     final title = fileName.replaceAll('.html', '');
//
//     return HtmlDocument(content: content, title: title);
//   }
//
//   /// 保存到文件
//   Future<File> saveToFile() async {
//     final directory = await getApplicationDocumentsDirectory();
//     final fileName =
//         '${title.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.html';
//     final filePath = '${directory.path}/$fileName';
//
//     final file = File(filePath);
//     return await file.writeAsString(content);
//   }
//
//   /// 分享HTML
//   Future<void> share() async {
//     final file = await saveToFile();
//     await SharePlus.instance.share(
//       ShareParams(files: [XFile(file.path)], text: title),
//     );
//   }
//
//   /// 提取HTML标题
//   String? extractHtmlTitle() {
//     final RegExp titleRegex = RegExp(r'<title>(.*?)</title>');
//     final match = titleRegex.firstMatch(content);
//
//     if (match != null && match.groupCount >= 1) {
//       return match.group(1);
//     }
//
//     return null;
//   }
//
//   /// 复制HTML文档
//   HtmlDocument copy() {
//     return HtmlDocument(
//       content: content,
//       title: '$title - 副本',
//       createdAt: DateTime.now(),
//     );
//   }
//
//   /// 获取基本HTML模板
//   static String getBasicTemplate() {
//     return '';
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/html/html_document.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/html/html_editor_screen.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:io';
// import 'dart:async';
//
// import 'package:file_picker/file_picker.dart';
// import 'package:flutter/material.dart';
// import 'package:webview_flutter/webview_flutter.dart';
//
// import '../config/app_theme.dart';
// import '../services/service_locator.dart';
// import '../services/content_service.dart';
// import '../models/content_item.dart';
// import '../content/content_save_button.dart';
// import 'html_document.dart';
//
// /// HTML编辑器屏幕
// class HtmlEditorScreen extends StatefulWidget {
//   /// 初始HTML内容
//   final String? initialHtmlContent;
//
//   /// 初始标题
//   final String? initialTitle;
//
//   /// 文档ID（如果是编辑现有文档）
//   final String? documentId;
//
//   /// 构造函数
//   const HtmlEditorScreen({
//     super.key,
//     this.initialHtmlContent,
//     this.initialTitle,
//     this.documentId,
//   });
//
//   @override
//   State<HtmlEditorScreen> createState() => _HtmlEditorScreenState();
// }
//
// class _HtmlEditorScreenState extends State<HtmlEditorScreen> {
//   /// HTML服务
//   final _htmlService = ServiceLocator().htmlService;
//
//   /// 编辑控制器
//   late TextEditingController _htmlController;
//
//   /// 标题控制器
//   late TextEditingController _titleController;
//
//   /// WebView控制器
//   WebViewController? _webViewController;
//
//   /// 当前文档
//   HtmlDocument? _document;
//
//   /// 是否正在处理
//   bool _isProcessing = false;
//
//   /// 是否有错误
//   bool _hasError = false;
//
//   /// 错误消息
//   String _errorMessage = '';
//
//   /// 是否显示预览
//   bool _showPreview = false;
//
//   /// 分屏比例
//   double _splitRatio = 0.5;
//
//   /// 是否为分屏模式
//   bool _isSplitView = false;
//
//   /// 拖动处理中
//   bool _isDragging = false;
//
//   /// WebView是否已初始化
//   bool _isWebViewInitialized = false;
//
//   /// 是否显示标题栏
//   bool _isAppBarVisible = true;
//
//   /// 标题栏隐藏定时器
//   Timer? _appBarTimer;
//
//   /// 内容更新防抖定时器
//   Timer? _contentUpdateTimer;
//
//   /// 是否正在处理大文本
//   bool _isProcessingLargeText = false;
//
//   @override
//   void initState() {
//     super.initState();
//
//     _htmlController = TextEditingController(
//       text: widget.initialHtmlContent ?? HtmlDocument.getBasicTemplate(),
//     );
//     _titleController = TextEditingController(
//       text: widget.initialTitle ?? '新HTML文档',
//     );
//
//     _htmlController.addListener(_onHtmlChanged);
//
//     // 如果有文档ID，加载文档
//     if (widget.documentId != null) {
//       _loadDocument(widget.documentId!);
//     } else if (widget.initialHtmlContent != null) {
//       // 如果有初始内容，创建新文档
//       _createNewDocument();
//     }
//
//     // 启动标题栏自动隐藏定时器
//     _resetAppBarTimer();
//   }
//
//   /// 初始化WebView
//   void _initWebView() {
//     // 避免重复初始化
//     if (_isWebViewInitialized) {
//       return;
//     }
//
//     try {
//       _webViewController =
//           WebViewController()
//             ..setJavaScriptMode(JavaScriptMode.unrestricted)
//             ..setBackgroundColor(Colors.white)
//             ..enableZoom(true)
//             ..setNavigationDelegate(
//               NavigationDelegate(
//                 onPageFinished: (String url) {
//                   debugPrint('页面加载完成: $url');
//                   // 注入JavaScript以启用缩放和平移
//                   _webViewController?.runJavaScript('''
//                 document.body.style.overflow = 'auto';
//                 document.body.style.touchAction = 'auto';
//                 document.documentElement.style.overflow = 'auto';
//                 document.documentElement.style.touchAction = 'auto';
//               ''');
//
//                   // 注入JavaScript来捕获点击事件并调用Flutter
//                   _webViewController?.runJavaScript('''
//                 document.addEventListener('click', function(e) {
//                   window.flutter_inappwebview.callHandler('onTap');
//                 });
//               ''');
//                 },
//                 onWebResourceError: (WebResourceError error) {
//                   debugPrint('WebView错误: ${error.description}');
//                 },
//               ),
//             )
//             // 添加JavaScript通道处理WebView中的点击
//             ..addJavaScriptChannel(
//               'flutter_inappwebview',
//               onMessageReceived: (JavaScriptMessage message) {
//                 _showAppBar();
//               },
//             );
//
//       // 加载初始HTML
//       _updateWebViewContent();
//       _isWebViewInitialized = true;
//     } catch (e) {
//       debugPrint('WebView初始化失败: $e');
//       _isWebViewInitialized = false;
//     }
//   }
//
//   @override
//   void dispose() {
//     _htmlController.removeListener(_onHtmlChanged);
//     _htmlController.dispose();
//     _titleController.dispose();
//
//     // 清理防抖定时器
//     _contentUpdateTimer?.cancel();
//
//     // 清理WebView资源
//     _disposeWebView();
//
//     // 取消定时器
//     _appBarTimer?.cancel();
//
//     super.dispose();
//   }
//
//   /// 释放WebView资源
//   void _disposeWebView() {
//     _webViewController = null;
//     _isWebViewInitialized = false;
//   }
//
//   /// 加载文档
//   Future<void> _loadDocument(String id) async {
//     setState(() {
//       _isProcessing = true;
//     });
//
//     try {
//       final document = _htmlService.getDocument(id);
//
//       if (document != null) {
//         setState(() {
//           _document = document;
//           _htmlController.text = document.content;
//           _titleController.text = document.title;
//           _isProcessing = false;
//           _hasError = false;
//         });
//
//         _updateWebViewContent();
//       } else {
//         setState(() {
//           _isProcessing = false;
//           _hasError = true;
//           _errorMessage = '找不到文档';
//         });
//       }
//     } catch (e) {
//       setState(() {
//         _isProcessing = false;
//         _hasError = true;
//         _errorMessage = '加载文档失败: $e';
//       });
//     }
//   }
//
//   /// 创建新文档
//   Future<void> _createNewDocument() async {
//     setState(() {
//       _isProcessing = true;
//     });
//
//     try {
//       final document = await _htmlService.createFromString(
//         _htmlController.text,
//         title: _titleController.text,
//       );
//
//       setState(() {
//         _document = document;
//         _isProcessing = false;
//         _hasError = false;
//       });
//     } catch (e) {
//       setState(() {
//         _isProcessing = false;
//         _hasError = true;
//         _errorMessage = '创建文档失败: $e';
//       });
//     }
//   }
//
//   /// 保存文档
//   Future<void> _saveDocument() async {
//     // 如果是新文档或需要重命名，显示输入对话框
//     if (_document == null || _document!.title == '新HTML文档') {
//       final newTitle = await _showTitleInputDialog();
//       if (newTitle == null) {
//         // 用户取消了输入
//         return;
//       }
//       _titleController.text = newTitle;
//     }
//
//     if (_document == null) {
//       await _createNewDocument();
//       return;
//     }
//
//     setState(() {
//       _isProcessing = true;
//     });
//
//     try {
//       _document!.updateContent(_htmlController.text);
//       _document!.updateTitle(_titleController.text);
//
//       await _htmlService.updateDocument(_document!);
//
//       setState(() {
//         _isProcessing = false;
//         _hasError = false;
//       });
//
//       _showSnackBar('保存成功');
//     } catch (e) {
//       setState(() {
//         _isProcessing = false;
//         _hasError = true;
//         _errorMessage = '保存文档失败: $e';
//       });
//     }
//   }
//
//   /// 显示标题输入对话框
//   Future<String?> _showTitleInputDialog() async {
//     return showDialog<String>(
//       context: context,
//       builder: (context) {
//         final textController = TextEditingController(
//           text: _titleController.text,
//         );
//         return AlertDialog(
//           title: const Text('输入HTML文件名'),
//           content: TextField(
//             controller: textController,
//             decoration: const InputDecoration(
//               labelText: '文件名',
//               hintText: '请输入HTML文件名',
//             ),
//             autofocus: true,
//           ),
//           actions: [
//             TextButton(
//               onPressed: () => Navigator.pop(context),
//               child: const Text('取消'),
//             ),
//             TextButton(
//               onPressed: () {
//                 if (textController.text.trim().isNotEmpty) {
//                   Navigator.pop(context, textController.text.trim());
//                 } else {
//                   _showSnackBar('文件名不能为空');
//                 }
//               },
//               child: const Text('确定'),
//             ),
//           ],
//         );
//       },
//     );
//   }
//
//   /// 导入HTML文件
//   Future<void> _importHtmlFile() async {
//     try {
//       final result = await FilePicker.platform.pickFiles(
//         type: FileType.custom,
//         allowedExtensions: ['html', 'htm'],
//       );
//
//       if (result != null && result.files.isNotEmpty) {
//         final file = File(result.files.first.path!);
//         final document = await HtmlDocument.fromFile(file);
//
//         setState(() {
//           _htmlController.text = document.content;
//           _titleController.text = document.title;
//           _hasError = false;
//         });
//
//         _updateWebViewContent();
//       }
//     } catch (e) {
//       setState(() {
//         _hasError = true;
//         _errorMessage = '导入HTML文件失败: $e';
//       });
//     }
//   }
//
//   /// 导出为图片
//   Future<void> _exportAsImage() async {
//     if (_document == null) {
//       await _saveDocument();
//     }
//
//     if (_document == null) {
//       _showSnackBar('请先保存文档');
//       return;
//     }
//
//     setState(() {
//       _isProcessing = true;
//     });
//
//     try {
//       // 先让用户选择保存位置
//       String? outputPath = await FilePicker.platform.getDirectoryPath(
//         dialogTitle: '选择保存位置',
//       );
//
//       if (outputPath == null) {
//         // 用户取消了选择
//         setState(() {
//           _isProcessing = false;
//         });
//         return;
//       }
//
//       // 使用用户选择的路径保存图片
//       final file = await _htmlService.saveAsImage(
//         _document!,
//         outputPath: outputPath,
//       );
//
//       setState(() {
//         _isProcessing = false;
//       });
//
//       if (file != null) {
//         _showSnackBar('导出图片成功: ${file.path}');
//       } else {
//         _showSnackBar('导出图片失败');
//       }
//     } catch (e) {
//       setState(() {
//         _isProcessing = false;
//         _hasError = true;
//         _errorMessage = '导出图片失败: $e';
//       });
//     }
//   }
//
//   /// 分享HTML
//   Future<void> _shareHtml() async {
//     if (_document == null) {
//       await _saveDocument();
//     }
//
//     if (_document == null) {
//       _showSnackBar('请先保存文档');
//       return;
//     }
//
//     setState(() {
//       _isProcessing = true;
//     });
//
//     try {
//       await _document!.share();
//
//       setState(() {
//         _isProcessing = false;
//       });
//     } catch (e) {
//       setState(() {
//         _isProcessing = false;
//         _hasError = true;
//         _errorMessage = '分享HTML失败: $e';
//       });
//     }
//   }
//
//   /// 分享为图片
//   Future<void> _shareAsImage() async {
//     if (_document == null) {
//       await _saveDocument();
//     }
//
//     if (_document == null) {
//       _showSnackBar('请先保存文档');
//       return;
//     }
//
//     setState(() {
//       _isProcessing = true;
//     });
//
//     try {
//       await _htmlService.shareAsImage(_document!);
//
//       setState(() {
//         _isProcessing = false;
//       });
//     } catch (e) {
//       setState(() {
//         _isProcessing = false;
//         _hasError = true;
//         _errorMessage = '分享图片失败: $e';
//       });
//     }
//   }
//
//   /// 重置标题栏隐藏定时器
//   void _resetAppBarTimer() {
//     _appBarTimer?.cancel();
//     setState(() {
//       _isAppBarVisible = true;
//     });
//     _appBarTimer = Timer(const Duration(seconds: 3), () {
//       setState(() {
//         _isAppBarVisible = false;
//       });
//     });
//   }
//
//   /// 显示标题栏
//   void _showAppBar() {
//     if (!_isAppBarVisible) {
//       _resetAppBarTimer();
//     }
//   }
//
//   /// HTML内容变更处理
//   void _onHtmlChanged() {
//     setState(() {
//       // 通知UI刷新
//     });
//
//     // 使用防抖处理，避免频繁更新WebView
//     _contentUpdateTimer?.cancel();
//
//     // 检查是否是大文本
//     if (_htmlController.text.length > 10000) {
//       // 大文本需要延迟更长时间处理
//       setState(() {
//         _isProcessingLargeText = true;
//       });
//
//       _contentUpdateTimer = Timer(const Duration(milliseconds: 500), () {
//         _updateWebViewContent();
//         setState(() {
//           _isProcessingLargeText = false;
//         });
//       });
//     } else {
//       // 小文本可以更快处理
//       _contentUpdateTimer = Timer(const Duration(milliseconds: 200), () {
//         _updateWebViewContent();
//       });
//     }
//
//     _resetAppBarTimer();
//   }
//
//   /// 更新WebView内容
//   void _updateWebViewContent() {
//     if (_htmlController.text.isEmpty) return;
//
//     // 使用异步处理WebView内容更新，避免阻塞UI线程
//     Future.microtask(() {
//       // 如果WebView未初始化，先初始化
//       if (!_isWebViewInitialized) {
//         _initWebView();
//       }
//
//       try {
//         _webViewController?.loadHtmlString(_htmlController.text);
//       } catch (e) {
//         debugPrint('更新WebView内容失败: $e');
//         // 如果更新失败，尝试重新初始化
//         _disposeWebView();
//         _initWebView();
//       }
//     });
//   }
//
//   /// 显示提示消息
//   void _showSnackBar(String message) {
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         content: Text(message),
//         behavior: SnackBarBehavior.floating,
//         margin: const EdgeInsets.all(8),
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
//       ),
//     );
//   }
//
//   /// 切换视图模式
//   void _toggleViewMode() {
//     setState(() {
//       _showPreview = !_showPreview;
//
//       // 如果切换到预览模式并且WebView未初始化，则初始化WebView
//       if (_showPreview && !_isWebViewInitialized) {
//         _initWebView();
//       }
//     });
//   }
//
//   /// 切换分屏模式
//   void _toggleSplitView() {
//     setState(() {
//       _isSplitView = !_isSplitView;
//       if (!_isSplitView) {
//         _showPreview = false;
//       } else if (!_isWebViewInitialized) {
//         // 如果开启分屏模式，确保WebView已初始化
//         _initWebView();
//       }
//     });
//   }
//
//   /// 保存HTML到内容库
//   Future<void> _saveToContentLibrary() async {
//     // 如果文档未保存，先保存
//     if (_document == null) {
//       await _saveDocument();
//     }
//
//     if (_document == null) {
//       _showSnackBar('请先保存文档');
//       return;
//     }
//
//     setState(() {
//       _isProcessing = true;
//     });
//
//     try {
//       final contentService = ContentService();
//       await contentService.initialize();
//
//       final contentItem = await contentService.createTextContent(
//         title: _document!.title,
//         type: ContentType.html,
//         content: _document!.content,
//         tags: [],
//       );
//
//       setState(() {
//         _isProcessing = false;
//       });
//
//       _showSnackBar('已保存到内容库: ${contentItem.title}');
//     } catch (e) {
//       setState(() {
//         _isProcessing = false;
//         _hasError = true;
//         _errorMessage = '保存到内容库失败: $e';
//       });
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final theme = Theme.of(context);
//     final isDarkMode = theme.brightness == Brightness.dark;
//     final size = MediaQuery.of(context).size;
//     final isLandscape = size.width > size.height;
//
//     // 获取状态栏高度
//     final statusBarHeight = MediaQuery.of(context).padding.top;
//     // 计算标题栏总高度（状态栏 + 工具栏）
//     final appBarTotalHeight = statusBarHeight + kToolbarHeight;
//
//     final appBar = AppBar(
//       elevation: 0,
//       scrolledUnderElevation: 2,
//       title: Row(
//         children: [
//           Expanded(
//             child: Text(
//               _titleController.text,
//               overflow: TextOverflow.ellipsis,
//               style: const TextStyle(fontSize: 18),
//             ),
//           ),
//         ],
//       ),
//       actions: [
//         // 保存到内容库按钮
//         if (_document != null)
//           ContentSaveButton(
//             title: _titleController.text,
//             content: _htmlController.text,
//             contentType: ContentType.html,
//             onSaved: (savedItem) {
//               _showSnackBar('已保存到内容库: ${savedItem.title}');
//             },
//           ),
//         // 视图切换按钮
//         if (!_isSplitView)
//           IconButton(
//             icon: Icon(_showPreview ? Icons.code : Icons.visibility),
//             tooltip: _showPreview ? '编辑模式' : '预览模式',
//             onPressed: _toggleViewMode,
//           ),
//         // 分屏按钮
//         IconButton(
//           icon: Icon(_isSplitView ? Icons.fullscreen : Icons.splitscreen),
//           tooltip: _isSplitView ? '单屏模式' : '分屏模式',
//           onPressed: _toggleSplitView,
//         ),
//         // 更多菜单
//         PopupMenuButton<String>(
//           icon: const Icon(Icons.more_vert),
//           tooltip: '更多操作',
//           onSelected: (value) {
//             switch (value) {
//               case 'import':
//                 _importHtmlFile();
//                 break;
//               case 'export_image':
//                 _exportAsImage();
//                 break;
//               case 'share_image':
//                 _shareAsImage();
//                 break;
//               case 'share_html':
//                 _shareHtml();
//                 break;
//               case 'rename':
//                 _showTitleInputDialog().then((newTitle) {
//                   if (newTitle != null) {
//                     setState(() {
//                       _titleController.text = newTitle;
//                     });
//                   }
//                 });
//                 break;
//               case 'save_to_library':
//                 _saveToContentLibrary();
//                 break;
//             }
//           },
//           itemBuilder:
//               (context) => [
//                 const PopupMenuItem(
//                   value: 'import',
//                   child: Row(
//                     children: [
//                       Icon(Icons.add_box_outlined),
//                       SizedBox(width: 12),
//                       Text('导入HTML文件'),
//                     ],
//                   ),
//                 ),
//                 const PopupMenuItem(
//                   value: 'export_image',
//                   child: Row(
//                     children: [
//                       Icon(Icons.image_outlined),
//                       SizedBox(width: 12),
//                       Text('导出为图片'),
//                     ],
//                   ),
//                 ),
//                 const PopupMenuItem(
//                   value: 'share_image',
//                   child: Row(
//                     children: [
//                       Icon(Icons.share_outlined),
//                       SizedBox(width: 12),
//                       Text('分享为图片'),
//                     ],
//                   ),
//                 ),
//                 const PopupMenuItem(
//                   value: 'share_html',
//                   child: Row(
//                     children: [
//                       Icon(Icons.code),
//                       SizedBox(width: 12),
//                       Text('分享HTML'),
//                     ],
//                   ),
//                 ),
//                 const PopupMenuItem(
//                   value: 'rename',
//                   child: Row(
//                     children: [
//                       Icon(Icons.edit),
//                       SizedBox(width: 12),
//                       Text('重命名'),
//                     ],
//                   ),
//                 ),
//                 const PopupMenuItem(
//                   value: 'save_to_library',
//                   child: Row(
//                     children: [
//                       Icon(Icons.bookmark_add_outlined),
//                       SizedBox(width: 12),
//                       Text('保存到内容库'),
//                     ],
//                   ),
//                 ),
//               ],
//         ),
//       ],
//     );
//
//     return Scaffold(
//       extendBodyBehindAppBar: true,
//       body: GestureDetector(
//         onTap: _showAppBar,
//         child: Stack(
//           children: [
//             // 主内容区域
//             AnimatedContainer(
//               duration: const Duration(milliseconds: 300),
//               padding: EdgeInsets.only(
//                 // 根据标题栏可见状态动态调整顶部边距
//                 top: _isAppBarVisible ? appBarTotalHeight : statusBarHeight,
//               ),
//               child:
//                   _isProcessing
//                       ? const Center(
//                         child: Column(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             CircularProgressIndicator(),
//                             SizedBox(height: 16),
//                             Text('处理中...'),
//                           ],
//                         ),
//                       )
//                       : Column(
//                         children: [
//                           // 错误提示
//                           if (_hasError)
//                             Container(
//                               padding: const EdgeInsets.all(12),
//                               margin: const EdgeInsets.all(8),
//                               decoration: BoxDecoration(
//                                 color: AppTheme.redLight,
//                                 borderRadius: BorderRadius.circular(
//                                   AppTheme.borderRadiusMD,
//                                 ),
//                               ),
//                               width: double.infinity,
//                               child: Row(
//                                 children: [
//                                   Icon(
//                                     Icons.error_outline,
//                                     color: AppTheme.redDark,
//                                   ),
//                                   const SizedBox(width: 8),
//                                   Expanded(
//                                     child: Text(
//                                       _errorMessage,
//                                       style: TextStyle(color: AppTheme.redDark),
//                                     ),
//                                   ),
//                                   IconButton(
//                                     icon: Icon(
//                                       Icons.close,
//                                       color: AppTheme.redDark,
//                                     ),
//                                     onPressed:
//                                         () => setState(() => _hasError = false),
//                                     iconSize: 18,
//                                     padding: EdgeInsets.zero,
//                                     constraints: const BoxConstraints(),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           // 主内容区域
//                           Expanded(
//                             child:
//                                 _isSplitView
//                                     ? _buildSplitView(isDarkMode, isLandscape)
//                                     : _showPreview
//                                     ? _buildPreviewView(isDarkMode)
//                                     : _buildEditorView(isDarkMode),
//                           ),
//                         ],
//                       ),
//             ),
//
//             // 标题栏
//             AnimatedPositioned(
//               duration: const Duration(milliseconds: 300),
//               curve: Curves.easeInOut,
//               top: _isAppBarVisible ? 0 : -kToolbarHeight - statusBarHeight,
//               left: 0,
//               right: 0,
//               child: appBar,
//             ),
//           ],
//         ),
//       ),
//       floatingActionButton: AnimatedOpacity(
//         opacity: _isAppBarVisible ? 1.0 : 0.0,
//         duration: const Duration(milliseconds: 300),
//         // 当透明度为0时，禁用按钮功能
//         child: IgnorePointer(
//           ignoring: !_isAppBarVisible,
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               // 保存到内容库按钮
//               Padding(
//                 padding: const EdgeInsets.only(right: 8.0),
//                 child: FloatingActionButton(
//                   heroTag: "save_to_library",
//                   onPressed: _saveToContentLibrary,
//                   tooltip: '保存到内容库',
//                   backgroundColor: Colors.green,
//                   child: const Icon(Icons.bookmark_add),
//                 ),
//               ),
//               // 保存按钮
//               FloatingActionButton(
//                 heroTag: "save_html",
//                 onPressed: _saveDocument,
//                 tooltip: '保存',
//                 child: const Icon(Icons.save),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   /// 构建分屏视图
//   Widget _buildSplitView(bool isDarkMode, bool isLandscape) {
//     final orientation = isLandscape ? Axis.horizontal : Axis.vertical;
//
//     return LayoutBuilder(
//       builder: (context, constraints) {
//         final maxWidth = constraints.maxWidth;
//         final maxHeight = constraints.maxHeight;
//
//         return Stack(
//           children: [
//             // 分屏布局
//             orientation == Axis.horizontal
//                 ? Row(
//                   children: [
//                     SizedBox(
//                       width: maxWidth * _splitRatio,
//                       child: _buildEditorView(isDarkMode),
//                     ),
//                     SizedBox(
//                       width: maxWidth * (1 - _splitRatio),
//                       child: _buildPreviewView(isDarkMode),
//                     ),
//                   ],
//                 )
//                 : Column(
//                   children: [
//                     SizedBox(
//                       height: maxHeight * _splitRatio,
//                       child: _buildEditorView(isDarkMode),
//                     ),
//                     SizedBox(
//                       height: maxHeight * (1 - _splitRatio),
//                       child: _buildPreviewView(isDarkMode),
//                     ),
//                   ],
//                 ),
//
//             // 分隔线和拖动手柄
//             orientation == Axis.horizontal
//                 ? Positioned(
//                   left: maxWidth * _splitRatio - 5,
//                   top: 0,
//                   bottom: 0,
//                   child: GestureDetector(
//                     onHorizontalDragStart:
//                         (_) => setState(() => _isDragging = true),
//                     onHorizontalDragEnd:
//                         (_) => setState(() => _isDragging = false),
//                     onHorizontalDragUpdate: (details) {
//                       setState(() {
//                         _splitRatio += details.delta.dx / maxWidth;
//                         _splitRatio = _splitRatio.clamp(0.2, 0.8);
//                       });
//                     },
//                     child: Container(
//                       width: 10,
//                       color:
//                           _isDragging
//                               ? AppTheme.primaryColor.withValues(alpha: 0.5)
//                               : Colors.transparent,
//                       child: Center(
//                         child: Container(
//                           width: 4,
//                           color:
//                               isDarkMode
//                                   ? AppTheme.darkBorderColor
//                                   : AppTheme.borderColor,
//                         ),
//                       ),
//                     ),
//                   ),
//                 )
//                 : Positioned(
//                   top: maxHeight * _splitRatio - 5,
//                   left: 0,
//                   right: 0,
//                   child: GestureDetector(
//                     onVerticalDragStart:
//                         (_) => setState(() => _isDragging = true),
//                     onVerticalDragEnd:
//                         (_) => setState(() => _isDragging = false),
//                     onVerticalDragUpdate: (details) {
//                       setState(() {
//                         _splitRatio += details.delta.dy / maxHeight;
//                         _splitRatio = _splitRatio.clamp(0.2, 0.8);
//                       });
//                     },
//                     child: Container(
//                       height: 10,
//                       color:
//                           _isDragging
//                               ? AppTheme.primaryColor.withValues(alpha: 0.5)
//                               : Colors.transparent,
//                       child: Center(
//                         child: Container(
//                           height: 4,
//                           color:
//                               isDarkMode
//                                   ? AppTheme.darkBorderColor
//                                   : AppTheme.borderColor,
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//           ],
//         );
//       },
//     );
//   }
//
//   /// 构建编辑器视图
//   Widget _buildEditorView(bool isDarkMode) {
//     return Card(
//       margin: const EdgeInsets.all(8),
//       elevation: 0,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//         side: BorderSide(
//           color: isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
//         ),
//       ),
//       child: Stack(
//         children: [
//           Padding(
//             padding: const EdgeInsets.all(8),
//             child: TextField(
//               controller: _htmlController,
//               maxLines: null,
//               expands: true,
//               keyboardType: TextInputType.multiline,
//               decoration: InputDecoration(
//                 hintText: '输入HTML代码',
//                 border: InputBorder.none,
//                 fillColor:
//                     isDarkMode ? AppTheme.darkBgColor : AppTheme.bgLightColor,
//                 filled: true,
//                 contentPadding: const EdgeInsets.all(12),
//               ),
//               style: TextStyle(
//                 fontFamily: 'monospace',
//                 fontSize: 14,
//                 color:
//                     isDarkMode
//                         ? AppTheme.darkTextColor
//                         : AppTheme.textDarkColor,
//               ),
//             ),
//           ),
//           // 处理大文本时显示加载指示器
//           if (_isProcessingLargeText)
//             Positioned.fill(
//               child: Container(
//                 color: Colors.black.withValues(alpha: 0.3),
//                 child: Center(
//                   child: Column(
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       const CircularProgressIndicator(),
//                       const SizedBox(height: 16),
//                       Text(
//                         '处理大文本...',
//                         style: TextStyle(
//                           color: isDarkMode ? Colors.white : Colors.black,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//         ],
//       ),
//     );
//   }
//
//   /// 构建预览视图
//   Widget _buildPreviewView(bool isDarkMode) {
//     // 确保预览模式下WebView已初始化
//     if (!_isWebViewInitialized && _htmlController.text.isNotEmpty) {
//       _initWebView();
//     }
//
//     return Card(
//       margin: const EdgeInsets.all(8),
//       elevation: 0,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//         side: BorderSide(
//           color: isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
//         ),
//       ),
//       child:
//           _htmlController.text.isEmpty
//               ? Center(
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     Icon(
//                       Icons.web_asset_off_outlined,
//                       size: 80,
//                       color:
//                           isDarkMode
//                               ? AppTheme.darkTextLightColor
//                               : AppTheme.textLightColor,
//                     ),
//                     const SizedBox(height: 16),
//                     Text(
//                       '无HTML内容',
//                       style: TextStyle(
//                         fontSize: 18,
//                         color:
//                             isDarkMode
//                                 ? AppTheme.darkTextLightColor
//                                 : AppTheme.textLightColor,
//                       ),
//                     ),
//                     const SizedBox(height: 8),
//                     Text(
//                       '请输入HTML代码',
//                       style: TextStyle(
//                         color:
//                             isDarkMode
//                                 ? AppTheme.darkTextLightColor
//                                 : AppTheme.textLightColor,
//                       ),
//                     ),
//                   ],
//                 ),
//               )
//               : ClipRRect(
//                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//                 child:
//                     _isWebViewInitialized && _webViewController != null
//                         ? WebViewWidget(controller: _webViewController!)
//                         : const Center(child: CircularProgressIndicator()),
//               ),
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/html/html_editor_screen.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/html/html_manager_screen.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:io';
//
// import 'package:file_picker/file_picker.dart';
// import 'package:flutter/material.dart';
//
// import '../config/app_theme.dart';
// import '../content/content_home_page.dart';
// import '../models/content_item.dart';
// import '../services/content_service.dart';
// import '../services/service_locator.dart';
// import 'html_editor_screen.dart';
//
// /// HTML管理屏幕
// class HtmlManagerScreen extends StatefulWidget {
//   /// 构造函数
//   const HtmlManagerScreen({super.key});
//
//   @override
//   State<HtmlManagerScreen> createState() => _HtmlManagerScreenState();
// }
//
// class _HtmlManagerScreenState extends State<HtmlManagerScreen>
//     with SingleTickerProviderStateMixin {
//   /// HTML服务
//   final _htmlService = ServiceLocator().htmlService;
//
//   /// 内容服务
//   final _contentService = ContentService();
//
//   /// 动画控制器
//   late final AnimationController _animationController;
//
//   /// 透明度动画
//   late final Animation<double> _fadeAnimation;
//
//   /// 平移动画
//   late final Animation<Offset> _slideAnimation;
//
//   /// 是否正在导入
//   bool _isImporting = false;
//
//   /// 导入文件数量
//   int _totalFiles = 0;
//
//   /// 已导入文件数量
//   int _importedFiles = 0;
//
//   @override
//   void initState() {
//     super.initState();
//     // 确保初始化内容服务
//     _contentService.initialize();
//
//     // 初始化动画控制器
//     _animationController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 800),
//     );
//
//     _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
//       CurvedAnimation(
//         parent: _animationController,
//         curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
//       ),
//     );
//
//     _slideAnimation = Tween<Offset>(
//       begin: const Offset(0, 0.1),
//       end: Offset.zero,
//     ).animate(
//       CurvedAnimation(
//         parent: _animationController,
//         curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
//       ),
//     );
//
//     // 启动动画
//     _animationController.forward();
//   }
//
//   @override
//   void dispose() {
//     _animationController.dispose();
//     super.dispose();
//   }
//
//   /// 创建新HTML
//   void _createNewHtml() {
//     Navigator.push(
//       context,
//       MaterialPageRoute(builder: (context) => const HtmlEditorScreen()),
//     );
//   }
//
//   /// 导入HTML文件
//   Future<void> _importHtmlFile() async {
//     try {
//       final result = await FilePicker.platform.pickFiles(
//         type: FileType.custom,
//         allowedExtensions: ['html', 'htm'],
//         allowMultiple: true,
//       );
//
//       if (result != null && result.files.isNotEmpty) {
//         // 显示加载指示器
//         setState(() {
//           _isImporting = true;
//           _totalFiles = result.files.length;
//           _importedFiles = 0;
//         });
//
//         for (final file in result.files) {
//           if (file.path != null) {
//             final htmlDoc = await _htmlService.importFromFile(File(file.path!));
//
//             // 将导入的HTML保存到内容库
//             await _contentService.createTextContent(
//               title: htmlDoc!.title,
//               type: ContentType.html,
//               content: htmlDoc.content,
//               tags: [],
//             );
//
//             setState(() {
//               _importedFiles++;
//             });
//           }
//         }
//
//         // 完成导入
//         setState(() {
//           _isImporting = false;
//         });
//
//         // 显示成功提示
//         if (mounted) {
//           _showSuccessDialog(result.files.length);
//         }
//       }
//     } catch (e) {
//       // 重置导入状态
//       setState(() {
//         _isImporting = false;
//       });
//
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text('导入HTML文件失败: $e'),
//             backgroundColor: Colors.red.shade700,
//             behavior: SnackBarBehavior.floating,
//           ),
//         );
//       }
//     }
//   }
//
//   /// 显示导入成功对话框
//   void _showSuccessDialog(int count) {
//     showDialog(
//       context: context,
//       builder:
//           (context) => AlertDialog(
//             shape: RoundedRectangleBorder(
//               borderRadius: BorderRadius.circular(16),
//             ),
//             title: Row(
//               children: [
//                 Icon(
//                   Icons.check_circle,
//                   color: Colors.green.shade500,
//                   size: 28,
//                 ),
//                 const SizedBox(width: 8),
//                 const Text('导入成功'),
//               ],
//             ),
//             content: Text('已成功导入 $count 个HTML文件到内容库'),
//             actions: [
//               TextButton(
//                 onPressed: () => Navigator.of(context).pop(),
//                 child: const Text('关闭'),
//               ),
//               FilledButton(
//                 onPressed: () {
//                   Navigator.of(context).pop();
//                   Navigator.push(
//                     context,
//                     MaterialPageRoute(
//                       builder: (context) => const ContentHomePage(),
//                     ),
//                   );
//                 },
//                 style: FilledButton.styleFrom(
//                   backgroundColor: AppTheme.primaryColor,
//                 ),
//                 child: const Text('前往内容库'),
//               ),
//             ],
//           ),
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     // 获取屏幕尺寸
//     final screenSize = MediaQuery.of(context).size;
//     final isSmallScreen = screenSize.width < 360;
//
//     // 获取主题
//     final isDarkMode = Theme.of(context).brightness == Brightness.dark;
//     final textColor = isDarkMode ? Colors.white : Colors.black;
//     final subtitleColor = isDarkMode ? Colors.white70 : Colors.grey[600];
//
//     // 根据屏幕尺寸调整按钮宽度
//     final buttonWidth = isSmallScreen ? 180.0 : 220.0;
//
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('HTML 管理'),
//         backgroundColor: AppTheme.primaryColor,
//         foregroundColor: Colors.white,
//         actions: [
//           // 导入按钮
//           Padding(
//             padding: const EdgeInsets.only(right: 8),
//             child: TextButton.icon(
//               icon:
//                   _isImporting
//                       ? Container(
//                         width: 20,
//                         height: 20,
//                         padding: const EdgeInsets.all(2),
//                         child: const CircularProgressIndicator(
//                           color: Colors.white,
//                           strokeWidth: 2,
//                         ),
//                       )
//                       : const Icon(
//                         Icons.file_download_outlined,
//                         color: Colors.white,
//                         size: 20,
//                       ),
//               label: Text(
//                 _isImporting ? '导入中...' : '导入',
//                 style: const TextStyle(
//                   color: Colors.white,
//                   fontWeight: FontWeight.w500,
//                 ),
//               ),
//               onPressed: _isImporting ? null : _importHtmlFile,
//               style: TextButton.styleFrom(
//                 shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(20),
//                 ),
//                 backgroundColor: Colors.white.withValues(alpha: 0.15),
//               ),
//             ),
//           ),
//         ],
//       ),
//       body: Stack(
//         children: [
//           // 主内容
//           Center(
//             child: SingleChildScrollView(
//               padding: const EdgeInsets.symmetric(vertical: 16),
//               child: FadeTransition(
//                 opacity: _fadeAnimation,
//                 child: SlideTransition(
//                   position: _slideAnimation,
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       // 顶部图标
//                       Container(
//                         width: isSmallScreen ? 80 : 100,
//                         height: isSmallScreen ? 80 : 100,
//                         decoration: BoxDecoration(
//                           color: AppTheme.primaryColor.withValues(alpha:
//                             isDarkMode ? 0.2 : 0.1,
//                           ),
//                           shape: BoxShape.circle,
//                         ),
//                         child: Icon(
//                           Icons.html,
//                           size: isSmallScreen ? 44 : 56,
//                           color: AppTheme.primaryColor,
//                         ),
//                       ),
//                       SizedBox(height: isSmallScreen ? 20 : 28),
//
//                       // 标题和说明
//                       Text(
//                         'HTML 管理',
//                         style: TextStyle(
//                           fontSize: isSmallScreen ? 22 : 24,
//                           fontWeight: FontWeight.bold,
//                           color: textColor,
//                         ),
//                       ),
//                       const SizedBox(height: 16),
//                       Padding(
//                         padding: EdgeInsets.symmetric(
//                           horizontal: isSmallScreen ? 24 : 40,
//                         ),
//                         child: Text(
//                           '创建和编辑HTML文档，所有内容将自动保存至内容库进行统一管理',
//                           style: TextStyle(
//                             fontSize: isSmallScreen ? 14 : 16,
//                             color: subtitleColor,
//                           ),
//                           textAlign: TextAlign.center,
//                         ),
//                       ),
//
//                       SizedBox(height: isSmallScreen ? 32 : 40),
//
//                       // 创建HTML按钮
//                       SizedBox(
//                         width: buttonWidth,
//                         child: FilledButton.icon(
//                           onPressed: _isImporting ? null : _createNewHtml,
//                           icon: const Icon(Icons.add_circle_outline, size: 20),
//                           label: const Text('创建新的HTML文档'),
//                           style: FilledButton.styleFrom(
//                             backgroundColor: AppTheme.primaryColor,
//                             foregroundColor: Colors.white,
//                             padding: EdgeInsets.symmetric(
//                               vertical: isSmallScreen ? 12 : 16,
//                               horizontal: isSmallScreen ? 16 : 24,
//                             ),
//                             shape: RoundedRectangleBorder(
//                               borderRadius: BorderRadius.circular(30),
//                             ),
//                           ),
//                         ),
//                       ),
//
//                       const SizedBox(height: 16),
//
//                       // 导入HTML按钮
//                       SizedBox(
//                         width: buttonWidth,
//                         child: OutlinedButton.icon(
//                           onPressed: _isImporting ? null : _importHtmlFile,
//                           icon: const Icon(Icons.file_download_outlined),
//                           label: const Text('导入HTML文件'),
//                           style: OutlinedButton.styleFrom(
//                             foregroundColor: AppTheme.primaryColor,
//                             side: BorderSide(
//                               color: AppTheme.primaryColor.withValues(alpha:
//                                 isDarkMode ? 0.7 : 0.5,
//                               ),
//                             ),
//                             padding: EdgeInsets.symmetric(
//                               vertical: isSmallScreen ? 12 : 16,
//                               horizontal: isSmallScreen ? 16 : 24,
//                             ),
//                             shape: RoundedRectangleBorder(
//                               borderRadius: BorderRadius.circular(30),
//                             ),
//                           ),
//                         ),
//                       ),
//
//                       SizedBox(height: isSmallScreen ? 32 : 40),
//
//                       // 前往内容库
//                       InkWell(
//                         onTap:
//                             _isImporting
//                                 ? null
//                                 : () {
//                                   Navigator.push(
//                                     context,
//                                     MaterialPageRoute(
//                                       builder:
//                                           (context) => const ContentHomePage(),
//                                     ),
//                                   );
//                                 },
//                         borderRadius: BorderRadius.circular(12),
//                         child: Padding(
//                           padding: const EdgeInsets.all(8.0),
//                           child: Row(
//                             mainAxisSize: MainAxisSize.min,
//                             children: [
//                               Icon(
//                                 Icons.library_books_outlined,
//                                 size: 20,
//                                 color: AppTheme.primaryColor,
//                               ),
//                               const SizedBox(width: 8),
//                               Text(
//                                 '查看内容库',
//                                 style: TextStyle(
//                                   color: AppTheme.primaryColor,
//                                   fontWeight: FontWeight.w500,
//                                 ),
//                               ),
//                               const SizedBox(width: 4),
//                               Icon(
//                                 Icons.arrow_forward_ios,
//                                 size: 14,
//                                 color: AppTheme.primaryColor,
//                               ),
//                             ],
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//           ),
//
//           // 导入进度指示
//           if (_isImporting)
//             Positioned(
//               bottom: 0,
//               left: 0,
//               right: 0,
//               child: Container(
//                 padding: const EdgeInsets.symmetric(
//                   vertical: 12,
//                   horizontal: 16,
//                 ),
//                 decoration: BoxDecoration(
//                   color: isDarkMode ? Colors.grey.shade800 : Colors.white,
//                   boxShadow: [
//                     BoxShadow(
//                       color: Colors.black.withValues(alpha: 0.1),
//                       blurRadius: 10,
//                       offset: const Offset(0, -2),
//                     ),
//                   ],
//                 ),
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Row(
//                       children: [
//                         const SizedBox(
//                           width: 20,
//                           height: 20,
//                           child: CircularProgressIndicator(strokeWidth: 2),
//                         ),
//                         const SizedBox(width: 12),
//                         Text(
//                           '正在导入HTML文件 ($_importedFiles/$_totalFiles)',
//                           style: TextStyle(
//                             fontWeight: FontWeight.w500,
//                             color: textColor,
//                           ),
//                         ),
//                       ],
//                     ),
//                     const SizedBox(height: 8),
//                     LinearProgressIndicator(
//                       value: _totalFiles > 0 ? _importedFiles / _totalFiles : 0,
//                       backgroundColor: Colors.grey.withValues(alpha: 0.2),
//                       valueColor: AlwaysStoppedAnimation<Color>(
//                         AppTheme.primaryColor,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//         ],
//       ),
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/html/html_manager_screen.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/html/html_service.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:convert';
// import 'dart:io';
// import 'dart:ui' as ui;
//
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:share_plus/share_plus.dart';
// import 'package:shared_preferences/shared_preferences.dart';
//
// import 'html_document.dart';
//
// /// HTML服务类
// class HtmlService {
//   /// 存储键
//   static const String _keyHtmlDocuments = 'html_documents';
//
//   /// 单例实例
//   static final HtmlService _instance = HtmlService._internal();
//
//   /// 工厂构造函数
//   factory HtmlService() => _instance;
//
//   /// 内部构造函数
//   HtmlService._internal();
//
//   /// 保存的HTML文档列表
//   final List<HtmlDocument> _documents = [];
//
//   /// 获取所有HTML文档
//   List<HtmlDocument> get documents => List.unmodifiable(_documents);
//
//   /// 初始化服务
//   Future<void> initialize() async {
//     // 从存储中加载HTML文档
//     await _loadDocuments();
//   }
//
//   /// 从存储中加载HTML文档
//   Future<void> _loadDocuments() async {
//     try {
//       final prefs = await SharedPreferences.getInstance();
//       final jsonString = prefs.getString(_keyHtmlDocuments);
//
//       if (jsonString != null) {
//         final jsonList = jsonDecode(jsonString) as List;
//         _documents.clear();
//
//         for (final item in jsonList) {
//           _documents.add(HtmlDocument.fromJson(item as Map<String, dynamic>));
//         }
//       }
//     } catch (e) {
//       debugPrint('加载HTML文档失败: $e');
//     }
//   }
//
//   /// 保存HTML文档到存储
//   Future<void> _saveDocuments() async {
//     try {
//       final prefs = await SharedPreferences.getInstance();
//       final jsonList = _documents.map((doc) => doc.toJson()).toList();
//       final jsonString = jsonEncode(jsonList);
//
//       await prefs.setString(_keyHtmlDocuments, jsonString);
//     } catch (e) {
//       debugPrint('保存HTML文档失败: $e');
//     }
//   }
//
//   /// 添加HTML文档
//   Future<void> addDocument(HtmlDocument document) async {
//     _documents.add(document);
//     await _saveDocuments();
//   }
//
//   /// 更新HTML文档
//   Future<void> updateDocument(HtmlDocument document) async {
//     final index = _documents.indexWhere((doc) => doc.id == document.id);
//
//     if (index != -1) {
//       _documents[index] = document;
//       await _saveDocuments();
//     }
//   }
//
//   /// 删除HTML文档
//   Future<void> deleteDocument(String id) async {
//     _documents.removeWhere((doc) => doc.id == id);
//     await _saveDocuments();
//   }
//
//   /// 获取HTML文档
//   HtmlDocument? getDocument(String id) {
//     try {
//       return _documents.firstWhere((doc) => doc.id == id);
//     } catch (e) {
//       return null;
//     }
//   }
//
//   /// 从文件导入HTML
//   Future<HtmlDocument?> importFromFile(File file) async {
//     try {
//       final document = await HtmlDocument.fromFile(file);
//       await addDocument(document);
//       return document;
//     } catch (e) {
//       debugPrint('导入HTML文件失败: $e');
//       return null;
//     }
//   }
//
//   /// 从字符串创建HTML
//   Future<HtmlDocument> createFromString(
//     String htmlContent, {
//     String? title,
//   }) async {
//     final document = HtmlDocument(
//       content: htmlContent,
//       title: title ?? '未命名HTML',
//     );
//
//     await addDocument(document);
//     return document;
//   }
//
//   /// 创建新的HTML文档
//   Future<HtmlDocument> createNewDocument({String? title}) async {
//     final document = HtmlDocument(
//       content: HtmlDocument.getBasicTemplate(),
//       title: title ?? '新HTML文档',
//     );
//
//     await addDocument(document);
//     return document;
//   }
//
//   /// 将HTML转换为图片
//   Future<Uint8List?> convertToImage(
//     HtmlDocument document, {
//     double width = 800,
//     double height = 1200, // 增加默认高度
//     double scale = 2.0,
//   }) async {
//     try {
//       // 创建一个临时文件来存储HTML内容
//       final tempDir = await getTemporaryDirectory();
//       final tempFile = File('${tempDir.path}/temp_${document.id}.html');
//
//       // 修改HTML内容，添加更好的样式
//       String enhancedHtml = document.content;
//
//       // 如果HTML没有设置合适的样式，添加一些基本样式
//       if (!enhancedHtml.contains('<style')) {
//         enhancedHtml = enhancedHtml.replaceFirst('</head>', '''
//   <style>
//     body {
//       font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
//       line-height: 1.6;
//       color: #333;
//       max-width: 100%;
//       margin: 0 auto;
//       padding: 20px;
//     }
//     img { max-width: 100%; height: auto; }
//     pre, code { white-space: pre-wrap; overflow-wrap: break-word; }
//     table { width: 100%; border-collapse: collapse; }
//     th, td { padding: 8px; border: 1px solid #ddd; }
//   </style>
// </head>''');
//       }
//
//       // 确保内容不会被挤压
//       enhancedHtml = enhancedHtml.replaceFirst(
//         '<body',
//         '<body style="width:100%; box-sizing:border-box; padding:20px;"',
//       );
//
//       await tempFile.writeAsString(enhancedHtml);
//
//       // 创建一个临时的PNG文件
//       final pngFile = File('${tempDir.path}/temp_${document.id}.png');
//
//       // 尝试使用系统命令将HTML转换为图片
//       try {
//         // 尝试使用wkhtmltoimage命令（如果系统上安装了）
//         final result = await Process.run('wkhtmltoimage', [
//           '--width',
//           width.toString(),
//           '--height',
//           height.toString(),
//           '--quality',
//           '90',
//           tempFile.path,
//           pngFile.path,
//         ]);
//
//         if (result.exitCode == 0 && await pngFile.exists()) {
//           final bytes = await pngFile.readAsBytes();
//
//           // 清理临时文件
//           await tempFile.delete();
//           await pngFile.delete();
//
//           debugPrint('HTML转图片成功 (使用系统命令): ${bytes.length} 字节');
//           return bytes;
//         }
//       } catch (e) {
//         debugPrint('系统命令转换失败，尝试使用备用方法: $e');
//       }
//
//       // 备用方法：创建一个简单的图像
//       final recorder = ui.PictureRecorder();
//       final canvas = Canvas(recorder);
//
//       // 绘制白色背景
//       final paint = Paint()..color = Colors.white;
//       canvas.drawRect(Rect.fromLTWH(0, 0, width, height), paint);
//
//       // 绘制一些文本
//       final textPainter = TextPainter(
//         text: TextSpan(
//           text: '无法渲染HTML内容',
//           style: const TextStyle(color: Colors.black, fontSize: 24),
//         ),
//         textDirection: TextDirection.ltr,
//       );
//
//       textPainter.layout(maxWidth: width - 40);
//       textPainter.paint(canvas, Offset(20, 20));
//
//       // 完成绘制并获取图像
//       final picture = recorder.endRecording();
//       final image = await picture.toImage(width.toInt(), height.toInt());
//
//       // 获取图像数据
//       final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
//
//       // 清理临时文件
//       await tempFile.delete();
//       if (await pngFile.exists()) {
//         await pngFile.delete();
//       }
//
//       // 释放资源
//       image.dispose();
//
//       if (byteData != null) {
//         debugPrint(
//           'HTML转图片成功 (使用备用方法): ${byteData.buffer.asUint8List().length} 字节',
//         );
//         return byteData.buffer.asUint8List();
//       }
//
//       return null;
//     } catch (e) {
//       debugPrint('HTML转图片失败: $e');
//       return null;
//     }
//   }
//
//   /// 分享为图片
//   Future<void> shareAsImage(HtmlDocument document) async {
//     try {
//       final imageData = await convertToImage(document);
//
//       if (imageData != null) {
//         // 保存图片到临时文件
//         final tempDir = await getTemporaryDirectory();
//         final tempFile = File(
//           '${tempDir.path}/${document.title}_${DateTime.now().millisecondsSinceEpoch}.png',
//         );
//
//         await tempFile.writeAsBytes(imageData);
//
//         // 分享文件
//         await SharePlus.instance.share(
//           ShareParams(files: [XFile(tempFile.path)], text: document.title),
//         );
//       }
//     } catch (e) {
//       debugPrint('分享HTML图片失败: $e');
//     }
//   }
//
//   /// 保存为图片
//   Future<File?> saveAsImage(HtmlDocument document, {String? outputPath}) async {
//     try {
//       final imageData = await convertToImage(document);
//
//       if (imageData != null) {
//         // 使用用户指定的路径或默认路径
//         final String filePath;
//         if (outputPath != null) {
//           filePath =
//               '$outputPath/${document.title}_${DateTime.now().millisecondsSinceEpoch}.png';
//         } else {
//           // 保存图片到文档目录
//           final docDir = await getApplicationDocumentsDirectory();
//           filePath =
//               '${docDir.path}/${document.title}_${DateTime.now().millisecondsSinceEpoch}.png';
//         }
//
//         final imageFile = File(filePath);
//         await imageFile.writeAsBytes(imageData);
//         return imageFile;
//       }
//
//       return null;
//     } catch (e) {
//       debugPrint('保存HTML图片失败: $e');
//       return null;
//     }
//   }
//
//   /// 从Markdown内容中提取HTML代码
//   List<String> extractHtmlFromMarkdown(String markdown) {
//     final htmlRegex = RegExp(r'```html\s*([\s\S]*?)\s*```');
//     final matches = htmlRegex.allMatches(markdown);
//
//     return matches.map((match) => match.group(1) ?? '').toList();
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/html/html_service.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/markdown/controllers/markdown_render_controller.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
// import '../../services/service_locator.dart';
// import '../../services/settings_service.dart';
// import '../../services/storage_service.dart';
// import '../models/markdown_template.dart';
// import '../models/markdown_watermark.dart';
// import '../models/markdown_render_style.dart';
//
// /// Markdown渲染控制器
// class MarkdownRenderController extends ChangeNotifier {
//   /// 文本编辑控制器
//   late final TextEditingController markdownController;
//
//   /// 当前选中的模板
//   late MarkdownTemplate _selectedTemplate;
//   MarkdownTemplate get selectedTemplate => _selectedTemplate;
//
//   /// 服务定位器
//   final _serviceLocator = ServiceLocator();
//
//   /// 存储服务
//   late final StorageService _storageService;
//
//   /// 设置服务
//   late final SettingsService _settingsService;
//
//   /// 保存的模板键
//   static const String _keySavedTemplate = 'saved_markdown_template';
//
//   /// 内容区域的高度
//   double _contentHeight = 0;
//   double get contentHeight => _contentHeight;
//
//   /// 是否正在加载
//   bool _isLoading = false;
//   bool get isLoading => _isLoading;
//
//   MarkdownRenderController({String? initialMarkdown}) {
//     markdownController = TextEditingController(text: initialMarkdown ?? '');
//
//     // 初始化服务
//     _storageService = _serviceLocator.storageService;
//     _settingsService = _serviceLocator.settingsService;
//
//     // 加载保存的模板或使用默认模板
//     _loadSavedTemplate();
//   }
//
//   /// 设置内容高度
//   void setContentHeight(double height) {
//     _contentHeight = height > 0 ? height : 1000;
//     debugPrint("内容高度设置为: $_contentHeight");
//   }
//
//   /// 设置加载状态
//   void setLoading(bool loading) {
//     _isLoading = loading;
//     notifyListeners();
//   }
//
//   /// 更新Markdown文本
//   void updateMarkdownText(String text) {
//     markdownController.text = text;
//     notifyListeners();
//   }
//
//   /// 触发UI更新
//   void triggerUpdate() {
//     notifyListeners();
//   }
//
//   /// 更新模板
//   void updateTemplate(MarkdownTemplate template) {
//     _selectedTemplate = template;
//     _saveTemplate();
//     notifyListeners();
//   }
//
//   /// 更新样式
//   void updateStyle(MarkdownRenderStyle style) {
//     _selectedTemplate = _selectedTemplate.copyWith(style: style);
//     _saveTemplate();
//     notifyListeners();
//   }
//
//   /// 更新水印
//   void updateWatermark(MarkdownWatermark watermark) {
//     _selectedTemplate = _selectedTemplate.copyWith(watermark: watermark);
//     _saveTemplate();
//     notifyListeners();
//   }
//
//   /// 加载保存的模板
//   void _loadSavedTemplate() {
//     final savedTemplateJson = _storageService.getJson(_keySavedTemplate);
//     if (savedTemplateJson != null) {
//       try {
//         // 从JSON中恢复水印设置
//         final watermarkJson =
//             savedTemplateJson['watermark'] as Map<String, dynamic>?;
//         MarkdownWatermark watermark;
//
//         if (watermarkJson != null) {
//           watermark = MarkdownWatermark(
//             text:
//                 watermarkJson['text'] ??
//                 _settingsService.settings.watermark.text,
//             textColor: Color(
//               watermarkJson['textColor'] ?? Colors.grey.toARGB32(),
//             ),
//             fontSize: watermarkJson['fontSize'] ?? 12.0,
//             fontFamily: watermarkJson['fontFamily'] ?? 'Roboto',
//             fontStyle:
//                 watermarkJson['fontStyle'] == 1
//                     ? FontStyle.italic
//                     : FontStyle.normal,
//             fontWeight:
//                 watermarkJson['fontWeight'] == 1
//                     ? FontWeight.bold
//                     : FontWeight.normal,
//             isVisible: watermarkJson['isVisible'] ?? false,
//             position:
//                 WatermarkPosition.values[watermarkJson['position'] ??
//                     WatermarkPosition.bottomCenter.index],
//             opacity: watermarkJson['opacity'] ?? 0.7,
//           );
//         } else {
//           watermark = _settingsService.settings.watermark;
//         }
//
//         // 从JSON中恢复样式设置
//         final styleJson = savedTemplateJson['style'] as Map<String, dynamic>?;
//
//         // 创建模板
//         final templateId = savedTemplateJson['id'] as String? ?? 'modern';
//         MarkdownTemplate baseTemplate =
//             MarkdownTemplate.getPredefinedTemplates().firstWhere(
//               (t) => t.id == templateId,
//               orElse: () => MarkdownTemplate.modern(),
//             );
//
//         // 如果有保存的样式修改，应用这些修改
//         MarkdownRenderStyle style = baseTemplate.style;
//         if (styleJson != null) {
//           style = style.copyWith(
//             baseFontSize: styleJson['baseFontSize']?.toDouble(),
//             borderRadius: styleJson['borderRadius']?.toDouble(),
//             headingAlignment:
//                 styleJson['headingAlignment'] != null
//                     ? TextAlign.values[styleJson['headingAlignment']]
//                     : null,
//             listItemStyle: styleJson['listItemStyle'],
//             checkboxUncheckedStyle: styleJson['checkboxUncheckedStyle'],
//             checkboxCheckedStyle: styleJson['checkboxCheckedStyle'],
//             fontFamily: styleJson['fontFamily'],
//             codeFontFamily: styleJson['codeFontFamily'],
//           );
//         }
//
//         _selectedTemplate = baseTemplate.copyWith(
//           watermark: watermark,
//           style: style,
//         );
//       } catch (e) {
//         debugPrint('加载保存的模板失败: $e');
//         _selectedTemplate = MarkdownTemplate.modern().copyWith(
//           watermark: _settingsService.settings.watermark,
//         );
//       }
//     } else {
//       _selectedTemplate = MarkdownTemplate.modern().copyWith(
//         watermark: _settingsService.settings.watermark,
//       );
//     }
//   }
//
//   /// 保存当前模板
//   Future<void> _saveTemplate() async {
//     try {
//       // 将模板转换为JSON
//       final templateJson = {
//         'id': _selectedTemplate.id,
//         'name': _selectedTemplate.name,
//         'description': _selectedTemplate.description,
//         'style': {
//           'baseFontSize': _selectedTemplate.style.baseFontSize,
//           'borderRadius': _selectedTemplate.style.borderRadius,
//           'headingAlignment': _selectedTemplate.style.headingAlignment.index,
//           'listItemStyle': _selectedTemplate.style.listItemStyle,
//           'checkboxUncheckedStyle':
//               _selectedTemplate.style.checkboxUncheckedStyle,
//           'checkboxCheckedStyle': _selectedTemplate.style.checkboxCheckedStyle,
//           'fontFamily': _selectedTemplate.style.fontFamily,
//           'codeFontFamily': _selectedTemplate.style.codeFontFamily,
//         },
//         'watermark': {
//           'text': _selectedTemplate.watermark.text,
//           'textColor': _selectedTemplate.watermark.textColor.toARGB32(),
//           'fontSize': _selectedTemplate.watermark.fontSize,
//           'fontFamily': _selectedTemplate.watermark.fontFamily,
//           'fontStyle':
//               _selectedTemplate.watermark.fontStyle == FontStyle.italic ? 1 : 0,
//           'fontWeight':
//               _selectedTemplate.watermark.fontWeight == FontWeight.bold ? 1 : 0,
//           'isVisible': _selectedTemplate.watermark.isVisible,
//           'position': _selectedTemplate.watermark.position.index,
//           'opacity': _selectedTemplate.watermark.opacity,
//         },
//       };
//
//       // 保存到存储
//       await _storageService.setJson(_keySavedTemplate, templateJson);
//
//       // 同时更新应用设置中的水印
//       await _settingsService.updateWatermark(_selectedTemplate.watermark);
//
//       debugPrint('模板和水印设置已保存');
//     } catch (e) {
//       debugPrint('保存模板失败: $e');
//     }
//   }
//
//   @override
//   void dispose() {
//     markdownController.dispose();
//     super.dispose();
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/markdown/controllers/markdown_render_controller.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/markdown/markdown_render_screen.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
//
// import '../content/content_save_button.dart';
// import '../models/content_item.dart';
// import 'controllers/markdown_render_controller.dart';
// import 'screens/markdown_section_editor_screen.dart';
// import 'services/markdown_export_service.dart';
// import 'widgets/markdown_render_tabs.dart';
//
// /// Markdown渲染屏幕
// class MarkdownRenderScreen extends StatefulWidget {
//   /// 初始Markdown文本
//   final String? initialMarkdown;
//
//   const MarkdownRenderScreen({super.key, this.initialMarkdown});
//
//   @override
//   State<MarkdownRenderScreen> createState() => _MarkdownRenderScreenState();
// }
//
// class _MarkdownRenderScreenState extends State<MarkdownRenderScreen>
//     with SingleTickerProviderStateMixin {
//   /// 标签控制器
//   late TabController _tabController;
//
//   /// 渲染组件的全局键，用于截图
//   final GlobalKey _renderKey = GlobalKey();
//
//   /// 内容区域的全局键
//   final GlobalKey _contentAreaKey = GlobalKey();
//
//   /// 滚动控制器
//   final ScrollController _scrollController = ScrollController();
//
//   /// 控制器
//   late MarkdownRenderController _controller;
//
//   @override
//   void initState() {
//     super.initState();
//     _tabController = TabController(length: 4, vsync: this);
//     _controller = MarkdownRenderController(
//       initialMarkdown: widget.initialMarkdown,
//     );
//
//     // 延迟计算内容高度，确保渲染完成
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       _calculateContentHeight();
//     });
//   }
//
//   /// 计算内容区域高度
//   void _calculateContentHeight() {
//     final RenderBox? renderBox =
//         _contentAreaKey.currentContext?.findRenderObject() as RenderBox?;
//     if (renderBox != null) {
//       _controller.setContentHeight(renderBox.size.height);
//     } else {
//       _controller.setContentHeight(1000);
//       // 延迟再次尝试计算
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         _calculateContentHeight();
//       });
//     }
//   }
//
//   @override
//   void dispose() {
//     // 安全处理ScrollController
//     if (_scrollController.hasClients) {
//       _scrollController.removeListener(() {});
//     }
//     _scrollController.dispose();
//     _tabController.dispose();
//     _controller.dispose();
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         // 点击空白处隐藏键盘
//         FocusScope.of(context).unfocus();
//       },
//       child: Scaffold(
//         appBar: AppBar(
//           title: const Text('Markdown渲染'),
//           centerTitle: false,
//           actions: [
//             ContentSaveButton(
//               title: '保存的 Markdown',
//               content: _controller.markdownController.text,
//               contentType: ContentType.markdown,
//               onSaved: (item) {
//                 ScaffoldMessenger.of(context).showSnackBar(
//                   const SnackBar(
//                     content: Text('内容已保存到内容库'),
//                     backgroundColor: Colors.green,
//                   ),
//                 );
//               },
//             ),
//             // 导出菜单
//             PopupMenuButton<String>(
//               icon: Icon(
//                 Icons.more_vert,
//                 color:
//                     Theme.of(context).brightness == Brightness.light
//                         ? Colors.black87
//                         : Colors.white70,
//               ),
//               tooltip: '导出选项',
//               elevation: 3,
//               shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(12),
//               ),
//               position: PopupMenuPosition.under,
//               offset: const Offset(0, 8),
//               color: Theme.of(context).cardColor,
//               onSelected: (value) async {
//                 switch (value) {
//                   case 'share':
//                     await _handleShare();
//                     break;
//                   case 'copy':
//                     await _handleCopy();
//                     break;
//                   case 'save':
//                     await _handleSave();
//                     break;
//                   case 'section_render':
//                     _navigateToSectionEditor();
//                     break;
//                 }
//               },
//               itemBuilder:
//                   (context) => [
//                     PopupMenuItem(
//                       height: 48,
//                       value: 'share',
//                       child: Row(
//                         children: [
//                           Icon(
//                             Icons.ios_share,
//                             color: Theme.of(context).primaryColor,
//                             size: 22,
//                           ),
//                           const SizedBox(width: 12),
//                           const Text(
//                             '分享图片',
//                             style: TextStyle(
//                               fontWeight: FontWeight.w500,
//                               fontSize: 15,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                     const PopupMenuDivider(height: 1),
//                     PopupMenuItem(
//                       height: 48,
//                       value: 'copy',
//                       child: Row(
//                         children: [
//                           Icon(
//                             Icons.content_copy,
//                             color: Theme.of(context).primaryColor,
//                             size: 22,
//                           ),
//                           const SizedBox(width: 12),
//                           const Text(
//                             '复制markdown内容',
//                             style: TextStyle(
//                               fontWeight: FontWeight.w500,
//                               fontSize: 15,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                     const PopupMenuDivider(height: 1),
//                     PopupMenuItem(
//                       height: 48,
//                       value: 'save',
//                       child: Row(
//                         children: [
//                           Icon(
//                             Icons.save_alt,
//                             color: Theme.of(context).primaryColor,
//                             size: 22,
//                           ),
//                           const SizedBox(width: 12),
//                           const Text(
//                             '保存到相册',
//                             style: TextStyle(
//                               fontWeight: FontWeight.w500,
//                               fontSize: 15,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                     const PopupMenuDivider(height: 1),
//                     PopupMenuItem(
//                       height: 48,
//                       value: 'section_render',
//                       child: Row(
//                         children: [
//                           Icon(
//                             Icons.content_cut,
//                             color: Theme.of(context).primaryColor,
//                             size: 22,
//                           ),
//                           const SizedBox(width: 12),
//                           const Text(
//                             '分段渲染',
//                             style: TextStyle(
//                               fontWeight: FontWeight.w500,
//                               fontSize: 15,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//             ),
//           ],
//           bottom: TabBar(
//             controller: _tabController,
//             labelStyle: const TextStyle(
//               fontWeight: FontWeight.w600,
//               fontSize: 14,
//             ),
//             unselectedLabelStyle: const TextStyle(
//               fontWeight: FontWeight.w400,
//               fontSize: 14,
//             ),
//             indicatorSize: TabBarIndicatorSize.label,
//             indicatorWeight: 3,
//             tabs: const [
//               Tab(text: '编辑'),
//               Tab(text: '模板'),
//               Tab(text: '样式'),
//               Tab(text: '水印'),
//             ],
//           ),
//         ),
//         body: MarkdownRenderTabs(
//           controller: _controller,
//           renderKey: _renderKey,
//           contentAreaKey: _contentAreaKey,
//           scrollController: _scrollController,
//           tabController: _tabController,
//         ),
//       ),
//     );
//   }
//
//   /// 处理分享
//   Future<void> _handleShare() async {
//     await MarkdownExportService.handleShare(
//       markdownText: _controller.markdownController.text,
//       renderKey: _renderKey,
//       context: context,
//       tabController: _tabController,
//       showSnackBar: _showSnackBar,
//     );
//   }
//
//   /// 处理复制
//   Future<void> _handleCopy() async {
//     await MarkdownExportService.handleCopy(
//       markdownText: _controller.markdownController.text,
//       renderKey: _renderKey,
//       showSnackBar: _showSnackBar,
//     );
//   }
//
//   /// 处理保存
//   Future<void> _handleSave() async {
//     await MarkdownExportService.handleSave(
//       markdownText: _controller.markdownController.text,
//       renderKey: _renderKey,
//       context: context,
//       showSnackBar: _showSnackBar,
//     );
//   }
//
//   /// 显示提示消息
//   void _showSnackBar(String message) {
//     ScaffoldMessenger.of(
//       context,
//     ).showSnackBar(SnackBar(content: Text(message)));
//   }
//
//   /// 导航到分段渲染编辑器
//   void _navigateToSectionEditor() {
//     Navigator.of(context).push(
//       MaterialPageRoute(
//         builder:
//             (context) => MarkdownSectionEditorScreen(
//               initialMarkdown: _controller.markdownController.text,
//               initialTitle: '', // 可以根据需要添加标题
//               initialSubtitle: '', // 可以根据需要添加副标题
//             ),
//       ),
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/markdown/markdown_render_screen.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/markdown/models/html_template.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
//
//
// /// HTML模板类型
// enum HtmlTemplateType {
//   /// 文章类
//   article,
//
//   /// 卡片类
//   card,
//
//   /// 海报类
//   poster,
//
//   /// 简报类
//   brief,
//
//   /// 自定义
//   custom,
// }
//
// /// HTML模板模型
// class HtmlTemplate {
//   /// 模板ID
//   final String id;
//
//   /// 模板名称
//   final String name;
//
//   /// 模板描述
//   final String description;
//
//   /// 模板类型
//   final HtmlTemplateType type;
//
//   /// 模板HTML内容
//   final String htmlContent;
//
//   /// 模板缩略图URL
//   final String? thumbnailUrl;
//
//   /// 模板创建时间
//   final DateTime createdAt;
//
//   /// 模板是否为系统预设
//   final bool isSystem;
//
//   /// 创建一个HTML模板
//   const HtmlTemplate({
//     required this.id,
//     required this.name,
//     required this.description,
//     required this.type,
//     required this.htmlContent,
//     this.thumbnailUrl,
//     required this.createdAt,
//     this.isSystem = false,
//   });
//
//   /// 创建一个模板的副本并修改部分属性
//   HtmlTemplate copyWith({
//     String? id,
//     String? name,
//     String? description,
//     HtmlTemplateType? type,
//     String? htmlContent,
//     String? thumbnailUrl,
//     DateTime? createdAt,
//     bool? isSystem,
//   }) {
//     return HtmlTemplate(
//       id: id ?? this.id,
//       name: name ?? this.name,
//       description: description ?? this.description,
//       type: type ?? this.type,
//       htmlContent: htmlContent ?? this.htmlContent,
//       thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
//       createdAt: createdAt ?? this.createdAt,
//       isSystem: isSystem ?? this.isSystem,
//     );
//   }
//
//   /// 从Map创建模板
//   factory HtmlTemplate.fromJson(Map<String, dynamic> json) {
//     return HtmlTemplate(
//       id: json['id'] as String,
//       name: json['name'] as String,
//       description: json['description'] as String,
//       type: HtmlTemplateType.values[json['type'] as int],
//       htmlContent: json['htmlContent'] as String,
//       thumbnailUrl: json['thumbnailUrl'] as String?,
//       createdAt: DateTime.parse(json['createdAt'] as String),
//       isSystem: json['isSystem'] as bool? ?? false,
//     );
//   }
//
//   /// 转换为Map
//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'name': name,
//       'description': description,
//       'type': type.index,
//       'htmlContent': htmlContent,
//       'thumbnailUrl': thumbnailUrl,
//       'createdAt': createdAt.toIso8601String(),
//       'isSystem': isSystem,
//     };
//   }
//
//   /// 获取预设HTML模板列表
//   static List<HtmlTemplate> getPredefinedTemplates() {
//     return [simpleArticle(), modernCard(), elegantPoster(), simpleBrief()];
//   }
//
//   /// 简约文章模板
//   static HtmlTemplate simpleArticle() {
//     return HtmlTemplate(
//       id: 'simple_article',
//       name: '简约文章',
//       description: '简洁清爽的文章排版风格',
//       type: HtmlTemplateType.article,
//       htmlContent: '''
// <!DOCTYPE html>
// <html>
// <head>
//   <meta charset="UTF-8">
//   <meta name="viewport" content="width=device-width, initial-scale=1.0">
//   <style>
//     body {
//       font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
//       line-height: 1.6;
//       color: #333;
//       max-width: 800px;
//       margin: 0 auto;
//       padding: 20px;
//       background-color: #fff;
//     }
//     h1 {
//       font-size: 28px;
//       margin-bottom: 16px;
//       color: #222;
//     }
//     h2 {
//       font-size: 22px;
//       margin-top: 24px;
//       margin-bottom: 12px;
//       color: #333;
//     }
//     p {
//       margin-bottom: 16px;
//       font-size: 16px;
//     }
//     img {
//       max-width: 100%;
//       height: auto;
//       border-radius: 6px;
//     }
//     code {
//       background-color: #f5f5f5;
//       padding: 2px 4px;
//       border-radius: 3px;
//       font-family: monospace;
//       font-size: 14px;
//     }
//     pre {
//       background-color: #f5f5f5;
//       padding: 12px;
//       border-radius: 6px;
//       overflow-x: auto;
//     }
//     blockquote {
//       border-left: 4px solid #ddd;
//       padding-left: 16px;
//       margin-left: 0;
//       color: #666;
//     }
//     a {
//       color: #0070f3;
//       text-decoration: none;
//     }
//     a:hover {
//       text-decoration: underline;
//     }
//     .content-container {
//       padding: 24px;
//       background-color: #fff;
//       border-radius: 12px;
//       box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
//     }
//     .footer {
//       margin-top: 40px;
//       text-align: center;
//       color: #999;
//       font-size: 14px;
//     }
//     .watermark {
//       position: fixed;
//       bottom: 10px;
//       right: 10px;
//       opacity: 0.5;
//       font-size: 12px;
//       color: #999;
//     }
//   </style>
// </head>
// <body>
//   <div class="content-container">
//     <!-- 标题区域 -->
//     <h1>{{title}}</h1>
//
//     <!-- 内容区域 -->
//     <div class="markdown-content">
//       {{content}}
//     </div>
//
//     <!-- 页脚区域 -->
//     <div class="footer">
//       <p>{{footer}}</p>
//     </div>
//   </div>
//
//   <!-- 水印 -->
//   <div class="watermark">{{watermark}}</div>
// </body>
// </html>
// ''',
//       createdAt: DateTime.now(),
//       isSystem: true,
//     );
//   }
//
//   /// 现代卡片模板
//   static HtmlTemplate modernCard() {
//     return HtmlTemplate(
//       id: 'modern_card',
//       name: '现代卡片',
//       description: '现代化的卡片设计风格',
//       type: HtmlTemplateType.card,
//       htmlContent: '''
// <!DOCTYPE html>
// <html>
// <head>
//   <meta charset="UTF-8">
//   <meta name="viewport" content="width=device-width, initial-scale=1.0">
//   <style>
//     body {
//       font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
//       background-color: #f7f9fc;
//       margin: 0;
//       padding: 20px;
//       display: flex;
//       justify-content: center;
//       align-items: center;
//       min-height: 100vh;
//     }
//     .card {
//       background: white;
//       border-radius: 16px;
//       overflow: hidden;
//       width: 100%;
//       max-width: 500px;
//       box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
//     }
//     .card-header {
//       background: linear-gradient(135deg, #6e8efb, #a777e3);
//       color: white;
//       padding: 20px;
//     }
//     .card-header h1 {
//       margin: 0;
//       font-size: 24px;
//       font-weight: 600;
//     }
//     .card-content {
//       padding: 24px;
//     }
//     p {
//       margin: 0 0 16px;
//       font-size: 16px;
//       line-height: 1.6;
//       color: #4a5568;
//     }
//     img {
//       max-width: 100%;
//       border-radius: 8px;
//       margin: 16px 0;
//     }
//     .card-footer {
//       border-top: 1px solid #eee;
//       padding: 16px 24px;
//       display: flex;
//       justify-content: space-between;
//       align-items: center;
//       color: #718096;
//       font-size: 14px;
//     }
//     code {
//       background-color: #edf2f7;
//       padding: 2px 4px;
//       border-radius: 4px;
//       font-family: monospace;
//       font-size: 14px;
//       color: #5a67d8;
//     }
//     blockquote {
//       border-left: 4px solid #e2e8f0;
//       padding-left: 16px;
//       margin-left: 0;
//       color: #718096;
//     }
//   </style>
// </head>
// <body>
//   <div class="card">
//     <div class="card-header">
//       <h1>{{title}}</h1>
//     </div>
//     <div class="card-content">
//       {{content}}
//     </div>
//     <div class="card-footer">
//       <div>{{date}}</div>
//       <div>{{watermark}}</div>
//     </div>
//   </div>
// </body>
// </html>
// ''',
//       createdAt: DateTime.now(),
//       isSystem: true,
//     );
//   }
//
//   /// 优雅海报模板
//   static HtmlTemplate elegantPoster() {
//     return HtmlTemplate(
//       id: 'elegant_poster',
//       name: '优雅海报',
//       description: '适合制作精美海报的模板',
//       type: HtmlTemplateType.poster,
//       htmlContent: '''
// <!DOCTYPE html>
// <html>
// <head>
//   <meta charset="UTF-8">
//   <meta name="viewport" content="width=device-width, initial-scale=1.0">
//   <style>
//     body {
//       font-family: 'Helvetica Neue', Arial, sans-serif;
//       margin: 0;
//       padding: 0;
//       background-color: #f8f9fa;
//       display: flex;
//       justify-content: center;
//       align-items: center;
//       min-height: 100vh;
//     }
//     .poster {
//       width: 100%;
//       max-width: 800px;
//       aspect-ratio: 16 / 9;
//       background: linear-gradient(to right, #ffffff, #f5f7fa);
//       position: relative;
//       overflow: hidden;
//       box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
//       border-radius: 24px;
//     }
//     .poster-content {
//       display: flex;
//       height: 100%;
//     }
//     .poster-left {
//       width: 55%;
//       padding: 40px;
//       box-sizing: border-box;
//       display: flex;
//       flex-direction: column;
//       justify-content: center;
//     }
//     .poster-right {
//       width: 45%;
//       background-size: cover;
//       background-position: center;
//       background-image: url('{{image_url}}');
//     }
//     h1 {
//       font-size: 36px;
//       font-weight: 800;
//       margin: 0 0 20px;
//       background: linear-gradient(135deg, #6366F1, #4F46E5);
//       -webkit-background-clip: text;
//       -webkit-text-fill-color: transparent;
//       line-height: 1.2;
//     }
//     .subtitle {
//       font-size: 18px;
//       color: #64748b;
//       margin: 0 0 30px;
//       line-height: 1.6;
//     }
//     .highlights {
//       margin: 20px 0;
//     }
//     .highlight-item {
//       display: flex;
//       align-items: center;
//       margin-bottom: 16px;
//     }
//     .highlight-icon {
//       width: 28px;
//       height: 28px;
//       border-radius: 50%;
//       background: #4F46E5;
//       display: flex;
//       align-items: center;
//       justify-content: center;
//       margin-right: 16px;
//       color: white;
//       font-weight: bold;
//     }
//     .highlight-text {
//       font-size: 16px;
//       color: #334155;
//     }
//     .poster-footer {
//       position: absolute;
//       bottom: 20px;
//       left: 40px;
//       font-size: 14px;
//       color: #94a3b8;
//     }
//     .overlay {
//       position: absolute;
//       top: 0;
//       right: 0;
//       width: 45%;
//       height: 100%;
//       background: linear-gradient(to right, rgba(255,255,255,0.9), rgba(255,255,255,0.5));
//       z-index: 1;
//     }
//   </style>
// </head>
// <body>
//   <div class="poster">
//     <div class="poster-content">
//       <div class="poster-left">
//         <h1>{{title}}</h1>
//         <div class="subtitle">{{subtitle}}</div>
//         <div class="highlights">
//           {{content}}
//         </div>
//       </div>
//       <div class="poster-right"></div>
//       <div class="overlay"></div>
//     </div>
//     <div class="poster-footer">{{footer}} | {{watermark}}</div>
//   </div>
// </body>
// </html>
// ''',
//       createdAt: DateTime.now(),
//       isSystem: true,
//     );
//   }
//
//   /// 简洁简报模板
//   static HtmlTemplate simpleBrief() {
//     return HtmlTemplate(
//       id: 'simple_brief',
//       name: '简洁简报',
//       description: '适合制作简洁简报的模板',
//       type: HtmlTemplateType.brief,
//       htmlContent: '''
// <!DOCTYPE html>
// <html>
// <head>
//   <meta charset="UTF-8">
//   <meta name="viewport" content="width=device-width, initial-scale=1.0">
//   <style>
//     body {
//       font-family: 'Helvetica Neue', Arial, sans-serif;
//       margin: 0;
//       padding: 30px;
//       background-color: #ffffff;
//       color: #333;
//     }
//     .brief {
//       max-width: 800px;
//       margin: 0 auto;
//       border: 1px solid #e5e7eb;
//       border-radius: 12px;
//       overflow: hidden;
//     }
//     .brief-header {
//       padding: 24px;
//       background-color: #f9fafb;
//       border-bottom: 1px solid #e5e7eb;
//     }
//     .brief-title {
//       margin: 0;
//       font-size: 24px;
//       font-weight: 600;
//       color: #111827;
//     }
//     .brief-date {
//       margin-top: 8px;
//       font-size: 14px;
//       color: #6b7280;
//     }
//     .brief-content {
//       padding: 24px;
//     }
//     .brief-section {
//       margin-bottom: 24px;
//     }
//     .section-title {
//       font-size: 18px;
//       font-weight: 600;
//       color: #374151;
//       margin: 0 0 16px 0;
//       padding-bottom: 8px;
//       border-bottom: 2px solid #e5e7eb;
//     }
//     p {
//       margin: 0 0 16px;
//       font-size: 16px;
//       line-height: 1.6;
//     }
//     .brief-footer {
//       padding: 16px 24px;
//       background-color: #f9fafb;
//       border-top: 1px solid #e5e7eb;
//       font-size: 14px;
//       color: #6b7280;
//       text-align: center;
//     }
//     ul {
//       padding-left: 24px;
//     }
//     li {
//       margin-bottom: 8px;
//     }
//   </style>
// </head>
// <body>
//   <div class="brief">
//     <div class="brief-header">
//       <h1 class="brief-title">{{title}}</h1>
//       <div class="brief-date">{{date}}</div>
//     </div>
//     <div class="brief-content">
//       {{content}}
//     </div>
//     <div class="brief-footer">
//       {{footer}} | {{watermark}}
//     </div>
//   </div>
// </body>
// </html>
// ''',
//       createdAt: DateTime.now(),
//       isSystem: true,
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/markdown/models/html_template.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/markdown/models/markdown_render_style.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
//
// /// Markdown渲染样式配置
// class MarkdownRenderStyle {
//   /// 背景颜色
//   final Color backgroundColor;
//
//   /// 文本颜色
//   final Color textColor;
//
//   /// 代码块背景颜色
//   final Color codeBackgroundColor;
//
//   /// 代码文本颜色
//   final Color codeTextColor;
//
//   /// 引用块背景颜色
//   final Color quoteBackgroundColor;
//
//   /// 引用文本颜色
//   final Color quoteTextColor;
//
//   /// 引用边框颜色
//   final Color quoteBorderColor;
//
//   /// 主字体
//   final String fontFamily;
//
//   /// 代码字体
//   final String codeFontFamily;
//
//   /// 基础字体大小
//   final double baseFontSize;
//
//   /// 内边距
//   final EdgeInsets padding;
//
//   /// 圆角大小
//   final double borderRadius;
//
//   /// 样式名称
//   final String name;
//
//   /// 样式ID
//   final String id;
//
//   /// 背景图片资源路径
//   final String? backgroundImage;
//
//   /// 是否使用渐变背景
//   final bool useGradientBackground;
//
//   /// 渐变背景颜色列表
//   final List<Color>? gradientColors;
//
//   /// 渐变开始位置
//   final Alignment gradientBegin;
//
//   /// 渐变结束位置
//   final Alignment gradientEnd;
//
//   /// 标题对齐方式
//   final TextAlign headingAlignment;
//
//   /// 列表项标记样式
//   final String listItemStyle;
//
//   /// 复选框未选中样式
//   final String checkboxUncheckedStyle;
//
//   /// 复选框已选中样式
//   final String checkboxCheckedStyle;
//
//   /// 表格边框样式
//   final String tableStyle;
//
//   const MarkdownRenderStyle({
//     required this.backgroundColor,
//     required this.textColor,
//     required this.codeBackgroundColor,
//     required this.codeTextColor,
//     required this.quoteBackgroundColor,
//     required this.quoteTextColor,
//     required this.quoteBorderColor,
//     required this.fontFamily,
//     required this.codeFontFamily,
//     required this.baseFontSize,
//     required this.padding,
//     required this.borderRadius,
//     required this.name,
//     required this.id,
//     this.backgroundImage,
//     this.useGradientBackground = false,
//     this.gradientColors,
//     this.gradientBegin = Alignment.topLeft,
//     this.gradientEnd = Alignment.bottomRight,
//     this.headingAlignment = TextAlign.left,
//     this.listItemStyle = '•',
//     this.checkboxUncheckedStyle = '☐',
//     this.checkboxCheckedStyle = '☑',
//     this.tableStyle = 'default',
//   });
//
//   /// 创建一个样式的副本并修改部分属性
//   MarkdownRenderStyle copyWith({
//     Color? backgroundColor,
//     Color? textColor,
//     Color? codeBackgroundColor,
//     Color? codeTextColor,
//     Color? quoteBackgroundColor,
//     Color? quoteTextColor,
//     Color? quoteBorderColor,
//     String? fontFamily,
//     String? codeFontFamily,
//     double? baseFontSize,
//     EdgeInsets? padding,
//     double? borderRadius,
//     String? name,
//     String? id,
//     String? backgroundImage,
//     bool? useGradientBackground,
//     List<Color>? gradientColors,
//     Alignment? gradientBegin,
//     Alignment? gradientEnd,
//     TextAlign? headingAlignment,
//     String? listItemStyle,
//     String? checkboxUncheckedStyle,
//     String? checkboxCheckedStyle,
//     String? tableStyle,
//   }) {
//     return MarkdownRenderStyle(
//       backgroundColor: backgroundColor ?? this.backgroundColor,
//       textColor: textColor ?? this.textColor,
//       codeBackgroundColor: codeBackgroundColor ?? this.codeBackgroundColor,
//       codeTextColor: codeTextColor ?? this.codeTextColor,
//       quoteBackgroundColor: quoteBackgroundColor ?? this.quoteBackgroundColor,
//       quoteTextColor: quoteTextColor ?? this.quoteTextColor,
//       quoteBorderColor: quoteBorderColor ?? this.quoteBorderColor,
//       fontFamily: fontFamily ?? this.fontFamily,
//       codeFontFamily: codeFontFamily ?? this.codeFontFamily,
//       baseFontSize: baseFontSize ?? this.baseFontSize,
//       padding: padding ?? this.padding,
//       borderRadius: borderRadius ?? this.borderRadius,
//       name: name ?? this.name,
//       id: id ?? this.id,
//       backgroundImage: backgroundImage ?? this.backgroundImage,
//       useGradientBackground:
//           useGradientBackground ?? this.useGradientBackground,
//       gradientColors: gradientColors ?? this.gradientColors,
//       gradientBegin: gradientBegin ?? this.gradientBegin,
//       gradientEnd: gradientEnd ?? this.gradientEnd,
//       headingAlignment: headingAlignment ?? this.headingAlignment,
//       listItemStyle: listItemStyle ?? this.listItemStyle,
//       checkboxUncheckedStyle:
//           checkboxUncheckedStyle ?? this.checkboxUncheckedStyle,
//       checkboxCheckedStyle: checkboxCheckedStyle ?? this.checkboxCheckedStyle,
//       tableStyle: tableStyle ?? this.tableStyle,
//     );
//   }
//
//   /// 预定义的样式 - 浅色主题
//   static MarkdownRenderStyle light() {
//     return MarkdownRenderStyle(
//       backgroundColor: const Color(0xFFF5F5F5),
//       textColor: const Color(0xFF333333),
//       codeBackgroundColor: const Color(0xFFEEEEEE),
//       codeTextColor: const Color(0xFF333333),
//       quoteBackgroundColor: const Color(0xFFF5F5F5),
//       quoteTextColor: const Color(0xFF666666),
//       quoteBorderColor: const Color(0xFFDDDDDD),
//       fontFamily: 'Roboto',
//       codeFontFamily: 'monospace',
//       baseFontSize: 16.0,
//       padding: const EdgeInsets.all(20.0),
//       borderRadius: 16.0,
//       name: '浅色主题',
//       id: 'light',
//       listItemStyle: '•',
//     );
//   }
//
//   /// 预定义的样式 - 深色主题
//   static MarkdownRenderStyle dark() {
//     return MarkdownRenderStyle(
//       backgroundColor: const Color(0xFF1E1E1E),
//       textColor: const Color(0xFFE0E0E0),
//       codeBackgroundColor: const Color(0xFF2D3748),
//       codeTextColor: const Color(0xFFE2E8F0),
//       quoteBackgroundColor: const Color(0xFF2D3748),
//       quoteTextColor: const Color(0xFFAAAAAA),
//       quoteBorderColor: const Color(0xFF444444),
//       fontFamily: 'Roboto',
//       codeFontFamily: 'monospace',
//       baseFontSize: 16.0,
//       padding: const EdgeInsets.all(20.0),
//       borderRadius: 16.0,
//       name: '深色主题',
//       id: 'dark',
//       listItemStyle: '•',
//     );
//   }
//
//   /// 预定义的样式 - 蓝色主题
//   static MarkdownRenderStyle blue() {
//     return MarkdownRenderStyle(
//       backgroundColor: const Color(0xFFE3F2FD),
//       textColor: const Color(0xFF0D47A1),
//       codeBackgroundColor: const Color(0xFFBBDEFB),
//       codeTextColor: const Color(0xFF1565C0),
//       quoteBackgroundColor: const Color(0xFFE3F2FD),
//       quoteTextColor: const Color(0xFF1976D2),
//       quoteBorderColor: const Color(0xFF90CAF9),
//       fontFamily: 'Roboto',
//       codeFontFamily: 'monospace',
//       baseFontSize: 16.0,
//       padding: const EdgeInsets.all(20.0),
//       borderRadius: 16.0,
//       name: '蓝色主题',
//       id: 'blue',
//       listItemStyle: '•',
//     );
//   }
//
//   /// 预定义的样式 - 绿色主题
//   static MarkdownRenderStyle green() {
//     return MarkdownRenderStyle(
//       backgroundColor: const Color(0xFFE8F5E9),
//       textColor: const Color(0xFF1B5E20),
//       codeBackgroundColor: const Color(0xFFC8E6C9),
//       codeTextColor: const Color(0xFF2E7D32),
//       quoteBackgroundColor: const Color(0xFFE8F5E9),
//       quoteTextColor: const Color(0xFF388E3C),
//       quoteBorderColor: const Color(0xFFA5D6A7),
//       fontFamily: 'Roboto',
//       codeFontFamily: 'monospace',
//       baseFontSize: 16.0,
//       padding: const EdgeInsets.all(20.0),
//       borderRadius: 16.0,
//       name: '绿色主题',
//       id: 'green',
//       listItemStyle: '•',
//     );
//   }
//
//   /// 莫兰迪风格
//   static MarkdownRenderStyle morandi() {
//     return MarkdownRenderStyle(
//       backgroundColor: const Color(0xFFEFEBE7),
//       textColor: const Color(0xFF5D5B56),
//       codeBackgroundColor: const Color(0xFFE1DCD7),
//       codeTextColor: const Color(0xFF6D695F),
//       quoteBackgroundColor: const Color(0xFFE5E2DD),
//       quoteTextColor: const Color(0xFF8C857A),
//       quoteBorderColor: const Color(0xFFD2CCC6),
//       fontFamily: 'Noto Serif',
//       codeFontFamily: 'monospace',
//       baseFontSize: 16.0,
//       padding: const EdgeInsets.all(24.0),
//       borderRadius: 12.0,
//       name: '莫兰迪风格',
//       id: 'morandi',
//       headingAlignment: TextAlign.center,
//       listItemStyle: '○',
//       checkboxUncheckedStyle: '◯',
//       checkboxCheckedStyle: '●',
//       tableStyle: 'minimal',
//     );
//   }
//
//   /// 中国传统色 - 青花
//   static MarkdownRenderStyle chineseBlueWhite() {
//     return MarkdownRenderStyle(
//       backgroundColor: const Color(0xFFF8FDFF),
//       textColor: const Color(0xFF10487A),
//       codeBackgroundColor: const Color(0xFFE8F4FF),
//       codeTextColor: const Color(0xFF003972),
//       quoteBackgroundColor: const Color(0xFFECF6FF),
//       quoteTextColor: const Color(0xFF1A3A6C),
//       quoteBorderColor: const Color(0xFF6CA3D4),
//       fontFamily: 'Noto Serif SC',
//       codeFontFamily: 'Fira Code',
//       baseFontSize: 16.0,
//       padding: const EdgeInsets.all(24.0),
//       borderRadius: 8.0,
//       name: '青花瓷风格',
//       id: 'chinese_blue_white',
//       headingAlignment: TextAlign.center,
//       listItemStyle: '•',
//       checkboxUncheckedStyle: '□',
//       checkboxCheckedStyle: '■',
//       tableStyle: 'classic',
//     );
//   }
//
//   /// 中国传统色 - 胭脂
//   static MarkdownRenderStyle chineseVermilion() {
//     return MarkdownRenderStyle(
//       backgroundColor: const Color(0xFFFFF8EF),
//       textColor: const Color(0xFF8C3B3A),
//       codeBackgroundColor: const Color(0xFFF9E9E4),
//       codeTextColor: const Color(0xFF9A4639),
//       quoteBackgroundColor: const Color(0xFFFAEEE5),
//       quoteTextColor: const Color(0xFFBA5140),
//       quoteBorderColor: const Color(0xFFD08B75),
//       fontFamily: 'Noto Serif SC',
//       codeFontFamily: 'Fira Code',
//       baseFontSize: 16.0,
//       padding: const EdgeInsets.all(24.0),
//       borderRadius: 16.0,
//       name: '胭脂红风格',
//       id: 'chinese_vermilion',
//       headingAlignment: TextAlign.center,
//       listItemStyle: '❧',
//       checkboxUncheckedStyle: '❏',
//       checkboxCheckedStyle: '❐',
//       tableStyle: 'elegant',
//     );
//   }
//
//   /// 渐变背景主题
//   static MarkdownRenderStyle gradient() {
//     return MarkdownRenderStyle(
//       backgroundColor: Colors.white, // 渐变模式下仅作为fallback
//       textColor: Colors.black,
//       codeBackgroundColor: Colors.black.withValues(alpha: 0.3),
//       codeTextColor: Colors.white,
//       quoteBackgroundColor: Colors.black.withValues(alpha: 0.2),
//       quoteTextColor: Colors.white.withValues(alpha: 0.9),
//       quoteBorderColor: Colors.white.withValues(alpha: 0.5),
//       fontFamily: 'Roboto',
//       codeFontFamily: 'Fira Code',
//       baseFontSize: 16.0,
//       padding: const EdgeInsets.all(28.0),
//       borderRadius: 20.0,
//       name: '渐变背景',
//       id: 'gradient',
//       useGradientBackground: true,
//       gradientColors: [const Color(0xFF6A11CB), const Color(0xFF2575FC)],
//       gradientBegin: Alignment.topLeft,
//       gradientEnd: Alignment.bottomRight,
//       headingAlignment: TextAlign.center,
//       listItemStyle: '✧',
//       checkboxUncheckedStyle: '☆',
//       checkboxCheckedStyle: '★',
//       tableStyle: 'modern',
//     );
//   }
//
//   /// 节日风格 - 春节红
//   static MarkdownRenderStyle festiveRed() {
//     return MarkdownRenderStyle(
//       backgroundColor: const Color(0xFFFFF1F0),
//       textColor: const Color(0xFF9D2933),
//       codeBackgroundColor: const Color(0xFFFFE4E1),
//       codeTextColor: const Color(0xFFC83C23),
//       quoteBackgroundColor: const Color(0xFFFFE6E0),
//       quoteTextColor: const Color(0xFFCB3A56),
//       quoteBorderColor: const Color(0xFFEF7A82),
//       fontFamily: 'Ma Shan Zheng',
//       codeFontFamily: 'Fira Code',
//       baseFontSize: 16.0,
//       padding: const EdgeInsets.all(24.0),
//       borderRadius: 16.0,
//       name: '春节红',
//       id: 'festive_red',
//       headingAlignment: TextAlign.center,
//       listItemStyle: '❁',
//       checkboxUncheckedStyle: '❍',
//       checkboxCheckedStyle: '✓',
//       tableStyle: 'festive',
//     );
//   }
//
//   /// 竹简风格
//   static MarkdownRenderStyle bambooSlip() {
//     return MarkdownRenderStyle(
//       backgroundColor: const Color(0xFFF5ECD7),
//       textColor: const Color(0xFF4A3113),
//       codeBackgroundColor: const Color(0xFFECDFC2),
//       codeTextColor: const Color(0xFF62461B),
//       quoteBackgroundColor: const Color(0xFFEDE3CB),
//       quoteTextColor: const Color(0xFF7D6C46),
//       quoteBorderColor: const Color(0xFFC3AD7C),
//       fontFamily: 'Noto Serif SC',
//       codeFontFamily: 'monospace',
//       baseFontSize: 16.0,
//       padding: const EdgeInsets.all(28.0),
//       borderRadius: 0.0, // 方形边角模拟竹简
//       name: '竹简风格',
//       id: 'bamboo_slip',
//       headingAlignment: TextAlign.right,
//       listItemStyle: '•',
//       checkboxUncheckedStyle: '□',
//       checkboxCheckedStyle: '■',
//       tableStyle: 'bamboo',
//     );
//   }
//
//   /// 获取所有预定义样式
//   static List<MarkdownRenderStyle> getPredefinedStyles() {
//     return [
//       light(),
//       dark(),
//       blue(),
//       green(),
//       morandi(),
//       chineseBlueWhite(),
//       chineseVermilion(),
//       gradient(),
//       festiveRed(),
//       bambooSlip(),
//     ];
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/markdown/models/markdown_render_style.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/markdown/models/markdown_section.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
//
//
// /// Markdown分段标识类型
// enum SectionSeparatorType {
//   /// 水平分割线
//   horizontalRule,
//
//   /// 主标题 (# 一级标题)
//   h1,
//
//   /// 自定义分隔符
//   custom,
// }
//
// /// Markdown内容段落
// class MarkdownSection {
//   /// 段落标识符
//   final String id;
//
//   /// 段落标题（从内容中提取）
//   final String title;
//
//   /// 段落内容
//   final String content;
//
//   /// 段落原始内容（包含标题）
//   final String rawContent;
//
//   /// 在原始文档中的起始位置
//   final int startPosition;
//
//   /// 在原始文档中的结束位置
//   final int endPosition;
//
//   /// 段落的顺序编号
//   final int index;
//
//   /// 创建时间
//   final DateTime createdAt;
//
//   /// 段落关联的图片URL (可选)
//   final String? imageUrl;
//
//   /// 创建一个Markdown段落
//   const MarkdownSection({
//     required this.id,
//     required this.title,
//     required this.content,
//     required this.rawContent,
//     required this.startPosition,
//     required this.endPosition,
//     required this.index,
//     required this.createdAt,
//     this.imageUrl,
//   });
//
//   /// 创建一个段落的副本并修改部分属性
//   MarkdownSection copyWith({
//     String? id,
//     String? title,
//     String? content,
//     String? rawContent,
//     int? startPosition,
//     int? endPosition,
//     int? index,
//     DateTime? createdAt,
//     String? imageUrl,
//   }) {
//     return MarkdownSection(
//       id: id ?? this.id,
//       title: title ?? this.title,
//       content: content ?? this.content,
//       rawContent: rawContent ?? this.rawContent,
//       startPosition: startPosition ?? this.startPosition,
//       endPosition: endPosition ?? this.endPosition,
//       index: index ?? this.index,
//       createdAt: createdAt ?? this.createdAt,
//       imageUrl: imageUrl ?? this.imageUrl,
//     );
//   }
//
//   /// 从Map创建段落
//   factory MarkdownSection.fromJson(Map<String, dynamic> json) {
//     return MarkdownSection(
//       id: json['id'] as String,
//       title: json['title'] as String,
//       content: json['content'] as String,
//       rawContent: json['rawContent'] as String,
//       startPosition: json['startPosition'] as int,
//       endPosition: json['endPosition'] as int,
//       index: json['index'] as int,
//       createdAt: DateTime.parse(json['createdAt'] as String),
//       imageUrl: json['imageUrl'] as String?,
//     );
//   }
//
//   /// 转换为Map
//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'title': title,
//       'content': content,
//       'rawContent': rawContent,
//       'startPosition': startPosition,
//       'endPosition': endPosition,
//       'index': index,
//       'createdAt': createdAt.toIso8601String(),
//       'imageUrl': imageUrl,
//     };
//   }
// }
//
// /// Markdown文档段落集合
// class MarkdownSectionDocument {
//   /// 文档标题
//   final String title;
//
//   /// 文档子标题
//   final String? subtitle;
//
//   /// 创建时间
//   final DateTime createdAt;
//
//   /// 最后更新时间
//   final DateTime updatedAt;
//
//   /// 文档段落列表
//   final List<MarkdownSection> sections;
//
//   /// 原始Markdown内容
//   final String rawContent;
//
//   /// 创建一个段落文档
//   const MarkdownSectionDocument({
//     required this.title,
//     this.subtitle,
//     required this.createdAt,
//     required this.updatedAt,
//     required this.sections,
//     required this.rawContent,
//   });
//
//   /// 创建一个段落文档的副本并修改部分属性
//   MarkdownSectionDocument copyWith({
//     String? title,
//     String? subtitle,
//     DateTime? createdAt,
//     DateTime? updatedAt,
//     List<MarkdownSection>? sections,
//     String? rawContent,
//   }) {
//     return MarkdownSectionDocument(
//       title: title ?? this.title,
//       subtitle: subtitle ?? this.subtitle,
//       createdAt: createdAt ?? this.createdAt,
//       updatedAt: updatedAt ?? this.updatedAt,
//       sections: sections ?? this.sections,
//       rawContent: rawContent ?? this.rawContent,
//     );
//   }
//
//   /// 从Map创建段落文档
//   factory MarkdownSectionDocument.fromJson(Map<String, dynamic> json) {
//     return MarkdownSectionDocument(
//       title: json['title'] as String,
//       subtitle: json['subtitle'] as String?,
//       createdAt: DateTime.parse(json['createdAt'] as String),
//       updatedAt: DateTime.parse(json['updatedAt'] as String),
//       sections:
//           (json['sections'] as List)
//               .map((e) => MarkdownSection.fromJson(e as Map<String, dynamic>))
//               .toList(),
//       rawContent: json['rawContent'] as String,
//     );
//   }
//
//   /// 转换为Map
//   Map<String, dynamic> toJson() {
//     return {
//       'title': title,
//       'subtitle': subtitle,
//       'createdAt': createdAt.toIso8601String(),
//       'updatedAt': updatedAt.toIso8601String(),
//       'sections': sections.map((e) => e.toJson()).toList(),
//       'rawContent': rawContent,
//     };
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/markdown/models/markdown_section.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/markdown/models/markdown_template.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
// import 'markdown_render_style.dart';
// import 'markdown_watermark.dart';
//
// /// Markdown渲染模板配置
// class MarkdownTemplate {
//   /// 模板ID
//   final String id;
//
//   /// 模板名称
//   final String name;
//
//   /// 模板描述
//   final String description;
//
//   /// 模板样式
//   final MarkdownRenderStyle style;
//
//   /// 模板水印
//   final MarkdownWatermark watermark;
//
//   /// 是否显示头部信息（标题、日期等）
//   final bool showHeader;
//
//   /// 是否显示边框
//   final bool showBorder;
//
//   /// 边框颜色
//   final Color borderColor;
//
//   /// 边框宽度
//   final double borderWidth;
//
//   /// 是否显示阴影
//   final bool showShadow;
//
//   /// 阴影颜色
//   final Color shadowColor;
//
//   /// 阴影偏移
//   final Offset shadowOffset;
//
//   /// 阴影模糊半径
//   final double shadowBlurRadius;
//
//   /// 是否显示内容区域内阴影
//   final bool showInnerShadow;
//
//   /// 内容区域圆角系数（相对于整体圆角的比例）
//   final double contentRadiusRatio;
//
//   const MarkdownTemplate({
//     required this.id,
//     required this.name,
//     required this.description,
//     required this.style,
//     required this.watermark,
//     required this.showHeader,
//     required this.showBorder,
//     required this.borderColor,
//     required this.borderWidth,
//     required this.showShadow,
//     required this.shadowColor,
//     required this.shadowOffset,
//     required this.shadowBlurRadius,
//     this.showInnerShadow = false,
//     this.contentRadiusRatio = 0.8,
//   });
//
//   /// 创建一个模板的副本并修改部分属性
//   MarkdownTemplate copyWith({
//     String? id,
//     String? name,
//     String? description,
//     MarkdownRenderStyle? style,
//     MarkdownWatermark? watermark,
//     bool? showHeader,
//     bool? showBorder,
//     Color? borderColor,
//     double? borderWidth,
//     bool? showShadow,
//     Color? shadowColor,
//     Offset? shadowOffset,
//     double? shadowBlurRadius,
//     bool? showInnerShadow,
//     double? contentRadiusRatio,
//   }) {
//     return MarkdownTemplate(
//       id: id ?? this.id,
//       name: name ?? this.name,
//       description: description ?? this.description,
//       style: style ?? this.style,
//       watermark: watermark ?? this.watermark,
//       showHeader: showHeader ?? this.showHeader,
//       showBorder: showBorder ?? this.showBorder,
//       borderColor: borderColor ?? this.borderColor,
//       borderWidth: borderWidth ?? this.borderWidth,
//       showShadow: showShadow ?? this.showShadow,
//       shadowColor: shadowColor ?? this.shadowColor,
//       shadowOffset: shadowOffset ?? this.shadowOffset,
//       shadowBlurRadius: shadowBlurRadius ?? this.shadowBlurRadius,
//       showInnerShadow: showInnerShadow ?? this.showInnerShadow,
//       contentRadiusRatio: contentRadiusRatio ?? this.contentRadiusRatio,
//     );
//   }
//
//   /// 获取预定义模板列表
//   static List<MarkdownTemplate> getPredefinedTemplates() {
//     return [
//       simple(),
//       modern(),
//       elegant(),
//       code(),
//       card(),
//       morandiStyle(),
//       chineseBlueWhite(),
//       chineseVermilion(),
//       gradientPurple(),
//       festiveRed(),
//       bambooSlip(),
//     ];
//   }
//
//   /// 简约模板
//   static MarkdownTemplate simple() {
//     return MarkdownTemplate(
//       id: 'simple',
//       name: '简约',
//       description: '简洁清爽的设计风格',
//       style: MarkdownRenderStyle.light(),
//       watermark: MarkdownWatermark.defaultWatermark(),
//       showHeader: false,
//       showBorder: false,
//       borderColor: Colors.transparent,
//       borderWidth: 0,
//       showShadow: false,
//       shadowColor: Colors.transparent,
//       shadowOffset: const Offset(0, 0),
//       shadowBlurRadius: 0,
//     );
//   }
//
//   /// 现代模板
//   static MarkdownTemplate modern() {
//     return MarkdownTemplate(
//       id: 'modern',
//       name: '现代',
//       description: '现代化的设计风格，带有阴影效果',
//       style: MarkdownRenderStyle.light(),
//       watermark: MarkdownWatermark.defaultWatermark(),
//       showHeader: true,
//       showBorder: false,
//       borderColor: Colors.transparent,
//       borderWidth: 0,
//       showShadow: true,
//       shadowColor: Colors.black.withValues(alpha: 0.1),
//       shadowOffset: const Offset(0, 2),
//       shadowBlurRadius: 10,
//       showInnerShadow: false,
//     );
//   }
//
//   /// 优雅模板
//   static MarkdownTemplate elegant() {
//     return MarkdownTemplate(
//       id: 'elegant',
//       name: '优雅',
//       description: '优雅的设计风格，带有细边框',
//       style: MarkdownRenderStyle.light().copyWith(
//         backgroundColor: Colors.white,
//         padding: const EdgeInsets.all(24.0),
//       ),
//       watermark: MarkdownWatermark.defaultWatermark(),
//       showHeader: true,
//       showBorder: true,
//       borderColor: Colors.grey.withValues(alpha: 0.3),
//       borderWidth: 1,
//       showShadow: false,
//       shadowColor: Colors.transparent,
//       shadowOffset: const Offset(0, 0),
//       shadowBlurRadius: 0,
//       showInnerShadow: false,
//     );
//   }
//
//   /// 代码模板
//   static MarkdownTemplate code() {
//     return MarkdownTemplate(
//       id: 'code',
//       name: '代码',
//       description: '适合展示代码的暗色主题',
//       style: MarkdownRenderStyle.dark(),
//       watermark: MarkdownWatermark.defaultWatermark().copyWith(
//         textColor: Colors.grey.withValues(alpha: 0.5),
//       ),
//       showHeader: false,
//       showBorder: false,
//       borderColor: Colors.transparent,
//       borderWidth: 0,
//       showShadow: true,
//       shadowColor: Colors.black.withValues(alpha: 0.3),
//       shadowOffset: const Offset(0, 3),
//       shadowBlurRadius: 15,
//       showInnerShadow: false,
//     );
//   }
//
//   /// 卡片模板
//   static MarkdownTemplate card() {
//     return MarkdownTemplate(
//       id: 'card',
//       name: '卡片',
//       description: '类似社交媒体卡片的风格设计',
//       style: MarkdownRenderStyle.light().copyWith(
//         backgroundColor: Colors.white,
//         padding: const EdgeInsets.all(24.0),
//         borderRadius: 20.0,
//       ),
//       watermark: MarkdownWatermark.defaultWatermark(),
//       showHeader: true,
//       showBorder: false,
//       borderColor: Colors.transparent,
//       borderWidth: 0,
//       showShadow: true,
//       shadowColor: Colors.black.withValues(alpha: 0.08),
//       shadowOffset: const Offset(0, 8),
//       shadowBlurRadius: 20,
//       showInnerShadow: true,
//       contentRadiusRatio: 0.9,
//     );
//   }
//
//   /// 莫兰迪风格模板
//   static MarkdownTemplate morandiStyle() {
//     return MarkdownTemplate(
//       id: 'morandi',
//       name: '莫兰迪',
//       description: '高级莫兰迪配色，柔和雅致',
//       style: MarkdownRenderStyle.morandi(),
//       watermark: MarkdownWatermark.defaultWatermark().copyWith(
//         textColor: const Color(0xFFA39E93).withValues(alpha: 0.15),
//         fontSize: 80,
//         position: WatermarkPosition.bottomRight,
//       ),
//       showHeader: true,
//       showBorder: true,
//       borderColor: const Color(0xFFD5CEC5),
//       borderWidth: 1.5,
//       showShadow: true,
//       shadowColor: const Color(0xFFBBB5AC).withValues(alpha: 0.2),
//       shadowOffset: const Offset(0, 4),
//       shadowBlurRadius: 15,
//       showInnerShadow: false,
//       contentRadiusRatio: 0.85,
//     );
//   }
//
//   /// 青花瓷风格模板
//   static MarkdownTemplate chineseBlueWhite() {
//     return MarkdownTemplate(
//       id: 'chinese_blue_white',
//       name: '青花',
//       description: '中国传统青花瓷配色与纹样设计',
//       style: MarkdownRenderStyle.chineseBlueWhite(),
//       watermark: MarkdownWatermark.defaultWatermark().copyWith(
//         textColor: const Color(0xFF6CA3D4).withValues(alpha: 0.07),
//         fontSize: 100,
//         position: WatermarkPosition.bottomRight,
//         text: '青花',
//       ),
//       showHeader: true,
//       showBorder: true,
//       borderColor: const Color(0xFF6CA3D4),
//       borderWidth: 2,
//       showShadow: true,
//       shadowColor: const Color(0xFF6CA3D4).withValues(alpha: 0.15),
//       shadowOffset: const Offset(0, 5),
//       shadowBlurRadius: 20,
//       showInnerShadow: false,
//       contentRadiusRatio: 0.9,
//     );
//   }
//
//   /// 胭脂红风格模板
//   static MarkdownTemplate chineseVermilion() {
//     return MarkdownTemplate(
//       id: 'chinese_vermilion',
//       name: '胭脂',
//       description: '中国传统胭脂红配色，优雅庄重',
//       style: MarkdownRenderStyle.chineseVermilion(),
//       watermark: MarkdownWatermark.defaultWatermark().copyWith(
//         textColor: const Color(0xFFCF4F50).withValues(alpha: 0.06),
//         fontSize: 120,
//         position: WatermarkPosition.tiled,
//         text: '赤',
//       ),
//       showHeader: true,
//       showBorder: true,
//       borderColor: const Color(0xFFECBEA9),
//       borderWidth: 2,
//       showShadow: false,
//       shadowColor: Colors.transparent,
//       shadowOffset: const Offset(0, 0),
//       shadowBlurRadius: 0,
//       showInnerShadow: true,
//       contentRadiusRatio: 0.9,
//     );
//   }
//
//   /// 渐变紫色风格模板
//   static MarkdownTemplate gradientPurple() {
//     return MarkdownTemplate(
//       id: 'gradient_purple',
//       name: '渐变紫',
//       description: '现代紫蓝渐变背景，时尚优雅',
//       style: MarkdownRenderStyle.gradient(),
//       watermark: MarkdownWatermark.defaultWatermark().copyWith(
//         textColor: Colors.white.withValues(alpha: 0.1),
//         fontSize: 100,
//         position: WatermarkPosition.tiled,
//       ),
//       showHeader: true,
//       showBorder: false,
//       borderColor: Colors.transparent,
//       borderWidth: 0,
//       showShadow: true,
//       shadowColor: const Color(0xFF6A11CB).withValues(alpha: 0.3),
//       shadowOffset: const Offset(0, 10),
//       shadowBlurRadius: 30,
//       showInnerShadow: false,
//       contentRadiusRatio: 0.95,
//     );
//   }
//
//   /// 节日红风格模板
//   static MarkdownTemplate festiveRed() {
//     return MarkdownTemplate(
//       id: 'festive_red',
//       name: '节日红',
//       description: '喜庆节日主题，适合春节等场合',
//       style: MarkdownRenderStyle.festiveRed(),
//       watermark: MarkdownWatermark.defaultWatermark().copyWith(
//         textColor: const Color(0xFFE34D59).withValues(alpha: 0.08),
//         fontSize: 120,
//         position: WatermarkPosition.bottomRight,
//         text: '福',
//       ),
//       showHeader: true,
//       showBorder: true,
//       borderColor: const Color(0xFFEF7A82),
//       borderWidth: 3,
//       showShadow: true,
//       shadowColor: const Color(0xFFE34D59).withValues(alpha: 0.2),
//       shadowOffset: const Offset(0, 5),
//       shadowBlurRadius: 15,
//       showInnerShadow: false,
//       contentRadiusRatio: 0.9,
//     );
//   }
//
//   /// 竹简风格模板
//   static MarkdownTemplate bambooSlip() {
//     return MarkdownTemplate(
//       id: 'bamboo_slip',
//       name: '竹简',
//       description: '传统竹简文风，古韵浓厚',
//       style: MarkdownRenderStyle.bambooSlip(),
//       watermark: MarkdownWatermark.defaultWatermark().copyWith(
//         textColor: const Color(0xFF8C6E3E).withValues(alpha: 0.1),
//         fontSize: 140,
//         position: WatermarkPosition.topRight,
//         text: '竹',
//       ),
//       showHeader: true,
//       showBorder: true,
//       borderColor: const Color(0xFFA38B60),
//       borderWidth: 2,
//       showShadow: true,
//       shadowColor: const Color(0xFF8C6E3E).withValues(alpha: 0.15),
//       shadowOffset: const Offset(2, 2),
//       shadowBlurRadius: 10,
//       showInnerShadow: false,
//       contentRadiusRatio: 0.0, // 方形边角模拟竹简
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/markdown/models/markdown_template.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/markdown/models/markdown_watermark.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
//
// import '../../config/constants.dart';
//
// /// Markdown渲染水印配置
// class MarkdownWatermark {
//   /// 水印文本
//   final String text;
//
//   /// 水印文本颜色
//   final Color textColor;
//
//   /// 水印文本大小
//   final double fontSize;
//
//   /// 水印文本字体
//   final String fontFamily;
//
//   /// 水印文本样式
//   final FontStyle fontStyle;
//
//   /// 水印文本粗细
//   final FontWeight fontWeight;
//
//   /// 是否显示水印
//   final bool isVisible;
//
//   /// 水印位置
//   final WatermarkPosition position;
//
//   /// 水印透明度
//   final double opacity;
//
//   /// 水印旋转角度（弧度）
//   final double rotation;
//
//   /// 平铺水印的水平间距
//   final double tileHorizontalGap;
//
//   /// 平铺水印的垂直间距
//   final double tileVerticalGap;
//
//   /// 平铺水印的行数
//   final int tileRows;
//
//   /// 平铺水印的列数
//   final int tileColumns;
//
//   const MarkdownWatermark({
//     required this.text,
//     required this.textColor,
//     required this.fontSize,
//     required this.fontFamily,
//     required this.fontStyle,
//     required this.fontWeight,
//     required this.isVisible,
//     required this.position,
//     required this.opacity,
//     this.rotation = 0.0,
//     this.tileHorizontalGap = 100.0,
//     this.tileVerticalGap = 100.0,
//     this.tileRows = 3,
//     this.tileColumns = 3,
//   });
//
//   /// 创建一个水印的副本并修改部分属性
//   MarkdownWatermark copyWith({
//     String? text,
//     Color? textColor,
//     double? fontSize,
//     String? fontFamily,
//     FontStyle? fontStyle,
//     FontWeight? fontWeight,
//     bool? isVisible,
//     WatermarkPosition? position,
//     double? opacity,
//     double? rotation,
//     double? tileHorizontalGap,
//     double? tileVerticalGap,
//     int? tileRows,
//     int? tileColumns,
//   }) {
//     return MarkdownWatermark(
//       text: text ?? this.text,
//       textColor: textColor ?? this.textColor,
//       fontSize: fontSize ?? this.fontSize,
//       fontFamily: fontFamily ?? this.fontFamily,
//       fontStyle: fontStyle ?? this.fontStyle,
//       fontWeight: fontWeight ?? this.fontWeight,
//       isVisible: isVisible ?? this.isVisible,
//       position: position ?? this.position,
//       opacity: opacity ?? this.opacity,
//       rotation: rotation ?? this.rotation,
//       tileHorizontalGap: tileHorizontalGap ?? this.tileHorizontalGap,
//       tileVerticalGap: tileVerticalGap ?? this.tileVerticalGap,
//       tileRows: tileRows ?? this.tileRows,
//       tileColumns: tileColumns ?? this.tileColumns,
//     );
//   }
//
//   /// 默认水印
//   static MarkdownWatermark defaultWatermark() {
//     return MarkdownWatermark(
//       text: AppConstants.appNameChinese,
//       textColor: Colors.grey,
//       fontSize: 12.0,
//       fontFamily: 'Roboto',
//       fontStyle: FontStyle.italic,
//       fontWeight: FontWeight.normal,
//       isVisible: false,
//       position: WatermarkPosition.bottomCenter,
//       opacity: 0.7,
//       rotation: -0.2, // 默认轻微倾斜
//     );
//   }
// }
//
// /// 水印位置枚举
// enum WatermarkPosition {
//   topLeft,
//   topCenter,
//   topRight,
//   bottomLeft,
//   bottomCenter,
//   bottomRight,
//
//   /// 平铺
//   tiled,
// }
//
// /// 水印位置扩展
// extension WatermarkPositionExtension on WatermarkPosition {
//   String get displayName {
//     switch (this) {
//       case WatermarkPosition.topLeft:
//         return '左上角';
//       case WatermarkPosition.topCenter:
//         return '顶部居中';
//       case WatermarkPosition.topRight:
//         return '右上角';
//       case WatermarkPosition.bottomLeft:
//         return '左下角';
//       case WatermarkPosition.bottomCenter:
//         return '底部居中';
//       case WatermarkPosition.bottomRight:
//         return '右下角';
//       case WatermarkPosition.tiled:
//         return '平铺';
//     }
//   }
//
//   Alignment get alignment {
//     switch (this) {
//       case WatermarkPosition.topLeft:
//         return Alignment.topLeft;
//       case WatermarkPosition.topCenter:
//         return Alignment.topCenter;
//       case WatermarkPosition.topRight:
//         return Alignment.topRight;
//       case WatermarkPosition.bottomLeft:
//         return Alignment.bottomLeft;
//       case WatermarkPosition.bottomCenter:
//         return Alignment.bottomCenter;
//       case WatermarkPosition.bottomRight:
//         return Alignment.bottomRight;
//       case WatermarkPosition.tiled:
//         return Alignment.center; // 平铺时使用中心对齐
//     }
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/markdown/models/markdown_watermark.dart
// // ===================================================================
//
