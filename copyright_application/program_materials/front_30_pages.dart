// // ======================================================
// // ContentPal内容处理工具 V1.0.0 - 程序鉴别材料（前30页）
// // 版权所有: [请填写著作权人信息]
// // 生成时间: 2025-06-05 12:39:16
// // ======================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/main.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:hive_flutter/hive_flutter.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:shared_preferences/shared_preferences.dart';
//
// import 'common/utils/permission_helper.dart';
// import 'config/app_theme.dart';
// import 'config/constants.dart';
// import 'home.dart';
// import 'services/service_locator.dart';
//
// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
//
//   PermissionHelper.checkIosPermissions();
//
//   // 设置状态栏
//   SystemChrome.setSystemUIOverlayStyle(
//     const SystemUiOverlayStyle(
//       statusBarColor: Colors.transparent,
//       statusBarIconBrightness: Brightness.dark,
//       statusBarBrightness: Brightness.light,
//     ),
//   );
//
//   // 设置设备方向
//   await SystemChrome.setPreferredOrientations([
//     DeviceOrientation.portraitUp,
//     DeviceOrientation.portraitDown,
//   ]);
//
//   // 初始化数据存储
//   await initializeStorage();
//
//   // 初始化服务定位器
//   await ServiceLocator().initServices();
//
//   // 启动应用
//   runApp(const ContentPalApp());
// }
//
// // 初始化本地存储
// Future<void> initializeStorage() async {
//   final appDocumentDir = await getApplicationDocumentsDirectory();
//   await Hive.initFlutter(appDocumentDir.path);
//   await Hive.openBox(AppConstants.mainBoxName);
//
//   // 检查首次启动
//   final prefs = await SharedPreferences.getInstance();
//   final hasLaunchedBefore = prefs.getBool(AppConstants.keyHasLaunchedBefore);
//
//   if (hasLaunchedBefore == null) {
//     // 首次启动，初始化默认设置
//     await prefs.setBool(AppConstants.keyHasLaunchedBefore, true);
//     // 其他首次启动设置...
//   }
// }
//
// class ContentPalApp extends StatefulWidget {
//   const ContentPalApp({super.key});
//
//   @override
//   State<ContentPalApp> createState() => _ContentPalAppState();
// }
//
// class _ContentPalAppState extends State<ContentPalApp> {
//   ThemeMode _themeMode = ThemeMode.light;
//
//   @override
//   void initState() {
//     super.initState();
//     _loadThemeMode();
//   }
//
//   // 加载主题设置
//   Future<void> _loadThemeMode() async {
//     final prefs = await SharedPreferences.getInstance();
//     final themeModeStr = prefs.getString(AppConstants.keyThemeMode);
//
//     if (themeModeStr != null) {
//       setState(() {
//         if (themeModeStr == 'dark') {
//           _themeMode = ThemeMode.dark;
//         } else if (themeModeStr == 'system') {
//           _themeMode = ThemeMode.system;
//         } else {
//           _themeMode = ThemeMode.light;
//         }
//       });
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return MaterialApp(
//       navigatorKey: ServiceLocator().globalKey,
//       title: AppConstants.appName,
//       debugShowCheckedModeBanner: false,
//       theme: _getDefaultTheme(),
//       darkTheme: _getDartTheme(),
//       themeMode: _themeMode,
//       home: const HomePage(),
//     );
//   }
//
//   ThemeData _getDefaultTheme() {
//     return ThemeData(
//       useMaterial3: true,
//       colorScheme: ColorScheme.fromSeed(
//         seedColor: AppTheme.primaryColor,
//         brightness: Brightness.light,
//       ),
//       scaffoldBackgroundColor: AppTheme.bgLightColor,
//       appBarTheme: const AppBarTheme(
//         backgroundColor: AppTheme.bgWhiteColor,
//         elevation: 0,
//         iconTheme: IconThemeData(color: AppTheme.textDarkColor),
//         titleTextStyle: TextStyle(
//           color: AppTheme.textDarkColor,
//           fontSize: 20,
//           fontWeight: FontWeight.bold,
//         ),
//       ),
//       cardTheme: CardThemeData(
//         color: AppTheme.bgWhiteColor,
//         elevation: 0.5,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//         ),
//       ),
//       elevatedButtonTheme: ElevatedButtonThemeData(
//         style: ElevatedButton.styleFrom(
//           backgroundColor: AppTheme.primaryColor,
//           foregroundColor: Colors.white,
//           elevation: 0,
//           padding: const EdgeInsets.symmetric(
//             horizontal: AppTheme.paddingMD,
//             vertical: AppTheme.paddingSM,
//           ),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//           ),
//         ),
//       ),
//       textButtonTheme: TextButtonThemeData(
//         style: TextButton.styleFrom(foregroundColor: AppTheme.primaryColor),
//       ),
//       outlinedButtonTheme: OutlinedButtonThemeData(
//         style: OutlinedButton.styleFrom(
//           foregroundColor: AppTheme.primaryColor,
//           side: const BorderSide(color: AppTheme.primaryColor),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//           ),
//         ),
//       ),
//       inputDecorationTheme: InputDecorationTheme(
//         filled: true,
//         fillColor: AppTheme.bgIndigo50,
//         contentPadding: const EdgeInsets.all(AppTheme.paddingMD),
//         border: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//           borderSide: BorderSide.none,
//         ),
//         enabledBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//           borderSide: BorderSide.none,
//         ),
//         focusedBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//           borderSide: const BorderSide(color: AppTheme.borderActiveColor),
//         ),
//         hintStyle: const TextStyle(color: AppTheme.textLightColor),
//       ),
//     );
//   }
//
//   ThemeData _getDartTheme() {
//     return ThemeData(
//       useMaterial3: true,
//       colorScheme: ColorScheme.fromSeed(
//         seedColor: AppTheme.darkPrimaryColor,
//         brightness: Brightness.dark,
//       ),
//       scaffoldBackgroundColor: AppTheme.darkBgColor,
//       appBarTheme: const AppBarTheme(
//         backgroundColor: AppTheme.darkBgLightColor,
//         elevation: 0,
//         iconTheme: IconThemeData(color: AppTheme.darkTextColor),
//         titleTextStyle: TextStyle(
//           color: AppTheme.darkTextColor,
//           fontSize: 20,
//           fontWeight: FontWeight.bold,
//         ),
//       ),
//       cardTheme: CardThemeData(
//         color: AppTheme.darkBgLightColor,
//         elevation: 0.5,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//         ),
//       ),
//       elevatedButtonTheme: ElevatedButtonThemeData(
//         style: ElevatedButton.styleFrom(
//           backgroundColor: AppTheme.darkPrimaryColor,
//           foregroundColor: Colors.white,
//           elevation: 0,
//           padding: const EdgeInsets.symmetric(
//             horizontal: AppTheme.paddingMD,
//             vertical: AppTheme.paddingSM,
//           ),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//           ),
//         ),
//       ),
//       textButtonTheme: TextButtonThemeData(
//         style: TextButton.styleFrom(
//           foregroundColor: AppTheme.darkPrimaryColor,
//         ),
//       ),
//       outlinedButtonTheme: OutlinedButtonThemeData(
//         style: OutlinedButton.styleFrom(
//           foregroundColor: AppTheme.darkPrimaryColor,
//           side: const BorderSide(color: AppTheme.darkPrimaryColor),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//           ),
//         ),
//       ),
//       inputDecorationTheme: InputDecorationTheme(
//         filled: true,
//         fillColor: AppTheme.darkBgLightColor,
//         contentPadding: const EdgeInsets.all(AppTheme.paddingMD),
//         border: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//           borderSide: BorderSide.none,
//         ),
//         enabledBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//           borderSide: BorderSide.none,
//         ),
//         focusedBorder: OutlineInputBorder(
//           borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
//           borderSide: const BorderSide(color: AppTheme.darkBorderActiveColor),
//         ),
//         hintStyle: const TextStyle(color: AppTheme.darkTextLightColor),
//       ),
//     );
//   }
//
// }
//
// // ===================================================================
// // 文件结束: lib/main.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/home.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:math' as math;
//
// import 'package:flutter/material.dart';
//
// import 'config/app_theme.dart';
// import 'config/constants.dart';
// import 'content/content_home_page.dart';
// import 'html/html_manager_screen.dart';
// import 'markdown/markdown_render_screen.dart';
// import 'pdf/pdf_manager_screen.dart';
// import 'settings/settings_screen.dart';
// import 'svg/svg_manager_screen.dart';
// import 'voice/voice_home_page.dart';
// import 'text_cards/text_cards_home_page.dart';
// import 'home/widgets/background_painter.dart';
// import 'home/widgets/home_tool_card.dart';
// import 'home/widgets/create_content_bottom_sheet.dart';
//
// class HomePage extends StatefulWidget {
//   const HomePage({super.key});
//
//   @override
//   State<HomePage> createState() => _HomePageState();
// }
//
// class _HomePageState extends State<HomePage>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _animationController;
//   final ScrollController _scrollController = ScrollController();
//   bool _isScrolled = false;
//
//   @override
//   void initState() {
//     super.initState();
//     _animationController = AnimationController(
//       vsync: this,
//       duration: const Duration(seconds: 20),
//     )..repeat();
//
//     _scrollController.addListener(_onScroll);
//   }
//
//   void _onScroll() {
//     if (_scrollController.offset > 0 && !_isScrolled) {
//       setState(() => _isScrolled = true);
//     } else if (_scrollController.offset <= 0 && _isScrolled) {
//       setState(() => _isScrolled = false);
//     }
//   }
//
//   @override
//   void dispose() {
//     _animationController.dispose();
//     _scrollController.dispose();
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: AppTheme.bgLightColor,
//       extendBodyBehindAppBar: true, // 允许内容延伸到应用栏后面
//       body: Stack(
//         children: [
//           // 背景渐变动画
//           Positioned.fill(
//             child: AnimatedBuilder(
//               animation: _animationController,
//               builder: (context, child) {
//                 return CustomPaint(
//                   painter: BackgroundPainter(_animationController.value),
//                 );
//               },
//             ),
//           ),
//           // 主内容
//           CustomScrollView(
//             controller: _scrollController,
//             physics: const BouncingScrollPhysics(),
//             slivers: [
//               // 应用栏
//               SliverAppBar(
//                 expandedHeight: 130,
//                 floating: false,
//                 pinned: true,
//                 stretch: true,
//                 elevation: _isScrolled ? 4 : 0,
//                 backgroundColor:
//                     _isScrolled
//                         ? AppTheme.bgWhiteColor.withValues(alpha: 0.95)
//                         : Colors.transparent,
//                 title:
//                     _isScrolled
//                         ? Text(
//                           MediaQuery.of(context).size.width > 320
//                               ? AppConstants.appNameChinese
//                               : AppConstants.appName,
//                           style: const TextStyle(
//                             color: AppTheme.textDarkColor,
//                             fontWeight: FontWeight.bold,
//                             fontSize: 20,
//                           ),
//                         )
//                         : null,
//                 flexibleSpace: FlexibleSpaceBar(
//                   title: null,
//                   centerTitle: false,
//                   collapseMode: CollapseMode.pin,
//                   background: Stack(
//                     fit: StackFit.expand,
//                     children: [
//                       // 顶部渐变背景
//                       Container(
//                         decoration: BoxDecoration(
//                           gradient: LinearGradient(
//                             begin: Alignment.topCenter,
//                             end: Alignment.bottomCenter,
//                             colors: [
//                               AppTheme.primaryColor.withValues(alpha: 0.7),
//                               AppTheme.primaryColor.withValues(alpha: 0.5),
//                               AppTheme.primaryColor.withValues(alpha: 0.2),
//                               Colors.transparent,
//                             ],
//                           ),
//                         ),
//                       ),
//                       // 顶部装饰图案
//                       Positioned(
//                         top: -30,
//                         right: -30,
//                         child: Transform.rotate(
//                           angle: math.pi / 6,
//                           child: Container(
//                             width: 180,
//                             height: 180,
//                             decoration: BoxDecoration(
//                               gradient: AppTheme.primaryGradient,
//                               borderRadius: BorderRadius.circular(40),
//                             ),
//                           ),
//                         ),
//                       ),
//                       // 应用标题区
//                       Positioned(
//                         left: 20,
//                         bottom: 35,
//                         child: Text(
//                           MediaQuery.of(context).size.width > 320
//                               ? AppConstants.appNameChinese
//                               : AppConstants.appName,
//                           style: const TextStyle(
//                             color: AppTheme.bgWhiteColor,
//                             fontWeight: FontWeight.bold,
//                             fontSize: 28,
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 actions: [
//                   IconButton(
//                     icon: Icon(
//                       Icons.settings,
//                       color:
//                           _isScrolled
//                               ? AppTheme.textDarkColor
//                               : AppTheme.bgWhiteColor,
//                     ),
//                     onPressed: () {
//                       // 打开设置页面
//                       Navigator.push(
//                         context,
//                         MaterialPageRoute(
//                           builder: (context) => const SettingsScreen(),
//                         ),
//                       );
//                     },
//                   ),
//                 ],
//               ),
//
//               // 内容管理区
//               SliverToBoxAdapter(
//                 child: Padding(
//                   padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
//                   child: InkWell(
//                     onTap: () {
//                       Navigator.push(
//                         context,
//                         MaterialPageRoute(
//                           builder: (context) => const ContentHomePage(),
//                         ),
//                       );
//                     },
//                     borderRadius: BorderRadius.circular(16),
//                     child: Container(
//                       height: 90,
//                       padding: const EdgeInsets.symmetric(horizontal: 20),
//                       decoration: BoxDecoration(
//                         gradient: AppTheme.chineseGradient,
//                         borderRadius: BorderRadius.circular(16),
//                         boxShadow: [
//                           BoxShadow(
//                             color: AppTheme.chineseGradient.colors.first
//                                 .withValues(alpha: 0.3),
//                             offset: const Offset(0, 4),
//                             blurRadius: 15,
//                           ),
//                         ],
//                       ),
//                       child: Row(
//                         children: [
//                           Container(
//                             width: 50,
//                             height: 50,
//                             decoration: BoxDecoration(
//                               color: Colors.white.withValues(alpha: 0.25),
//                               borderRadius: BorderRadius.circular(12),
//                             ),
//                             child: const Icon(
//                               Icons.dashboard_rounded,
//                               color: Colors.white,
//                               size: 28,
//                             ),
//                           ),
//                           const SizedBox(width: 20),
//                           Expanded(
//                             child: Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 const Text(
//                                   '我的内容库',
//                                   style: TextStyle(
//                                     color: Colors.white,
//                                     fontWeight: FontWeight.bold,
//                                     fontSize: 20,
//                                   ),
//                                 ),
//                                 const SizedBox(height: 4),
//                                 Text(
//                                   '管理和浏览您的所有内容',
//                                   style: TextStyle(
//                                     color: Colors.white.withValues(alpha: 0.85),
//                                     fontSize: 14,
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                           const Icon(Icons.arrow_forward, color: Colors.white),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//
//               // 内容工具标题
//               SliverToBoxAdapter(
//                 child: Padding(
//                   padding: const EdgeInsets.fromLTRB(20, 26, 20, 16),
//                   child: Row(
//                     children: [
//                       Container(
//                         width: 24,
//                         height: 24,
//                         decoration: BoxDecoration(
//                           color: AppTheme.primaryColor.withValues(alpha: 0.1),
//                           borderRadius: BorderRadius.circular(6),
//                         ),
//                         child: const Icon(
//                           Icons.apps_rounded,
//                           size: 16,
//                           color: AppTheme.primaryColor,
//                         ),
//                       ),
//                       const SizedBox(width: 10),
//                       const Text(
//                         '内容工具',
//                         style: TextStyle(
//                           fontSize: 18,
//                           fontWeight: FontWeight.bold,
//                           color: AppTheme.textDarkColor,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//
//               // 工具网格
//               SliverPadding(
//                 padding: const EdgeInsets.fromLTRB(16, 0, 16, 20),
//                 sliver: SliverGrid(
//                   gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
//                     crossAxisCount: 2,
//                     childAspectRatio: 2.0,
//                     crossAxisSpacing: 16,
//                     mainAxisSpacing: 16,
//                   ),
//                   delegate: SliverChildListDelegate([
//                     HomeToolCard(
//                       title: 'Markdown',
//                       description: '文档编辑与渲染',
//                       icon: Icons.text_fields,
//                       gradient: AppTheme.blueGradient,
//                       onTap: () {
//                         Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) => const MarkdownRenderScreen(),
//                           ),
//                         );
//                       },
//                     ),
//                     HomeToolCard(
//                       title: 'SVG',
//                       description: '矢量图形处理',
//                       icon: Icons.image,
//                       gradient: AppTheme.purpleGradient,
//                       onTap: () {
//                         Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) => const SvgManagerScreen(),
//                           ),
//                         );
//                       },
//                     ),
//                     HomeToolCard(
//                       title: 'HTML',
//                       description: '网页内容编辑',
//                       icon: Icons.code,
//                       gradient: AppTheme.greenGradient,
//                       onTap: () {
//                         Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) => const HtmlManagerScreen(),
//                           ),
//                         );
//                       },
//                     ),
//                     HomeToolCard(
//                       title: 'PDF',
//                       description: '文档查看与注释',
//                       icon: Icons.picture_as_pdf,
//                       gradient: AppTheme.orangeGradient,
//                       onTap: () {
//                         Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) => const PdfManagerScreen(),
//                           ),
//                         );
//                       },
//                     ),
//                     HomeToolCard(
//                       title: '语音',
//                       description: '录制与文本转换',
//                       icon: Icons.mic,
//                       gradient: AppTheme.purpleGradient,
//                       onTap: () {
//                         Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) => const VoiceHomePage(),
//                           ),
//                         );
//                       },
//                     ),
//                     HomeToolCard(
//                       title: '文本卡片',
//                       description: '知识卡片定制渲染',
//                       icon: Icons.text_snippet,
//                       gradient: AppTheme.chineseGradient,
//                       onTap: () {
//                         Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) => const TextCardsHomePage(),
//                           ),
//                         );
//                       },
//                     ),
//                   ]),
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//       floatingActionButton: FloatingActionButton(
//         heroTag: 'home_page_fab',
//         backgroundColor: AppTheme.primaryColor,
//         elevation: 4,
//         onPressed: () {
//           // 显示操作菜单
//           showModalBottomSheet(
//             context: context,
//             shape: const RoundedRectangleBorder(
//               borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
//             ),
//             builder: (context) => const CreateContentBottomSheet(),
//           );
//         },
//         child: const Icon(Icons.add, color: Colors.white),
//       ),
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/home.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/services/storage_service.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:convert';
// import 'package:flutter/material.dart';
// import 'package:hive_flutter/hive_flutter.dart';
//
// import '../config/constants.dart';
//
// /// 存储服务，使用Hive替代SharedPreferences
// /// 提供统一的数据存储和读取接口
// class StorageService {
//   late Box _box;
//
//   // 单例模式
//   static final StorageService _instance = StorageService._internal();
//   factory StorageService() => _instance;
//   StorageService._internal();
//
//   // 初始化
//   Future<void> init() async {
//     // 初始化Hive
//     await Hive.initFlutter();
//
//     // 打开默认Box
//     _box = await Hive.openBox(AppConstants.mainBoxName);
//
//     debugPrint('StorageService初始化完成，使用Hive存储');
//   }
//
//   // 存储字符串
//   Future<void> setString(String key, String value) async {
//     try {
//       await _box.put(key, value);
//       debugPrint('Hive存储字符串成功: $key');
//     } catch (e, stackTrace) {
//       debugPrint('Hive存储字符串失败: $key, 错误: $e');
//       debugPrint('错误堆栈: $stackTrace');
//       rethrow;
//     }
//   }
//
//   // 获取字符串
//   String? getString(String key) {
//     try {
//       final value = _box.get(key);
//       return value as String?;
//     } catch (e) {
//       debugPrint('Hive获取字符串失败: $key, 错误: $e');
//       return null;
//     }
//   }
//
//   // 存储布尔值
//   Future<void> setBool(String key, bool value) async {
//     try {
//       await _box.put(key, value);
//     } catch (e) {
//       debugPrint('Hive存储布尔值失败: $key, 错误: $e');
//       rethrow;
//     }
//   }
//
//   // 获取布尔值
//   bool? getBool(String key) {
//     try {
//       final value = _box.get(key);
//       return value as bool?;
//     } catch (e) {
//       debugPrint('Hive获取布尔值失败: $key, 错误: $e');
//       return null;
//     }
//   }
//
//   // 存储整数
//   Future<void> setInt(String key, int value) async {
//     try {
//       await _box.put(key, value);
//     } catch (e) {
//       debugPrint('Hive存储整数失败: $key, 错误: $e');
//       rethrow;
//     }
//   }
//
//   // 获取整数
//   int? getInt(String key) {
//     try {
//       final value = _box.get(key);
//       return value as int?;
//     } catch (e) {
//       debugPrint('Hive获取整数失败: $key, 错误: $e');
//       return null;
//     }
//   }
//
//   // 存储双精度浮点数
//   Future<void> setDouble(String key, double value) async {
//     try {
//       await _box.put(key, value);
//     } catch (e) {
//       debugPrint('Hive存储浮点数失败: $key, 错误: $e');
//       rethrow;
//     }
//   }
//
//   // 获取双精度浮点数
//   double? getDouble(String key) {
//     try {
//       final value = _box.get(key);
//       return value as double?;
//     } catch (e) {
//       debugPrint('Hive获取浮点数失败: $key, 错误: $e');
//       return null;
//     }
//   }
//
//   // 存储字符串列表
//   Future<void> setStringList(String key, List<String> value) async {
//     try {
//       await _box.put(key, value);
//     } catch (e) {
//       debugPrint('Hive存储字符串列表失败: $key, 错误: $e');
//       rethrow;
//     }
//   }
//
//   // 获取字符串列表
//   List<String>? getStringList(String key) {
//     try {
//       final value = _box.get(key);
//       if (value is List) {
//         return value.cast<String>();
//       }
//       return null;
//     } catch (e) {
//       debugPrint('Hive获取字符串列表失败: $key, 错误: $e');
//       return null;
//     }
//   }
//
//   // 存储JSON对象
//   Future<void> setJson(String key, Map<String, dynamic> value) async {
//     try {
//       final jsonString = json.encode(value);
//       await _box.put(key, jsonString);
//     } catch (e) {
//       debugPrint('Hive存储JSON失败: $key, 错误: $e');
//       rethrow;
//     }
//   }
//
//   // 获取JSON对象
//   Map<String, dynamic>? getJson(String key) {
//     try {
//       final jsonString = _box.get(key);
//       if (jsonString != null && jsonString is String) {
//         return json.decode(jsonString) as Map<String, dynamic>;
//       }
//       return null;
//     } catch (e) {
//       debugPrint('Hive获取JSON失败: $key, 错误: $e');
//       return null;
//     }
//   }
//
//   // 存储JSON列表
//   Future<void> setJsonList(String key, List<Map<String, dynamic>> value) async {
//     try {
//       final jsonString = json.encode(value);
//       await _box.put(key, jsonString);
//     } catch (e) {
//       debugPrint('Hive存储JSON列表失败: $key, 错误: $e');
//       rethrow;
//     }
//   }
//
//   // 获取JSON列表
//   List<Map<String, dynamic>>? getJsonList(String key) {
//     try {
//       final jsonString = _box.get(key);
//       if (jsonString != null && jsonString is String) {
//         final List<dynamic> list = json.decode(jsonString);
//         return list.cast<Map<String, dynamic>>();
//       }
//       return null;
//     } catch (e) {
//       debugPrint('Hive获取JSON列表失败: $key, 错误: $e');
//       return null;
//     }
//   }
//
//   // 检查键是否存在
//   bool containsKey(String key) {
//     return _box.containsKey(key);
//   }
//
//   // 删除指定键的数据
//   Future<void> remove(String key) async {
//     try {
//       await _box.delete(key);
//     } catch (e) {
//       debugPrint('Hive删除数据失败: $key, 错误: $e');
//       rethrow;
//     }
//   }
//
//   // 清除所有数据
//   Future<void> clear() async {
//     try {
//       await _box.clear();
//       debugPrint('Hive清除所有数据成功');
//     } catch (e) {
//       debugPrint('Hive清除所有数据失败: $e');
//       rethrow;
//     }
//   }
//
//   // 强制刷新
//   Future<void> reload() async {
//     try {
//       // Hive不需要显式刷新，但我们可以关闭并重新打开Box
//       final boxName = _box.name;
//       await _box.close();
//       _box = await Hive.openBox(boxName);
//       debugPrint('Hive重新加载成功');
//     } catch (e) {
//       debugPrint('Hive重新加载失败: $e');
//       rethrow;
//     }
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/services/storage_service.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/common/utils/permission_helper.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:permission_handler/permission_handler.dart';
//
// /// 权限帮助工具
// class PermissionHelper {
//   /// 请求存储权限
//   static Future<bool> requestStoragePermission() async {
//     try {
//       // 检查是否已有权限
//       if (await Permission.storage.isGranted) {
//         return true;
//       }
//
//       // 请求权限
//       PermissionStatus status = await Permission.storage.request();
//       return status.isGranted;
//     } catch (e) {
//       debugPrint('请求存储权限失败: $e');
//       return false;
//     }
//   }
//
//   /// 请求相机权限
//   static Future<bool> requestCameraPermission() async {
//     try {
//       // 检查是否已有权限
//       if (await Permission.camera.isGranted) {
//         return true;
//       }
//
//       // 请求权限
//       PermissionStatus status = await Permission.camera.request();
//       return status.isGranted;
//     } catch (e) {
//       debugPrint('请求相机权限失败: $e');
//       return false;
//     }
//   }
//
//   /// 请求多个权限
//   static Future<Map<Permission, PermissionStatus>> requestMultiplePermissions(
//     List<Permission> permissions,
//   ) async {
//     try {
//       return await permissions.request();
//     } catch (e) {
//       debugPrint('请求多个权限失败: $e');
//       return {
//         for (var permission in permissions) permission: PermissionStatus.denied,
//       };
//     }
//   }
//
//   static Future<void> checkIosPermissions() async {
//     if (Platform.isIOS) {
//       try {
//         debugPrint("===== 应用启动时强制注册iOS权限 =====");
//
//         // 检查麦克风权限状态（不请求，只检查）
//         final micStatus = await Permission.microphone.status;
//         debugPrint("iOS麦克风权限状态: $micStatus");
//
//         // 检查语音识别权限状态（不请求，只检查）
//         final speechStatus = await Permission.speech.status;
//         debugPrint("iOS语音识别权限状态: $speechStatus");
//
//         // 检查照片权限状态
//         final photosStatus = await Permission.photos.status;
//         debugPrint("iOS照片权限状态: $photosStatus");
//
//         // 注意：这里仅记录权限状态，不主动请求，确保应用首次运行时能正确识别权限
//       } catch (e) {
//         debugPrint("启动时权限状态检查失败: $e");
//       }
//     }
//   }
//
// }
//
// // ===================================================================
// // 文件结束: lib/common/utils/permission_helper.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/common/utils/subscription_helper.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
// import '../../services/service_locator.dart';
// import '../../subscription/subscription_model.dart';
// import '../../subscription/subscription_screen.dart' show SubscriptionScreen;
//
// /// 订阅工具类，提供订阅相关的辅助方法
// class SubscriptionHelper {
//   // 私有构造函数，防止实例化
//   SubscriptionHelper._();
//
//   /// 检查用户是否已订阅（包括任何付费计划）
//   static bool isSubscribed() {
//     final subscriptionService = ServiceLocator().subscriptionService;
//     final subscription = subscriptionService.subscription;
//
//     return subscription.isActive && subscription.isPaid;
//   }
//
//   /// 检查用户是否有终身订阅
//   static bool hasLifetimeSubscription() {
//     final subscriptionService = ServiceLocator().subscriptionService;
//     final subscription = subscriptionService.subscription;
//
//     return subscription.isActive && subscription.isLifetime;
//   }
//
//   /// 检查订阅是否已过期
//   static bool isSubscriptionExpired() {
//     final subscriptionService = ServiceLocator().subscriptionService;
//     final subscription = subscriptionService.subscription;
//
//     return subscription.isExpired;
//   }
//
//   /// 获取当前订阅类型
//   static SubscriptionType getCurrentSubscriptionType() {
//     final subscriptionService = ServiceLocator().subscriptionService;
//     return subscriptionService.subscription.type;
//   }
//
//   /// 获取订阅剩余天数（如果适用）
//   static int? getRemainingDays() {
//     final subscriptionService = ServiceLocator().subscriptionService;
//     return subscriptionService.subscription.remainingDays;
//   }
//
//   /// 检查用户是否可以访问特定功能
//   static bool canAccessFeature(String featureId) {
//     final subscriptionService = ServiceLocator().subscriptionService;
//     return subscriptionService.canAccessFeature(featureId);
//   }
//
//   /// 显示订阅页面
//   static Future<bool?> showSubscriptionScreen(BuildContext context) async {
//     return await Navigator.of(context).push<bool>(
//       MaterialPageRoute(builder: (context) => const SubscriptionScreen()),
//     );
//   }
//
//   /// 显示续订页面
//   static Future<bool?> showRenewalScreen(BuildContext context) async {
//     return await Navigator.of(context).push<bool>(
//       MaterialPageRoute(builder: (context) => const SubscriptionScreen()),
//     );
//   }
//
//   /// 检查并处理订阅状态，如果需要则显示订阅页面
//   /// 返回是否可以继续（true表示已订阅或用户取消了订阅页面，false表示需要订阅但用户取消了操作）
//   static Future<bool> checkAndHandleSubscription(
//     BuildContext context, {
//     bool forceShow = false,
//     String? requiredFeatureId,
//   }) async {
//     final subscriptionService = ServiceLocator().subscriptionService;
//
//     // 检查是否需要显示订阅页面
//     bool needsSubscription = forceShow;
//
//     // 如果指定了特定功能，检查是否可以访问
//     if (requiredFeatureId != null && !needsSubscription) {
//       needsSubscription =
//           !subscriptionService.canAccessFeature(requiredFeatureId);
//     }
//
//     // 如果没有指定特定功能，检查是否需要显示订阅页面
//     if (!needsSubscription && !forceShow && requiredFeatureId == null) {
//       needsSubscription = subscriptionService.shouldShowSubscriptionScreen();
//     }
//
//     // 如果需要订阅，显示订阅页面
//     if (needsSubscription) {
//       final result = await showSubscriptionScreen(context);
//
//       // 如果用户完成了订阅，返回true
//       if (result == true) {
//         return true;
//       }
//
//       // 如果用户取消了订阅，返回false
//       return false;
//     }
//
//     // 检查是否需要续订
//     final needsRenewal = subscriptionService.shouldShowRenewalScreen();
//     if (needsRenewal) {
//       final result = await showRenewalScreen(context);
//
//       // 如果用户完成了续订，返回true
//       if (result == true) {
//         return true;
//       }
//
//       // 如果用户取消了续订，返回false
//       return false;
//     }
//
//     // 默认情况下，返回true（已订阅或不需要订阅）
//     return true;
//   }
//
//   /// 获取订阅状态文本描述
//   static String getSubscriptionStatusText() {
//     final subscriptionService = ServiceLocator().subscriptionService;
//     final subscription = subscriptionService.subscription;
//     if (subscription.isActive) {
//       switch (subscription.type) {
//         case SubscriptionType.monthly:
//           return '月度订阅';
//         case SubscriptionType.yearly:
//           return '年度订阅';
//         case SubscriptionType.lifetime:
//           return '终身订阅';
//         case SubscriptionType.free:
//         return '免费版';
//       }
//     } else if (subscription.isExpired) {
//       return '订阅已过期';
//     } else {
//       return '未订阅';
//     }
//   }
//
//   /// 获取订阅到期日期文本（如果适用）
//   static String? getExpiryDateText() {
//     final subscriptionService = ServiceLocator().subscriptionService;
//     final subscription = subscriptionService.subscription;
//
//     if (subscription.isActive &&
//         subscription.type != SubscriptionType.free &&
//         subscription.type != SubscriptionType.lifetime &&
//         subscription.endDate != null) {
//       final endDate = subscription.endDate!;
//       return '${endDate.year}年${endDate.month}月${endDate.day}日到期';
//     }
//
//     return null;
//   }
//
//   /// 获取订阅计划的特性列表
//   static List<SubscriptionFeature> getFeaturesByType(SubscriptionType type) {
//     final subscriptionService = ServiceLocator().subscriptionService;
//     final plan = subscriptionService.getPlanByType(type);
//     return plan?.features ?? [];
//   }
//
//   /// 获取所有订阅计划
//   static List<SubscriptionPlan> getAllPlans() {
//     final subscriptionService = ServiceLocator().subscriptionService;
//     return subscriptionService.plans;
//   }
//
//   /// 获取付费订阅计划
//   static List<SubscriptionPlan> getPaidPlans() {
//     final subscriptionService = ServiceLocator().subscriptionService;
//     return subscriptionService.plans
//         .where((plan) => plan.type != SubscriptionType.free)
//         .toList();
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/common/utils/subscription_helper.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/common/widgets/app_loading_indicator.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
//
// /// 应用加载指示器
// class AppLoadingIndicator extends StatelessWidget {
//   /// 指示器大小
//   final double size;
//
//   /// 指示器颜色
//   final Color? color;
//
//   /// 创建加载指示器
//   const AppLoadingIndicator({super.key, this.size = 36.0, this.color});
//
//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       width: size,
//       height: size,
//       child: CircularProgressIndicator(
//         strokeWidth: 3.0,
//         valueColor: AlwaysStoppedAnimation<Color>(
//           color ?? Theme.of(context).colorScheme.primary,
//         ),
//       ),
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/common/widgets/app_loading_indicator.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/config/app_settings.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
//
// import '../markdown/models/markdown_watermark.dart';
// import 'app_theme.dart';
// import 'constants.dart';
//
// class AppSettings {
//   final ThemeMode themeMode;
//   final AppThemeType themeType;
//   final double aiTemperature;
//   final bool autoSaveChat;
//   final bool privacyEnhanced;
//   final bool enableNotifications;
//   final String? defaultModelId;
//   final bool enableStreamingResponse;
//   final int requestTimeout; // 请求超时时间（秒）
//   final MarkdownWatermark watermark; // 添加水印设置
//
//   AppSettings({
//     this.themeMode = ThemeMode.light,
//     this.themeType = AppThemeType.materialYou,
//     this.aiTemperature = AppConstants.defaultTemperature,
//     this.autoSaveChat = AppConstants.defaultAutoSaveChat,
//     this.privacyEnhanced = AppConstants.defaultPrivacyEnhanced,
//     this.enableNotifications = AppConstants.defaultNotifications,
//     this.defaultModelId,
//     this.enableStreamingResponse = AppConstants.defaultStreamingResponse,
//     this.requestTimeout = AppConstants.defaultRequestTimeout,
//     MarkdownWatermark? watermark,
//   }) : watermark = watermark ?? MarkdownWatermark.defaultWatermark();
//
//   factory AppSettings.fromJson(Map<String, dynamic> json) {
//     MarkdownWatermark? watermark;
//     if (json.containsKey('watermark')) {
//       final watermarkJson = json['watermark'] as Map<String, dynamic>;
//       watermark = MarkdownWatermark(
//         text: watermarkJson['text'] ?? '',
//         textColor: Color(watermarkJson['textColor'] ?? 0),
//         fontSize: watermarkJson['fontSize']?.toDouble() ?? 12.0,
//         fontFamily: watermarkJson['fontFamily'] ?? '',
//         fontStyle:
//             watermarkJson['fontStyle'] == 1
//                 ? FontStyle.italic
//                 : FontStyle.normal,
//         fontWeight:
//             watermarkJson['fontWeight'] == 1
//                 ? FontWeight.bold
//                 : FontWeight.normal,
//         isVisible: watermarkJson['isVisible'] ?? false,
//         position: WatermarkPosition.values[watermarkJson['position'] ?? 0],
//         opacity: watermarkJson['opacity']?.toDouble() ?? 0.1,
//       );
//     }
//
//     return AppSettings(
//       themeMode: ThemeMode.values[json['themeMode'] ?? 1],
//       themeType:
//           json.containsKey('themeType')
//               ? AppThemeType.values[json['themeType']]
//               : AppThemeType.materialYou,
//       aiTemperature: json['aiTemperature'] ?? AppConstants.defaultTemperature,
//       autoSaveChat: json['autoSaveChat'] ?? AppConstants.defaultAutoSaveChat,
//       privacyEnhanced:
//           json['privacyEnhanced'] ?? AppConstants.defaultPrivacyEnhanced,
//       enableNotifications:
//           json['enableNotifications'] ?? AppConstants.defaultNotifications,
//       defaultModelId: json['defaultModelId'],
//       enableStreamingResponse:
//           json['enableStreamingResponse'] ??
//           AppConstants.defaultStreamingResponse,
//       requestTimeout:
//           json['requestTimeout'] ?? AppConstants.defaultRequestTimeout,
//       watermark: watermark,
//     );
//   }
//
//   Map<String, dynamic> toJson() {
//     return {
//       'themeMode': themeMode.index,
//       'themeType': themeType.index,
//       'aiTemperature': aiTemperature,
//       'autoSaveChat': autoSaveChat,
//       'privacyEnhanced': privacyEnhanced,
//       'enableNotifications': enableNotifications,
//       'defaultModelId': defaultModelId,
//       'enableStreamingResponse': enableStreamingResponse,
//       'requestTimeout': requestTimeout,
//       'watermark': {
//         'text': watermark.text,
//         'textColor': watermark.textColor.toARGB32(),
//         'fontSize': watermark.fontSize,
//         'fontFamily': watermark.fontFamily,
//         'fontStyle': watermark.fontStyle == FontStyle.italic ? 1 : 0,
//         'fontWeight': watermark.fontWeight == FontWeight.bold ? 1 : 0,
//         'isVisible': watermark.isVisible,
//         'position': watermark.position.index,
//         'opacity': watermark.opacity,
//       },
//     };
//   }
//
//   AppSettings copyWith({
//     ThemeMode? themeMode,
//     AppThemeType? themeType,
//     double? aiTemperature,
//     bool? autoSaveChat,
//     bool? privacyEnhanced,
//     bool? enableNotifications,
//     String? defaultModelId,
//     bool? enableStreamingResponse,
//     int? requestTimeout,
//     MarkdownWatermark? watermark,
//   }) {
//     return AppSettings(
//       themeMode: themeMode ?? this.themeMode,
//       themeType: themeType ?? this.themeType,
//       aiTemperature: aiTemperature ?? this.aiTemperature,
//       autoSaveChat: autoSaveChat ?? this.autoSaveChat,
//       privacyEnhanced: privacyEnhanced ?? this.privacyEnhanced,
//       enableNotifications: enableNotifications ?? this.enableNotifications,
//       defaultModelId: defaultModelId ?? this.defaultModelId,
//       enableStreamingResponse:
//           enableStreamingResponse ?? this.enableStreamingResponse,
//       requestTimeout: requestTimeout ?? this.requestTimeout,
//       watermark: watermark ?? this.watermark,
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/config/app_settings.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/config/app_theme.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
//
// /// 主题类型枚举
// enum AppThemeType {
//   /// Material You 动态主题
//   materialYou,
//
//   /// 莫兰迪色系主题
//   morandiTheme,
//
//   /// 极简黑白主题
//   monochromeTheme,
//
//   /// 自然色系主题
//   natureTheme,
//
//   /// 科技感主题
//   techTheme,
//
//   /// 中国传统色主题
//   chineseTheme,
// }
//
// /// 主题配置
// class ThemeConfig {
//   final double cornerRadius;
//   final double fontSizeScale;
//   final double colorSaturation;
//   final double contrast;
//   final bool isDark;
//
//   const ThemeConfig({
//     this.cornerRadius = 1.0,
//     this.fontSizeScale = 1.0,
//     this.colorSaturation = 1.0,
//     this.contrast = 1.0,
//     this.isDark = false,
//   });
// }
//
// class AppTheme {
//   // 防止实例化
//   AppTheme._();
//
//   // 亮色模式颜色
//   static const Color primaryColor = Color(0xFF4F46E5); // 靛蓝色
//   static const Color primaryLightColor = Color(0xFF818CF8);
//   static const Color secondaryColor = Color(0xFF3B82F6); // 蓝色
//
//   static const Color textDarkColor = Color(0xFF1F2937);
//   static const Color textMediumColor = Color(0xFF4B5563);
//   static const Color textLightColor = Color(0xFF9CA3AF);
//
//   static const Color bgLightColor = Color(0xFFF9FAFB);
//   static const Color bgWhiteColor = Color(0xFFFFFFFF);
//   static const Color bgIndigo50 = Color(0xFFEEF2FF);
//
//   static const Color borderColor = Color(0xFFE5E7EB);
//   static const Color borderActiveColor = Color(0xFFA5B4FC);
//
//   // 深色模式颜色
//   static const Color darkPrimaryColor = Color(0xFF6366F1); // 亮一点的靛蓝色
//   static const Color darkPrimaryLightColor = Color(0xFF818CF8);
//   static const Color darkSecondaryColor = Color(0xFF60A5FA); // 亮一点的蓝色
//
//   static const Color darkTextColor = Color(0xFFF9FAFB);
//   static const Color darkTextMediumColor = Color(0xFFD1D5DB);
//   static const Color darkTextLightColor = Color(0xFF9CA3AF);
//
//   static const Color darkBgColor = Color(0xFF111827);
//   static const Color darkBgLightColor = Color(0xFF1F2937);
//   static const Color darkBgIndigo = Color(0xFF312E81);
//
//   static const Color darkBorderColor = Color(0xFF374151);
//   static const Color darkBorderActiveColor = Color(0xFF6366F1);
//
//   // 通用颜色
//   static const Color greenLight = Color(0xFFDCFCE7);
//   static const Color greenDark = Color(0xFF10B981);
//
//   static const Color redLight = Color(0xFFFEE2E2);
//   static const Color redDark = Color(0xFFEF4444);
//
//   static const Color yellowLight = Color(0xFFFEF3C7);
//   static const Color yellowDark = Color(0xFFF59E0B);
//
//   static const Color purpleLight = Color(0xFFF3E8FF);
//   static const Color purpleDark = Color(0xFF8B5CF6);
//
//   static const Color orangeLight = Color(0xFFFFEDD5);
//   static const Color orangeDark = Color(0xFFF97316);
//
//   // 中国传统色
//   static const Color chineseRed = Color(0xFFCB1B45); // 茜红 - xiān hóng
//   static const Color chineseRedLight = Color(0xFFFFE4E1);
//   static const Color chineseBlue = Color(0xFF2E4E7E); // 靛青 - diàn qīng
//   static const Color chineseBlueLight = Color(0xFFE6EEFF);
//   static const Color chineseYellow = Color(0xFFF7C242); // 缃色 - xiāng sè
//   static const Color chineseYellowLight = Color(0xFFFFF8E1);
//   static const Color chineseGreen = Color(0xFF1A6840); // 松绿 - sōng lǜ
//   static const Color chineseGreenLight = Color(0xFFE8F5E9);
//   static const Color chineseCyan = Color(0xFF00A6AC); // 石青 - shí qīng
//   static const Color chineseCyanLight = Color(0xFFE0F7FA);
//   static const Color chinesePurple = Color(0xFF8B2671); // 紫色 - zǐ sè
//   static const Color chinesePurpleLight = Color(0xFFF3E5F5);
//   static const Color chineseWhite = Color(0xFFF2ECDE); // 缟 - gǎo
//   static const Color chineseBlack = Color(0xFF161823); // 玄青 - xuán qīng
//
//   // 渐变
//   static const LinearGradient primaryGradient = LinearGradient(
//     begin: Alignment.topLeft,
//     end: Alignment.bottomRight,
//     colors: [primaryColor, secondaryColor],
//   );
//
//   static const LinearGradient blueGradient = LinearGradient(
//     begin: Alignment.topLeft,
//     end: Alignment.bottomRight,
//     colors: [Color(0xFF60A5FA), Color(0xFF3B82F6)],
//   );
//
//   static const LinearGradient greenGradient = LinearGradient(
//     begin: Alignment.topLeft,
//     end: Alignment.bottomRight,
//     colors: [Color(0xFF34D399), Color(0xFF10B981)],
//   );
//
//   static const LinearGradient purpleGradient = LinearGradient(
//     begin: Alignment.topLeft,
//     end: Alignment.bottomRight,
//     colors: [Color(0xFFA78BFA), Color(0xFF8B5CF6)],
//   );
//
//   static const LinearGradient yellowGradient = LinearGradient(
//     begin: Alignment.topLeft,
//     end: Alignment.bottomRight,
//     colors: [Color(0xFFFCD34D), Color(0xFFF59E0B)],
//   );
//
//   static const LinearGradient orangeGradient = LinearGradient(
//     begin: Alignment.topLeft,
//     end: Alignment.bottomRight,
//     colors: [Color(0xFFFFBA7C), Color(0xFFF97316)],
//   );
//
//   static const LinearGradient chineseGradient = LinearGradient(
//     begin: Alignment.topLeft,
//     end: Alignment.bottomRight,
//     colors: [chineseRed, chinesePurple],
//   );
//
//   // 圆角
//   static const double borderRadiusXS = 8.0;
//   static const double borderRadiusSM = 12.0;
//   static const double borderRadiusMD = 16.0;
//   static const double borderRadiusLG = 24.0;
//   static const double borderRadiusXL = 40.0;
//
//   // 边距
//   static const double paddingXS = 4.0;
//   static const double paddingSM = 8.0;
//   static const double paddingMD = 16.0;
//   static const double paddingLG = 24.0;
//   static const double paddingXL = 32.0;
//
//   // 莫兰迪色系
//   static const Color morandiPrimary = Color(0xFF94A6B4);
//   static const Color morandiSecondary = Color(0xFFB4A394);
//   static const Color morandiAccent = Color(0xFFB494A6);
//   static const Color morandiBg = Color(0xFFF5F5F3);
//   static const Color morandiText = Color(0xFF4A4A4A);
//
//   // 极简黑白
//   static const Color monoPrimary = Color(0xFF2C2C2C);
//   static const Color monoSecondary = Color(0xFF5C5C5C);
//   static const Color monoAccent = Color(0xFF8C8C8C);
//   static const Color monoBg = Color(0xFFFAFAFA);
//   static const Color monoText = Color(0xFF1A1A1A);
//
//   // 自然色系
//   static const Color naturePrimary = Color(0xFF7FA480);
//   static const Color natureSecondary = Color(0xFFB7C9B7);
//   static const Color natureAccent = Color(0xFFD4B7A4);
//   static const Color natureBg = Color(0xFFF8F6F3);
//   static const Color natureText = Color(0xFF3C4A3C);
//
//   // 科技感
//   static const Color techPrimary = Color(0xFF0084FF);
//   static const Color techSecondary = Color(0xFF00E5FF);
//   static const Color techAccent = Color(0xFF00FFD1);
//   static const Color techBg = Color(0xFF0A1929);
//   static const Color techText = Color(0xFFE6F3FF);
//
//   // 优化后的中国传统色
//   static const Color chinesePrimary = Color(0xFFB4002D); // 枫红
//   static const Color chineseSecondary = Color(0xFF247B70); // 玉色
//   static const Color chineseAccent = Color(0xFFB68D4C); // 琥珀
//   static const Color chineseBg = Color(0xFFF7F4ED); // 玉脂
//   static const Color chineseText = Color(0xFF2D2B27); // 墨
//
//   // 亮色主题
//   static ThemeData lightTheme = ThemeData(
//     useMaterial3: true,
//     colorScheme: ColorScheme.fromSeed(
//       seedColor: primaryColor,
//       primary: primaryColor,
//       secondary: secondaryColor,
//       surface: bgWhiteColor,
//     ),
//     scaffoldBackgroundColor: bgLightColor,
//     textTheme: _getTextTheme(),
//     elevatedButtonTheme: ElevatedButtonThemeData(
//       style: ElevatedButton.styleFrom(
//         backgroundColor: primaryColor,
//         foregroundColor: Colors.white,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(borderRadiusMD),
//         ),
//         elevation: 0,
//         padding: const EdgeInsets.symmetric(
//           horizontal: paddingLG,
//           vertical: paddingMD,
//         ),
//         textStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
//       ),
//     ),
//     outlinedButtonTheme: OutlinedButtonThemeData(
//       style: OutlinedButton.styleFrom(
//         foregroundColor: primaryColor,
//         side: const BorderSide(color: borderColor),
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(borderRadiusMD),
//         ),
//         padding: const EdgeInsets.symmetric(
//           horizontal: paddingLG,
//           vertical: paddingMD,
//         ),
//         textStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
//       ),
//     ),
//     inputDecorationTheme: InputDecorationTheme(
//       fillColor: bgLightColor,
//       filled: true,
//       border: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//         borderSide: const BorderSide(color: borderColor),
//       ),
//       enabledBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//         borderSide: const BorderSide(color: borderColor),
//       ),
//       focusedBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//         borderSide: const BorderSide(color: borderActiveColor),
//       ),
//       contentPadding: const EdgeInsets.all(paddingMD),
//     ),
//     appBarTheme: const AppBarTheme(
//       backgroundColor: bgWhiteColor,
//       elevation: 0,
//       centerTitle: false,
//       iconTheme: IconThemeData(color: textDarkColor),
//       titleTextStyle: TextStyle(
//         color: textDarkColor,
//         fontSize: 20,
//         fontWeight: FontWeight.w600,
//       ),
//     ),
//     cardTheme: CardThemeData(
//       elevation: 0,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//         side: const BorderSide(color: borderColor),
//       ),
//       color: bgWhiteColor,
//     ),
//     dividerTheme: const DividerThemeData(
//       color: borderColor,
//       thickness: 1,
//       space: 1,
//     ),
//   );
//
//   // 深色主题
//   static ThemeData darkTheme = ThemeData(
//     useMaterial3: true,
//     colorScheme: ColorScheme.fromSeed(
//       seedColor: darkPrimaryColor,
//       brightness: Brightness.dark,
//       primary: darkPrimaryColor,
//       secondary: darkSecondaryColor,
//       surface: darkBgLightColor,
//     ),
//     scaffoldBackgroundColor: darkBgColor,
//     textTheme: _getTextTheme(AppThemeType.materialYou, true),
//     elevatedButtonTheme: ElevatedButtonThemeData(
//       style: ElevatedButton.styleFrom(
//         backgroundColor: darkPrimaryColor,
//         foregroundColor: Colors.white,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(borderRadiusMD),
//         ),
//         elevation: 0,
//         padding: const EdgeInsets.symmetric(
//           horizontal: paddingLG,
//           vertical: paddingMD,
//         ),
//         textStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
//       ),
//     ),
//     outlinedButtonTheme: OutlinedButtonThemeData(
//       style: OutlinedButton.styleFrom(
//         foregroundColor: darkPrimaryColor,
//         side: const BorderSide(color: darkBorderColor),
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(borderRadiusMD),
//         ),
//         padding: const EdgeInsets.symmetric(
//           horizontal: paddingLG,
//           vertical: paddingMD,
//         ),
//         textStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
//       ),
//     ),
//     inputDecorationTheme: InputDecorationTheme(
//       fillColor: darkBgLightColor,
//       filled: true,
//       border: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//         borderSide: const BorderSide(color: darkBorderColor),
//       ),
//       enabledBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//         borderSide: const BorderSide(color: darkBorderColor),
//       ),
//       focusedBorder: OutlineInputBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//         borderSide: const BorderSide(color: darkBorderActiveColor),
//       ),
//       contentPadding: const EdgeInsets.all(paddingMD),
//       hintStyle: const TextStyle(color: darkTextLightColor),
//     ),
//     appBarTheme: const AppBarTheme(
//       backgroundColor: darkBgLightColor,
//       elevation: 0,
//       centerTitle: false,
//       iconTheme: IconThemeData(color: darkTextColor),
//       titleTextStyle: TextStyle(
//         color: darkTextColor,
//         fontSize: 20,
//         fontWeight: FontWeight.w600,
//       ),
//     ),
//     cardTheme: CardThemeData(
//       elevation: 0,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//         side: const BorderSide(color: darkBorderColor),
//       ),
//       color: darkBgLightColor,
//     ),
//     dividerTheme: const DividerThemeData(
//       color: darkBorderColor,
//       thickness: 1,
//       space: 1,
//     ),
//     dialogTheme: DialogThemeData(
//       backgroundColor: darkBgLightColor,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//       ),
//     ),
//     bottomSheetTheme: const BottomSheetThemeData(
//       backgroundColor: darkBgLightColor,
//     ),
//     popupMenuTheme: PopupMenuThemeData(
//       color: darkBgLightColor,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//       ),
//     ),
//     switchTheme: SwitchThemeData(
//       thumbColor: WidgetStateProperty.resolveWith((states) {
//         if (states.contains(WidgetState.selected)) {
//           return darkPrimaryColor;
//         }
//         return null;
//       }),
//       trackColor: WidgetStateProperty.resolveWith((states) {
//         if (states.contains(WidgetState.selected)) {
//           return darkPrimaryColor.withValues(alpha: 0.5);
//         }
//         return null;
//       }),
//     ),
//     checkboxTheme: CheckboxThemeData(
//       fillColor: WidgetStateProperty.resolveWith((states) {
//         if (states.contains(WidgetState.selected)) {
//           return darkPrimaryColor;
//         }
//         return null;
//       }),
//     ),
//     radioTheme: RadioThemeData(
//       fillColor: WidgetStateProperty.resolveWith((states) {
//         if (states.contains(WidgetState.selected)) {
//           return darkPrimaryColor;
//         }
//         return null;
//       }),
//     ),
//   );
//
//   // 蓝色主题 - 亮色模式
//   static ThemeData blueLightTheme = ThemeData(
//     useMaterial3: true,
//     colorScheme: ColorScheme.fromSeed(
//       seedColor: const Color(0xFF1E88E5),
//       primary: const Color(0xFF1E88E5),
//       secondary: const Color(0xFF42A5F5),
//       surface: bgWhiteColor,
//     ),
//     scaffoldBackgroundColor: const Color(0xFFF5F9FF),
//     textTheme: _getTextTheme(),
//     elevatedButtonTheme: ElevatedButtonThemeData(
//       style: ElevatedButton.styleFrom(
//         backgroundColor: const Color(0xFF1E88E5),
//         foregroundColor: Colors.white,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(borderRadiusMD),
//         ),
//       ),
//     ),
//   );
//
//   // 蓝色主题 - 深色模式
//   static ThemeData blueDarkTheme = ThemeData(
//     useMaterial3: true,
//     brightness: Brightness.dark,
//     colorScheme: ColorScheme.fromSeed(
//       seedColor: const Color(0xFF42A5F5),
//       brightness: Brightness.dark,
//       primary: const Color(0xFF42A5F5),
//       secondary: const Color(0xFF90CAF9),
//       surface: const Color(0xFF1A2536),
//     ),
//     scaffoldBackgroundColor: const Color(0xFF0A1929),
//   );
//
//   // 绿色主题 - 亮色模式
//   static ThemeData greenLightTheme = ThemeData(
//     useMaterial3: true,
//     colorScheme: ColorScheme.fromSeed(
//       seedColor: const Color(0xFF2E7D32),
//       primary: const Color(0xFF2E7D32),
//       secondary: const Color(0xFF66BB6A),
//       surface: bgWhiteColor,
//     ),
//     scaffoldBackgroundColor: const Color(0xFFF5FFF7),
//   );
//
//   // 绿色主题 - 深色模式
//   static ThemeData greenDarkTheme = ThemeData(
//     useMaterial3: true,
//     brightness: Brightness.dark,
//     colorScheme: ColorScheme.fromSeed(
//       seedColor: const Color(0xFF66BB6A),
//       brightness: Brightness.dark,
//       primary: const Color(0xFF66BB6A),
//       secondary: const Color(0xFFA5D6A7),
//       surface: const Color(0xFF1A2E1C),
//     ),
//     scaffoldBackgroundColor: const Color(0xFF0A1F0F),
//   );
//
//   // 紫色主题 - 亮色模式
//   static ThemeData purpleLightTheme = ThemeData(
//     useMaterial3: true,
//     colorScheme: ColorScheme.fromSeed(
//       seedColor: const Color(0xFF7B1FA2),
//       primary: const Color(0xFF7B1FA2),
//       secondary: const Color(0xFFAB47BC),
//       surface: bgWhiteColor,
//     ),
//     scaffoldBackgroundColor: const Color(0xFFFAF5FF),
//   );
//
//   // 紫色主题 - 深色模式
//   static ThemeData purpleDarkTheme = ThemeData(
//     useMaterial3: true,
//     brightness: Brightness.dark,
//     colorScheme: ColorScheme.fromSeed(
//       seedColor: const Color(0xFFAB47BC),
//       brightness: Brightness.dark,
//       primary: const Color(0xFFAB47BC),
//       secondary: const Color(0xFFCE93D8),
//       surface: const Color(0xFF2A1930),
//     ),
//     scaffoldBackgroundColor: const Color(0xFF170A1E),
//   );
//
//   // 中国传统色主题 - 亮色模式
//   static ThemeData chineseLightTheme = ThemeData(
//     useMaterial3: true,
//     colorScheme: ColorScheme.fromSeed(
//       seedColor: chinesePrimary,
//       primary: chinesePrimary,
//       secondary: chineseCyan,
//       surface: chineseWhite,
//     ),
//     scaffoldBackgroundColor: const Color(0xFFF9F6F0),
//     textTheme: _getTextTheme(),
//     elevatedButtonTheme: ElevatedButtonThemeData(
//       style: ElevatedButton.styleFrom(
//         backgroundColor: chinesePrimary,
//         foregroundColor: Colors.white,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(borderRadiusMD),
//         ),
//       ),
//     ),
//     cardTheme: CardThemeData(
//       elevation: 0,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//         side: const BorderSide(color: Color(0xFFE8E0D5)),
//       ),
//       color: chineseWhite,
//     ),
//   );
//
//   // 中国传统色主题 - 深色模式
//   static ThemeData chineseDarkTheme = ThemeData(
//     useMaterial3: true,
//     brightness: Brightness.dark,
//     colorScheme: ColorScheme.fromSeed(
//       seedColor: chinesePrimary,
//       brightness: Brightness.dark,
//       primary: chinesePrimary,
//       secondary: chineseCyan,
//       surface: const Color(0xFF2A2A35),
//     ),
//     scaffoldBackgroundColor: chineseBlack,
//     textTheme: _getTextTheme(),
//     cardTheme: CardThemeData(
//       elevation: 0,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(borderRadiusMD),
//         side: const BorderSide(color: Color(0xFF3A3A45)),
//       ),
//       color: const Color(0xFF2A2A35),
//     ),
//   );
//
//   /// 获取安全的文本主题
//   static TextTheme _getTextTheme([
//     AppThemeType themeType = AppThemeType.materialYou,
//     bool isDark = false,
//   ]) {
//     // 使用系统默认字体
//     final Color primaryTextColor = isDark ? darkTextColor : textDarkColor;
//     final Color mediumTextColor =
//         isDark ? darkTextMediumColor : textMediumColor;
//     final Color lightTextColor = isDark ? darkTextLightColor : textLightColor;
//
//     return TextTheme(
//       displayLarge: TextStyle(color: primaryTextColor),
//       displayMedium: TextStyle(color: primaryTextColor),
//       displaySmall: TextStyle(color: primaryTextColor),
//       headlineLarge: TextStyle(color: primaryTextColor),
//       headlineMedium: TextStyle(color: primaryTextColor),
//       headlineSmall: TextStyle(color: primaryTextColor),
//       titleLarge: TextStyle(color: primaryTextColor),
//       titleMedium: TextStyle(color: primaryTextColor),
//       titleSmall: TextStyle(color: primaryTextColor),
//       bodyLarge: TextStyle(color: primaryTextColor),
//       bodyMedium: TextStyle(color: primaryTextColor),
//       bodySmall: TextStyle(color: mediumTextColor),
//       labelLarge: TextStyle(color: primaryTextColor),
//       labelMedium: TextStyle(color: mediumTextColor),
//       labelSmall: TextStyle(color: lightTextColor),
//     );
//   }
//
//   /// 获取主题
//   static ThemeData getTheme(AppThemeType themeType, bool isDark) {
//     switch (themeType) {
//       case AppThemeType.materialYou:
//         return isDark ? darkTheme : lightTheme;
//       case AppThemeType.morandiTheme:
//         return morandiTheme(isDark);
//       case AppThemeType.monochromeTheme:
//         return monochromeTheme(isDark);
//       case AppThemeType.natureTheme:
//         return natureTheme(isDark);
//       case AppThemeType.techTheme:
//         return techTheme(isDark);
//       case AppThemeType.chineseTheme:
//         return chineseTheme(isDark);
//     }
//   }
//
//   // 适配暗黑模式的颜色
//   static Color adaptDarkColor(
//     Color lightColor,
//     Color darkColor,
//     bool isDarkMode,
//   ) {
//     return isDarkMode ? darkColor : lightColor;
//   }
//
//   // 获取主题名称
//   static String getThemeName(AppThemeType type) {
//     switch (type) {
//       case AppThemeType.materialYou:
//         return 'Material You';
//       case AppThemeType.morandiTheme:
//         return '莫兰迪风格';
//       case AppThemeType.monochromeTheme:
//         return '极简黑白';
//       case AppThemeType.natureTheme:
//         return '自然色系';
//       case AppThemeType.techTheme:
//         return '科技感';
//       case AppThemeType.chineseTheme:
//         return '中国传统色';
//     }
//   }
//
//   /// 获取主题图标
//   static IconData getThemeIcon(AppThemeType type) {
//     switch (type) {
//       case AppThemeType.materialYou:
//         return Icons.palette_outlined;
//       case AppThemeType.morandiTheme:
//         return Icons.opacity;
//       case AppThemeType.monochromeTheme:
//         return Icons.contrast;
//       case AppThemeType.natureTheme:
//         return Icons.eco_outlined;
//       case AppThemeType.techTheme:
//         return Icons.memory;
//       case AppThemeType.chineseTheme:
//         return Icons.brush;
//     }
//   }
//
//   /// 获取主题描述
//   static String getThemeDescription(AppThemeType type) {
//     switch (type) {
//       case AppThemeType.materialYou:
//         return '根据壁纸自动提取的动态主题';
//       case AppThemeType.morandiTheme:
//         return '柔和优雅的莫兰迪色系';
//       case AppThemeType.monochromeTheme:
//         return '简约纯粹的黑白配色';
//       case AppThemeType.natureTheme:
//         return '舒适自然的生态色系';
//       case AppThemeType.techTheme:
//         return '充满未来感的科技色彩';
//       case AppThemeType.chineseTheme:
//         return '传统与现代结合的东方美学';
//     }
//   }
//
//   /// 获取主题颜色
//   static Color getThemeColor(AppThemeType type) {
//     switch (type) {
//       case AppThemeType.materialYou:
//         return const Color(0xFF6750A4);
//       case AppThemeType.morandiTheme:
//         return morandiPrimary;
//       case AppThemeType.monochromeTheme:
//         return monoPrimary;
//       case AppThemeType.natureTheme:
//         return naturePrimary;
//       case AppThemeType.techTheme:
//         return techPrimary;
//       case AppThemeType.chineseTheme:
//         return chinesePrimary;
//     }
//   }
//
//   // 中国传统色主题
//   static ThemeData chineseTheme(bool isDark) {
//     return ThemeData(
//       useMaterial3: true,
//       colorScheme: ColorScheme.fromSeed(
//         seedColor: chinesePrimary,
//         brightness: isDark ? Brightness.dark : Brightness.light,
//         primary: chinesePrimary,
//         secondary: chineseSecondary,
//       ),
//       scaffoldBackgroundColor: isDark ? chineseBlack : chineseBg,
//       textTheme: _getTextTheme(AppThemeType.chineseTheme, isDark),
//       cardTheme: CardThemeData(
//         color: Colors.white,
//         elevation: 0,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(16),
//           side: BorderSide(color: chinesePrimary.withAlpha(20)),
//         ),
//       ),
//     );
//   }
//
//   // 莫兰迪风格主题
//   static ThemeData morandiTheme(bool isDark) {
//     return ThemeData(
//       useMaterial3: true,
//       colorScheme: ColorScheme.fromSeed(
//         seedColor: morandiPrimary,
//         brightness: isDark ? Brightness.dark : Brightness.light,
//         primary: morandiPrimary,
//         secondary: morandiSecondary,
//       ),
//       scaffoldBackgroundColor: isDark ? const Color(0xFF2E2E2E) : morandiBg,
//       textTheme: _getTextTheme(AppThemeType.morandiTheme, isDark),
//       cardTheme: CardThemeData(
//         color: Colors.white,
//         elevation: 0,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(16),
//           side: BorderSide(color: morandiPrimary.withAlpha(20)),
//         ),
//       ),
//     );
//   }
//
//   // 极简黑白主题
//   static ThemeData monochromeTheme(bool isDark) {
//     return ThemeData(
//       useMaterial3: true,
//       colorScheme: ColorScheme.fromSeed(
//         seedColor: monoPrimary,
//         brightness: isDark ? Brightness.dark : Brightness.light,
//         primary: monoPrimary,
//         secondary: monoSecondary,
//       ),
//       scaffoldBackgroundColor: isDark ? const Color(0xFF121212) : monoBg,
//       textTheme: _getTextTheme(AppThemeType.monochromeTheme, isDark),
//       cardTheme: CardThemeData(
//         color: Colors.white,
//         elevation: 0,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(12),
//           side: const BorderSide(color: Color(0xFFEEEEEE)),
//         ),
//       ),
//     );
//   }
//
//   // 自然色系主题
//   static ThemeData natureTheme(bool isDark) {
//     return ThemeData(
//       useMaterial3: true,
//       colorScheme: ColorScheme.fromSeed(
//         seedColor: naturePrimary,
//         brightness: isDark ? Brightness.dark : Brightness.light,
//         primary: naturePrimary,
//         secondary: natureSecondary,
//       ),
//       scaffoldBackgroundColor: isDark ? const Color(0xFF1C2A1C) : natureBg,
//       textTheme: _getTextTheme(AppThemeType.natureTheme, isDark),
//       cardTheme: CardThemeData(
//         color: Colors.white,
//         elevation: 0,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(16),
//           side: BorderSide(color: naturePrimary.withAlpha(20)),
//         ),
//       ),
//     );
//   }
//
//   // 科技感主题
//   static ThemeData techTheme(bool isDark) {
//     return ThemeData(
//       useMaterial3: true,
//       colorScheme: ColorScheme.fromSeed(
//         seedColor: techPrimary,
//         brightness: isDark ? Brightness.dark : Brightness.light,
//         primary: techPrimary,
//         secondary: techSecondary,
//       ),
//       scaffoldBackgroundColor: isDark ? techBg : Colors.white,
//       textTheme: _getTextTheme(AppThemeType.techTheme, isDark),
//       cardTheme: CardThemeData(
//         color: Colors.white,
//         elevation: 0,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(8),
//           side: BorderSide(color: techPrimary.withAlpha(20)),
//         ),
//       ),
//     );
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/config/app_theme.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/config/constants.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// class AppConstants {
//   // App 信息
//   static const String appName = 'ContentPal';
//   static const String appNameChinese = '内容君';
//   static const String appVersion = '1.0.0';
//   static const String appDescription = '专业的内容处理工具，让内容创作更轻松';
//   static const String buildDate = '2023-03-05';
//
//   // 链接
//   static const String projectUrl = 'https://www.jiangkang.tech';
//   static const String privacyPolicyUrl = 'https://www.jiangkang.tech';
//   static const String termsOfServiceUrl = 'https://www.jiangkang.tech';
//
//   // Hive存储相关
//   static const String mainBoxName = 'ai_local_chat_box';
//
//   // 本地存储键
//   static const String keyThemeMode = 'theme_mode';
//   static const String keyApiKeys = 'api_keys';
//   static const String keyActiveModels = 'active_models';
//   static const String keyActiveProviders = 'active_providers';
//   static const String keyAIModels = 'ai_models';
//   static const String keyConversations = 'conversations';
//   static const String keySettings = 'settings';
//   static const String keyPrompts = 'prompts';
//   static const String keyHasLaunchedBefore = 'has_launched_before';
//   static const String keyHasLoadedBuiltInPrompts =
//       'has_loaded_built_in_prompts';
//   static const String keySubscription = 'subscription';
//
//   // 默认设置
//   static const double defaultTemperature = 0.7;
//   static const bool defaultAutoSaveChat = true;
//   static const bool defaultPrivacyEnhanced = true;
//   static const bool defaultNotifications = true;
//   static const bool defaultStreamingResponse = true;
//   static const int defaultRequestTimeout = 30; // 默认请求超时时间（秒）
// }
//
// // ===================================================================
// // 文件结束: lib/config/constants.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/content/content_detail_page.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_markdown/flutter_markdown.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:photo_view/photo_view.dart';
// import 'package:share_plus/share_plus.dart';
// import 'package:webview_flutter/webview_flutter.dart';
// import 'package:url_launcher/url_launcher.dart';
// import 'package:markdown/markdown.dart' as md;
//
// import '../config/app_theme.dart';
// import '../models/content_item.dart';
// import '../services/content_service.dart';
// import 'content_editor_page.dart';
//
// class ContentDetailPage extends StatefulWidget {
//   final ContentItem contentItem;
//
//   const ContentDetailPage({super.key, required this.contentItem});
//
//   @override
//   State<ContentDetailPage> createState() => _ContentDetailPageState();
// }
//
// class _ContentDetailPageState extends State<ContentDetailPage> {
//   late ContentItem _contentItem;
//   final ContentService _contentService = ContentService();
//   bool _isLoading = false;
//   WebViewController? _webViewController;
//
//   @override
//   void initState() {
//     super.initState();
//     _contentItem = widget.contentItem;
//     _initializeWebView();
//   }
//
//   void _initializeWebView() {
//     if (_contentItem.type == ContentType.html) {
//       _webViewController =
//           WebViewController()
//             ..setBackgroundColor(Colors.white)
//             ..setJavaScriptMode(JavaScriptMode.unrestricted)
//             ..loadHtmlString(_contentItem.content as String);
//     }
//   }
//
//   Future<void> _toggleFavorite() async {
//     setState(() {
//       _isLoading = true;
//     });
//
//     try {
//       final updatedItem = await _contentService.toggleFavorite(_contentItem.id);
//       setState(() {
//         _contentItem = updatedItem;
//       });
//     } catch (e) {
//       _showErrorSnackBar('无法更新收藏状态');
//     } finally {
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }
//
//   Future<void> _deleteContent() async {
//     final confirmed = await showDialog<bool>(
//       context: context,
//       builder:
//           (context) => AlertDialog(
//             title: const Text('删除内容'),
//             content: const Text('确定要删除此内容吗？此操作无法撤销。'),
//             actions: [
//               TextButton(
//                 onPressed: () => Navigator.of(context).pop(false),
//                 child: const Text('取消'),
//               ),
//               TextButton(
//                 onPressed: () => Navigator.of(context).pop(true),
//                 child: const Text('删除', style: TextStyle(color: Colors.red)),
//               ),
//             ],
//           ),
//     );
//
//     if (confirmed == true) {
//       setState(() {
//         _isLoading = true;
//       });
//
//       try {
//         await _contentService.deleteItem(_contentItem.id);
//         if (mounted) {
//           Navigator.of(context).pop();
//         }
//       } catch (e) {
//         _showErrorSnackBar('删除内容失败');
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     }
//   }
//
//   Future<void> _shareContent() async {
//     try {
//       switch (_contentItem.type) {
//         case ContentType.markdown:
//         case ContentType.html:
//           final String content = _contentItem.content as String;
//           await SharePlus.instance.share(ShareParams(text: content, subject: _contentItem.title));
//           break;
//
//         case ContentType.image:
//         case ContentType.svg:
//           final String filePath = _contentItem.content as String;
//           if (filePath.startsWith('http')) {
//             await SharePlus.instance.share(ShareParams(uri: Uri.tryParse(filePath),subject: _contentItem.title));
//           } else {
//             final file = File(filePath);
//             if (await file.exists()) {
//               final bytes = await file.readAsBytes();
//               final tempDir = await getTemporaryDirectory();
//               final tempFile = File(
//                 '${tempDir.path}/${_contentItem.title}.${_contentItem.fileExtension}',
//               );
//               await tempFile.writeAsBytes(bytes);
//               await SharePlus.instance.share(
//                 ShareParams(files: [XFile(tempFile.path)], text: _contentItem.title),
//               );
//             } else {
//               _showErrorSnackBar('文件不存在');
//             }
//           }
//           break;
//       }
//     } catch (e) {
//       _showErrorSnackBar('分享失败: $e');
//     }
//   }
//
//   Future<void> _editContent() async {
//     switch (_contentItem.type) {
//       case ContentType.markdown:
//       case ContentType.html:
//       case ContentType.svg:
//         // 文本类型的内容使用通用编辑器
//         String content = _contentItem.content.toString();
//
//         if (_contentItem.type == ContentType.svg &&
//             (_contentItem.content as String).startsWith('/')) {
//           final file = File(_contentItem.content as String);
//           if (await file.exists()) {
//             content = await file.readAsString();
//           } else {
//             _showErrorSnackBar('无法加载SVG文件');
//             return;
//           }
//         }
//
//         final result = await Navigator.push<String>(
//           context,
//           MaterialPageRoute(
//             builder:
//                 (context) => ContentEditorPage(
//                   title: '编辑 ${_contentItem.title}',
//                   initialContent: content,
//                   contentType: _contentItem.type,
//                 ),
//           ),
//         );
//
//         if (result != null) {
//           await _updateContentData(result);
//           if (_contentItem.type == ContentType.html) {
//             _initializeWebView();
//           }
//         }
//         break;
//
//       case ContentType.image:
//         _showErrorSnackBar('图片编辑暂未实现');
//         break;
//     }
//   }
//
//   Future<void> _updateContentData(dynamic newContent) async {
//     setState(() {
//       _isLoading = true;
//     });
//
//     try {
//       final updatedItem = _contentItem.copyWith(
//         content: newContent,
//         updatedAt: DateTime.now(),
//       );
//
//       final savedItem = await _contentService.updateItem(updatedItem);
//
//       setState(() {
//         _contentItem = savedItem;
//       });
//     } catch (e) {
//       _showErrorSnackBar('更新内容失败');
//     } finally {
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }
//
//   void _showErrorSnackBar(String message) {
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(content: Text(message), backgroundColor: Colors.red),
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(_contentItem.title),
//         actions: [
//           IconButton(
//             icon: Icon(
//               _contentItem.isFavorite ? Icons.favorite : Icons.favorite_border,
//               color: _contentItem.isFavorite ? Colors.red : null,
//             ),
//             onPressed: _toggleFavorite,
//           ),
//           IconButton(icon: const Icon(Icons.share), onPressed: _shareContent),
//           IconButton(icon: const Icon(Icons.edit), onPressed: _editContent),
//           PopupMenuButton<String>(
//             onSelected: (value) {
//               if (value == 'delete') {
//                 _deleteContent();
//               }
//             },
//             itemBuilder:
//                 (context) => [
//                   const PopupMenuItem<String>(
//                     value: 'delete',
//                     child: Row(
//                       children: [
//                         Icon(Icons.delete, color: Colors.red),
//                         SizedBox(width: 8),
//                         Text('删除', style: TextStyle(color: Colors.red)),
//                       ],
//                     ),
//                   ),
//                 ],
//           ),
//         ],
//       ),
//       body:
//           _isLoading
//               ? const Center(child: CircularProgressIndicator())
//               : _buildContentView(),
//     );
//   }
//
//   Widget _buildContentView() {
//     switch (_contentItem.type) {
//       case ContentType.markdown:
//         return _buildMarkdownView();
//       case ContentType.image:
//         return _buildImageView();
//       case ContentType.svg:
//         return _buildSvgView();
//       case ContentType.html:
//         return _buildHtmlView();
//     }
//   }
//
//   Widget _buildMarkdownView() {
//     return Markdown(
//       data: _contentItem.content as String,
//       selectable: true,
//       styleSheet: MarkdownStyleSheet(
//         h1: const TextStyle(
//           fontSize: 28,
//           fontWeight: FontWeight.bold,
//           color: AppTheme.textDarkColor,
//           height: 1.7,
//         ),
//         h1Padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
//         h2: const TextStyle(
//           fontSize: 24,
//           fontWeight: FontWeight.bold,
//           color: AppTheme.textDarkColor,
//           height: 1.6,
//         ),
//         h2Padding: const EdgeInsets.only(top: 12.0, bottom: 6.0),
//         h3: const TextStyle(
//           fontSize: 20,
//           fontWeight: FontWeight.bold,
//           color: AppTheme.textDarkColor,
//           height: 1.5,
//         ),
//         h3Padding: const EdgeInsets.only(top: 8.0, bottom: 4.0),
//         h4: const TextStyle(
//           fontSize: 18,
//           fontWeight: FontWeight.bold,
//           color: AppTheme.textDarkColor,
//         ),
//         h4Padding: const EdgeInsets.only(top: 6.0, bottom: 3.0),
//         p: const TextStyle(
//           fontSize: 16,
//           height: 1.5,
//           color: AppTheme.textDarkColor,
//         ),
//         pPadding: const EdgeInsets.symmetric(vertical: 8.0),
//         listIndent: 24.0,
//         listBullet: const TextStyle(
//           fontSize: 16,
//           color: AppTheme.textDarkColor,
//         ),
//         listBulletPadding: const EdgeInsets.only(right: 8.0),
//         blockquoteDecoration: BoxDecoration(
//           color: const Color(0xFFF5F7F9),
//           borderRadius: BorderRadius.circular(4),
//           border: Border(
//             left: BorderSide(color: Colors.grey.shade400, width: 4),
//           ),
//         ),
//         blockquotePadding: const EdgeInsets.all(12.0),
//         blockquote: TextStyle(
//           fontSize: 16,
//           fontStyle: FontStyle.italic,
//           color: Colors.grey.shade700,
//         ),
//         code: const TextStyle(
//           fontSize: 14,
//           backgroundColor: Color(0xFFF5F7F9),
//           fontFamily: 'monospace',
//           color: Color(0xFF333333),
//         ),
//         codeblockDecoration: BoxDecoration(
//           color: const Color(0xFFF5F7F9),
//           borderRadius: BorderRadius.circular(4),
//           border: Border.all(color: Colors.grey.shade300),
//         ),
//         codeblockPadding: const EdgeInsets.all(12.0),
//         horizontalRuleDecoration: BoxDecoration(
//           border: Border(
//             top: BorderSide(width: 1.0, color: Colors.grey.shade400),
//           ),
//         ),
//         tableHead: const TextStyle(
//           fontWeight: FontWeight.bold,
//           color: AppTheme.textDarkColor,
//         ),
//         tableBody: const TextStyle(color: AppTheme.textDarkColor),
//         tableBorder: TableBorder.all(color: Colors.grey.shade300, width: 0.5),
//         tableHeadAlign: TextAlign.center,
//         tableCellsPadding: const EdgeInsets.all(8.0),
//       ),
//       extensionSet: md.ExtensionSet(
//         md.ExtensionSet.gitHubFlavored.blockSyntaxes,
//         [md.EmojiSyntax(), ...md.ExtensionSet.gitHubFlavored.inlineSyntaxes],
//       ),
//       padding: const EdgeInsets.all(16.0),
//       onTapLink: (text, href, title) {
//         if (href != null) {
//           launchUrl(Uri.parse(href));
//         }
//       },
//     );
//   }
//
//   Widget _buildImageView() {
//     final content = _contentItem.content;
//
//     if (content is String && content.startsWith('http')) {
//       // 网络图片
//       return PhotoView(
//         imageProvider: NetworkImage(content),
//         backgroundDecoration: const BoxDecoration(color: Colors.white),
//         loadingBuilder:
//             (context, event) =>
//                 const Center(child: CircularProgressIndicator()),
//       );
//     } else if (content is String &&
//         (content.startsWith('/') || content.contains('content_files'))) {
//       // 本地图片
//       return PhotoView(
//         imageProvider: FileImage(File(content)),
//         backgroundDecoration: const BoxDecoration(color: Colors.white),
//         loadingBuilder:
//             (context, event) =>
//                 const Center(child: CircularProgressIndicator()),
//       );
//     } else {
//       // 未知图片类型
//       return const Center(
//         child: Icon(
//           Icons.image_not_supported_outlined,
//           color: Colors.grey,
//           size: 80,
//         ),
//       );
//     }
//   }
//
//   Widget _buildSvgView() {
//     final content = _contentItem.content;
//
//     try {
//       if (content is String && content.startsWith('http')) {
//         // 网络SVG
//         return Center(
//           child: SvgPicture.network(
//             content,
//             placeholderBuilder:
//                 (context) => const Center(child: CircularProgressIndicator()),
//           ),
//         );
//       } else if (content is String &&
//           (content.startsWith('/') || content.contains('content_files'))) {
//         // 本地SVG
//         return Center(
//           child: SvgPicture.file(
//             File(content),
//             placeholderBuilder:
//                 (context) => const Center(child: CircularProgressIndicator()),
//           ),
//         );
//       } else if (content is String && content.startsWith('<svg')) {
//         // SVG字符串
//         return Center(
//           child: SvgPicture.string(
//             content,
//             placeholderBuilder:
//                 (context) => const Center(child: CircularProgressIndicator()),
//           ),
//         );
//       } else {
//         // 未知SVG类型
//         return const Center(
//           child: Icon(
//             Icons.image_not_supported_outlined,
//             color: Colors.grey,
//             size: 80,
//           ),
//         );
//       }
//     } catch (e) {
//       return Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             const Icon(Icons.error_outline, color: Colors.red, size: 60),
//             const SizedBox(height: 16),
//             Text(
//               'SVG 渲染错误: $e',
//               style: const TextStyle(color: Colors.red),
//               textAlign: TextAlign.center,
//             ),
//           ],
//         ),
//       );
//     }
//   }
//
//   Widget _buildHtmlView() {
//     if (_webViewController == null) {
//       return const Center(child: Text('加载 HTML 渲染器失败'));
//     }
//
//     return WebViewWidget(controller: _webViewController!);
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/content/content_detail_page.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/content/content_editor_page.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
// import 'package:flutter_markdown/flutter_markdown.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:webview_flutter/webview_flutter.dart';
//
// import '../config/app_theme.dart';
// import '../models/content_item.dart';
//
// /// 通用内容编辑器页面
// class ContentEditorPage extends StatefulWidget {
//   final String title;
//   final String initialContent;
//   final ContentType contentType;
//
//   const ContentEditorPage({
//     super.key,
//     required this.title,
//     required this.initialContent,
//     required this.contentType,
//   });
//
//   @override
//   State<ContentEditorPage> createState() => _ContentEditorPageState();
// }
//
// class _ContentEditorPageState extends State<ContentEditorPage> {
//   late TextEditingController _textController;
//   late String _previewContent;
//   bool _showPreview = false;
//   WebViewController? _webViewController;
//
//   @override
//   void initState() {
//     super.initState();
//     _textController = TextEditingController(text: widget.initialContent);
//     _previewContent = widget.initialContent;
//
//     if (widget.contentType == ContentType.html) {
//       _initializeWebView();
//     }
//   }
//
//   @override
//   void dispose() {
//     _textController.dispose();
//     super.dispose();
//   }
//
//   void _initializeWebView() {
//     _webViewController =
//         WebViewController()
//           ..setBackgroundColor(Colors.white)
//           ..setJavaScriptMode(JavaScriptMode.unrestricted)
//           ..loadHtmlString(_previewContent);
//   }
//
//   void _updatePreview() {
//     setState(() {
//       _previewContent = _textController.text;
//       if (widget.contentType == ContentType.html &&
//           _webViewController != null) {
//         _webViewController!.loadHtmlString(_previewContent);
//       }
//     });
//   }
//
//   void _togglePreview() {
//     setState(() {
//       _showPreview = !_showPreview;
//       if (_showPreview) {
//         _updatePreview();
//       }
//     });
//   }
//
//   void _saveContent() {
//     Navigator.pop(context, _textController.text);
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(widget.title),
//         actions: [
//           // 预览切换按钮
//           IconButton(
//             icon: Icon(_showPreview ? Icons.edit : Icons.preview),
//             onPressed: _togglePreview,
//             tooltip: _showPreview ? '编辑' : '预览',
//           ),
//           // 保存按钮
//           IconButton(
//             icon: const Icon(Icons.save),
//             onPressed: _saveContent,
//             tooltip: '保存',
//           ),
//         ],
//       ),
//       body: _showPreview ? _buildPreviewWidget() : _buildEditorWidget(),
//     );
//   }
//
//   Widget _buildEditorWidget() {
//     return Column(
//       children: [
//         // 工具栏
//         _buildToolbar(),
//
//         // 编辑器
//         Expanded(
//           child: Container(
//             padding: const EdgeInsets.all(16),
//             child: TextField(
//               controller: _textController,
//               maxLines: null,
//               expands: true,
//               autofocus: true,
//               decoration: const InputDecoration(
//                 border: InputBorder.none,
//                 hintText: '输入内容...',
//               ),
//               style: const TextStyle(fontFamily: 'monospace', fontSize: 16),
//               onChanged: (value) {
//                 // 实时更新预览内容，避免预览时再计算
//                 _previewContent = value;
//               },
//             ),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildToolbar() {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//       decoration: BoxDecoration(
//         color: Colors.grey.shade100,
//         border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
//       ),
//       child: SingleChildScrollView(
//         scrollDirection: Axis.horizontal,
//         child: Row(children: _getToolbarButtons()),
//       ),
//     );
//   }
//
//   List<Widget> _getToolbarButtons() {
//     // 为不同类型的内容提供不同的工具栏按钮
//     switch (widget.contentType) {
//       case ContentType.markdown:
//         return [
//           _buildToolButton('B', '**粗体**'),
//           _buildToolButton('I', '*斜体*'),
//           _buildToolButton('H1', '# 标题1'),
//           _buildToolButton('H2', '## 标题2'),
//           _buildToolButton('H3', '### 标题3'),
//           _buildToolButton('列表', '- 列表项\n- 列表项'),
//           _buildToolButton('链接', '[链接文本](URL)'),
//           _buildToolButton('图片', '![图片描述](图片URL)'),
//           _buildToolButton('代码', '`代码`'),
//           _buildToolButton('代码块', '```\n代码块\n```'),
//           _buildToolButton('引用', '> 引用文本'),
//           _buildToolButton('表格', '| 列1 | 列2 |\n| --- | --- |\n| 内容1 | 内容2 |'),
//         ];
//
//       case ContentType.html:
//         return [
//           _buildToolButton('段落', '<p>段落</p>'),
//           _buildToolButton('标题', '<h1>标题</h1>'),
//           _buildToolButton('粗体', '<strong>粗体</strong>'),
//           _buildToolButton('斜体', '<em>斜体</em>'),
//           _buildToolButton('链接', '<a href="URL">链接文本</a>'),
//           _buildToolButton('图片', '<img src="图片URL" alt="图片描述">'),
//           _buildToolButton('列表', '<ul>\n  <li>列表项</li>\n  <li>列表项</li>\n</ul>'),
//           _buildToolButton(
//             '表格',
//             '<table>\n  <tr>\n    <th>列1</th>\n    <th>列2</th>\n  </tr>\n  <tr>\n    <td>内容1</td>\n    <td>内容2</td>\n  </tr>\n</table>',
//           ),
//           _buildToolButton('div', '<div>\n  内容\n</div>'),
//           _buildToolButton('样式', '<style>\n  /* CSS样式 */\n</style>'),
//           _buildToolButton('脚本', '<script>\n  // JavaScript代码\n</script>'),
//           _buildToolButton('注释', '<!-- 注释 -->'),
//         ];
//
//       case ContentType.svg:
//         return [
//           _buildToolButton(
//             'SVG',
//             '<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg"></svg>',
//           ),
//           _buildToolButton(
//             '矩形',
//             '<rect x="10" y="10" width="80" height="80" fill="blue" />',
//           ),
//           _buildToolButton(
//             '圆形',
//             '<circle cx="50" cy="50" r="40" fill="red" />',
//           ),
//           _buildToolButton(
//             '椭圆',
//             '<ellipse cx="50" cy="50" rx="40" ry="20" fill="green" />',
//           ),
//           _buildToolButton(
//             '线条',
//             '<line x1="10" y1="10" x2="90" y2="90" stroke="black" stroke-width="2" />',
//           ),
//           _buildToolButton(
//             '多边形',
//             '<polygon points="50,10 90,90 10,90" fill="purple" />',
//           ),
//           _buildToolButton('路径', '<path d="M10,30 L90,30" stroke="black" />'),
//           _buildToolButton(
//             '文本',
//             '<text x="10" y="20" fill="black">SVG文本</text>',
//           ),
//           _buildToolButton('组', '<g>\n  <!-- 组内元素 -->\n</g>'),
//           _buildToolButton('样式', '<style>\n  /* CSS样式 */\n</style>'),
//         ];
//
//       default:
//         return [];
//     }
//   }
//
//   Widget _buildToolButton(String label, String insertText) {
//     return Container(
//       margin: const EdgeInsets.symmetric(horizontal: 2),
//       child: ElevatedButton(
//         onPressed: () => _insertText(insertText),
//         style: ElevatedButton.styleFrom(
//           padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
//           minimumSize: const Size(40, 36),
//           shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
//           backgroundColor: AppTheme.primaryColor,
//           foregroundColor: Colors.white,
//         ),
//         child: Text(label, style: const TextStyle(fontSize: 12)),
//       ),
//     );
//   }
//
//   void _insertText(String text) {
//     final currentText = _textController.text;
//     final selection = _textController.selection;
//
//     // 如果有选中文本，则使用选中的文本替换插入文本中的占位符
//     if (selection.start != selection.end) {
//       final selectedText = currentText.substring(
//         selection.start,
//         selection.end,
//       );
//
//       // 根据内容类型处理不同的插入方式
//       String finalText;
//       switch (widget.contentType) {
//         case ContentType.markdown:
//           if (text == '**粗体**') {
//             finalText = '**$selectedText**';
//           } else if (text == '*斜体*') {
//             finalText = '*$selectedText*';
//           } else if (text == '`代码`') {
//             finalText = '`$selectedText`';
//           } else if (text == '```\n代码块\n```') {
//             finalText = '```\n$selectedText\n```';
//           } else if (text == '> 引用文本') {
//             finalText = '> $selectedText';
//           } else if (text == '[链接文本](URL)') {
//             finalText = '[$selectedText](URL)';
//           } else {
//             finalText = text;
//           }
//           break;
//
//         case ContentType.html:
//           if (text == '<p>段落</p>') {
//             finalText = '<p>$selectedText</p>';
//           } else if (text == '<strong>粗体</strong>') {
//             finalText = '<strong>$selectedText</strong>';
//           } else if (text == '<em>斜体</em>') {
//             finalText = '<em>$selectedText</em>';
//           } else if (text == '<a href="URL">链接文本</a>') {
//             finalText = '<a href="URL">$selectedText</a>';
//           } else {
//             finalText = text;
//           }
//           break;
//
//         default:
//           finalText = text;
//           break;
//       }
//
//       final newText = currentText.replaceRange(
//         selection.start,
//         selection.end,
//         finalText,
//       );
//       _textController.text = newText;
//       _textController.selection = TextSelection.collapsed(
//         offset: selection.start + finalText.length,
//       );
//     } else {
//       // 没有选中文本，直接在光标位置插入
//       final newText = currentText.replaceRange(
//         selection.start,
//         selection.end,
//         text,
//       );
//       _textController.text = newText;
//       _textController.selection = TextSelection.collapsed(
//         offset: selection.start + text.length,
//       );
//     }
//   }
//
//   Widget _buildPreviewWidget() {
//     switch (widget.contentType) {
//       case ContentType.markdown:
//         return Markdown(
//           data: _previewContent,
//           selectable: true,
//           styleSheet: MarkdownStyleSheet(
//             h1: const TextStyle(
//               fontSize: 24,
//               fontWeight: FontWeight.bold,
//               color: AppTheme.textDarkColor,
//             ),
//             h2: const TextStyle(
//               fontSize: 20,
//               fontWeight: FontWeight.bold,
//               color: AppTheme.textDarkColor,
//             ),
//             h3: const TextStyle(
//               fontSize: 18,
//               fontWeight: FontWeight.bold,
//               color: AppTheme.textDarkColor,
//             ),
//             p: const TextStyle(
//               fontSize: 16,
//               height: 1.5,
//               color: AppTheme.textDarkColor,
//             ),
//             blockquoteDecoration: BoxDecoration(
//               color: Colors.grey.shade100,
//               borderRadius: BorderRadius.circular(4),
//               border: Border.all(color: Colors.grey.shade300),
//             ),
//             blockquote: TextStyle(
//               fontSize: 16,
//               fontStyle: FontStyle.italic,
//               color: Colors.grey.shade700,
//             ),
//             code: TextStyle(
//               fontSize: 14,
//               backgroundColor: Colors.grey.shade200,
//               fontFamily: 'monospace',
//             ),
//             codeblockDecoration: BoxDecoration(
//               color: Colors.grey.shade100,
//               borderRadius: BorderRadius.circular(4),
//               border: Border.all(color: Colors.grey.shade300),
//             ),
//           ),
//           padding: const EdgeInsets.all(16.0),
//         );
//
//       case ContentType.html:
//         return _webViewController != null
//             ? WebViewWidget(controller: _webViewController!)
//             : const Center(child: Text('无法加载HTML预览'));
//
//       case ContentType.svg:
//         try {
//           return Center(
//             child: SvgPicture.string(
//               _previewContent,
//               placeholderBuilder:
//                   (context) => const Center(child: CircularProgressIndicator()),
//             ),
//           );
//         } catch (e) {
//           return Center(
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 const Icon(Icons.error_outline, color: Colors.red, size: 60),
//                 const SizedBox(height: 16),
//                 Text(
//                   'SVG 渲染错误: $e',
//                   style: const TextStyle(color: Colors.red),
//                   textAlign: TextAlign.center,
//                 ),
//               ],
//             ),
//           );
//         }
//
//       default:
//         return const Center(child: Text('不支持的内容类型'));
//     }
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/content/content_editor_page.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/content/content_home_page.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:math';
// import 'dart:ui';
//
// import 'package:file_picker/file_picker.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
//
// import '../config/app_theme.dart';
// import '../models/content_item.dart';
// import '../services/content_service.dart';
// import 'content_detail_page.dart';
// import 'content_editor_page.dart';
// import 'content_item_card.dart';
// import 'save_content_page.dart';
//
// class ContentHomePage extends StatefulWidget {
//   const ContentHomePage({super.key});
//
//   @override
//   State<ContentHomePage> createState() => _ContentHomePageState();
// }
//
// class _ContentHomePageState extends State<ContentHomePage>
//     with TickerProviderStateMixin {
//   final ContentService _contentService = ContentService();
//   late TabController _tabController;
//   List<ContentItem> _allItems = [];
//   List<ContentItem> _filteredItems = [];
//   bool _isLoading = true;
//   String _searchQuery = '';
//   ContentType? _filterType;
//   String? _filterTag;
//   bool _showFavoritesOnly = false;
//   final List<String> _allTags = [];
//
//   // 动画控制器
//   late final AnimationController _animationController;
//
//   @override
//   void initState() {
//     super.initState();
//     _tabController = TabController(length: 4, vsync: this);
//     _tabController.addListener(_handleTabChange);
//     _animationController = AnimationController(
//       vsync: this,
//       duration: const Duration(seconds: 20),
//     )..repeat();
//     _initializeContent();
//   }
//
//   @override
//   void dispose() {
//     _tabController.removeListener(_handleTabChange);
//     _tabController.dispose();
//     _animationController.dispose();
//     super.dispose();
//   }
//
//   void _handleTabChange() {
//     if (_tabController.indexIsChanging) {
//       _filterByType(_tabTypeFromIndex(_tabController.index));
//     }
//   }
//
//   ContentType? _tabTypeFromIndex(int index) {
//     switch (index) {
//       case 0:
//         return null; // 全部
//       case 1:
//         return ContentType.markdown;
//       case 2:
//         return ContentType.image;
//       case 3:
//         return ContentType.html;
//       default:
//         return null;
//     }
//   }
//
//   Future<void> _initializeContent() async {
//     setState(() {
//       _isLoading = true;
//     });
//
//     await _contentService.initialize();
//     _loadContent();
//
//     setState(() {
//       _isLoading = false;
//     });
//   }
//
//   void _loadContent() {
//     _allItems = _contentService.getAllItems();
//     _allTags.clear();
//     _allTags.addAll(_contentService.getAllTags());
//     _filterItems();
//   }
//
//   void _filterItems() {
//     List<ContentItem> items = _allItems;
//
//     // 按类型筛选
//     if (_filterType != null) {
//       if (_filterType == ContentType.image) {
//         // 图片类型包含image和svg
//         items =
//             items
//                 .where(
//                   (item) =>
//                       item.type == ContentType.image ||
//                       item.type == ContentType.svg,
//                 )
//                 .toList();
//       } else {
//         items = items.where((item) => item.type == _filterType).toList();
//       }
//     }
//
//     // 按标签筛选
//     if (_filterTag != null && _filterTag!.isNotEmpty) {
//       items = items.where((item) => item.tags.contains(_filterTag)).toList();
//     }
//
//     // 按收藏筛选
//     if (_showFavoritesOnly) {
//       items = items.where((item) => item.isFavorite).toList();
//     }
//
//     // 搜索筛选
//     if (_searchQuery.isNotEmpty) {
//       final query = _searchQuery.toLowerCase();
//       items =
//           items.where((item) {
//             return item.title.toLowerCase().contains(query) ||
//                 (item.tags.any((tag) => tag.toLowerCase().contains(query)));
//           }).toList();
//     }
//
//     setState(() {
//       _filteredItems = items;
//     });
//   }
//
//   void _filterByType(ContentType? type) {
//     setState(() {
//       _filterType = type;
//     });
//     _filterItems();
//   }
//
//   void _filterByTag(String? tag) {
//     setState(() {
//       _filterTag = tag;
//     });
//     _filterItems();
//   }
//
//   void _toggleFavoriteFilter() {
//     setState(() {
//       _showFavoritesOnly = !_showFavoritesOnly;
//     });
//     _filterItems();
//   }
//
//   void _handleSearch(String query) {
//     setState(() {
//       _searchQuery = query;
//     });
//     _filterItems();
//   }
//
//   Future<void> _refreshContent() async {
//     await _initializeContent();
//   }
//
//   void _viewContentItem(ContentItem item) {
//     Navigator.push(
//       context,
//       MaterialPageRoute(
//         builder: (context) => ContentDetailPage(contentItem: item),
//       ),
//     ).then((_) => _refreshContent());
//   }
//
//   void _showCreateContentDialog() {
//     showModalBottomSheet(
//       context: context,
//       shape: const RoundedRectangleBorder(
//         borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
//       ),
//       builder:
//           (context) => Padding(
//             padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
//             child: Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 const Text(
//                   '新建内容',
//                   style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
//                 ),
//                 const SizedBox(height: 24),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceAround,
//                   children: [
//                     _buildCreateOption(
//                       context,
//                       icon: Icons.text_fields,
//                       label: 'Markdown',
//                       color: Colors.blue,
//                       onTap: () => _createMarkdown(context),
//                     ),
//                     _buildCreateOption(
//                       context,
//                       icon: Icons.code,
//                       label: 'HTML',
//                       color: Colors.orange,
//                       onTap: () => _createHtml(context),
//                     ),
//                     _buildCreateOption(
//                       context,
//                       icon: Icons.architecture,
//                       label: 'SVG',
//                       color: Colors.green,
//                       onTap: () => _createSvg(context),
//                     ),
//                     _buildCreateOption(
//                       context,
//                       icon: Icons.image,
//                       label: '图片',
//                       color: Colors.purple,
//                       onTap: () => _importImage(context),
//                     ),
//                   ],
//                 ),
//                 const SizedBox(height: 16),
//               ],
//             ),
//           ),
//     );
//   }
//
//   Widget _buildCreateOption(
//     BuildContext context, {
//     required IconData icon,
//     required String label,
//     required Color color,
//     required VoidCallback onTap,
//   }) {
//     return InkWell(
//       onTap: onTap,
//       borderRadius: BorderRadius.circular(12),
//       child: Container(
//         width: 70,
//         padding: const EdgeInsets.all(8),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Container(
//               padding: const EdgeInsets.all(12),
//               decoration: BoxDecoration(
//                 color: color.withValues(alpha: 0.1),
//                 borderRadius: BorderRadius.circular(12),
//               ),
//               child: Icon(icon, color: color, size: 28),
//             ),
//             const SizedBox(height: 8),
//             Text(
//               label,
//               style: const TextStyle(fontSize: 12),
//               textAlign: TextAlign.center,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Future<void> _createMarkdown(BuildContext context) async {
//     Navigator.pop(context); // 关闭底部菜单
//
//     final result = await Navigator.push<String>(
//       context,
//       MaterialPageRoute(
//         builder:
//             (context) => const ContentEditorPage(
//               title: '新建 Markdown',
//               initialContent: '# 新的 Markdown 文档\n\n在这里输入内容...',
//               contentType: ContentType.markdown,
//             ),
//       ),
//     );
//
//     if (result != null) {
//       await _saveNewContent(
//         initialTitle: '新建 Markdown 文档',
//         content: result,
//         contentType: ContentType.markdown,
//       );
//     }
//   }
//
//   Future<void> _createHtml(BuildContext context) async {
//     Navigator.pop(context);
//
//     final result = await Navigator.push<String>(
//       context,
//       MaterialPageRoute(
//         builder:
//             (context) => const ContentEditorPage(
//               title: '新建 HTML',
//               initialContent:
//                   '<!DOCTYPE html>\n<html>\n<head>\n  <title>新页面</title>\n</head>\n<body>\n  <h1>Hello World</h1>\n  <p>在这里输入内容...</p>\n</body>\n</html>',
//               contentType: ContentType.html,
//             ),
//       ),
//     );
//
//     if (result != null) {
//       await _saveNewContent(
//         initialTitle: '新建 HTML 文档',
//         content: result,
//         contentType: ContentType.html,
//       );
//     }
//   }
//
//   Future<void> _createSvg(BuildContext context) async {
//     Navigator.pop(context);
//
//     final result = await Navigator.push<String>(
//       context,
//       MaterialPageRoute(
//         builder:
//             (context) => const ContentEditorPage(
//               title: '新建 SVG',
//               initialContent:
//                   '<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">\n  <circle cx="100" cy="100" r="80" fill="#6366F1" />\n  <rect x="60" y="60" width="80" height="80" fill="#60A5FA" />\n</svg>',
//               contentType: ContentType.svg,
//             ),
//       ),
//     );
//
//     if (result != null) {
//       await _saveNewContent(
//         initialTitle: '新建 SVG 图像',
//         content: result,
//         contentType: ContentType.svg,
//       );
//     }
//   }
//
//   Future<void> _importImage(BuildContext context) async {
//     Navigator.pop(context);
//
//     try {
//       final result = await FilePicker.platform.pickFiles(
//         type: FileType.image,
//         allowMultiple: false,
//       );
//
//       if (result != null && result.files.isNotEmpty) {
//         final file = result.files.first;
//
//         if (file.path != null) {
//           final path = file.path!;
//           final fileName = file.name;
//
//           if (path.toLowerCase().endsWith('.svg')) {
//             // SVG文件
//             await _saveNewContent(
//               initialTitle: fileName,
//               content: path,
//               contentType: ContentType.svg,
//             );
//           } else {
//             // 其他图片
//             await _saveNewContent(
//               initialTitle: fileName,
//               content: path,
//               contentType: ContentType.image,
//             );
//           }
//         }
//       }
//     } catch (e) {
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text('导入图片失败: $e'), backgroundColor: Colors.red),
//         );
//       }
//     }
//   }
//
//   Future<void> _saveNewContent({
//     required String initialTitle,
//     required dynamic content,
//     required ContentType contentType,
//   }) async {
//     if (!mounted) return;
//
//     final result = await Navigator.push<ContentItem>(
//       context,
//       MaterialPageRoute(
//         builder:
//             (context) => SaveContentPage(
//               initialTitle: initialTitle,
//               content: content,
//               contentType: contentType,
//             ),
//       ),
//     );
//
//     if (result != null) {
//       await _refreshContent();
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: NestedScrollView(
//         headerSliverBuilder: (context, innerBoxIsScrolled) {
//           return [
//             SliverAppBar(
//               expandedHeight: 200.0,
//               floating: false,
//               pinned: true,
//               stretch: true,
//               centerTitle: false,
//               systemOverlayStyle: SystemUiOverlayStyle.light,
//               backgroundColor: AppTheme.primaryColor,
//               leading: IconButton(
//                 icon: const Icon(Icons.arrow_back, color: Colors.white),
//                 onPressed: () => Navigator.of(context).pop(),
//               ),
//               flexibleSpace: FlexibleSpaceBar(
//                 title: Text(
//                   innerBoxIsScrolled ? '内容库' : '',
//                   style: const TextStyle(color: Colors.white),
//                 ),
//                 background: Stack(
//                   fit: StackFit.expand,
//                   children: [
//                     // 粒子背景
//                     AnimatedBuilder(
//                       animation: _animationController,
//                       builder: (context, child) {
//                         return CustomPaint(
//                           painter: ParticleBackgroundPainter(
//                             _animationController.value,
//                           ),
//                         );
//                       },
//                     ),
//                     // 高斯模糊
//                     ClipRect(
//                       child: BackdropFilter(
//                         filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
//                         child: Container(
//                           color: AppTheme.primaryColor.withValues(alpha: 0.4),
//                           alignment: Alignment.center,
//                         ),
//                       ),
//                     ),
//                     // 标题和搜索区域
//                     Padding(
//                       padding: const EdgeInsets.fromLTRB(16, 50, 16, 0),
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           const Text(
//                             '我的内容库',
//                             style: TextStyle(
//                               color: Colors.white,
//                               fontSize: 30,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                           const SizedBox(height: 16),
//                           Container(
//                             height: 50,
//                             decoration: BoxDecoration(
//                               color: Colors.white.withValues(alpha: 0.15),
//                               borderRadius: BorderRadius.circular(25),
//                             ),
//                             child: TextField(
//                               onChanged: _handleSearch,
//                               style: const TextStyle(color: Colors.white),
//                               decoration: InputDecoration(
//                                 hintText: '搜索内容...',
//                                 hintStyle: TextStyle(
//                                   color: Colors.white.withValues(alpha: 0.7),
//                                 ),
//                                 prefixIcon: const Icon(
//                                   Icons.search,
//                                   color: Colors.white,
//                                 ),
//                                 border: InputBorder.none,
//                                 contentPadding: const EdgeInsets.symmetric(
//                                   horizontal: 20,
//                                   vertical: 15,
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               bottom: TabBar(
//                 controller: _tabController,
//                 labelColor: Colors.white,
//                 unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
//                 indicatorColor: Colors.white,
//                 indicatorWeight: 3,
//                 tabs: const [
//                   Tab(text: '全部'),
//                   Tab(text: 'Markdown'),
//                   Tab(text: '图片'),
//                   Tab(text: 'HTML'),
//                 ],
//               ),
//               actions: [
//                 IconButton(
//                   icon: Icon(
//                     _showFavoritesOnly ? Icons.favorite : Icons.favorite_border,
//                     color: Colors.white,
//                   ),
//                   onPressed: _toggleFavoriteFilter,
//                 ),
//                 PopupMenuButton<String?>(
//                   icon: const Icon(Icons.filter_list, color: Colors.white),
//                   onSelected: _filterByTag,
//                   itemBuilder: (context) {
//                     return [
//                       const PopupMenuItem<String?>(
//                         value: null,
//                         child: Text('全部标签'),
//                       ),
//                       ..._allTags.map(
//                         (tag) => PopupMenuItem<String?>(
//                           value: tag,
//                           child: Text(tag),
//                         ),
//                       ),
//                     ];
//                   },
//                 ),
//               ],
//             ),
//           ];
//         },
//         body:
//             _isLoading
//                 ? const Center(child: CircularProgressIndicator())
//                 : _filteredItems.isEmpty
//                 ? _buildEmptyView()
//                 : RefreshIndicator(
//                   onRefresh: _refreshContent,
//                   child: Padding(
//                     padding: const EdgeInsets.all(12.0),
//                     child: StaggeredContentGrid(
//                       items: _filteredItems,
//                       onItemTap: _viewContentItem,
//                     ),
//                   ),
//                 ),
//       ),
//       floatingActionButton: FloatingActionButton(
//         heroTag: 'content_home_page_fab',
//         backgroundColor: AppTheme.primaryColor,
//         child: const Icon(Icons.add, color: Colors.white),
//         onPressed: () {
//           _showCreateContentDialog();
//         },
//       ),
//     );
//   }
//
//   Widget _buildEmptyView() {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           Icon(Icons.inbox_outlined, size: 80, color: Colors.grey.shade400),
//           const SizedBox(height: 16),
//           Text(
//             _searchQuery.isEmpty ? '还没有保存任何内容' : '找不到匹配的内容',
//             style: TextStyle(
//               fontSize: 18,
//               color: Colors.grey.shade600,
//               fontWeight: FontWeight.w500,
//             ),
//           ),
//           const SizedBox(height: 8),
//           Text(
//             _searchQuery.isEmpty
//                 ? '使用各种工具创建内容后，可以将其保存在这里'
//                 : '尝试使用其他搜索关键词或清除筛选条件',
//             style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
//             textAlign: TextAlign.center,
//           ),
//         ],
//       ),
//     );
//   }
// }
//
// class StaggeredContentGrid extends StatelessWidget {
//   final List<ContentItem> items;
//   final Function(ContentItem) onItemTap;
//
//   const StaggeredContentGrid({
//     super.key,
//     required this.items,
//     required this.onItemTap,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     // 根据屏幕宽度决定网格列数
//     final width = MediaQuery.of(context).size.width;
//     final crossAxisCount = width > 700 ? 3 : 2;
//
//     return GridView.builder(
//       gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//         crossAxisCount: crossAxisCount,
//         childAspectRatio: 0.85,
//         crossAxisSpacing: 12,
//         mainAxisSpacing: 12,
//       ),
//       itemCount: items.length,
//       itemBuilder: (context, index) {
//         final item = items[index];
//         return ContentItemCard(contentItem: item, onTap: () => onItemTap(item));
//       },
//     );
//   }
// }
//
// class ParticleBackgroundPainter extends CustomPainter {
//   final double animationValue;
//   final List<Particle> particles = List.generate(
//     30,
//     (index) => Particle(
//       position: Offset(
//         Random().nextDouble() * 400,
//         Random().nextDouble() * 400,
//       ),
//       radius: Random().nextDouble() * 10 + 5,
//       color: Colors.white.withValues(alpha: Random().nextDouble() * 0.2 + 0.1),
//       speed: Random().nextDouble() * 2 + 0.5,
//     ),
//   );
//
//   ParticleBackgroundPainter(this.animationValue);
//
//   @override
//   void paint(Canvas canvas, Size size) {
//     final gradient = LinearGradient(
//       begin: Alignment.topLeft,
//       end: Alignment.bottomRight,
//       colors: [
//         AppTheme.primaryColor,
//         AppTheme.primaryLightColor,
//         AppTheme.secondaryColor,
//       ],
//     ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
//
//     final paint = Paint()..shader = gradient;
//     canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);
//
//     for (var particle in particles) {
//       final particlePaint =
//           Paint()
//             ..color = particle.color
//             ..style = PaintingStyle.fill;
//
//       // 使用动画值移动粒子
//       final offsetX =
//           (particle.position.dx + particle.speed * animationValue * 50) %
//           size.width;
//       final offsetY =
//           (particle.position.dy + particle.speed * animationValue * 30) %
//           size.height;
//
//       canvas.drawCircle(
//         Offset(offsetX, offsetY),
//         particle.radius,
//         particlePaint,
//       );
//     }
//   }
//
//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) {
//     return true;
//   }
// }
//
// class Particle {
//   Offset position;
//   double radius;
//   Color color;
//   double speed;
//
//   Particle({
//     required this.position,
//     required this.radius,
//     required this.color,
//     required this.speed,
//   });
// }
//
// // ===================================================================
// // 文件结束: lib/content/content_home_page.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/content/content_item_card.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:io';
//
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_markdown/flutter_markdown.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:intl/intl.dart';
// import 'package:webview_flutter/webview_flutter.dart';
//
// import '../config/app_theme.dart';
// import '../models/content_item.dart';
//
// class ContentItemCard extends StatefulWidget {
//   final ContentItem contentItem;
//   final VoidCallback onTap;
//
//   const ContentItemCard({
//     super.key,
//     required this.contentItem,
//     required this.onTap,
//   });
//
//   @override
//   State<ContentItemCard> createState() => _ContentItemCardState();
// }
//
// class _ContentItemCardState extends State<ContentItemCard> {
//   WebViewController? _webViewController;
//   bool _isWebViewLoaded = false;
//
//   @override
//   void initState() {
//     super.initState();
//
//     // 仅为HTML类型初始化WebView
//     if (widget.contentItem.type == ContentType.html) {
//       _initWebView();
//     }
//   }
//
//   @override
//   void dispose() {
//     _webViewController = null;
//     super.dispose();
//   }
//
//   void _initWebView() {
//     _webViewController =
//         WebViewController()
//           ..setJavaScriptMode(JavaScriptMode.unrestricted)
//           ..setBackgroundColor(Colors.white)
//           ..setNavigationDelegate(
//             NavigationDelegate(
//               onPageFinished: (String url) {
//                 if (mounted) {
//                   setState(() {
//                     _isWebViewLoaded = true;
//                   });
//                 }
//               },
//             ),
//           )
//           ..loadHtmlString(widget.contentItem.content.toString());
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: widget.onTap,
//       child: Container(
//         margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
//         decoration: BoxDecoration(
//           color: Theme.of(context).cardColor,
//           borderRadius: BorderRadius.circular(20),
//           boxShadow: [
//             BoxShadow(
//               color: _getTypeColor().withValues(alpha: 0.08),
//               blurRadius: 16,
//               offset: const Offset(0, 6),
//               spreadRadius: -2,
//             ),
//             BoxShadow(
//               color: Colors.black.withValues(alpha: 0.04),
//               blurRadius: 8,
//               offset: const Offset(0, 2),
//             ),
//           ],
//         ),
//         clipBehavior: Clip.antiAlias,
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [_buildContentPreview(context), _buildCardFooter(context)],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildContentPreview(BuildContext context) {
//     return Expanded(
//       child: Container(
//         width: double.infinity,
//         padding: const EdgeInsets.all(14),
//         decoration: BoxDecoration(gradient: _getGradientByType()),
//         child: _buildPreviewContent(context),
//       ),
//     );
//   }
//
//   Widget _buildPreviewContent(BuildContext context) {
//     switch (widget.contentItem.type) {
//       case ContentType.markdown:
//         return Container(
//           decoration: BoxDecoration(
//             color: Colors.white,
//             borderRadius: BorderRadius.circular(16),
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withValues(alpha: 0.03),
//                 blurRadius: 12,
//                 offset: const Offset(0, 4),
//                 spreadRadius: -4,
//               ),
//             ],
//           ),
//           padding: const EdgeInsets.all(16),
//           child: Stack(
//             children: [
//               SingleChildScrollView(
//                 physics: const NeverScrollableScrollPhysics(),
//                 child: MarkdownBody(
//                   data:
//                       widget.contentItem.content.toString().length > 300
//                           ? '${widget.contentItem.content.toString().substring(0, 300)}...'
//                           : widget.contentItem.content.toString(),
//                   styleSheet: MarkdownStyleSheet(
//                     p: TextStyle(
//                       fontSize: 13,
//                       height: 1.6,
//                       color: Colors.grey.shade800,
//                     ),
//                     h1: TextStyle(
//                       fontSize: 18,
//                       fontWeight: FontWeight.w700,
//                       color: Colors.grey.shade900,
//                       height: 1.3,
//                     ),
//                     h2: TextStyle(
//                       fontSize: 16,
//                       fontWeight: FontWeight.w600,
//                       color: Colors.grey.shade900,
//                       height: 1.4,
//                     ),
//                     h3: TextStyle(
//                       fontSize: 14,
//                       fontWeight: FontWeight.w600,
//                       color: Colors.grey.shade900,
//                       height: 1.5,
//                     ),
//                     code: TextStyle(
//                       fontSize: 12,
//                       backgroundColor: _getTypeColor().withValues(alpha: 0.08),
//                       color: _getTypeColor(),
//                       fontFamily: 'monospace',
//                     ),
//                     blockquoteDecoration: BoxDecoration(
//                       color: Colors.grey.shade50,
//                       borderRadius: BorderRadius.circular(4),
//                       border: Border(
//                         left: BorderSide(
//                           color: _getTypeColor().withValues(alpha: 0.4),
//                           width: 3,
//                         ),
//                       ),
//                     ),
//                     blockquote: TextStyle(
//                       fontSize: 13,
//                       fontStyle: FontStyle.italic,
//                       color: Colors.grey.shade700,
//                       height: 1.6,
//                     ),
//                     listBullet: TextStyle(
//                       fontSize: 13,
//                       color: _getTypeColor(),
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                 ),
//               ),
//               // 渐变遮罩
//               Positioned(
//                 bottom: 0,
//                 left: 0,
//                 right: 0,
//                 height: 60,
//                 child: Container(
//                   decoration: BoxDecoration(
//                     gradient: LinearGradient(
//                       begin: Alignment.topCenter,
//                       end: Alignment.bottomCenter,
//                       colors: [
//                         Colors.white.withValues(alpha: 0),
//                         Colors.white.withValues(alpha: 0.9),
//                         Colors.white,
//                       ],
//                       stops: const [0.1, 0.7, 1.0],
//                     ),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         );
//
//       case ContentType.image:
//         return Container(
//           decoration: BoxDecoration(
//             color: Colors.white,
//             borderRadius: BorderRadius.circular(16),
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withValues(alpha: 0.03),
//                 blurRadius: 12,
//                 offset: const Offset(0, 4),
//                 spreadRadius: -4,
//               ),
//             ],
//           ),
//           child: ClipRRect(
//             borderRadius: BorderRadius.circular(16),
//             child: _buildImage(),
//           ),
//         );
//
//       case ContentType.svg:
//         return Container(
//           decoration: BoxDecoration(
//             color: Colors.white,
//             borderRadius: BorderRadius.circular(16),
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withValues(alpha: 0.03),
//                 blurRadius: 12,
//                 offset: const Offset(0, 4),
//                 spreadRadius: -4,
//               ),
//             ],
//           ),
//           padding: const EdgeInsets.all(16),
//           child: _buildSvg(),
//         );
//
//       case ContentType.html:
//         return Container(
//           decoration: BoxDecoration(
//             color: Colors.white,
//             borderRadius: BorderRadius.circular(16),
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withValues(alpha: 0.03),
//                 blurRadius: 12,
//                 offset: const Offset(0, 4),
//                 spreadRadius: -4,
//               ),
//             ],
//           ),
//           clipBehavior: Clip.antiAlias,
//           child: Stack(
//             children: [
//               _buildHtmlPreview(),
//               // 顶部标题标识
//               Positioned(
//                 top: 0,
//                 left: 0,
//                 right: 0,
//                 child: Container(
//                   padding: const EdgeInsets.symmetric(
//                     horizontal: 16,
//                     vertical: 8,
//                   ),
//                   decoration: BoxDecoration(
//                     gradient: LinearGradient(
//                       begin: Alignment.topCenter,
//                       end: Alignment.bottomCenter,
//                       colors: [
//                         _getTypeColor().withValues(alpha: 0.2),
//                         _getTypeColor().withValues(alpha: 0.05),
//                         Colors.transparent,
//                       ],
//                     ),
//                   ),
//                   child: Row(
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       Icon(Icons.web, color: _getTypeColor(), size: 16),
//                       const SizedBox(width: 8),
//                       Text(
//                         '网页内容',
//                         style: TextStyle(
//                           fontSize: 13,
//                           fontWeight: FontWeight.w600,
//                           color: _getTypeColor(),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         );
//     }
//   }
//
//   Widget _buildHtmlPreview() {
//     if (_webViewController == null) {
//       return _buildHtmlPlaceholder();
//     }
//
//     return Stack(
//       children: [
//         SizedBox(
//           width: double.infinity,
//           height: double.infinity,
//           child: WebViewWidget(controller: _webViewController!),
//         ),
//         // 加载指示器，在WebView加载完成前显示
//         if (!_isWebViewLoaded)
//           Container(
//             color: Colors.white,
//             child: Center(
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   CircularProgressIndicator(color: _getTypeColor()),
//                   const SizedBox(height: 8),
//                   Text(
//                     '正在渲染HTML...',
//                     style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//       ],
//     );
//   }
//
//   Widget _buildHtmlPlaceholder() {
//     // 获取HTML的片段摘要，用于占位符显示
//     final htmlContent = widget.contentItem.content.toString();
//     String htmlPreview = '';
//
//     // 尝试提取HTML正文或者片段
//     final bodyMatch = RegExp(
//       r'<body[^>]*>(.*?)<\/body>',
//       dotAll: true,
//     ).firstMatch(htmlContent);
//     if (bodyMatch != null && bodyMatch.group(1) != null) {
//       // 移除HTML标签，只保留文本
//       final bodyText = bodyMatch.group(1)!.replaceAll(RegExp(r'<[^>]*>'), ' ');
//       htmlPreview = bodyText.replaceAll(RegExp(r'\s+'), ' ').trim();
//     } else {
//       // 如果没有找到body标签，直接移除所有HTML标签
//       htmlPreview = htmlContent.replaceAll(RegExp(r'<[^>]*>'), ' ');
//       htmlPreview = htmlPreview.replaceAll(RegExp(r'\s+'), ' ').trim();
//     }
//
//     // 限制长度
//     if (htmlPreview.length > 100) {
//       htmlPreview = '${htmlPreview.substring(0, 100)}...';
//     }
//
//     return Center(
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Icon(Icons.web, size: 32, color: _getTypeColor().withValues(alpha: 0.5)),
//             const SizedBox(height: 8),
//             Text(
//               htmlPreview.isEmpty ? '点击查看网页内容' : htmlPreview,
//               style: TextStyle(
//                 fontSize: 12,
//                 color: Colors.grey.shade600,
//                 height: 1.5,
//               ),
//               textAlign: TextAlign.center,
//               maxLines: 4,
//               overflow: TextOverflow.ellipsis,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildImage() {
//     final content = widget.contentItem.content;
//
//     if (content is String && content.startsWith('http')) {
//       // 网络图片
//       return CachedNetworkImage(
//         imageUrl: content,
//         fit: BoxFit.cover,
//         placeholder:
//             (context, url) => const Center(child: CircularProgressIndicator()),
//         errorWidget:
//             (context, url, error) =>
//                 const Icon(Icons.error_outline, color: Colors.red),
//       );
//     } else if (content is String &&
//         (content.startsWith('/') || content.contains('content_files'))) {
//       // 本地图片
//       return Image.file(
//         File(content),
//         fit: BoxFit.cover,
//         errorBuilder:
//             (context, error, stackTrace) =>
//                 const Icon(Icons.error_outline, color: Colors.red),
//       );
//     } else {
//       // 未知图片类型
//       return const Center(
//         child: Icon(
//           Icons.image_not_supported_outlined,
//           color: Colors.grey,
//           size: 40,
//         ),
//       );
//     }
//   }
//
//   Widget _buildSvg() {
//     final content = widget.contentItem.content;
//
//     if (content is String && content.startsWith('http')) {
//       // 网络SVG
//       return SvgPicture.network(
//         content,
//         placeholderBuilder:
//             (context) => const Center(child: CircularProgressIndicator()),
//       );
//     } else if (content is String &&
//         (content.startsWith('/') || content.contains('content_files'))) {
//       // 本地SVG
//       return SvgPicture.file(
//         File(content),
//         placeholderBuilder:
//             (context) => const Center(child: CircularProgressIndicator()),
//       );
//     } else if (content is String && content.startsWith('<svg')) {
//       // SVG字符串
//       return SvgPicture.string(
//         content,
//         placeholderBuilder:
//             (context) => const Center(child: CircularProgressIndicator()),
//       );
//     } else {
//       // 未知SVG类型
//       return const Center(
//         child: Icon(
//           Icons.image_not_supported_outlined,
//           color: Colors.grey,
//           size: 40,
//         ),
//       );
//     }
//   }
//
//   Widget _buildCardFooter(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
//       decoration: BoxDecoration(
//         color: Theme.of(context).cardColor,
//         border: Border(
//           top: BorderSide(color: Colors.grey.withValues(alpha: 0.1), width: 1),
//         ),
//       ),
//       child: Row(
//         children: [
//           // 标题和信息
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   widget.contentItem.title,
//                   style: const TextStyle(
//                     fontSize: 15,
//                     fontWeight: FontWeight.w600,
//                     color: Color(0xFF2D3142),
//                   ),
//                   maxLines: 1,
//                   overflow: TextOverflow.ellipsis,
//                 ),
//                 const SizedBox(height: 3),
//                 Text(
//                   '${DateFormat('yyyy-MM-dd').format(widget.contentItem.updatedAt)} · ${widget.contentItem.formattedSize}',
//                   style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
//                 ),
//               ],
//             ),
//           ),
//           // 收藏图标
//           if (widget.contentItem.isFavorite)
//             Container(
//               padding: const EdgeInsets.all(6),
//               decoration: BoxDecoration(
//                 color: Colors.red.withValues(alpha: 0.1),
//                 shape: BoxShape.circle,
//               ),
//               child: const Icon(
//                 Icons.favorite_rounded,
//                 color: Colors.red,
//                 size: 16,
//               ),
//             ),
//         ],
//       ),
//     );
//   }
//
//   Color _getTypeColor() {
//     switch (widget.contentItem.type) {
//       case ContentType.markdown:
//         return Colors.blue;
//       case ContentType.image:
//         return Colors.purple;
//       case ContentType.svg:
//         return Colors.green;
//       case ContentType.html:
//         return Colors.orange;
//     }
//   }
//
//   LinearGradient _getGradientByType() {
//     switch (widget.contentItem.type) {
//       case ContentType.markdown:
//         return AppTheme.blueGradient;
//       case ContentType.image:
//         return AppTheme.purpleGradient;
//       case ContentType.svg:
//         return AppTheme.greenGradient;
//       case ContentType.html:
//         return AppTheme.orangeGradient;
//     }
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/content/content_item_card.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/content/content_save_button.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'package:flutter/material.dart';
// import '../models/content_item.dart';
// import 'save_content_page.dart';
//
// /// 内容保存按钮 - 可以添加到任何内容页面，点击后打开保存对话框
// class ContentSaveButton extends StatelessWidget {
//   /// 内容标题
//   final String title;
//
//   /// 内容
//   final dynamic content;
//
//   /// 内容类型
//   final ContentType contentType;
//
//   /// 初始标签
//   final List<String>? initialTags;
//
//   /// 保存成功的回调
//   final void Function(ContentItem savedItem)? onSaved;
//
//   const ContentSaveButton({
//     super.key,
//     required this.title,
//     required this.content,
//     required this.contentType,
//     this.initialTags,
//     this.onSaved,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return IconButton(
//       icon: const Icon(Icons.bookmark_add_outlined),
//       tooltip: '保存到内容库',
//       onPressed: () => _handleSave(context),
//     );
//   }
//
//   Future<void> _handleSave(BuildContext context) async {
//     final result = await Navigator.push<ContentItem>(
//       context,
//       MaterialPageRoute(
//         builder:
//             (context) => SaveContentPage(
//               initialTitle: title,
//               content: content,
//               contentType: contentType,
//               initialTags: initialTags,
//             ),
//       ),
//     );
//
//     if (result != null && onSaved != null) {
//       onSaved!(result);
//     }
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/content/content_save_button.dart
// // ===================================================================
//
// /*
//  * ===================================================
//  * 软件名称: ContentPal内容处理工具
//  * 版本号: V1.0.0
//  * 文件路径: lib/content/save_content_page.dart
//  * 版权所有: [请填写著作权人信息]
//  * 生成时间: 2025-06-05 12:39:16
//  * ===================================================
//  */
//
// import 'dart:convert';
// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_markdown/flutter_markdown.dart';
// import 'package:flutter_svg/flutter_svg.dart';
//
// import '../config/app_theme.dart';
// import '../models/content_item.dart';
// import '../services/content_service.dart';
// import '../services/service_locator.dart';
//
// class SaveContentPage extends StatefulWidget {
//   final String initialTitle;
//   final dynamic content;
//   final ContentType contentType;
//   final List<String>? initialTags;
//
//   const SaveContentPage({
//     super.key,
//     required this.initialTitle,
//     required this.content,
//     required this.contentType,
//     this.initialTags,
//   });
//
//   @override
//   State<SaveContentPage> createState() => _SaveContentPageState();
// }
//
// class _SaveContentPageState extends State<SaveContentPage> {
//   final _titleController = TextEditingController();
//   final _tagController = TextEditingController();
//   final _formKey = GlobalKey<FormState>();
//   final List<String> _tags = [];
//   final ContentService _contentService = ServiceLocator().contentService;
//
//   bool _isSaving = false;
//
//   @override
//   void initState() {
//     super.initState();
//     _titleController.text = widget.initialTitle;
//     if (widget.initialTags != null) {
//       _tags.addAll(widget.initialTags!);
//     }
//   }
//
//   @override
//   void dispose() {
//     _titleController.dispose();
//     _tagController.dispose();
//     super.dispose();
//   }
//
//   void _addTag() {
//     final tag = _tagController.text.trim();
//     if (tag.isNotEmpty && !_tags.contains(tag)) {
//       setState(() {
//         _tags.add(tag);
//         _tagController.clear();
//       });
//     }
//   }
//
//   void _removeTag(String tag) {
//     setState(() {
//       _tags.remove(tag);
//     });
//   }
//
//   Future<void> _saveContent() async {
//     if (!_formKey.currentState!.validate()) {
//       return;
//     }
//
//     setState(() {
//       _isSaving = true;
//     });
//
//     try {
//       late ContentItem savedItem;
//
//       // 根据内容类型进行不同的保存处理
//       switch (widget.contentType) {
//         case ContentType.markdown:
//         case ContentType.html:
//           // 文本类型内容
//           savedItem = await _contentService.createTextContent(
//             title: _titleController.text,
//             type: widget.contentType,
//             content: widget.content as String,
//             tags: _tags,
//           );
//           break;
//
//         case ContentType.image:
//         case ContentType.svg:
//           // 图像类型内容
//           if (widget.content is String && widget.content.startsWith('http')) {
//             // 网络图像，直接使用URL
//             savedItem = await _contentService.createTextContent(
//               title: _titleController.text,
//               type: widget.contentType,
//               content: widget.content as String,
//               tags: _tags,
//             );
//           } else if (widget.content is String &&
//               (File(widget.content as String).existsSync())) {
//             // 本地文件路径
//             final file = File(widget.content as String);
//             final bytes = await file.readAsBytes();
//
//             savedItem = await _contentService.createImageContent(
//               title: _titleController.text,
//               type: widget.contentType,
//               imageData: bytes,
//               tags: _tags,
//               filePath: widget.content as String,
//             );
//           } else if (widget.content is Uint8List) {
//             // 二进制数据
//             savedItem = await _contentService.createImageContent(
//               title: _titleController.text,
//               type: widget.contentType,
//               imageData: widget.content as Uint8List,
//               tags: _tags,
//             );
//           } else if (widget.content is String &&
//               (widget.content as String).startsWith('<svg')) {
//             // SVG字符串
//             final bytes = utf8.encode(widget.content as String);
//
//             savedItem = await _contentService.createImageContent(
//               title: _titleController.text,
//               type: widget.contentType,
//               imageData: Uint8List.fromList(bytes),
//               tags: _tags,
//             );
//           } else {
//             throw Exception('不支持的内容格式');
//           }
//           break;
//       }
//
//       if (mounted) {
//         Navigator.of(context).pop(savedItem);
//
//         ScaffoldMessenger.of(context).showSnackBar(
//           const SnackBar(
//             content: Text('内容保存成功'),
//             backgroundColor: Colors.green,
//           ),
//         );
//       }
//     } catch (e) {
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(content: Text('保存失败: $e'), backgroundColor: Colors.red),
//         );
//       }
//     } finally {
//       if (mounted) {
//         setState(() {
//           _isSaving = false;
//         });
//       }
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('保存内容'),
//         actions: [
//           TextButton.icon(
//             onPressed: _isSaving ? null : _saveContent,
//             icon: const Icon(Icons.save),
//             label: const Text('保存'),
//           ),
//         ],
//       ),
//       body:
//           _isSaving
//               ? const Center(child: CircularProgressIndicator())
//               : SingleChildScrollView(
//                 padding: const EdgeInsets.all(16),
//                 child: Form(
//                   key: _formKey,
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       // 标题输入框
//                       TextFormField(
//                         controller: _titleController,
//                         decoration: const InputDecoration(
//                           labelText: '标题',
//                           border: OutlineInputBorder(),
//                           prefixIcon: Icon(Icons.title),
//                         ),
//                         validator: (value) {
//                           if (value == null || value.trim().isEmpty) {
//                             return '请输入标题';
//                           }
//                           return null;
//                         },
//                       ),
//
//                       const SizedBox(height: 24),
//
//                       // 标签输入区域
//                       const Text(
//                         '标签',
//                         style: TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                       const SizedBox(height: 8),
//                       Row(
//                         children: [
//                           Expanded(
//                             child: TextField(
//                               controller: _tagController,
//                               decoration: const InputDecoration(
//                                 hintText: '添加标签...',
//                                 border: OutlineInputBorder(),
//                                 prefixIcon: Icon(Icons.label),
//                               ),
//                               onSubmitted: (_) => _addTag(),
//                             ),
//                           ),
//                           const SizedBox(width: 8),
//                           ElevatedButton(
//                             onPressed: _addTag,
//                             style: ElevatedButton.styleFrom(
//                               shape: const CircleBorder(),
//                               padding: const EdgeInsets.all(12),
//                             ),
//                             child: const Icon(Icons.add),
//                           ),
//                         ],
//                       ),
//
//                       const SizedBox(height: 16),
//
//                       // 标签显示区域
//                       Wrap(
//                         spacing: 8,
//                         runSpacing: 8,
//                         children:
//                             _tags.map((tag) {
//                               return Chip(
//                                 label: Text(tag),
//                                 deleteIcon: const Icon(Icons.close, size: 18),
//                                 onDeleted: () => _removeTag(tag),
//                                 backgroundColor: AppTheme.bgIndigo50,
//                               );
//                             }).toList(),
//                       ),
//
//                       const SizedBox(height: 24),
//
//                       // 内容预览
//                       const Text(
//                         '内容预览',
//                         style: TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                       const SizedBox(height: 8),
//                       Container(
//                         height: 300,
//                         width: double.infinity,
//                         decoration: BoxDecoration(
//                           border: Border.all(color: Colors.grey.shade300),
//                           borderRadius: BorderRadius.circular(8),
//                         ),
//                         child: _buildPreview(),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//     );
//   }
//
//   Widget _buildPreview() {
//     switch (widget.contentType) {
//       case ContentType.markdown:
//         return Padding(
//           padding: const EdgeInsets.all(16),
//           child: Container(
//             constraints: BoxConstraints(
//               maxHeight: MediaQuery.of(context).size.height * 0.3,
//             ),
//             child: ClipRRect(
//               borderRadius: BorderRadius.circular(8),
//               child: SingleChildScrollView(
//                 physics: const ClampingScrollPhysics(),
//                 child: MarkdownBody(
//                   data: widget.content as String,
//                   selectable: true,
//                 ),
//               ),
//             ),
//           ),
//         );
//
//       case ContentType.html:
//         return Center(
//           child: Text(
//             '(HTML预览)\n${(widget.content as String).length} 字符',
//             textAlign: TextAlign.center,
//           ),
//         );
//
//       case ContentType.image:
//         if (widget.content is String && widget.content.startsWith('http')) {
//           return Image.network(
//             widget.content as String,
//             fit: BoxFit.contain,
//             loadingBuilder: (context, child, loadingProgress) {
//               if (loadingProgress == null) return child;
//               return Center(
//                 child: CircularProgressIndicator(
//                   value:
//                       loadingProgress.expectedTotalBytes != null
//                           ? loadingProgress.cumulativeBytesLoaded /
//                               loadingProgress.expectedTotalBytes!
//                           : null,
//                 ),
//               );
//             },
//           );
//         } else if (widget.content is String &&
//             File(widget.content as String).existsSync()) {
//           return Image.file(
//             File(widget.content as String),
//             fit: BoxFit.contain,
//           );
//         } else if (widget.content is Uint8List) {
//           return Image.memory(widget.content as Uint8List, fit: BoxFit.contain);
//         } else {
//           return const Center(
//             child: Icon(Icons.broken_image, size: 60, color: Colors.grey),
//           );
//         }
//
//       case ContentType.svg:
//         try {
//           if (widget.content is String && widget.content.startsWith('http')) {
//             return SvgPicture.network(
//               widget.content as String,
//               fit: BoxFit.contain,
//             );
//           } else if (widget.content is String &&
//               File(widget.content as String).existsSync()) {
//             return SvgPicture.file(
//               File(widget.content as String),
//               fit: BoxFit.contain,
//             );
//           } else if (widget.content is String &&
//               (widget.content as String).startsWith('<svg')) {
//             return SvgPicture.string(
//               widget.content as String,
//               fit: BoxFit.contain,
//             );
//           } else {
//             return const Center(
//               child: Icon(Icons.broken_image, size: 60, color: Colors.grey),
//             );
//           }
//         } catch (e) {
//           return Center(
//             child: Text(
//               'SVG 渲染错误: $e',
//               style: const TextStyle(color: Colors.red),
//               textAlign: TextAlign.center,
//             ),
//           );
//         }
//     }
//   }
// }
//
// // ===================================================================
// // 文件结束: lib/content/save_content_page.dart
// // ===================================================================
//
