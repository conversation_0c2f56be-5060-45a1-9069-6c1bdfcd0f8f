# ContentPal软件著作权申请材料清单

## 申请概述
- **申请软件**: ContentPal内容处理工具
- **版本号**: V1.0.0
- **申请类型**: 软件著作权登记
- **申请机构**: 中国版权保护中心

## 必需材料清单

### 1. 软件著作权登记申请表
- **状态**: ⏳ 待填写
- **说明**: 需要在版权保护中心官网在线填写
- **注意事项**: 
  - 所有信息必须与其他材料保持一致
  - 软件名称：ContentPal内容处理工具
  - 版本号：V1.0.0
  - 完成日期：[需要填写实际完成日期]

### 2. 程序鉴别材料
- **状态**: ⏳ 待制作
- **文件名**: `程序鉴别材料.pdf`
- **要求**: 
  - 格式：PDF文档
  - 页数：60页（前30页 + 后30页）
  - 字体：小四号宋体或等宽字体
  - 页眉：软件名称和版本号
  - 页脚：权利人信息

**制作步骤**:
1. 使用提供的脚本 `制作程序鉴别材料脚本.md`
2. 选择代表性源代码文件
3. 格式化并添加版权信息
4. 转换为PDF格式
5. 确保总页数为60页

### 3. 文档鉴别材料
- **状态**: ✅ 已准备
- **文件名**: `ContentPal软件设计说明书.pdf`
- **要求**:
  - 格式：PDF文档
  - 页数：不少于30页
  - 字体：宋体、小四号
  - 内容：详细的软件设计说明书

**包含内容**:
- 软件功能概述
- 系统架构设计
- 功能模块设计
- 用户界面设计
- 数据存储设计
- 技术实现方案

### 4. 身份证明文件
- **个人申请**: 身份证复印件
- **企业申请**: 营业执照副本复印件
- **要求**: 加盖公章（企业）或签字确认（个人）

### 5. 其他可能需要的材料

#### 5.1 委托书（如委托代理）
- **状态**: ⏳ 待填写
- **说明**: 如果委托代理机构办理，需要提供委托书

#### 5.2 合作开发协议（如适用）
- **状态**: ❌ 不适用
- **说明**: 如果是合作开发的软件，需要提供合作协议

#### 5.3 软件使用说明书（可选）
- **状态**: ⏳ 可选制作
- **说明**: 虽不是必需材料，但可以作为补充材料

## 技术信息汇总

### 软件基本信息
| 项目 | 内容 |
|------|------|
| 软件全称 | ContentPal内容处理工具 |
| 软件简称 | ContentPal |
| 版本号 | V1.0.0 |
| 首次发表日期 | [待填写] |
| 开发完成日期 | [待填写] |

### 技术环境信息
| 项目 | 内容 |
|------|------|
| 开发硬件环境 | macOS系统（Mac电脑，Apple M系列或Intel处理器），8GB内存，256GB SSD |
| 运行硬件环境 | 移动设备（iOS 12.0+/Android 6.0+），桌面设备（Windows 10+/macOS 10.14+/Linux） |
| 开发操作系统 | macOS 14.5.0 (Darwin 24.5.0) |
| 开发环境/工具 | Flutter 3.7.2+, Dart语言, Android Studio/VS Code/Cursor IDE |
| 运行平台/操作系统 | iOS, Android, Windows, macOS, Linux, Web |
| 运行支撑环境 | Flutter Engine, Dart Runtime, Skia图形引擎 |
| 编程语言 | Dart（主要），Swift/Objective-C（iOS），Kotlin/Java（Android） |
| 源程序量 | 96个文件，33,749行代码 |

### 功能特点信息
| 项目 | 内容 |
|------|------|
| 开发目的 | 开发跨平台内容处理工具，提供Markdown、SVG、HTML、PDF等格式的编辑和处理功能 |
| 面向领域/行业 | 教育、内容创作、软件开发、设计、企业办公、个人用户 |
| 主要功能 | Markdown文档处理、SVG图像处理、HTML内容处理、PDF文档处理、语音功能、文件管理 |
| 技术特点 | 跨平台兼容、实时渲染技术、高性能文档处理、现代化UI设计、数据安全性、插件化架构 |

## 申请流程

### 第一阶段：材料准备（预计1-2周）
1. **完善基本信息**
   - [ ] 确定著作权人信息
   - [ ] 确定软件完成日期
   - [ ] 确定首次发表日期

2. **制作程序鉴别材料**
   - [ ] 运行生成脚本
   - [ ] 格式化源代码
   - [ ] 制作PDF文档
   - [ ] 质量检查

3. **完善文档鉴别材料**
   - [ ] 审查软件设计说明书
   - [ ] 添加版权信息
   - [ ] 转换为PDF格式

4. **准备身份证明**
   - [ ] 复印身份证/营业执照
   - [ ] 签字或盖章确认

### 第二阶段：在线申请（预计1-2天）
1. **登录版权保护中心官网**
   - 网址：https://www.ccopyright.com.cn
   - 注册账户并登录

2. **填写申请表**
   - [ ] 软件基本信息
   - [ ] 技术环境信息
   - [ ] 功能描述信息
   - [ ] 权利人信息

3. **上传材料**
   - [ ] 程序鉴别材料PDF
   - [ ] 文档鉴别材料PDF
   - [ ] 身份证明文件

4. **提交申请**
   - [ ] 检查所有信息
   - [ ] 在线支付费用
   - [ ] 提交申请

### 第三阶段：审查等待（预计30-60个工作日）
1. **等待初步审查**
   - 版权中心进行形式审查
   - 如有问题会通知补正

2. **补正材料（如需要）**
   - 根据通知要求补充材料
   - 在规定时间内提交

3. **等待最终审查**
   - 版权中心进行实质审查
   - 确认无问题后颁发证书

## 费用说明

### 官方费用
- **登记费**: 300元（个人）/ 450元（企业）
- **证书费**: 50元
- **加急费**: 320元（可选）

### 可能的额外费用
- **代理费**: 如委托代理机构办理
- **快递费**: 如需寄送材料
- **公证费**: 如需要公证（特殊情况）

## 注意事项

### 重要提醒
1. **信息一致性**: 所有材料中的软件名称、版本号、权利人信息必须完全一致
2. **原创性声明**: 确保软件是原创作品，不侵犯他人著作权
3. **完整性要求**: 程序鉴别材料必须能够体现软件的主要功能
4. **格式规范**: 严格按照要求的格式制作材料

### 常见问题预防
1. **版本号统一**: 确保所有地方使用相同的版本号 V1.0.0
2. **软件名称统一**: 统一使用"ContentPal内容处理工具"
3. **页数要求**: 程序鉴别材料必须恰好60页
4. **文件格式**: 所有材料转换为PDF格式提交

## 项目状态跟踪

### 材料准备状态
- [ ] 软件著作权登记申请表
- [ ] 程序鉴别材料（60页PDF）
- [x] 文档鉴别材料（软件设计说明书）
- [ ] 身份证明文件
- [ ] 委托书（如需要）

### 信息确认状态
- [ ] 著作权人信息确认
- [ ] 软件完成日期确认
- [ ] 首次发表日期确认
- [x] 技术环境信息整理
- [x] 功能特点描述完成

### 下一步行动
1. **确定著作权人信息**（个人或企业）
2. **确定重要日期**（开发完成日期、首次发表日期）
3. **运行程序鉴别材料生成脚本**
4. **制作最终的PDF文档**
5. **在线提交申请**

## 联系信息

### 中国版权保护中心
- **官网**: https://www.ccopyright.com.cn
- **客服电话**: 010-68003887
- **地址**: 北京市西城区天桥南大街1号天桥艺术大厦A座

### 咨询时间
- **工作日**: 9:00-17:00
- **周末**: 不办公

---

**备注**: 本清单基于当前政策制作，实际申请时请以版权保护中心最新要求为准。 