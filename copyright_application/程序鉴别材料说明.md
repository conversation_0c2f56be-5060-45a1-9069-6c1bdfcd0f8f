# 程序鉴别材料准备说明

## 概述
程序鉴别材料是软件著作权登记的重要组成部分，需要提供源程序的前、后各连续30页，且第60页为源程序的结束页。

## ContentPal程序鉴别材料要求

### 基本要求
1. **格式要求**: PDF格式
2. **页数要求**: 共60页（前30页 + 后30页）
3. **字体要求**: 小四号宋体
4. **页面设置**: A4纸张，单面打印效果
5. **页眉要求**: 每页页眉标注软件名称和版本号

### 内容要求
- **软件名称**: ContentPal内容处理工具
- **版本号**: V1.0.0
- **权利人署名**: [需要在每页添加]
- **源程序语言**: 主要为Dart语言

### 文件结构说明
本软件采用Flutter框架开发，主要源代码位于`lib/`目录下，包含以下主要模块：

1. **主程序入口**: `main.dart`
2. **用户界面模块**: `home/`, `common/`
3. **功能模块**: 
   - `markdown/` - Markdown处理
   - `svg/` - SVG图片处理  
   - `html/` - HTML内容处理
   - `pdf/` - PDF文档处理
   - `voice/` - 语音功能
4. **服务模块**: `services/`
5. **数据模型**: `models/`
6. **配置文件**: `config/`

### 准备步骤

#### 1. 选择代表性源代码文件
需要选择最能体现软件功能和技术特点的核心源代码文件，建议按以下顺序：

**前30页应包含**:
- 程序入口文件（main.dart）
- 核心业务逻辑文件
- 主要功能模块的关键文件
- 确保包含完整的版权声明和软件标识信息

**后30页应包含**:
- 重要功能模块的实现代码
- 数据处理逻辑
- 用户界面核心组件
- 程序结束标识

#### 2. 格式化要求
- 每页需要包含行号
- 保持代码的完整性和可读性
- 确保版权信息在每页都清晰可见
- 页眉格式：`ContentPal内容处理工具 V1.0.0`

#### 3. 版权标识要求
在源代码中需要包含以下信息：
- 软件名称：ContentPal内容处理工具
- 版本号：V1.0.0  
- 权利人信息
- 开发完成时间

## 文档鉴别材料说明

### 文档类型
可以选择以下任一类型的文档作为文档鉴别材料：
1. **软件需求规格说明书**
2. **软件设计说明书** 
3. **软件用户手册**
4. **软件测试报告**

### 推荐选择：软件设计说明书
建议制作软件设计说明书作为文档鉴别材料，应包含：

1. **软件概述**
   - 软件功能概述
   - 软件运行环境
   - 软件特点

2. **软件架构设计**
   - 系统架构图
   - 模块划分
   - 数据流设计

3. **功能模块设计**
   - Markdown处理模块
   - SVG处理模块
   - HTML处理模块
   - PDF处理模块
   - 语音功能模块

4. **用户界面设计**
   - 界面布局设计
   - 交互流程设计
   - 用户体验设计

5. **数据库设计**
   - 数据存储方案
   - 数据结构设计

### 格式要求
- **页数**: 不少于30页
- **格式**: PDF格式
- **字体**: 宋体、小四号
- **版权标识**: 每页都要包含软件名称、版本号和权利人信息

## 注意事项

1. **一致性检查**: 确保程序和文档中的软件名称、版本号、权利人署名完全一致
2. **完整性检查**: 程序鉴别材料必须是连续的页面，不能有缺失
3. **原创性**: 所有材料必须是原创内容，不能包含第三方版权内容
4. **技术规范**: 确保代码符合编程规范，具有良好的可读性

## 文件清单

准备完成后，著作权申请材料应包含以下文件：
1. `程序鉴别材料.pdf` - 源程序前后各30页
2. `文档鉴别材料.pdf` - 软件设计说明书或其他技术文档
3. `软件著作权登记申请表` - 按照要求填写的申请表格

所有PDF文件都应当清晰可读，并在每页标注正确的版权信息。 