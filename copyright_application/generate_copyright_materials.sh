#!/bin/bash
# ContentPal程序鉴别材料自动生成脚本
# 使用方法: ./generate_copyright_materials.sh

echo "ContentPal软件著作权程序鉴别材料生成脚本"
echo "============================================"

# 检查是否在项目根目录
if [ ! -f "pubspec.yaml" ] || [ ! -d "lib" ]; then
    echo "错误: 请在Flutter项目根目录运行此脚本"
    echo "当前目录: $(pwd)"
    exit 1
fi

# 创建输出目录
output_dir="copyright_application/program_materials"
mkdir -p "$output_dir"

# 定义版权信息
SOFTWARE_NAME="ContentPal内容处理工具"
VERSION="V1.0.0"
COPYRIGHT_HOLDER="[请填写著作权人信息]"
CURRENT_DATE=$(date '+%Y-%m-%d %H:%M:%S')

echo "软件名称: $SOFTWARE_NAME"
echo "版本号: $VERSION"
echo "输出目录: $output_dir"
echo ""

# 生成版权声明头部
generate_copyright_header() {
    local file_path="$1"
    cat << EOF
/*
 * ===================================================
 * 软件名称: $SOFTWARE_NAME
 * 版本号: $VERSION
 * 文件路径: $file_path
 * 版权所有: $COPYRIGHT_HOLDER
 * 生成时间: $CURRENT_DATE
 * ===================================================
 */

EOF
}

# 生成文件分隔符
generate_file_separator() {
    local file_path="$1"
    cat << EOF

// ===================================================================
// 文件结束: $file_path
// ===================================================================

EOF
}

# 查找并列出所有Dart文件
echo "正在扫描项目文件..."
all_dart_files=($(find lib -name "*.dart" -type f | sort))
total_files=${#all_dart_files[@]}

echo "找到 $total_files 个Dart源文件"

if [ $total_files -eq 0 ]; then
    echo "错误: 未找到任何Dart源文件"
    exit 1
fi

# 选择重要文件作为前30页
echo ""
echo "选择前30页的核心文件..."

# 核心文件优先级列表
priority_files=(
    "lib/main.dart"
    "lib/home.dart"
    "lib/markdown/markdown_editor.dart"
    "lib/markdown/markdown_preview.dart"
    "lib/pdf/pdf_viewer.dart"
    "lib/pdf/pdf_processor.dart"
    "lib/svg/svg_editor.dart"
    "lib/html/html_editor.dart"
    "lib/services/file_service.dart"
    "lib/services/storage_service.dart"
    "lib/voice/speech_service.dart"
    "lib/voice/tts_service.dart"
    "lib/subscription/subscription_manager.dart"
    "lib/content/content_processor.dart"
    "lib/settings/settings_page.dart"
)

# 选择前30页文件
front_files=()
for file in "${priority_files[@]}"; do
    if [ -f "$file" ]; then
        front_files+=("$file")
        echo "✓ 已选择: $file"
    else
        echo "✗ 文件不存在: $file"
    fi
done

# 如果优先文件不够，从其他文件中补充
if [ ${#front_files[@]} -lt 10 ]; then
    echo "补充其他文件到前30页..."
    for file in "${all_dart_files[@]}"; do
        # 检查是否已在front_files中
        if [[ ! " ${front_files[@]} " =~ " ${file} " ]]; then
            front_files+=("$file")
            echo "+ 补充文件: $file"
            if [ ${#front_files[@]} -ge 15 ]; then
                break
            fi
        fi
    done
fi

# 选择后30页文件（从剩余文件中选择）
echo ""
echo "选择后30页文件..."
back_files=()
for file in "${all_dart_files[@]}"; do
    # 检查是否已在front_files中
    if [[ ! " ${front_files[@]} " =~ " ${file} " ]]; then
        back_files+=("$file")
        echo "+ 后30页文件: $file"
        if [ ${#back_files[@]} -ge 15 ]; then
            break
        fi
    fi
done

# 生成前30页源代码
echo ""
echo "生成前30页程序鉴别材料..."
front_output="$output_dir/front_30_pages.dart"
> "$front_output"  # 清空文件

{
    echo "// ======================================================"
    echo "// $SOFTWARE_NAME $VERSION - 程序鉴别材料（前30页）"
    echo "// 版权所有: $COPYRIGHT_HOLDER"
    echo "// 生成时间: $CURRENT_DATE"
    echo "// ======================================================"
    echo ""
} >> "$front_output"

for file in "${front_files[@]}"; do
    if [ -f "$file" ]; then
        echo "正在处理: $file"
        {
            generate_copyright_header "$file"
            cat "$file"
            generate_file_separator "$file"
        } >> "$front_output"
    fi
done

# 生成后30页源代码
echo ""
echo "生成后30页程序鉴别材料..."
back_output="$output_dir/back_30_pages.dart"
> "$back_output"  # 清空文件

{
    echo "// ======================================================"
    echo "// $SOFTWARE_NAME $VERSION - 程序鉴别材料（后30页）"
    echo "// 版权所有: $COPYRIGHT_HOLDER"
    echo "// 生成时间: $CURRENT_DATE"
    echo "// ======================================================"
    echo ""
} >> "$back_output"

for file in "${back_files[@]}"; do
    if [ -f "$file" ]; then
        echo "正在处理: $file"
        {
            generate_copyright_header "$file"
            cat "$file"
            generate_file_separator "$file"
        } >> "$back_output"
    fi
done

# 生成统计信息
echo ""
echo "生成统计信息..."
stats_file="$output_dir/生成统计.txt"
{
    echo "$SOFTWARE_NAME $VERSION - 程序鉴别材料生成统计"
    echo "=============================================="
    echo "生成时间: $CURRENT_DATE"
    echo "版权所有: $COPYRIGHT_HOLDER"
    echo ""
    echo "文件统计:"
    echo "- 项目总文件数: $total_files"
    echo "- 前30页文件数: ${#front_files[@]}"
    echo "- 后30页文件数: ${#back_files[@]}"
    echo ""
    echo "前30页文件列表:"
    for file in "${front_files[@]}"; do
        if [ -f "$file" ]; then
            lines=$(wc -l < "$file")
            echo "  $file ($lines 行)"
        fi
    done
    echo ""
    echo "后30页文件列表:"
    for file in "${back_files[@]}"; do
        if [ -f "$file" ]; then
            lines=$(wc -l < "$file")
            echo "  $file ($lines 行)"
        fi
    done
    echo ""
    echo "输出文件:"
    echo "  前30页: $front_output"
    echo "  后30页: $back_output"
    echo "  统计信息: $stats_file"
} > "$stats_file"

# 计算生成文件的行数
front_lines=$(wc -l < "$front_output")
back_lines=$(wc -l < "$back_output")

echo ""
echo "=============================================="
echo "程序鉴别材料生成完成！"
echo "=============================================="
echo "输出目录: $output_dir"
echo "前30页文件: $front_output ($front_lines 行)"
echo "后30页文件: $back_output ($back_lines 行)"
echo "统计信息: $stats_file"
echo ""
echo "下一步操作:"
echo "1. 检查生成的源代码文件内容"
echo "2. 使用文本编辑器打开并格式化"
echo "3. 添加页眉页脚信息"
echo "4. 转换为PDF格式（确保总页数为60页）"
echo "5. 检查PDF文件质量和版权信息"
echo ""
echo "注意事项:"
echo "- 确保所有版权信息一致"
echo "- PDF页数必须恰好60页"
echo "- 每页都要有页眉页脚"
echo "- 字体使用小四号宋体或等宽字体"

# 生成后续处理说明
process_guide="$output_dir/后续处理说明.txt"
{
    echo "$SOFTWARE_NAME - 程序鉴别材料后续处理指南"
    echo "================================================"
    echo ""
    echo "1. PDF制作步骤："
    echo "   a) 使用VS Code或其他代码编辑器打开生成的.dart文件"
    echo "   b) 确保代码格式正确，显示行号"
    echo "   c) 设置页面格式："
    echo "      - 页面大小: A4"
    echo "      - 字体: Consolas、Monaco或其他等宽字体"
    echo "      - 字号: 10-12pt"
    echo "      - 行间距: 1.2倍"
    echo "   d) 添加页眉: '$SOFTWARE_NAME $VERSION                第 X 页，共 60 页'"
    echo "   e) 添加页脚: '版权所有 © [年份] $COPYRIGHT_HOLDER'"
    echo "   f) 导出为PDF，确保总页数为60页"
    echo ""
    echo "2. 质量检查清单："
    echo "   □ 总页数恰好60页"
    echo "   □ 每页都有正确的页眉和页脚"
    echo "   □ 软件名称和版本号一致"
    echo "   □ 权利人信息一致"
    echo "   □ 代码清晰可读，无乱码"
    echo "   □ PDF文件可正常打开和打印"
    echo ""
    echo "3. 文件命名建议："
    echo "   - 程序鉴别材料.pdf"
    echo "   - ContentPal_V1.0.0_程序鉴别材料.pdf"
    echo ""
    echo "4. 备份建议："
    echo "   - 保留源代码文件作为备份"
    echo "   - 保存PDF的多个版本"
    echo "   - 记录制作过程和修改历史"
} > "$process_guide"

echo "已生成后续处理指南: $process_guide"
echo ""
echo "脚本执行完成！" 