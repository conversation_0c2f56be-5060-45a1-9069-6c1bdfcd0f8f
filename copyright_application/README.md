# ContentPal软件著作权申请材料

## 文件夹概述

这个文件夹包含了为ContentPal内容处理工具申请软件著作权所需的所有材料和文档。

## 文件列表

### 📋 信息文档
- **`软件著作权登记信息.md`** - 详细的软件技术信息和功能描述
- **`软件著作权申请清单.md`** - 完整的申请材料清单和流程指南
- **`程序鉴别材料说明.md`** - 程序鉴别材料的准备说明

### 📖 技术文档
- **`ContentPal软件设计说明书.md`** - 详细的软件设计说明书（文档鉴别材料）

### 🛠️ 工具脚本
- **`generate_copyright_materials.sh`** - 自动生成程序鉴别材料的脚本
- **`制作程序鉴别材料脚本.md`** - 脚本使用说明和手动制作指南

### 📁 输出目录
- **`program_materials/`** - 脚本生成的程序鉴别材料输出目录（运行脚本后创建）

## 快速开始

### 第一步：准备基本信息
1. 确定著作权人信息（个人或企业）
2. 确定软件开发完成日期
3. 确定软件首次发表日期

### 第二步：生成程序鉴别材料
```bash
# 在项目根目录执行
./copyright_application/generate_copyright_materials.sh
```

### 第三步：制作PDF文档
1. 打开生成的源代码文件
2. 添加页眉页脚
3. 转换为PDF格式
4. 确保总页数为60页

### 第四步：在线申请
1. 访问 https://www.ccopyright.com.cn
2. 注册账户并登录
3. 填写申请表
4. 上传材料
5. 提交申请

## 软件信息摘要

| 项目 | 信息 |
|------|------|
| **软件名称** | ContentPal内容处理工具 |
| **版本号** | V1.0.0 |
| **开发语言** | Dart（主要），Swift/Objective-C（iOS），Kotlin/Java（Android） |
| **源程序量** | 96个文件，33,749行代码 |
| **开发平台** | macOS 14.5.0, Flutter 3.7.2+ |
| **运行平台** | iOS, Android, Windows, macOS, Linux, Web |

## 主要功能

1. **Markdown文档处理** - 实时编辑、预览、格式化和导出
2. **SVG图像处理** - SVG编辑、优化和格式转换
3. **HTML内容处理** - HTML代码编辑、格式化和预览
4. **PDF文档处理** - PDF查看、注释、转换和编辑
5. **语音功能** - 语音转文字和文字转语音
6. **文件管理** - 文件导入、导出和同步功能

## 技术特点

- **跨平台兼容性** - 基于Flutter框架，支持多平台
- **实时渲染技术** - 实时预览和编辑功能
- **高性能处理** - 优化的渲染引擎，支持大文件
- **现代化UI设计** - Material Design设计语言
- **数据安全性** - 本地加密存储和权限管理
- **插件化架构** - 模块化设计，易于扩展

## 申请费用

- **登记费**: 300元（个人）/ 450元（企业）
- **证书费**: 50元
- **加急费**: 320元（可选）

## 注意事项

### ⚠️ 重要提醒
1. **信息一致性** - 所有材料中的软件名称、版本号、权利人信息必须完全一致
2. **原创性声明** - 确保软件是原创作品，不侵犯他人著作权
3. **完整性要求** - 程序鉴别材料必须能够体现软件的主要功能
4. **格式规范** - 严格按照要求的格式制作材料

### 📝 常见问题
1. **版本号统一** - 确保所有地方使用相同的版本号 V1.0.0
2. **软件名称统一** - 统一使用"ContentPal内容处理工具"
3. **页数要求** - 程序鉴别材料必须恰好60页
4. **文件格式** - 所有材料转换为PDF格式提交

## 进度追踪

### 材料准备状态
- [ ] 软件著作权登记申请表
- [ ] 程序鉴别材料（60页PDF）
- [x] 文档鉴别材料（软件设计说明书）
- [ ] 身份证明文件
- [ ] 委托书（如需要）

### 信息确认状态
- [ ] 著作权人信息确认
- [ ] 软件完成日期确认
- [ ] 首次发表日期确认
- [x] 技术环境信息整理
- [x] 功能特点描述完成

## 联系信息

### 中国版权保护中心
- **官网**: https://www.ccopyright.com.cn
- **客服电话**: 010-68003887
- **地址**: 北京市西城区天桥南大街1号天桥艺术大厦A座
- **工作时间**: 工作日 9:00-17:00

## 相关链接

- [所需文件说明](https://www.ccopyright.com.cn/index.php?optionid=1080)
- [填表说明](https://www.ccopyright.com.cn/index.php?optionid=1081)
- [Flutter官方文档](https://docs.flutter.dev/)
- [Dart编程语言](https://dart.dev/)

## 文件使用说明

### 对于开发者
1. 查看 `软件著作权登记信息.md` 了解技术细节
2. 运行 `generate_copyright_materials.sh` 生成程序鉴别材料
3. 使用 `ContentPal软件设计说明书.md` 作为文档鉴别材料
4. 按照 `软件著作权申请清单.md` 完成申请流程

### 对于法务人员
1. 查看 `软件著作权申请清单.md` 了解申请流程
2. 核实 `软件著作权登记信息.md` 中的法律信息
3. 确认所有材料的版权信息一致性
4. 协助完成在线申请表填写

### 对于项目经理
1. 确认项目信息和时间节点
2. 协调开发者和法务人员的工作
3. 跟踪申请进度
4. 确保材料质量符合要求

---

**版权声明**: 本文件夹内的所有文档和材料为ContentPal内容处理工具V1.0.0软件著作权申请专用，享有完整的知识产权。

**最后更新**: $(date '+%Y年%m月%d日') 