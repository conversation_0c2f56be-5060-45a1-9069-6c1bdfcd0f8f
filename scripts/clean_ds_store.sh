#!/bin/bash

# 清理 .DS_Store 文件的脚本
# 使用方法: ./scripts/clean_ds_store.sh

echo "🧹 开始清理 .DS_Store 文件..."

# 查找并删除所有 .DS_Store 文件
find . -name ".DS_Store" -type f -delete

# 从 Git 中移除已跟踪的 .DS_Store 文件
git rm --cached -r . 2>/dev/null || true
git reset HEAD 2>/dev/null || true

echo "✅ .DS_Store 文件清理完成！"

# 检查是否还有 .DS_Store 文件
if find . -name ".DS_Store" -type f | grep -q ".DS_Store"; then
    echo "⚠️  警告：仍然发现 .DS_Store 文件："
    find . -name ".DS_Store" -type f
else
    echo "✨ 所有 .DS_Store 文件已清理干净！"
fi

# 显示 Git 状态中的 .DS_Store 文件
echo ""
echo "📋 Git 状态中的 .DS_Store 文件："
git status --porcelain | grep ".DS_Store" || echo "无 .DS_Store 文件在 Git 状态中"

echo ""
echo "💡 提示："
echo "   - 已配置 .gitignore 忽略 .DS_Store 文件"
echo "   - 建议设置全局 Git 配置：git config --global core.excludesfile ~/.gitignore_global"
echo "   - 可以运行 'defaults write com.apple.desktopservices DSDontWriteNetworkStores true' 来减少 .DS_Store 文件生成"
