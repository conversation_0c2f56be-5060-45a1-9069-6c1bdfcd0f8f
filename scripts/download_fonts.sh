#!/bin/bash

# 字体下载脚本
# 下载开源中文字体到 assets/fonts/ 目录

set -e

# 创建字体目录
FONT_DIR="assets/fonts"
mkdir -p "$FONT_DIR"

echo "开始下载开源中文字体..."

# 1. 思源黑体 (Source Han Sans CN)
echo "下载思源黑体..."
if [ ! -f "$FONT_DIR/SourceHanSansCN-Regular.otf" ]; then
    curl -L "https://github.com/adobe-fonts/source-han-sans/releases/download/2.004R/SourceHanSansCN.zip" -o temp_source_han.zip
    unzip -j temp_source_han.zip "*/SourceHanSansCN-Regular.otf" -d "$FONT_DIR"
    unzip -j temp_source_han.zip "*/SourceHanSansCN-Medium.otf" -d "$FONT_DIR"
    unzip -j temp_source_han.zip "*/SourceHanSansCN-Bold.otf" -d "$FONT_DIR"
    rm temp_source_han.zip
    echo "✅ 思源黑体下载完成"
else
    echo "✅ 思源黑体已存在"
fi

# 2. 阿里巴巴普惠体
echo "下载阿里巴巴普惠体..."
if [ ! -f "$FONT_DIR/AlibabaPuHuiTi-Regular.ttf" ]; then
    # 注意：这些是示例URL，实际使用时需要替换为正确的下载链接
    echo "⚠️  请手动下载阿里巴巴普惠体字体文件到 $FONT_DIR 目录"
    echo "   下载地址：https://fonts.alibabagroup.com/"
    echo "   需要的文件："
    echo "   - AlibabaPuHuiTi-Regular.ttf"
    echo "   - AlibabaPuHuiTi-Medium.ttf"
    echo "   - AlibabaPuHuiTi-Bold.ttf"
else
    echo "✅ 阿里巴巴普惠体已存在"
fi

# 3. HarmonyOS Sans
echo "下载 HarmonyOS Sans..."
if [ ! -f "$FONT_DIR/HarmonyOS_Sans_SC_Regular.ttf" ]; then
    echo "⚠️  请手动下载 HarmonyOS Sans 字体文件到 $FONT_DIR 目录"
    echo "   下载地址：https://developer.harmonyos.com/cn/design/resource"
    echo "   需要的文件："
    echo "   - HarmonyOS_Sans_SC_Regular.ttf"
    echo "   - HarmonyOS_Sans_SC_Medium.ttf"
    echo "   - HarmonyOS_Sans_SC_Bold.ttf"
else
    echo "✅ HarmonyOS Sans 已存在"
fi

# 4. 小米兰亭
echo "下载小米兰亭..."
if [ ! -f "$FONT_DIR/MiSans-Regular.ttf" ]; then
    echo "⚠️  请手动下载小米兰亭字体文件到 $FONT_DIR 目录"
    echo "   下载地址：https://hyperos.mi.com/font"
    echo "   需要的文件："
    echo "   - MiSans-Regular.ttf"
    echo "   - MiSans-Medium.ttf"
    echo "   - MiSans-Bold.ttf"
else
    echo "✅ 小米兰亭已存在"
fi

# 创建占位字体文件（如果字体文件不存在）
create_placeholder_font() {
    local font_name="$1"
    local font_file="$2"
    
    if [ ! -f "$FONT_DIR/$font_file" ]; then
        echo "创建占位字体文件: $font_file"
        # 创建一个空的字体文件作为占位符
        touch "$FONT_DIR/$font_file"
    fi
}

# 创建占位字体文件
echo "创建占位字体文件..."
create_placeholder_font "阿里巴巴普惠体" "AlibabaPuHuiTi-Regular.ttf"
create_placeholder_font "阿里巴巴普惠体" "AlibabaPuHuiTi-Medium.ttf"
create_placeholder_font "阿里巴巴普惠体" "AlibabaPuHuiTi-Bold.ttf"
create_placeholder_font "HarmonyOS Sans" "HarmonyOS_Sans_SC_Regular.ttf"
create_placeholder_font "HarmonyOS Sans" "HarmonyOS_Sans_SC_Medium.ttf"
create_placeholder_font "HarmonyOS Sans" "HarmonyOS_Sans_SC_Bold.ttf"
create_placeholder_font "小米兰亭" "MiSans-Regular.ttf"
create_placeholder_font "小米兰亭" "MiSans-Medium.ttf"
create_placeholder_font "小米兰亭" "MiSans-Bold.ttf"

echo ""
echo "🎉 字体下载脚本执行完成！"
echo ""
echo "📝 注意事项："
echo "1. 部分字体需要手动下载，请按照上述提示操作"
echo "2. 确保所有字体文件都放在 $FONT_DIR 目录中"
echo "3. 字体文件名必须与 pubspec.yaml 中的配置一致"
echo "4. 运行 'flutter clean && flutter pub get' 重新构建项目"
echo ""
echo "📂 当前字体目录内容："
ls -la "$FONT_DIR"
