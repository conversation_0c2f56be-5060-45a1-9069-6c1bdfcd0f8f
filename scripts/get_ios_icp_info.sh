#!/bin/bash

# iOS ICP备案信息获取脚本
# 用于获取iOS应用的Bundle ID、公钥、SHA-1指纹等ICP备案所需信息

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的命令是否存在
check_dependencies() {
    local missing_deps=()
    
    if ! command -v security &> /dev/null; then
        missing_deps+=("security")
    fi
    
    if ! command -v openssl &> /dev/null; then
        missing_deps+=("openssl")
    fi
    
    if ! command -v plutil &> /dev/null; then
        missing_deps+=("plutil")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "缺少必要的命令: ${missing_deps[*]}"
        print_error "请确保在macOS系统上运行此脚本"
        exit 1
    fi
}

# 检查是否是Flutter iOS项目
check_flutter_project() {
    if [ ! -f "pubspec.yaml" ]; then
        print_error "当前目录不是Flutter项目根目录"
        print_error "请在Flutter项目根目录下运行此脚本"
        exit 1
    fi
    
    if [ ! -d "ios" ]; then
        print_error "未找到ios目录"
        print_error "请确保这是一个包含iOS平台的Flutter项目"
        exit 1
    fi
    
    if [ ! -f "ios/Runner.xcodeproj/project.pbxproj" ]; then
        print_error "未找到iOS项目配置文件"
        exit 1
    fi
    
    print_success "检测到Flutter iOS项目"
}

# 获取Bundle ID
get_bundle_id() {
    local pbxproj_file="ios/Runner.xcodeproj/project.pbxproj"
    local bundle_id=$(grep -m 1 "PRODUCT_BUNDLE_IDENTIFIER" "$pbxproj_file" | sed 's/.*= \(.*\);/\1/' | tr -d ' ')
    
    if [ -z "$bundle_id" ]; then
        print_error "无法从项目配置中获取Bundle ID"
        exit 1
    fi
    
    echo "$bundle_id"
}

# 获取Provisioning Profile信息
get_provisioning_profile() {
    local pbxproj_file="ios/Runner.xcodeproj/project.pbxproj"
    local profile_specifier=$(grep "PROVISIONING_PROFILE_SPECIFIER\[sdk=iphoneos\*\]" "$pbxproj_file" | sed 's/.*= "\(.*\)";/\1/' | head -1)
    
    if [ -z "$profile_specifier" ]; then
        profile_specifier=$(grep -m 1 "PROVISIONING_PROFILE_SPECIFIER" "$pbxproj_file" | sed 's/.*= "\(.*\)";/\1/')
    fi
    
    echo "$profile_specifier"
}

# 获取开发团队ID
get_development_team() {
    local pbxproj_file="ios/Runner.xcodeproj/project.pbxproj"
    local team_id=$(grep "DEVELOPMENT_TEAM\[sdk=iphoneos\*\]" "$pbxproj_file" | sed 's/.*= \(.*\);/\1/' | tr -d ' ' | head -1)
    
    if [ -z "$team_id" ]; then
        team_id=$(grep -m 1 "DEVELOPMENT_TEAM" "$pbxproj_file" | sed 's/.*= \(.*\);/\1/' | tr -d ' ')
    fi
    
    echo "$team_id"
}

# 列出所有代码签名证书
list_certificates() {
    print_info "正在查找代码签名证书..."
    
    local certs=$(security find-identity -v -p codesigning 2>/dev/null)
    
    if [ -z "$certs" ]; then
        print_error "未找到任何代码签名证书"
        print_error "请确保已在Xcode中配置了开发者证书"
        exit 1
    fi
    
    echo "$certs"
}

# 获取证书详细信息
get_certificate_info() {
    local cert_name="$1"
    local cert_info=""
    
    # 获取证书详细信息
    cert_info=$(security find-certificate -c "$cert_name" -p 2>/dev/null | openssl x509 -text -noout 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        print_error "无法获取证书 '$cert_name' 的详细信息"
        return 1
    fi
    
    echo "$cert_info"
}

# 提取SHA-1指纹
extract_sha1() {
    local cert_name="$1"
    local sha1_with_colons=""
    local sha1_without_colons=""
    
    sha1_with_colons=$(security find-certificate -c "$cert_name" -p 2>/dev/null | openssl x509 -fingerprint -sha1 -noout 2>/dev/null | cut -d'=' -f2)
    
    if [ $? -ne 0 ] || [ -z "$sha1_with_colons" ]; then
        print_error "无法提取证书 '$cert_name' 的SHA-1指纹"
        return 1
    fi
    
    sha1_without_colons=$(echo "$sha1_with_colons" | tr -d ':')
    
    echo "$sha1_with_colons|$sha1_without_colons"
}

# 提取公钥 (PEM格式)
extract_public_key_pem() {
    local cert_name="$1"
    local public_key=""

    public_key=$(security find-certificate -c "$cert_name" -p 2>/dev/null | openssl x509 -pubkey -noout 2>/dev/null)

    if [ $? -ne 0 ] || [ -z "$public_key" ]; then
        print_error "无法提取证书 '$cert_name' 的PEM格式公钥"
        return 1
    fi

    echo "$public_key"
}

# 提取公钥 (十六进制格式，用于ICP备案)
extract_public_key_hex() {
    local cert_name="$1"
    local public_key_hex=""

    # 提取公钥的模数部分（十六进制格式）
    public_key_hex=$(security find-certificate -c "$cert_name" -p 2>/dev/null | openssl x509 -noout -pubkey 2>/dev/null | openssl rsa -pubin -noout -modulus 2>/dev/null | cut -d'=' -f2)

    if [ $? -ne 0 ] || [ -z "$public_key_hex" ]; then
        print_error "无法提取证书 '$cert_name' 的十六进制公钥"
        return 1
    fi

    echo "$public_key_hex"
}

# 格式化输出ICP备案信息
format_icp_output() {
    local bundle_id="$1"
    local public_key_hex="$2"
    local sha1_no_colons="$3"

    echo ""
    echo "=========================================="
    echo "          iOS ICP备案信息"
    echo "=========================================="
    echo ""
    echo "1. iOS平台Bundle ID:"
    echo "$bundle_id"
    echo ""
    echo "2. 公钥 (十六进制格式):"
    echo "$public_key_hex"
    echo ""
    echo "3. 证书SHA-1指纹 (无冒号格式):"
    echo "$sha1_no_colons"
    echo ""
    echo "=========================================="
}

# 格式化输出详细信息
format_detailed_output() {
    local bundle_id="$1"
    local cert_name="$2"
    local cert_type="$3"
    local team_id="$4"
    local provisioning_profile="$5"
    local sha1_with_colons="$6"
    local sha1_no_colons="$7"
    local public_key_hex="$8"
    local public_key_pem="$9"

    echo ""
    echo "=========================================="
    echo "          详细证书信息"
    echo "=========================================="
    echo ""
    echo "项目信息:"
    echo "  Bundle ID: $bundle_id"
    echo "  开发团队ID: $team_id"
    echo "  Provisioning Profile: $provisioning_profile"
    echo ""
    echo "证书信息:"
    echo "  证书名称: $cert_name"
    echo "  证书类型: $cert_type"
    echo "  SHA-1指纹 (带冒号): $sha1_with_colons"
    echo "  SHA-1指纹 (无冒号): $sha1_no_colons"
    echo ""
    echo "公钥信息:"
    echo "  公钥 (十六进制格式，用于ICP备案): $public_key_hex"
    echo "  公钥 (PEM格式，用于技术参考):"
    echo "$public_key_pem"
    echo ""
    echo "=========================================="
}

# 保存信息到文件
save_to_file() {
    local output_file="$1"
    local content="$2"

    echo "$content" > "$output_file"
    print_success "信息已保存到: $output_file"
}

# 主函数
main() {
    echo ""
    echo "=========================================="
    echo "      iOS ICP备案信息获取工具"
    echo "=========================================="
    echo ""

    # 检查依赖
    check_dependencies

    # 检查Flutter项目
    check_flutter_project

    # 获取项目信息
    print_info "正在获取项目信息..."
    local bundle_id=$(get_bundle_id)
    local team_id=$(get_development_team)
    local provisioning_profile=$(get_provisioning_profile)

    print_success "Bundle ID: $bundle_id"
    print_success "开发团队ID: $team_id"
    print_success "Provisioning Profile: $provisioning_profile"

    # 列出证书
    echo ""
    print_info "可用的代码签名证书:"
    local certificates=$(list_certificates)
    echo "$certificates"

    # 让用户选择证书
    echo ""
    print_info "请选择要使用的证书:"
    echo "1) 自动选择发布证书 (Apple Distribution)"
    echo "2) 自动选择开发证书 (Apple Development)"
    echo "3) 手动输入证书名称"
    echo ""
    read -p "请输入选择 (1-3): " choice

    local selected_cert=""
    local cert_type=""

    case $choice in
        1)
            selected_cert=$(echo "$certificates" | grep "Apple Distribution" | head -1 | sed 's/.*"\(.*\)"/\1/')
            cert_type="发布证书 (Apple Distribution)"
            ;;
        2)
            selected_cert=$(echo "$certificates" | grep "Apple Development" | head -1 | sed 's/.*"\(.*\)"/\1/')
            cert_type="开发证书 (Apple Development)"
            ;;
        3)
            echo ""
            read -p "请输入完整的证书名称: " selected_cert
            if echo "$certificates" | grep -q "$selected_cert"; then
                if echo "$selected_cert" | grep -q "Distribution"; then
                    cert_type="发布证书"
                else
                    cert_type="开发证书"
                fi
            else
                print_error "未找到指定的证书: $selected_cert"
                exit 1
            fi
            ;;
        *)
            print_error "无效的选择"
            exit 1
            ;;
    esac

    if [ -z "$selected_cert" ]; then
        print_error "未找到匹配的证书"
        exit 1
    fi

    print_success "已选择证书: $selected_cert"
    print_success "证书类型: $cert_type"

    # 提取证书信息
    echo ""
    print_info "正在提取证书信息..."

    local sha1_result=$(extract_sha1 "$selected_cert")
    if [ $? -ne 0 ]; then
        exit 1
    fi

    local sha1_with_colons=$(echo "$sha1_result" | cut -d'|' -f1)
    local sha1_no_colons=$(echo "$sha1_result" | cut -d'|' -f2)

    # 提取十六进制格式公钥（用于ICP备案）
    local public_key_hex=$(extract_public_key_hex "$selected_cert")
    if [ $? -ne 0 ]; then
        exit 1
    fi

    # 提取PEM格式公钥（用于技术参考）
    local public_key_pem=$(extract_public_key_pem "$selected_cert")
    if [ $? -ne 0 ]; then
        exit 1
    fi

    print_success "证书信息提取完成"

    # 格式化输出
    local icp_output=$(format_icp_output "$bundle_id" "$public_key_hex" "$sha1_no_colons")
    local detailed_output=$(format_detailed_output "$bundle_id" "$selected_cert" "$cert_type" "$team_id" "$provisioning_profile" "$sha1_with_colons" "$sha1_no_colons" "$public_key_hex" "$public_key_pem")

    # 显示结果
    echo "$icp_output"
    echo "$detailed_output"

    # 询问是否保存到文件
    echo ""
    read -p "是否要将信息保存到文件? (y/n): " save_choice

    if [[ $save_choice =~ ^[Yy]$ ]]; then
        local timestamp=$(date +"%Y%m%d_%H%M%S")
        local icp_file="ios_icp_info_${timestamp}.txt"
        local detailed_file="ios_detailed_info_${timestamp}.txt"

        save_to_file "$icp_file" "$icp_output"
        save_to_file "$detailed_file" "$detailed_output"

        echo ""
        print_success "文件保存完成:"
        print_success "  ICP备案信息: $icp_file"
        print_success "  详细信息: $detailed_file"
    fi

    echo ""
    print_success "脚本执行完成!"
    echo ""
    print_info "使用说明:"
    print_info "1. 复制上面的 'iOS ICP备案信息' 部分用于ICP备案申请"
    print_info "2. SHA-1指纹已自动去除冒号，符合备案系统要求"
    print_info "3. 如需其他格式，请查看详细信息部分"
    echo ""
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
