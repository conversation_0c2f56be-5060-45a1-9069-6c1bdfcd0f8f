# ContentPal 文件关联功能

本文档介绍了 ContentPal 应用中实现的文件关联功能，允许用户从系统文件管理器中直接使用 ContentPal 打开支持的文件类型。

## 支持的文件类型

- **Markdown文件**: `.md`, `.markdown`
- **文本文件**: `.txt`, `.text`
- **SVG图像**: `.svg`
- **HTML文件**: `.html`, `.htm`

## 功能特性

### Android

- 支持 `ACTION_VIEW` Intent 处理文件打开
- 支持 `ACTION_SEND` Intent 处理文件分享
- 支持根据 MIME 类型和文件扩展名进行文件关联
- 自动处理 content:// URI，复制到临时文件

### iOS

- 支持文档类型关联 (CFBundleDocumentTypes)
- 支持 UTI (Uniform Type Identifiers) 声明
- 支持从文件应用和其他应用打开文件
- 支持应用间的文件传递

## 实现架构

### 1. 文件意图服务 (`FileIntentService`)

核心服务类，负责：
- 识别文件类型
- 路由到对应的编辑器
- 处理文件读取和错误处理

```dart
class FileIntentService {
  // 根据文件扩展名识别文件类型
  FileType getFileType(String filePath);
  
  // 导航到对应的编辑器
  Future<void> navigateToEditor(BuildContext context, String filePath, FileType fileType);
  
  // 处理文本内容
  void handleTextContent(BuildContext context, String content, {String? title});
}
```

### 2. 方法通道 (Method Channel)

Flutter 与原生平台之间的通信桥梁：

```dart
const platform = MethodChannel('com.example.contentpal/file_intent');
```

支持的方法：
- `getInitialIntent`: 获取应用启动时的文件意图
- `handleFileIntent`: 处理文件打开意图
- `handleTextIntent`: 处理文本分享意图

### 3. Android 原生实现

#### MainActivity.kt
```kotlin
class MainActivity: FlutterActivity() {
    // 处理文件 Intent
    private fun handleIntent(intent: Intent?)
    
    // 处理文件 URI
    private fun handleFileUri(uri: Uri)
    
    // 复制 content URI 到临时文件
    private fun copyContentToTempFile(uri: Uri): String?
}
```

#### AndroidManifest.xml
配置了多种 Intent Filter 来支持不同的文件关联方式：

```xml
<!-- 支持打开 Markdown 文件 -->
<intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <data android:mimeType="text/markdown" />
</intent-filter>

<!-- 支持文件扩展名匹配 -->
<intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <data android:scheme="file" />
    <data android:pathPattern=".*\\.md" />
</intent-filter>
```

### 4. iOS 原生实现

#### AppDelegate.swift
```swift
class AppDelegate: FlutterAppDelegate {
    // 处理文件 URL
    private func handleFileURL(_ url: URL)
    
    // 处理文本意图
    private func handleTextIntent(_ text: String)
}
```

#### Info.plist
配置文档类型和 UTI 支持：

```xml
<!-- 支持的文档类型 -->
<key>CFBundleDocumentTypes</key>
<array>
    <dict>
        <key>CFBundleTypeName</key>
        <string>Markdown Document</string>
        <key>CFBundleTypeRole</key>
        <string>Editor</string>
        <key>LSItemContentTypes</key>
        <array>
            <string>net.daringfireball.markdown</string>
        </array>
    </dict>
</array>
```

## 使用方法

### 1. 从文件管理器打开

**Android:**
1. 在文件管理器中找到支持的文件
2. 长按文件，选择"打开方式"
3. 选择 ContentPal 应用
4. 应用会自动跳转到对应的编辑器

**iOS:**
1. 在文件应用中找到支持的文件
2. 点击文件
3. 选择"分享" -> "拷贝到 ContentPal"
4. 或者在支持的应用中选择"在 ContentPal 中打开"

### 2. 从其他应用分享

支持从其他应用分享文本内容或文件到 ContentPal：

1. 在任意应用中选择文本或文件
2. 点击分享按钮
3. 选择 ContentPal
4. 内容会在相应的编辑器中打开

## 编辑器对应关系

| 文件类型 | 编辑器 | 功能 |
|---------|--------|------|
| .md, .markdown | MarkdownRenderScreen | Markdown 编辑、渲染和预览 |
| .txt, .text | TextCardEditorPage | 文本卡片编辑 |
| .svg | SvgEditorScreen | SVG 可视化编辑 |
| .html, .htm | HtmlEditorScreen | HTML 代码编辑和预览 |

## 错误处理

系统包含完善的错误处理机制：

1. **文件读取失败**: 显示错误对话框提示用户
2. **不支持的文件类型**: 显示支持的文件类型列表
3. **权限问题**: 自动处理文件访问权限

## 测试功能

可以使用 `FileIntentTestPage` 来测试文件意图功能：

```dart
import 'package:contentpal/services/file_intent_test.dart';

// 在应用中添加测试页面
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const FileIntentTestPage()),
);
```

## 开发注意事项

1. **权限**: 确保应用有必要的文件访问权限
2. **临时文件**: Android 上会创建临时文件，需要适当清理
3. **内存管理**: 大文件处理时注意内存使用
4. **用户体验**: 文件处理过程中显示适当的加载指示器

## 未来扩展

可以考虑添加以下功能：

1. 支持更多文件类型 (PDF, Word, Excel 等)
2. 批量文件处理
3. 云存储集成
4. 文件同步功能
5. 协作编辑支持

## 故障排除

### 常见问题

1. **文件关联不生效**
   - 检查 AndroidManifest.xml 和 Info.plist 配置
   - 确保应用已正确安装
   - 重启设备后重试

2. **无法读取文件**
   - 检查文件权限
   - 确认文件路径正确
   - 检查文件是否损坏

3. **编辑器无法正常打开**
   - 检查对应编辑器的依赖
   - 确认路由配置正确
   - 查看日志输出

### 调试建议

1. 启用详细日志输出
2. 使用测试页面验证功能
3. 在真实设备上测试（避免模拟器限制）
4. 检查原生平台的日志输出

---

通过以上实现，ContentPal 应用现在支持从系统文件管理器和其他应用中直接打开和编辑支持的文件类型，大大提升了用户体验和应用的实用性。 