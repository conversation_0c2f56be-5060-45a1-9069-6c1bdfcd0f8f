# ContentPal 项目架构文档

## 项目概述

ContentPal 是一个功能强大的内容处理工具 Flutter 应用，提供多种内容编辑和处理功能。项目采用模块化架构设计，支持跨平台部署（iOS、Android、Web、Desktop）。

## 技术栈

### 核心框架
- **Flutter**: 3.7.2+ (跨平台UI框架)
- **Dart**: 3.7.2+ (编程语言)

### 主要依赖

#### UI相关
- `flutter_svg`: SVG图片处理
- `shimmer`: 加载动画效果
- `flutter_markdown`: Markdown渲染
- `flutter_colorpicker`: 颜色选择器
- `webview_flutter`: Web视图组件
- `flutter_math_fork`: 数学公式渲染

#### 语音相关
- `speech_to_text`: 语音转文字
- `flutter_tts`: 文字转语音
- `just_audio`: 音频播放
- `flutter_sound`: 音频录制

#### 数据存储
- `shared_preferences`: 本地偏好设置
- `path_provider`: 文件路径管理
- `hive`: 本地数据库
- `hive_flutter`: Hive Flutter集成

#### 文件操作
- `file_picker`: 文件选择
- `share_plus`: 文件分享
- `image_picker`: 图片选择

#### PDF处理
- `pdf`: PDF生成
- `printing`: 打印功能
- `syncfusion_flutter_pdf`: PDF处理
- `syncfusion_flutter_pdfviewer`: PDF查看器

#### 其他工具
- `flutter_bloc`: 状态管理
- `in_app_purchase`: 应用内购买
- `permission_handler`: 权限管理
- `screenshot`: 截图功能

## 项目结构

```
lib/
├── main.dart                 # 应用入口
├── home.dart                 # 主页面
├── config/                   # 配置文件
│   ├── app_settings.dart     # 应用设置
│   ├── app_theme.dart        # 主题配置
│   └── constants.dart        # 常量定义
├── common/                   # 公共组件
│   ├── utils/               # 工具类
│   └── widgets/             # 公共组件
├── services/                # 服务层
│   ├── service_locator.dart # 服务定位器
│   ├── content_service.dart # 内容服务
│   ├── storage_service.dart # 存储服务
│   └── ...                  # 其他服务
├── models/                  # 数据模型
├── content/                 # 内容管理模块
├── markdown/                # Markdown处理模块
├── html/                    # HTML处理模块
├── svg/                     # SVG处理模块
├── pdf/                     # PDF处理模块
├── voice/                   # 语音处理模块
├── text_cards/              # 文本卡片模块
├── settings/                # 设置模块
└── subscription/            # 订阅模块
```

## 核心模块详解

### 1. 主应用模块 (main.dart)
- 应用初始化
- 主题管理
- 文件意图处理
- 全局配置

### 2. 主页面模块 (home.dart)
- 应用主界面
- 功能模块导航
- 背景动画效果
- 响应式布局

### 3. 配置模块 (config/)
- **app_settings.dart**: 应用全局设置
- **app_theme.dart**: 主题色彩配置
- **constants.dart**: 应用常量定义

### 4. 服务层 (services/)
- **service_locator.dart**: 依赖注入容器
- **content_service.dart**: 内容管理服务
- **storage_service.dart**: 数据存储服务
- **audio_recorder_service.dart**: 音频录制服务
- **tts_service.dart**: 文字转语音服务
- **file_intent_service.dart**: 文件意图处理服务

### 5. 功能模块

#### 5.1 内容管理模块 (content/)
- 内容列表管理
- 内容编辑功能
- 内容保存和导出

#### 5.2 Markdown模块 (markdown/)
- Markdown编辑器
- 实时预览
- 样式定制
- 水印功能
- 导出功能

#### 5.3 HTML模块 (html/)
- HTML编辑器
- 代码格式化
- 语法高亮
- 预览功能

#### 5.4 SVG模块 (svg/)
- SVG编辑器
- 图标优化
- 格式转换

#### 5.5 PDF模块 (pdf/)
- PDF查看器
- 文档注释
- 格式转换

#### 5.6 语音模块 (voice/)
- 语音录制
- 语音播放
- 语音转文字
- 文字转语音

#### 5.7 文本卡片模块 (text_cards/)
- 文本卡片创建
- 模板系统
- 导出功能
- 可视化渲染

## 架构特点

### 1. 模块化设计
- 每个功能模块独立开发
- 清晰的模块边界
- 便于维护和扩展

### 2. 服务层架构
- 业务逻辑与UI分离
- 依赖注入模式
- 服务可复用

### 3. 响应式设计
- 支持多种屏幕尺寸
- 自适应布局
- 流畅的用户体验

### 4. 跨平台支持
- iOS/Android移动端
- Web浏览器端
- Desktop桌面端

## 数据流

```
UI层 (Widgets)
    ↓
业务逻辑层 (Services)
    ↓
数据层 (Models/Storage)
```

## 状态管理

项目使用多种状态管理方式：
- **setState**: 简单组件状态
- **Provider**: 跨组件状态共享
- **BLoC**: 复杂业务逻辑状态管理

## 文件组织原则

1. **按功能模块分组**: 相关功能放在同一目录
2. **清晰的命名规范**: 文件名反映其功能
3. **合理的依赖关系**: 避免循环依赖
4. **可扩展性**: 便于添加新功能

## 开发规范

1. **代码风格**: 遵循Dart官方代码规范
2. **注释规范**: 重要功能必须添加注释
3. **错误处理**: 完善的异常处理机制
4. **性能优化**: 注意内存管理和性能优化 