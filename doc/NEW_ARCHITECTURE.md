# 纯文本定制渲染模块 - 新架构设计

## 设计理念

基于用户的反馈，我们重新设计了文本卡片模块，采用**分离式架构**：

1. **简单编辑器** - 专注于内容编辑和模块拆分
2. **可视化渲染器** - 提供所见即所得的样式定制
3. **智能导出系统** - 支持单卡片和批量导出

## 核心优势

### 🎯 清晰的责任分离
- **编辑阶段**：纯文本编辑，无格式干扰，专注内容创作
- **渲染阶段**：可视化样式定制，所见即所得
- **导出阶段**：高质量图片输出，支持分享

### 🚀 更好的用户体验
- 避免了复杂编辑器的性能问题
- 提供直观的样式定制界面
- 支持手动控制的内容拆分

### 🎨 强大的定制能力
- 选中任意内容块进行样式定制
- 不与Markdown格式耦合
- 支持丰富的视觉效果

## 架构组件

### 1. 数据模型层 (`models/content_models.dart`)

#### ContentType
定义支持的内容类型：
- `text` - 普通文本
- `heading` - 标题（支持多级）
- `bulletList` - 无序列表
- `numberedList` - 有序列表
- `checkbox` - 复选框列表
- `quote` - 引用
- `code` - 代码块
- `table` - 表格
- `divider` - 分割线

#### TextStyleConfig
完整的文本样式配置：
```dart
class TextStyleConfig {
  final double fontSize;
  final FontWeight fontWeight;
  final Color color;
  final String? fontFamily;
  final double? letterSpacing;
  final double? lineHeight;
  final TextAlign textAlign;
  final bool italic;
  final bool underline;
  final bool strikethrough;
  final Color? backgroundColor;
}
```

#### ContentBlock
代表一个可独立渲染的内容单元：
```dart
class ContentBlock {
  final String id;
  final ContentType type;
  final String content;
  final TextStyleConfig style;
  final Map<String, dynamic>? metadata;
  final bool isSelected;
}
```

#### RenderCard
完整的可渲染卡片：
```dart
class RenderCard {
  final String id;
  final String title;
  final List<ContentBlock> blocks;
  final CardConfig config;
  final DateTime createdAt;
  final DateTime updatedAt;
}
```

### 2. 内容编辑器 (`content_editor_page.dart`)

#### 核心功能
- **纯文本编辑**：简单的文本输入界面
- **实时预览**：切换查看解析后的内容结构
- **智能拆分**：在光标位置插入拆分标记
- **内容解析**：自动识别Markdown格式

#### 拆分标记系统
- 预定义标记：`--- 分割线 ---`、`=== 新卡片 ===` 等
- 自定义标记：用户可以输入任意拆分标记
- 可视化提示：显示将生成多少个卡片

### 3. 可视化渲染器 (`visual_renderer_page.dart`)

#### 交互设计
- **点击选择**：点击任意内容块进行选择
- **样式面板**：选中后自动显示样式编辑面板
- **实时预览**：样式修改立即生效
- **多卡片导航**：支持在多个卡片间切换

#### 样式控制
- 文本样式：字体大小、粗细、颜色、对齐等
- 文本装饰：斜体、下划线、删除线
- 背景样式：文本背景色、卡片背景色
- 卡片样式：边框、圆角、内边距、阴影

### 4. 样式面板 (`widgets/style_panel.dart`)

#### 控件类型
- **滑块控制**：字体大小、行高、边框宽度等
- **分段控制**：字体粗细、文本对齐等
- **颜色选择器**：文本颜色、背景颜色等
- **复选框组**：文本装饰选项
- **数值输入**：精确的内边距设置

### 5. 内容块渲染器 (`widgets/content_block_widget.dart`)

#### 渲染策略
- **类型特化**：根据内容类型使用不同的渲染方式
- **样式应用**：将TextStyleConfig转换为Flutter TextStyle
- **交互支持**：支持选择状态和点击事件
- **视觉反馈**：选中时显示蓝色边框和背景

### 6. 导出系统 (`export_system_page.dart`)

#### 导出功能
- **图片捕获**：使用RepaintBoundary捕获卡片为图片
- **批量处理**：支持同时导出多张卡片
- **进度显示**：实时显示导出进度
- **多种输出**：分享和保存到相册

## 使用流程

### 1. 内容创作阶段
```
用户输入/粘贴文本 → 实时预览解析结果 → 添加拆分标记 → 进入渲染器
```

### 2. 样式定制阶段
```
选择内容块 → 打开样式面板 → 调整样式参数 → 实时查看效果
```

### 3. 导出分享阶段
```
预览所有卡片 → 选择导出方式 → 生成高质量图片 → 分享或保存
```

## 技术实现

### 内容解析算法
```dart
class ContentParser {
  static List<ContentBlock> parseText(String text) {
    // 按行解析文本
    // 识别Markdown语法
    // 创建对应的ContentBlock
  }
}
```

### 样式应用机制
```dart
// 样式配置转换为Flutter样式
TextStyle toTextStyle() {
  return TextStyle(
    fontSize: fontSize,
    fontWeight: fontWeight,
    color: color,
    // ... 其他属性
  );
}
```

### 图片导出流程
```dart
// 使用RepaintBoundary捕获Widget为图片
final boundary = key.currentContext?.findRenderObject() as RenderRepaintBoundary;
final image = await boundary.toImage(pixelRatio: 3.0);
final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
```

## 扩展性设计

### 新内容类型
要添加新的内容类型，只需要：
1. 在`ContentType`枚举中添加新类型
2. 在`ContentParser`中添加识别逻辑
3. 在`ContentBlockWidget`中添加渲染逻辑

### 新样式属性
要添加新的样式属性，只需要：
1. 在`TextStyleConfig`中添加新字段
2. 在`StylePanel`中添加对应的控件
3. 在`toTextStyle`方法中应用新属性

### 新导出格式
要支持新的导出格式，只需要：
1. 在`ExportSystemPage`中添加新的导出方法
2. 实现对应的格式转换逻辑

## 性能优化

### 渲染优化
- 使用`RepaintBoundary`隔离重绘区域
- 懒加载样式面板
- 避免不必要的状态更新

### 内存管理
- 及时释放TextEditingController
- 使用const构造函数减少重建
- 合理管理图片资源

## 总结

新的分离式架构很好地解决了之前版本的问题：

1. **编辑器复杂性** → 简单的纯文本编辑
2. **样式耦合问题** → 独立的可视化样式定制
3. **用户体验不佳** → 所见即所得的直观操作
4. **导出功能薄弱** → 完整的导出分享系统

这个设计既满足了用户对简单易用的需求，又提供了强大的定制能力，是一个更加平衡和实用的解决方案。 