# 主题模式实现说明

## 概述

ContentPal 应用现在支持三种主题模式：

1. **浅色模式** (Light Mode) - 明亮的浅色主题
2. **深色模式** (Dark Mode) - 深色护眼主题  
3. **跟随系统** (System Mode) - 自动跟随系统主题设置

## 实现架构

### 1. 设置服务 (SettingsService)
- 位置：`lib/services/settings_service.dart`
- 负责主题设置的持久化存储和管理
- 提供主题变更回调机制

### 2. 应用设置模型 (AppSettings)
- 位置：`lib/config/app_settings.dart`
- 定义主题模式的数据结构
- 默认主题模式：跟随系统

### 3. 主题配置 (AppTheme)
- 位置：`lib/config/app_theme.dart`
- 定义浅色和深色主题的颜色方案
- 包含各种预定义的主题样式

### 4. 主应用 (ContentPalApp)
- 位置：`lib/main.dart`
- 管理全局主题状态
- 监听主题变更并更新UI

### 5. 设置界面 (SettingsScreen)
- 位置：`lib/settings/settings_screen.dart`
- 提供主题选择UI
- 三个主题选项的可视化界面

## 主要功能

### 主题切换
用户可以在设置页面选择以下三种模式：
- 🌞 **浅色** - 适合白天使用的明亮主题
- 🌙 **深色** - 适合夜间使用的深色主题
- 📱 **跟随系统** - 自动跟随设备系统主题设置

### 持久化存储
- 主题设置会自动保存到本地存储
- 应用重启后会恢复用户的主题选择
- 使用 SettingsService 统一管理所有设置

### 实时切换
- 主题变更立即生效，无需重启应用
- 使用回调机制确保主题状态同步
- 所有界面都会自动适应新主题

## 技术实现

### 回调机制
```dart
// 设置主题变更回调
ServiceLocator().settingsService.setThemeChangeCallback(_updateThemeMode);

// 更新主题模式
Future<void> updateThemeMode(ThemeMode themeMode) async {
  await updateSettings(themeMode: themeMode);
}
```

### 主题状态管理
```dart
// 在 main.dart 中
ThemeMode _themeMode = ThemeMode.system;

// 更新主题
void _updateThemeMode(ThemeMode themeMode) {
  setState(() {
    _themeMode = themeMode;
  });
}
```

### UI 组件
```dart
// 主题选项构建
_buildThemeOption(
  '跟随系统',
  Icons.phone_android,
  _themeMode == ThemeMode.system,
  () {
    setState(() {
      _themeMode = ThemeMode.system;
    });
    _saveThemeMode(ThemeMode.system);
  },
),
```

## 使用说明

1. 打开应用主页
2. 点击右上角设置图标
3. 在"应用设置"部分找到主题选择
4. 选择您偏好的主题模式：
   - 浅色：适合光线充足的环境
   - 深色：适合低光环境，减少眼部疲劳
   - 跟随系统：自动适应设备设置

## 扩展性

该主题系统设计具有良好的扩展性：
- 可以轻松添加新的主题样式
- 支持自定义颜色方案
- 可以集成动态主题功能
- 支持主题预览功能

## 注意事项

- 主题切换会影响整个应用的视觉风格
- 跟随系统模式需要设备系统支持
- 主题设置会在应用更新后保持