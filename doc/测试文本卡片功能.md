# 文本卡片功能测试指南

## 🧪 测试目标

验证修复后的文本卡片功能的三个关键问题：
1. **图片导出功能** - 卡片能否成功保存到相册
2. **内容库存储格式** - 卡片数据是否正确存储
3. **内容库导航** - 点击卡片是否正确跳转到编辑器

## 📱 测试步骤

### 第一步：创建文本卡片

1. **打开应用**
   - 启动 ContentPal 应用
   - 导航到"文本卡片"模块

2. **创建新卡片**
   - 点击"创建卡片"按钮
   - 应该看到现代风格的三步式创建器

3. **编辑内容**
   - 在编辑页面输入测试内容：
   ```
   测试卡片标题
   
   这是一个测试文本卡片的内容。
   
   功能测试包括：
   • 图片导出功能
   • 内容库存储
   • 编辑器导航
   
   #测试 #文本卡片
   ```

4. **选择模板**
   - 切换到"模板"标签页
   - 选择"经典风格"模板
   - 验证模板预览效果

5. **预览和导出**
   - 切换到"预览"标签页
   - 点击"导出"按钮
   - **验证点1**: 检查是否显示权限请求
   - **验证点2**: 检查是否显示"图片已成功保存到相册"消息
   - **验证点3**: 打开手机相册，确认图片已保存

6. **创建卡片**
   - 点击"创建卡片"按钮
   - **验证点4**: 检查是否显示成功消息
   - **验证点5**: 检查是否自动跳转到内容库

### 第二步：验证内容库存储

1. **查看内容库**
   - 在内容库中找到刚创建的文本卡片
   - **验证点6**: 卡片标题是否正确显示
   - **验证点7**: 卡片预览是否正常

2. **检查存储格式**
   - 长按卡片查看详情（如果有此功能）
   - **验证点8**: 数据格式是否包含完整的模板信息
   - **验证点9**: 标签是否包含 'text_card'

### 第三步：验证编辑器导航

1. **点击文本卡片**
   - 在内容库中点击刚创建的文本卡片
   - **验证点10**: 是否显示"正在打开文本卡片编辑器..."消息
   - **验证点11**: 是否正确跳转到文本卡片模块

2. **验证编辑功能**
   - 在打开的编辑器中验证功能
   - **验证点12**: 是否能正常编辑文本
   - **验证点13**: 是否能切换模板
   - **验证点14**: 是否能重新导出

## ✅ 预期结果

### 图片导出功能
- [x] 权限请求正常弹出
- [x] 导出过程有加载指示器
- [x] 成功消息正确显示
- [x] 图片保存到相册
- [x] 图片质量清晰（3倍像素密度）

### 内容库存储格式
- [x] 卡片数据包含完整模板信息
- [x] JSON格式正确序列化
- [x] 标签包含 'text_card'
- [x] 标题正确提取
- [x] 创建时间记录

### 内容库导航
- [x] 正确识别文本卡片类型
- [x] 点击跳转到文本卡片模块
- [x] 编辑器能正常工作
- [x] 支持重新编辑和导出

## 🐛 可能的问题和解决方案

### 问题1：权限被拒绝
**现象**: 导出时显示"权限不足"错误
**解决**: 
1. 检查iOS设置 > 隐私与安全 > 照片 > ContentPal
2. 确保权限设置为"所有照片"

### 问题2：导出失败
**现象**: 显示"截图失败"或"保存失败"
**解决**:
1. 重启应用重试
2. 检查设备存储空间
3. 检查网络连接

### 问题3：内容库显示异常
**现象**: 卡片数据格式错误或显示异常
**解决**:
1. 清除应用数据重新创建
2. 检查JSON序列化是否正确

### 问题4：导航失败
**现象**: 点击卡片没有反应或跳转错误
**解决**:
1. 检查卡片标签是否包含'text_card'
2. 验证内容格式是否正确

## 📊 测试报告模板

```
测试日期: ___________
测试设备: ___________
应用版本: ___________

功能测试结果:
□ 图片导出功能 - 通过/失败
□ 内容库存储格式 - 通过/失败  
□ 内容库导航 - 通过/失败

详细问题记录:
1. ___________
2. ___________
3. ___________

总体评价: ___________
```

## 🎯 成功标准

所有14个验证点都通过，即可认为文本卡片功能修复成功：
- 图片能成功导出到相册
- 卡片数据正确存储到内容库
- 从内容库能正确打开编辑器
- 整个工作流程流畅无阻

## 📝 注意事项

1. **首次使用**: 需要授予相册权限
2. **网络环境**: 某些功能可能需要网络连接
3. **设备兼容**: 在不同iOS版本上测试
4. **性能测试**: 创建多个卡片测试性能
5. **边界测试**: 测试极长文本和特殊字符
