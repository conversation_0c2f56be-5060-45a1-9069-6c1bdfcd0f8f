# ContentPal API文档

## 服务层API

### ServiceLocator (服务定位器)

#### 类定义
```dart
class ServiceLocator {
  static final ServiceLocator _instance = ServiceLocator._internal();
  factory ServiceLocator() => _instance;
  ServiceLocator._internal();
}
```

#### 主要方法
```dart
/// 初始化所有服务
Future<void> initServices() async

/// 获取全局导航键
GlobalKey<NavigatorState> get globalKey

/// 注册服务
void registerService<T>(T service)

/// 获取服务
T getService<T>()
```

### ContentService (内容服务)

#### 类定义
```dart
class ContentService {
  final StorageService _storageService;
  final Box<ContentItem> _contentBox;
}
```

#### 主要方法
```dart
/// 创建新内容
Future<ContentItem> createContent({
  required String title,
  required String content,
  required ContentType type,
  String? description,
}) async

/// 获取所有内容
Future<List<ContentItem>> getAllContent() async

/// 根据类型获取内容
Future<List<ContentItem>> getContentByType(ContentType type) async

/// 更新内容
Future<void> updateContent(ContentItem item) async

/// 删除内容
Future<void> deleteContent(String id) async

/// 搜索内容
Future<List<ContentItem>> searchContent(String query) async
```

### StorageService (存储服务)

#### 类定义
```dart
class StorageService {
  late Box<dynamic> _mainBox;
  late SharedPreferences _prefs;
}
```

#### 主要方法
```dart
/// 初始化存储
Future<void> initialize() async

/// 保存数据
Future<void> saveData<T>(String key, T value) async

/// 获取数据
T? getData<T>(String key)

/// 删除数据
Future<void> deleteData(String key) async

/// 清空所有数据
Future<void> clearAll() async
```

### AudioRecorderService (音频录制服务)

#### 类定义
```dart
class AudioRecorderService {
  FlutterSoundRecorder? _recorder;
  String? _recordingPath;
}
```

#### 主要方法
```dart
/// 初始化录音器
Future<void> initialize() async

/// 开始录音
Future<void> startRecording() async

/// 停止录音
Future<String?> stopRecording() async

/// 暂停录音
Future<void> pauseRecording() async

/// 恢复录音
Future<void> resumeRecording() async

/// 获取录音状态
bool get isRecording

/// 获取录音时长
Duration get recordingDuration
```

### TTSService (文字转语音服务)

#### 类定义
```dart
class TTSService {
  FlutterTts? _tts;
  String _currentLanguage = 'zh-CN';
}
```

#### 主要方法
```dart
/// 初始化TTS
Future<void> initialize() async

/// 设置语言
Future<void> setLanguage(String language) async

/// 设置语速
Future<void> setSpeechRate(double rate) async

/// 设置音量
Future<void> setVolume(double volume) async

/// 播放文本
Future<void> speak(String text) async

/// 停止播放
Future<void> stop() async

/// 暂停播放
Future<void> pause() async

/// 恢复播放
Future<void> resume() async

/// 获取可用语言列表
Future<List<Map<String, String>>> getLanguages() async
```

### FileIntentService (文件意图处理服务)

#### 类定义
```dart
class FileIntentService {
  final ContentService _contentService;
}
```

#### 主要方法
```dart
/// 处理文件打开意图
Future<void> handleFileOpen(BuildContext context, String filePath) async

/// 处理文本内容意图
Future<void> handleTextContent(
  BuildContext context, 
  String text, 
  {String? title}
) async

/// 检查文件类型
ContentType _getContentType(String filePath)

/// 读取文件内容
Future<String> _readFileContent(String filePath) async
```

## 模型类API

### ContentItem (内容项目)

#### 类定义
```dart
@HiveType(typeId: 0)
class ContentItem {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String content;

  @HiveField(3)
  final ContentType type;

  @HiveField(4)
  final DateTime createdAt;

  @HiveField(5)
  final DateTime updatedAt;

  @HiveField(6)
  String? description;
}
```

#### 构造函数
```dart
ContentItem({
  required this.id,
  required this.title,
  required this.content,
  required this.type,
  required this.createdAt,
  required this.updatedAt,
  this.description,
});
```

#### 方法
```dart
/// 创建副本
ContentItem copyWith({
  String? id,
  String? title,
  String? content,
  ContentType? type,
  DateTime? createdAt,
  DateTime? updatedAt,
  String? description,
})

/// 转换为Map
Map<String, dynamic> toMap()

/// 从Map创建
factory ContentItem.fromMap(Map<String, dynamic> map)
```

### VoiceRecord (语音记录)

#### 类定义
```dart
@HiveType(typeId: 1)
class VoiceRecord {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String title;

  @HiveField(2)
  final String filePath;

  @HiveField(3)
  final Duration duration;

  @HiveField(4)
  final DateTime createdAt;

  @HiveField(5)
  String? transcription;
}
```

#### 构造函数
```dart
VoiceRecord({
  required this.id,
  required this.title,
  required this.filePath,
  required this.duration,
  required this.createdAt,
  this.transcription,
});
```

## Markdown模块API

### MarkdownRenderController (Markdown渲染控制器)

#### 类定义
```dart
class MarkdownRenderController extends ChangeNotifier {
  String _content = '';
  MarkdownRenderStyle _style = MarkdownRenderStyle.defaultStyle();
  MarkdownTemplate _template = MarkdownTemplate.defaultTemplate();
  MarkdownWatermark? _watermark;
}
```

#### 主要方法
```dart
/// 设置内容
void setContent(String content)

/// 设置样式
void setStyle(MarkdownRenderStyle style)

/// 设置模板
void setTemplate(MarkdownTemplate template)

/// 设置水印
void setWatermark(MarkdownWatermark? watermark)

/// 获取渲染后的HTML
Future<String> getRenderedHtml() async

/// 导出为PDF
Future<Uint8List> exportToPdf() async

/// 导出为图片
Future<Uint8List> exportToImage() async
```

### MarkdownSectionService (章节服务)

#### 类定义
```dart
class MarkdownSectionService {
  List<MarkdownSection> _sections = [];
}
```

#### 主要方法
```dart
/// 解析Markdown为章节
List<MarkdownSection> parseSections(String markdown)

/// 添加章节
void addSection(MarkdownSection section)

/// 更新章节
void updateSection(int index, MarkdownSection section)

/// 删除章节
void removeSection(int index)

/// 重新排序章节
void reorderSections(int oldIndex, int newIndex)

/// 合并章节
void mergeSections(int startIndex, int endIndex)

/// 分割章节
void splitSection(int index, int splitPoint)
```

## PDF模块API

### PDFService (PDF服务)

#### 类定义
```dart
class PDFService {
  final PdfDocument _document;
}
```

#### 主要方法
```dart
/// 打开PDF文件
Future<PdfDocument> openPdf(String filePath) async

/// 保存PDF文件
Future<void> savePdf(String filePath) async

/// 添加文本注释
Future<void> addTextAnnotation(
  int pageIndex,
  Offset position,
  String text,
) async

/// 添加图形注释
Future<void> addShapeAnnotation(
  int pageIndex,
  PdfAnnotation annotation,
) async

/// 提取文本
Future<String> extractText() async

/// 转换为Markdown
Future<String> convertToMarkdown() async

/// 转换为HTML
Future<String> convertToHtml() async
```

### PdfDocument (PDF文档模型)

#### 类定义
```dart
class PdfDocument {
  final String id;
  final String filePath;
  final String fileName;
  final int pageCount;
  final DateTime lastModified;
  final List<PdfPage> pages;
}
```

#### 主要方法
```dart
/// 获取页面
PdfPage getPage(int index)

/// 获取页面缩略图
Future<Uint8List> getPageThumbnail(int index) async

/// 搜索文本
List<PdfSearchResult> searchText(String query)

/// 获取文档信息
Map<String, dynamic> getDocumentInfo()
```

## 文本卡片模块API

### TextCardModel (文本卡片模型)

#### 类定义
```dart
class TextCardModel {
  final String id;
  final String title;
  final String content;
  final CardTemplate template;
  final CardStyle style;
  final DateTime createdAt;
  final DateTime updatedAt;
}
```

#### 主要方法
```dart
/// 创建副本
TextCardModel copyWith({
  String? id,
  String? title,
  String? content,
  CardTemplate? template,
  CardStyle? style,
  DateTime? createdAt,
  DateTime? updatedAt,
})

/// 验证卡片
bool isValid()

/// 获取预览图片
Future<Uint8List> generatePreview() async
```

### DocumentModel (文档模型)

#### 类定义
```dart
class DocumentModel {
  final String id;
  final String title;
  final String content;
  final List<TextCardModel> cards;
  final DateTime createdAt;
}
```

#### 主要方法
```dart
/// 分割文档为卡片
List<TextCardModel> splitIntoCards({
  int maxCardsPerPage = 1,
  int maxCharactersPerCard = 500,
})

/// 合并卡片为文档
String mergeCardsToDocument()

/// 导出为PDF
Future<Uint8List> exportToPdf() async

/// 导出为图片
Future<List<Uint8List>> exportToImages() async
```

## 工具类API

### PermissionHelper (权限助手)

#### 类定义
```dart
class PermissionHelper {
  static const MethodChannel _channel = MethodChannel('permissions');
}
```

#### 主要方法
```dart
/// 检查iOS权限
static Future<void> checkIosPermissions() async

/// 请求麦克风权限
static Future<bool> requestMicrophonePermission() async

/// 请求存储权限
static Future<bool> requestStoragePermission() async

/// 请求相机权限
static Future<bool> requestCameraPermission() async

/// 检查权限状态
static Future<PermissionStatus> checkPermission(Permission permission)
```

### SubscriptionHelper (订阅助手)

#### 类定义
```dart
class SubscriptionHelper {
  static const MethodChannel _channel = MethodChannel('subscription');
}
```

#### 主要方法
```dart
/// 检查订阅状态
static Future<bool> checkSubscriptionStatus() async

/// 获取订阅信息
static Future<SubscriptionInfo?> getSubscriptionInfo() async

/// 购买订阅
static Future<bool> purchaseSubscription(String productId) async

/// 恢复购买
static Future<bool> restorePurchases() async

/// 验证收据
static Future<bool> validateReceipt(String receipt) async
```

## 错误处理

### 自定义异常类

```dart
/// 内容服务异常
class ContentServiceException implements Exception {
  final String message;
  final String? code;
  
  ContentServiceException(this.message, [this.code]);
}

/// 存储服务异常
class StorageServiceException implements Exception {
  final String message;
  final String? code;
  
  StorageServiceException(this.message, [this.code]);
}

/// 文件处理异常
class FileProcessingException implements Exception {
  final String message;
  final String filePath;
  
  FileProcessingException(this.message, this.filePath);
}
```

### 错误处理模式

```dart
/// 统一错误处理
class ErrorHandler {
  static void handleError(dynamic error, StackTrace? stackTrace) {
    if (error is ContentServiceException) {
      // 处理内容服务错误
      debugPrint('ContentService Error: ${error.message}');
    } else if (error is StorageServiceException) {
      // 处理存储服务错误
      debugPrint('StorageService Error: ${error.message}');
    } else {
      // 处理通用错误
      debugPrint('General Error: $error');
    }
  }
}
```

## 使用示例

### 创建内容
```dart
final contentService = ServiceLocator().getService<ContentService>();

try {
  final content = await contentService.createContent(
    title: '测试内容',
    content: '这是测试内容',
    type: ContentType.markdown,
    description: '测试描述',
  );
  print('内容创建成功: ${content.id}');
} catch (e) {
  print('创建失败: $e');
}
```

### 录制音频
```dart
final recorder = ServiceLocator().getService<AudioRecorderService>();

await recorder.initialize();
await recorder.startRecording();

// 停止录音
final filePath = await recorder.stopRecording();
if (filePath != null) {
  print('录音保存到: $filePath');
}
```

### 渲染Markdown
```dart
final controller = MarkdownRenderController();
controller.setContent('# 标题\n这是内容');
controller.setStyle(MarkdownRenderStyle.defaultStyle());

final html = await controller.getRenderedHtml();
print('渲染结果: $html');
``` 