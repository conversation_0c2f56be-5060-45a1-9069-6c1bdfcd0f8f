# ContentPal 开发指南

## 开发环境搭建

### 1. 系统要求
- **操作系统**: macOS 12.0+, Windows 10+, Ubuntu 18.04+
- **内存**: 8GB RAM (推荐16GB)
- **存储**: 至少10GB可用空间

### 2. 必需软件

#### Flutter SDK
```bash
# 下载并安装Flutter SDK
# 访问 https://flutter.dev/docs/get-started/install

# 验证安装
flutter doctor
```

#### IDE推荐
- **VS Code** (推荐)
  - 安装Flutter和Dart插件
  - 安装Flutter Widget Snippets插件
- **Android Studio**
  - 安装Flutter和Dart插件
  - 配置Android SDK

#### 其他工具
- **Git**: 版本控制
- **Xcode**: iOS开发 (仅macOS)
- **Android Studio**: Android开发

### 3. 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd contentpal

# 安装依赖
flutter pub get

# 运行项目
flutter run
```

## 代码规范

### 1. Dart代码规范

#### 命名规范
```dart
// 类名使用大驼峰命名
class ContentService {}

// 变量和方法使用小驼峰命名
String contentTitle;
void processContent() {}

// 常量使用小写加下划线
const String app_name = 'ContentPal';

// 私有成员以下划线开头
class _PrivateClass {}
String _privateVariable;
```

#### 文件组织
```dart
// 导入顺序
// 1. Dart标准库
import 'dart:async';
import 'dart:io';

// 2. Flutter库
import 'package:flutter/material.dart';

// 3. 第三方包
import 'package:provider/provider.dart';

// 4. 项目内部文件
import '../models/content_item.dart';
import '../services/content_service.dart';
```

#### 注释规范
```dart
/// 类级别的文档注释
/// 描述类的主要功能和用途
class ContentManager {
  /// 方法级别的文档注释
  /// 描述方法的功能、参数和返回值
  /// 
  /// [content] 要处理的内容
  /// [options] 处理选项
  /// 返回处理后的内容
  Future<String> processContent(String content, Map<String, dynamic> options) async {
    // 单行注释用于解释代码逻辑
    final result = await _service.process(content);
    return result;
  }
}
```

### 2. Flutter Widget规范

#### Widget结构
```dart
class MyWidget extends StatefulWidget {
  // 构造函数
  const MyWidget({
    super.key,
    required this.title,
    this.subtitle,
  });

  // 必需参数
  final String title;
  
  // 可选参数
  final String? subtitle;

  @override
  State<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  // 状态变量
  bool _isLoading = false;
  String _content = '';

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void dispose() {
    // 清理资源
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    return ListView(
      children: [
        Text(_content),
      ],
    );
  }
}
```

### 3. 状态管理规范

#### 使用Provider
```dart
// 创建Provider
class ContentProvider extends ChangeNotifier {
  List<ContentItem> _items = [];
  
  List<ContentItem> get items => _items;
  
  void addItem(ContentItem item) {
    _items.add(item);
    notifyListeners();
  }
}

// 在Widget中使用
class ContentList extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<ContentProvider>(
      builder: (context, provider, child) {
        return ListView.builder(
          itemCount: provider.items.length,
          itemBuilder: (context, index) {
            return ContentItemWidget(provider.items[index]);
          },
        );
      },
    );
  }
}
```

## 调试技巧

### 1. 日志调试
```dart
import 'package:flutter/foundation.dart';

// 使用debugPrint进行调试输出
debugPrint('调试信息: $variable');

// 条件调试输出
if (kDebugMode) {
  print('仅在调试模式下输出');
}
```

### 2. 性能调试
```dart
// 使用Flutter Inspector
// 在VS Code中按F5启动调试模式

// 性能分析
import 'package:flutter/rendering.dart';

// 开启性能覆盖层
debugPaintSizeEnabled = true;
debugPaintBaselinesEnabled = true;
```

### 3. 网络调试
```dart
// 使用Charles或Fiddler进行网络请求调试
// 在代码中添加详细的网络日志
try {
  final response = await http.get(url);
  debugPrint('网络请求成功: ${response.statusCode}');
} catch (e) {
  debugPrint('网络请求失败: $e');
}
```

## 测试指南

### 1. 单元测试
```dart
// test/services/content_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/services/content_service.dart';

void main() {
  group('ContentService Tests', () {
    late ContentService service;

    setUp(() {
      service = ContentService();
    });

    test('should create content successfully', () async {
      final result = await service.createContent('Test Content');
      expect(result, isNotNull);
      expect(result.title, 'Test Content');
    });
  });
}
```

### 2. Widget测试
```dart
// test/widgets/content_widget_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/widgets/content_widget.dart';

void main() {
  testWidgets('ContentWidget displays content correctly', (tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: ContentWidget(content: 'Test Content'),
      ),
    );

    expect(find.text('Test Content'), findsOneWidget);
  });
}
```

### 3. 集成测试
```dart
// integration_test/app_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:contentpal/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('App Test', () {
    testWidgets('full app test', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // 测试应用启动
      expect(find.text('ContentPal'), findsOneWidget);
    });
  });
}
```

## 发布指南

### 1. Android发布
```bash
# 构建APK
flutter build apk --release

# 构建App Bundle
flutter build appbundle --release

# 签名配置在android/app/build.gradle中
```

### 2. iOS发布
```bash
# 构建iOS应用
flutter build ios --release

# 使用Xcode进行签名和上传
```

### 3. Web发布
```bash
# 构建Web版本
flutter build web --release

# 部署到服务器
```

## 常见问题解决

### 1. 依赖冲突
```bash
# 清理依赖缓存
flutter clean
flutter pub get

# 查看依赖树
flutter pub deps
```

### 2. 编译错误
```bash
# 分析代码问题
flutter analyze

# 格式化代码
flutter format lib/

# 修复lint问题
flutter fix --apply
```

### 3. 性能问题
- 使用`const`构造函数
- 避免在build方法中创建对象
- 使用`ListView.builder`处理长列表
- 合理使用`setState`

## 开发工具推荐

### 1. VS Code插件
- Flutter
- Dart
- Flutter Widget Snippets
- Error Lens
- GitLens

### 2. 调试工具
- Flutter Inspector
- Dart DevTools
- Charles Proxy (网络调试)

### 3. 代码质量工具
- dart_code_metrics
- flutter_lints
- very_good_analysis

## 团队协作

### 1. Git工作流
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交代码
git add .
git commit -m "feat: 添加新功能"

# 推送到远程
git push origin feature/new-feature

# 创建Pull Request
```

### 2. 代码审查
- 使用Pull Request进行代码审查
- 确保代码符合规范
- 添加适当的测试
- 更新相关文档

### 3. 版本管理
- 使用语义化版本号
- 维护CHANGELOG.md
- 定期发布版本 