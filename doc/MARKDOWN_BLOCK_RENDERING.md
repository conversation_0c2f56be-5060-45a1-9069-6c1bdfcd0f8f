# Markdown 分块渲染功能

## 功能概述

Markdown 分块渲染是 ContentPal 的一个独立功能模块，允许用户将长篇 Markdown 文档分割成多个可管理的块，每个块可以独立显示、隐藏和操作。

## 主要特性

### 1. 自定义分隔标记渲染
- 支持连续短横线（`---`）等自定义分隔符
- 可配置正则表达式模式来定义分隔符
- 默认支持标准 Markdown 水平分割线

### 2. 标题分隔
- **一级标题分隔**：使用 `# 标题` 作为分块边界
- **二级标题分隔**：使用 `## 标题` 作为分块边界
- 可以同时启用多种分隔方式

### 3. 交互式操纵杆
- **可拖拽分隔杆**：在预览区域显示可移动的横向操纵杆
- **实时分块调整**：拖拽操纵杆可以实时重新分块
- **添加新分隔杆**：点击预览区域可以添加新的分隔位置
- **删除分隔杆**：悬停时显示删除按钮

### 4. 独立模块设计
- 与现有 Markdown 模块完全独立
- 不影响原有的渲染功能
- 可以随时启用或禁用分块模式

## 文件结构

```
lib/markdown/
├── models/
│   └── markdown_block.dart              # 分块数据模型
├── services/
│   └── markdown_block_service.dart      # 分块处理服务
├── controllers/
│   └── markdown_block_controller.dart   # 分块渲染控制器
├── widgets/
│   ├── markdown_block_renderer.dart     # 主要分块渲染组件
│   ├── markdown_block_divider.dart      # 可移动分隔操纵杆组件
│   └── markdown_block_config_panel.dart # 配置面板组件
└── screens/
    └── markdown_block_demo_screen.dart  # 演示页面
```

## 使用方法

### 1. 基本使用

```dart
import 'package:contentpal/markdown/controllers/markdown_block_controller.dart';
import 'package:contentpal/markdown/widgets/markdown_block_renderer.dart';
import 'package:contentpal/markdown/models/markdown_block.dart';

// 创建控制器
final controller = MarkdownBlockController();

// 初始化
controller.initialize(
  markdownText: yourMarkdownText,
  config: const BlockRenderConfig(
    enabled: true,
    splitByH1: true,
    splitByH2: false,
  ),
);

// 在 Widget 中使用
MarkdownBlockRenderer(
  controller: controller,
  template: yourTemplate,
  selectable: true,
)
```

### 2. 配置选项

```dart
const config = BlockRenderConfig(
  enabled: true,                    // 启用分块渲染
  showDividers: true,              // 显示分隔操纵杆
  allowDragDividers: true,         // 允许拖拽操纵杆
  splitByH1: true,                 // 按一级标题分隔
  splitByH2: false,                // 按二级标题分隔
  customSeparatorPattern: r'^\s*[-*_]{3,}\s*$', // 自定义分隔符
  blockSpacing: 16.0,              // 分块间距
  dividerHeight: 4.0,              // 操纵杆高度
  dividerColor: Colors.blue,       // 操纵杆颜色
);
```

### 3. 在现有 Markdown 渲染中集成

分块功能已经集成到现有的 `MarkdownRenderTabs` 中：

1. 在 Markdown 渲染页面中，会看到新增的"分块"标签页
2. 在"编辑"标签页中，可以通过模式切换按钮在普通模式和分块模式之间切换
3. 所有其他标签页（模板、样式、水印）都支持分块预览

## API 参考

### MarkdownBlockController

主要的分块渲染控制器，管理分块状态和操作。

#### 主要方法

```dart
// 初始化
void initialize({
  required String markdownText,
  BlockRenderConfig? config,
});

// 更新文本
void updateMarkdownText(String text);

// 更新配置
void updateConfig(BlockRenderConfig config);

// 添加分隔杆
void addDivider(double yPosition);

// 移动分隔杆
void moveDivider(String dividerId, double newYPosition);

// 删除分隔杆
void removeDivider(String dividerId);

// 切换分块可见性
void toggleBlockVisibility(String blockId);

// 获取合并后的文本
String getCombinedMarkdown();
```

#### 主要属性

```dart
List<MarkdownBlock> get blocks;        // 所有分块
List<MarkdownBlock> get visibleBlocks; // 可见分块
List<BlockDivider> get dividers;       // 分隔杆列表
BlockRenderConfig get config;          // 当前配置
bool get isLoading;                    // 是否正在加载
```

### MarkdownBlockService

分块处理的核心服务类。

#### 主要方法

```dart
// 根据配置分块
List<MarkdownBlock> splitMarkdownIntoBlocks(
  String markdownText,
  BlockRenderConfig config,
);

// 根据手动分隔位置分块
List<MarkdownBlock> splitByManualDividers(
  String markdownText,
  List<int> dividerPositions,
);

// 合并分块为文本
String combineBlocksToMarkdown(List<MarkdownBlock> blocks);

// 位置计算
double calculateScreenPosition(String text, int textPosition, double lineHeight);
int calculateTextPosition(String text, double screenPosition, double lineHeight);
```

## 分块类型

系统支持四种分块类型，用不同颜色区分：

1. **一级标题分块**（蓝色）：由 `# 标题` 分隔
2. **二级标题分块**（绿色）：由 `## 标题` 分隔  
3. **自定义分隔符分块**（橙色）：由自定义分隔符（如 `---`）分隔
4. **手动分块**（灰色）：由用户拖拽操纵杆创建

## 交互操作

### 添加分隔杆
- 在预览区域的任意位置点击
- 系统会自动将分隔位置调整到最近的行边界

### 移动分隔杆
- 将鼠标悬停在分隔杆上，会显示拖拽手柄
- 拖拽分隔杆到新位置
- 释放后会重新计算分块

### 删除分隔杆
- 悬停在分隔杆上时，右侧会显示删除按钮
- 点击删除按钮移除分隔杆

### 隐藏/显示分块
- 点击分块标题栏右侧的眼睛图标
- 隐藏的分块不会出现在最终的合并文本中

## 演示页面

运行演示页面来体验完整功能：

```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const MarkdownBlockDemoScreen(),
  ),
);
```

演示页面包含：
- 左侧：配置面板和文本编辑器
- 右侧：实时预览区域
- 完整的交互功能演示

## 测试

运行测试来验证功能：

```bash
flutter test test/markdown_block_test.dart
```

测试覆盖：
- 分块服务的各种分隔方式
- 位置计算的准确性
- 数据模型的序列化
- 配置选项的正确性

## 注意事项

1. **性能**：大文档分块时可能需要一些时间，建议显示加载指示器
2. **位置精度**：分隔杆位置会自动调整到行边界，确保分块的完整性
3. **兼容性**：分块功能与现有的模板、样式、水印功能完全兼容
4. **状态管理**：使用 `ChangeNotifier` 进行状态管理，确保 UI 实时更新

## 扩展功能

未来可以考虑添加的功能：

1. **分块导出**：单独导出特定分块
2. **分块重排**：拖拽改变分块顺序
3. **分块模板**：为不同类型的分块应用不同模板
4. **分块标签**：为分块添加标签和分类
5. **分块搜索**：在分块中搜索特定内容

## 故障排除

### 常见问题

1. **分隔杆不显示**
   - 检查 `showDividers` 配置是否为 `true`
   - 确保分块模式已启用

2. **拖拽不工作**
   - 检查 `allowDragDividers` 配置
   - 确保在正确的区域进行拖拽操作

3. **分块不正确**
   - 检查分隔符配置是否正确
   - 验证正则表达式模式是否有效

4. **性能问题**
   - 对于大文档，考虑限制分块数量
   - 使用 `RepaintBoundary` 优化渲染性能
