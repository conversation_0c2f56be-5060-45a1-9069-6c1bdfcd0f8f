# ContentPal 功能模块详解

## 1. 内容管理模块 (content/)

### 功能概述
内容管理模块是应用的核心模块，负责管理所有类型的内容项目，包括创建、编辑、保存和导出功能。

### 主要文件
- `content_home_page.dart`: 内容管理主页
- `content_editor_page.dart`: 内容编辑器页面
- `content_item_card.dart`: 内容项目卡片组件
- `content_save_button.dart`: 内容保存按钮组件
- `save_content_page.dart`: 内容保存页面

### 核心功能
- **内容列表展示**: 网格布局展示所有内容项目
- **内容创建**: 支持多种内容类型创建
- **内容编辑**: 富文本编辑器功能
- **内容保存**: 本地存储和云端同步
- **内容导出**: 支持多种格式导出

## 2. Markdown模块 (markdown/)

### 功能概述
提供完整的Markdown文档处理功能，包括编辑、预览、样式定制和导出。

### 主要文件
- `markdown_render_screen.dart`: Markdown渲染主屏幕
- `markdown_render_controller.dart`: 渲染控制器
- `markdown_section_service.dart`: 章节管理服务
- `html_renderer_service.dart`: HTML渲染服务
- `markdown_export_service.dart`: 导出服务

### 核心功能
- **实时预览**: 编辑时实时显示渲染结果
- **样式定制**: 多种主题和样式选择
- **水印功能**: 支持添加自定义水印
- **章节管理**: 支持文档分章节编辑
- **导出功能**: 支持PDF、HTML、图片等格式导出

### 模型文件
- `markdown_template.dart`: Markdown模板模型
- `markdown_section.dart`: 章节模型
- `markdown_render_style.dart`: 渲染样式模型
- `html_template.dart`: HTML模板模型

## 3. HTML模块 (html/)

### 功能概述
提供HTML代码编辑、格式化和预览功能。

### 主要文件
- `html_manager_screen.dart`: HTML管理主屏幕
- `html_editor_screen.dart`: HTML编辑器
- `html_service.dart`: HTML处理服务
- `html_document.dart`: HTML文档模型

### 核心功能
- **代码编辑器**: 语法高亮和自动补全
- **代码格式化**: 自动格式化HTML代码
- **实时预览**: WebView预览HTML效果
- **代码优化**: 代码压缩和优化

## 4. SVG模块 (svg/)

### 功能概述
提供SVG图标编辑、优化和转换功能。

### 主要文件
- `svg_manager_screen.dart`: SVG管理主屏幕
- `svg_editor_screen.dart`: SVG编辑器
- `svg_service.dart`: SVG处理服务
- `svg_document.dart`: SVG文档模型

### 核心功能
- **SVG查看器**: 高质量SVG渲染
- **图标优化**: 自动优化SVG文件大小
- **格式转换**: 支持转换为PNG、JPG等格式
- **批量处理**: 支持批量SVG处理

## 5. PDF模块 (pdf/)

### 功能概述
提供PDF文档查看、注释和编辑功能。

### 主要文件
- `pdf_manager_screen.dart`: PDF管理主屏幕
- `pdf_viewer_screen.dart`: PDF查看器
- `pdf_service.dart`: PDF处理服务
- `pdf_document.dart`: PDF文档模型
- `pdf_annotation.dart`: PDF注释模型

### 核心功能
- **PDF查看**: 高质量PDF文档查看
- **文档注释**: 支持添加文本、图形注释
- **页面导航**: 快速页面跳转和缩略图
- **文档搜索**: 全文搜索功能
- **格式转换**: PDF转其他格式

## 6. 语音模块 (voice/)

### 功能概述
提供语音录制、播放和语音转文字功能。

### 主要文件
- `voice_home_page.dart`: 语音功能主页
- `voice_recorder_page.dart`: 语音录制页面
- `voice_record_list_page.dart`: 录音列表页面
- `voice_record_detail_page.dart`: 录音详情页面
- `tts_player_page.dart`: 文字转语音播放页面

### 核心功能
- **语音录制**: 高质量音频录制
- **录音管理**: 录音文件列表和分类
- **语音转文字**: 支持多语言语音识别
- **文字转语音**: TTS语音合成
- **音频编辑**: 基础音频编辑功能

### 相关服务
- `audio_recorder_service.dart`: 音频录制服务
- `audio_player_service.dart`: 音频播放服务
- `speech_service.dart`: 语音识别服务
- `tts_service.dart`: 文字转语音服务
- `voice_record_storage_service.dart`: 录音存储服务

## 7. 文本卡片模块 (text_cards/)

### 功能概述
提供文本卡片创建、模板管理和可视化渲染功能。

### 主要文件
- `text_cards_home_page.dart`: 文本卡片主页
- `text_card_editor_page.dart`: 卡片编辑器
- `document_cards_page.dart`: 文档卡片页面
- `visual_renderer_page.dart`: 可视化渲染页面
- `card_export_page.dart`: 卡片导出页面

### 核心功能
- **卡片创建**: 支持多种卡片模板
- **模板系统**: 丰富的卡片模板库
- **可视化渲染**: 实时预览卡片效果
- **批量导出**: 支持批量导出为图片
- **文档分割**: 自动文档分割功能

### 模型文件
- `text_card_model.dart`: 文本卡片模型
- `document_model.dart`: 文档模型
- `content_models.dart`: 内容相关模型

### 组件文件
- `text_card_widget.dart`: 文本卡片组件
- `card_template_selector.dart`: 模板选择器
- `rich_text_renderer.dart`: 富文本渲染器
- `floating_style_toolbar.dart`: 浮动样式工具栏

## 8. 设置模块 (settings/)

### 功能概述
提供应用设置和配置管理功能。

### 主要文件
- `settings_screen.dart`: 设置主页面

### 核心功能
- **主题设置**: 明暗主题切换
- **语言设置**: 多语言支持
- **存储管理**: 本地存储空间管理
- **权限管理**: 应用权限设置
- **关于应用**: 应用信息展示

## 9. 订阅模块 (subscription/)

### 功能概述
提供应用内购买和订阅管理功能。

### 主要文件
- `subscription_screen.dart`: 订阅主页面
- `subscription_service.dart`: 订阅服务
- `subscription_model.dart`: 订阅模型
- `subscription_settings_screen.dart`: 订阅设置页面
- `subscription_debug_screen.dart`: 订阅调试页面

### 核心功能
- **订阅管理**: 订阅状态管理
- **购买功能**: 应用内购买
- **价格展示**: 订阅价格和功能对比
- **订阅恢复**: 跨设备订阅恢复

## 10. 公共组件 (common/)

### 功能概述
提供应用内通用的工具类和组件。

### 主要文件
- `permission_helper.dart`: 权限管理工具
- `subscription_helper.dart`: 订阅管理工具
- `app_loading_indicator.dart`: 加载指示器组件

### 核心功能
- **权限管理**: 统一权限请求和处理
- **订阅验证**: 订阅状态验证
- **UI组件**: 通用UI组件库

## 11. 服务层 (services/)

### 功能概述
提供应用的核心业务逻辑服务。

### 主要服务
- `service_locator.dart`: 依赖注入容器
- `content_service.dart`: 内容管理服务
- `storage_service.dart`: 数据存储服务
- `settings_service.dart`: 设置管理服务
- `file_intent_service.dart`: 文件意图处理服务

### 核心功能
- **依赖注入**: 服务注册和获取
- **数据持久化**: 本地数据存储
- **文件处理**: 文件打开和处理
- **设置管理**: 应用设置持久化

## 模块间关系

```
主页面 (home.dart)
    ↓
功能模块导航
    ↓
┌─────────────┬─────────────┬─────────────┐
│  内容管理   │  Markdown   │    HTML     │
│   (content) │ (markdown)  │   (html)    │
└─────────────┴─────────────┴─────────────┘
    ↓             ↓             ↓
┌─────────────┬─────────────┬─────────────┐
│    SVG      │    PDF      │    语音     │
│   (svg)     │   (pdf)     │  (voice)    │
└─────────────┴─────────────┴─────────────┘
    ↓             ↓             ↓
┌─────────────┬─────────────┬─────────────┐
│  文本卡片   │    设置     │   订阅      │
│(text_cards) │ (settings)  │(subscription)│
└─────────────┴─────────────┴─────────────┘
    ↓             ↓             ↓
    服务层 (services/)
    ↓
    数据层 (models/)
```

## 开发注意事项

1. **模块独立性**: 每个模块应尽量独立，减少模块间耦合
2. **服务复用**: 通过服务层共享通用功能
3. **状态管理**: 合理使用状态管理方案
4. **错误处理**: 完善的异常处理机制
5. **性能优化**: 注意内存使用和渲染性能 