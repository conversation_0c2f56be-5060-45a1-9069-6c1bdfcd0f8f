import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/text_cards/utils/card_export_helper.dart';
import 'package:contentpal/text_cards/models/enhanced_card_template.dart';

void main() {
  group('CardExportHelper Tests', () {
    testWidgets('should create export card widget without errors', (
      WidgetTester tester,
    ) async {
      // Create a simple template
      final template = EnhancedCardTemplate(
        id: 'test',
        name: 'Test Template',
        category: 'test',
        description: 'Test template for testing',
        backgroundGradient: const LinearGradient(
          colors: [Colors.blue, Colors.purple],
        ),
        textColor: Colors.white,
        titleColor: Colors.white,
        accentColor: Colors.orange,
        borderRadius: 16.0,
        padding: 20.0,
        contentFontSize: 16.0,
        titleFontSize: 24.0,
        contentFontWeight: FontWeight.normal,
        titleFontWeight: FontWeight.bold,
        lineHeight: 1.5,
        layoutStyle: 'simple',
      );

      // Test content
      const testContent =
          'Test Title\nThis is test content for the card export functionality.';

      // Try to build the export card widget
      expect(() {
        // This should not throw any errors
        final widget = CardExportHelper.buildExportCard(testContent, template);
        expect(widget, isA<Widget>());
      }, returnsNormally);
    });

    testWidgets('should create simple card wrapper without errors', (
      WidgetTester tester,
    ) async {
      // Create a simple child widget
      final child = Container(width: 100, height: 100, color: Colors.red);

      // Test the simple card wrapper
      await tester.pumpWidget(
        MaterialApp(home: Scaffold(body: SimpleCardWrapper(child: child))),
      );

      // Should not throw any errors - there will be multiple containers
      expect(find.byType(Container), findsWidgets);
    });
  });
}
