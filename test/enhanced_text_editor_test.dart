import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/text_cards/widgets/enhanced_text_editor.dart';

void main() {
  group('EnhancedTextEditor Tests', () {
    testWidgets('should create enhanced text editor with initial text', (WidgetTester tester) async {
      String currentText = 'Initial text';
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedTextEditor(
              initialText: currentText,
              onTextChanged: (text) {
                currentText = text;
              },
              onStyleChanged: (text, style) {
              },
            ),
          ),
        ),
      );

      // Verify the text field is present
      expect(find.byType(TextField), findsOneWidget);
      
      // Verify initial text is displayed
      expect(find.text('Initial text'), findsOneWidget);
    });

    testWidgets('should show custom context menu when text is selected', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedTextEditor(
              initialText: 'Test text for selection',
              onTextChanged: (text) {},
              onStyleChanged: (text, style) {},
            ),
          ),
        ),
      );

      // Find the text field
      final textField = find.byType(TextField);
      expect(textField, findsOneWidget);

      // Tap to focus
      await tester.tap(textField);
      await tester.pump();

      // Select some text by long pressing
      await tester.longPress(textField);
      await tester.pump();

      // The context menu should appear with our custom "修改样式" option
      // Note: This test might need adjustment based on the actual behavior
      // of the context menu in the test environment
    });

    testWidgets('should update text when typing', (WidgetTester tester) async {
      String currentText = 'Initial';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EnhancedTextEditor(
              initialText: currentText,
              onTextChanged: (text) {
                currentText = text;
              },
            ),
          ),
        ),
      );

      // Find the text field and enter text
      final textField = find.byType(TextField);
      await tester.enterText(textField, 'New text content');
      await tester.pump();

      // Verify the callback was called
      expect(currentText, equals('New text content'));
    });
  });
}
