import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/markdown/models/markdown_block.dart';
import 'package:contentpal/markdown/services/markdown_block_service.dart';
import 'package:contentpal/markdown/controllers/markdown_block_controller.dart';
import 'package:contentpal/markdown/utils/markdown_block_utils.dart';

void main() {
  group('MarkdownBlockService Tests', () {
    late MarkdownBlockService service;

    setUp(() {
      service = MarkdownBlockService();
    });

    test('should split markdown by H1 headers', () {
      const markdown = '''# First Header
Content for first section.

# Second Header
Content for second section.

# Third Header
Content for third section.''';

      const config = BlockRenderConfig(
        enabled: true,
        mode: BlockMode.headings,
      );

      final blocks = service.splitMarkdownIntoBlocks(markdown, config);

      expect(blocks.length, equals(3));
      expect(blocks[0].title, equals('First Header'));
      expect(blocks[1].title, equals('Second Header'));
      expect(blocks[2].title, equals('Third Header'));
    });

    test('should split markdown by custom separator', () {
      const markdown = '''First section content.

---

Second section content.

---

Third section content.''';

      const config = BlockRenderConfig(
        enabled: true,
        mode: BlockMode.separators,
      );

      final blocks = service.splitMarkdownIntoBlocks(markdown, config);

      expect(blocks.length, greaterThanOrEqualTo(2));
      // 检查是否有分块被创建
      expect(blocks.isNotEmpty, isTrue);
    });

    test('should handle empty markdown', () {
      const markdown = '';
      const config = BlockRenderConfig(enabled: true);

      final blocks = service.splitMarkdownIntoBlocks(markdown, config);

      expect(blocks.length, equals(1));
      expect(blocks[0].content, isEmpty);
    });

    test('should extract title from content', () {
      const markdown = '''# Main Title
Some content here.

## Subtitle
More content.''';

      const config = BlockRenderConfig(
        enabled: true,
        mode: BlockMode.headings,
      );

      final blocks = service.splitMarkdownIntoBlocks(markdown, config);

      expect(blocks.isNotEmpty, isTrue);
      if (blocks.isNotEmpty) {
        expect(blocks[0].title, contains('Main Title'));
      }
    });

    // 移除了手动分隔杆的测试

    // 移除了位置计算相关的测试

    // 移除了分隔杆位置验证的测试

    test('should combine blocks back to markdown', () {
      final blocks = [
        MarkdownBlock(
          id: '1',
          content: '# First',
          startPosition: 0,
          endPosition: 7,
          index: 0,
          title: 'First',
          separatorType: BlockSeparatorType.h1,
          createdAt: DateTime.now(),
        ),
        MarkdownBlock(
          id: '2',
          content: '# Second',
          startPosition: 8,
          endPosition: 16,
          index: 1,
          title: 'Second',
          separatorType: BlockSeparatorType.h1,
          createdAt: DateTime.now(),
        ),
      ];

      final combined = service.combineBlocksToMarkdown(blocks);
      expect(combined, equals('# First\n\n# Second'));
    });
  });

  group('BlockRenderConfig Tests', () {
    test('should create default config', () {
      const config = BlockRenderConfig();

      expect(config.enabled, isTrue);
      expect(config.mode, equals(BlockMode.auto));
      expect(config.splitByH1, isTrue);
      expect(config.splitByH2, isFalse);
      expect(config.blockSpacing, equals(16.0));
    });

    test('should copy with new values', () {
      const config = BlockRenderConfig();
      final newConfig = config.copyWith(
        enabled: false,
        mode: BlockMode.headings,
        splitByH1: false,
        splitByH2: true,
        blockSpacing: 24.0,
      );

      expect(newConfig.enabled, isFalse);
      expect(newConfig.mode, equals(BlockMode.headings));
      expect(newConfig.splitByH1, isFalse);
      expect(newConfig.splitByH2, isTrue);
      expect(newConfig.blockSpacing, equals(24.0));
    });

    test('should allow independent H1 and H2 splitting', () {
      const config1 = BlockRenderConfig(splitByH1: true, splitByH2: false);
      const config2 = BlockRenderConfig(splitByH1: false, splitByH2: true);
      const config3 = BlockRenderConfig(splitByH1: true, splitByH2: true);
      const config4 = BlockRenderConfig(splitByH1: false, splitByH2: false);

      expect(config1.splitByH1, isTrue);
      expect(config1.splitByH2, isFalse);

      expect(config2.splitByH1, isFalse);
      expect(config2.splitByH2, isTrue);

      expect(config3.splitByH1, isTrue);
      expect(config3.splitByH2, isTrue);

      expect(config4.splitByH1, isFalse);
      expect(config4.splitByH2, isFalse);
    });
  });

  group('MarkdownBlock Tests', () {
    test('should create block with required fields', () {
      final now = DateTime.now();
      final block = MarkdownBlock(
        id: 'test-id',
        content: 'Test content',
        startPosition: 0,
        endPosition: 12,
        index: 0,
        title: 'Test Title',
        separatorType: BlockSeparatorType.h1,
        createdAt: now,
      );

      expect(block.id, equals('test-id'));
      expect(block.content, equals('Test content'));
      expect(block.title, equals('Test Title'));
      expect(block.isVisible, isTrue); // Default value
      expect(block.separatorType, equals(BlockSeparatorType.h1));
    });

    test('should copy with new values', () {
      final original = MarkdownBlock(
        id: 'original',
        content: 'Original content',
        startPosition: 0,
        endPosition: 16,
        index: 0,
        title: 'Original Title',
        separatorType: BlockSeparatorType.h1,
        createdAt: DateTime.now(),
      );

      final copied = original.copyWith(
        content: 'New content',
        isVisible: false,
      );

      expect(copied.content, equals('New content'));
      expect(copied.isVisible, isFalse);
      // Other values should remain the same
      expect(copied.id, equals(original.id));
      expect(copied.title, equals(original.title));
    });

    test('should serialize to and from JSON', () {
      final original = MarkdownBlock(
        id: 'test-id',
        content: 'Test content',
        startPosition: 0,
        endPosition: 12,
        index: 0,
        title: 'Test Title',
        separatorType: BlockSeparatorType.h2,
        isVisible: false,
        createdAt: DateTime.parse('2023-01-01T00:00:00.000Z'),
      );

      final json = original.toJson();
      final restored = MarkdownBlock.fromJson(json);

      expect(restored.id, equals(original.id));
      expect(restored.content, equals(original.content));
      expect(restored.title, equals(original.title));
      expect(restored.separatorType, equals(original.separatorType));
      expect(restored.isVisible, equals(original.isVisible));
      expect(restored.createdAt, equals(original.createdAt));
    });
  });

  group('MarkdownBlockController Tests', () {
    late MarkdownBlockController controller;

    setUp(() {
      controller = MarkdownBlockController();
    });

    tearDown(() {
      controller.dispose();
    });

    test('should initialize with markdown text', () {
      const markdown = '# Test\nContent';
      const config = BlockRenderConfig(enabled: true);

      controller.initialize(markdownText: markdown, config: config);

      expect(controller.markdownText, equals(markdown));
      expect(controller.config, equals(config));
      expect(controller.blocks, isNotEmpty);
    });

    test('should update markdown text and refresh blocks', () {
      controller.initialize(markdownText: '# First', config: const BlockRenderConfig(enabled: true));
      final initialBlockCount = controller.blocks.length;

      controller.updateMarkdownText('# First\n\n# Second');

      expect(controller.blocks.length, greaterThan(initialBlockCount));
    });

    // 移除了操纵杆添加和删除的测试

    test('should toggle block visibility', () {
      controller.initialize(
        markdownText: '# Test Block',
        config: const BlockRenderConfig(enabled: true),
      );

      final block = controller.blocks.first;
      final initialVisibility = block.isVisible;

      controller.toggleBlockVisibility(block.id);

      final updatedBlock = controller.blocks.firstWhere((b) => b.id == block.id);
      expect(updatedBlock.isVisible, equals(!initialVisibility));
    });

    test('should get combined markdown from visible blocks', () {
      controller.initialize(
        markdownText: '# Block 1\nContent 1\n\n# Block 2\nContent 2',
        config: const BlockRenderConfig(enabled: true, mode: BlockMode.headings),
      );

      // Hide first block
      if (controller.blocks.isNotEmpty) {
        controller.toggleBlockVisibility(controller.blocks.first.id);
      }

      final combined = controller.getCombinedMarkdown();
      expect(combined, isNot(contains('Block 1')));
    });
  });

  group('MarkdownBlockUtils Tests', () {
    test('should validate regex patterns', () {
      expect(MarkdownBlockUtils.isValidRegexPattern(r'^\s*[-]{3,}\s*$'), isTrue);
      expect(MarkdownBlockUtils.isValidRegexPattern(''), isTrue); // Empty is valid
      expect(MarkdownBlockUtils.isValidRegexPattern('['), isFalse); // Invalid regex
    });

    test('should estimate line count correctly', () {
      const text = 'Line 1\nLine 2\nLine 3';
      expect(MarkdownBlockUtils.estimateLineCount(text), equals(3));
      expect(MarkdownBlockUtils.estimateLineCount(''), equals(0));
      expect(MarkdownBlockUtils.estimateLineCount('Single line'), equals(1));
    });

    test('should calculate block statistics', () {
      final blocks = [
        MarkdownBlock(
          id: '1',
          content: 'Content 1',
          startPosition: 0,
          endPosition: 9,
          index: 0,
          title: 'Block 1',
          separatorType: BlockSeparatorType.h1,
          createdAt: DateTime.now(),
        ),
        MarkdownBlock(
          id: '2',
          content: 'Content 2',
          startPosition: 10,
          endPosition: 19,
          index: 1,
          title: 'Block 2',
          separatorType: BlockSeparatorType.h2,
          isVisible: false,
          createdAt: DateTime.now(),
        ),
      ];

      final stats = MarkdownBlockUtils.calculateBlockStatistics(blocks);

      expect(stats.totalBlocks, equals(2));
      expect(stats.visibleBlocks, equals(1));
      expect(stats.hiddenBlocks, equals(1));
      expect(stats.totalCharacters, equals(18));
      expect(stats.blockTypeCounts[BlockSeparatorType.h1], equals(1));
      expect(stats.blockTypeCounts[BlockSeparatorType.h2], equals(1));
    });

    test('should find block at position', () {
      final blocks = [
        MarkdownBlock(
          id: '1',
          content: 'Content',
          startPosition: 0,
          endPosition: 10,
          index: 0,
          title: 'Block 1',
          separatorType: BlockSeparatorType.h1,
          createdAt: DateTime.now(),
        ),
        MarkdownBlock(
          id: '2',
          content: 'Content',
          startPosition: 11,
          endPosition: 20,
          index: 1,
          title: 'Block 2',
          separatorType: BlockSeparatorType.h2,
          createdAt: DateTime.now(),
        ),
      ];

      final block1 = MarkdownBlockUtils.findBlockAtPosition(blocks, 5);
      final block2 = MarkdownBlockUtils.findBlockAtPosition(blocks, 15);
      final noBlock = MarkdownBlockUtils.findBlockAtPosition(blocks, 25);

      expect(block1?.id, equals('1'));
      expect(block2?.id, equals('2'));
      expect(noBlock, isNull);
    });

    test('should generate block summary', () {
      final block = MarkdownBlock(
        id: '1',
        content: 'This is a very long content that should be truncated when generating a summary because it exceeds the maximum length limit.',
        startPosition: 0,
        endPosition: 100,
        index: 0,
        title: 'Long Block',
        separatorType: BlockSeparatorType.manual,
        createdAt: DateTime.now(),
      );

      final summary = MarkdownBlockUtils.generateBlockSummary(block);
      expect(summary.length, lessThanOrEqualTo(100));
      expect(summary, endsWith('...'));
    });

    test('should validate block config', () {
      const validConfig = BlockRenderConfig(
        enabled: true,
        mode: BlockMode.auto,
      );

      const invalidConfig = BlockRenderConfig(
        enabled: true,
        mode: BlockMode.manual, // No automatic split methods enabled
        splitByH1: false,
        splitByH2: false,
      );

      final validIssues = MarkdownBlockUtils.validateBlockConfig(validConfig);
      final invalidIssues = MarkdownBlockUtils.validateBlockConfig(invalidConfig);

      expect(validIssues, isEmpty);
      expect(invalidIssues, isNotEmpty);
      expect(invalidIssues.first, contains('至少需要启用一种分隔方式'));
    });

    test('should format block types correctly', () {
      expect(MarkdownBlockUtils.formatBlockType(BlockSeparatorType.h1), equals('一级标题'));
      expect(MarkdownBlockUtils.formatBlockType(BlockSeparatorType.h2), equals('二级标题'));
      expect(MarkdownBlockUtils.formatBlockType(BlockSeparatorType.custom), equals('自定义分隔符'));
      expect(MarkdownBlockUtils.formatBlockType(BlockSeparatorType.manual), equals('手动分隔'));
    });
  });

  group('Integration Tests', () {
    test('should work with complex markdown document', () {
      const complexMarkdown = '''# Introduction

This is the introduction section.

## Overview

Brief overview of the topic.

---

# Main Content

This is the main content section.

### Subsection 1

Content for subsection 1.

### Subsection 2

Content for subsection 2.

---

# Conclusion

This is the conclusion section.

## Summary

Final summary of the document.''';

      const config = BlockRenderConfig(
        enabled: true,
        mode: BlockMode.auto,
      );

      final service = MarkdownBlockService();
      final blocks = service.splitMarkdownIntoBlocks(complexMarkdown, config);

      expect(blocks.length, greaterThanOrEqualTo(2)); // Should have multiple blocks
      expect(blocks.isNotEmpty, isTrue);
      // 检查是否有标题分块
      expect(blocks.any((b) => b.title.contains('Introduction') || b.title.contains('Main Content') || b.title.contains('Conclusion')), isTrue);
    });

    test('should handle edge cases gracefully', () {
      final service = MarkdownBlockService();

      // Empty markdown
      final emptyBlocks = service.splitMarkdownIntoBlocks('', const BlockRenderConfig(enabled: true));
      expect(emptyBlocks.length, equals(1));

      // Only whitespace
      final whitespaceBlocks = service.splitMarkdownIntoBlocks('   \n\n   ', const BlockRenderConfig(enabled: true));
      expect(whitespaceBlocks.length, equals(1));

      // Single line
      final singleLineBlocks = service.splitMarkdownIntoBlocks('Single line', const BlockRenderConfig(enabled: true));
      expect(singleLineBlocks.length, equals(1));
    });
  });
}
