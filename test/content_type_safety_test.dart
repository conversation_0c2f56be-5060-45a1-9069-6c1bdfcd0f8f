import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/models/content_item.dart';

void main() {
  group('ContentItem Type Safety Tests', () {
    test('should handle String content safely', () {
      final item = ContentItem(
        title: 'Test Markdown',
        type: ContentType.markdown,
        content: 'This is markdown content',
      );

      expect(item.previewText, 'This is markdown content');
      expect(item.contentSize, greaterThan(0));
    });

    test('should handle Map content safely', () {
      final item = ContentItem(
        title: 'Test Markdown',
        type: ContentType.markdown,
        content: {'text': 'This is map content', 'templateId': 'test'},
      );

      // Should not throw an exception
      expect(() => item.previewText, returnsNormally);
      expect(item.previewText, isNotEmpty);
    });

    test('should handle null content safely', () {
      final item = ContentItem(
        title: 'Test Markdown',
        type: ContentType.markdown,
        content: null,
      );

      // Should not throw an exception
      expect(() => item.previewText, returnsNormally);
      expect(item.previewText, '');
    });

    test('should handle JSON serialization/deserialization', () {
      final originalItem = ContentItem(
        title: 'Test Markdown',
        type: ContentType.markdown,
        content: 'This is markdown content',
      );

      final json = originalItem.toJson();
      final deserializedItem = ContentItem.fromJson(json);

      expect(deserializedItem.title, originalItem.title);
      expect(deserializedItem.type, originalItem.type);
      expect(deserializedItem.content, originalItem.content);
      expect(() => deserializedItem.previewText, returnsNormally);
    });

    test('should handle complex JSON content', () {
      final json = {
        'id': 'test-id',
        'title': 'Test Item',
        'type': 'markdown',
        'content': {'text': 'Complex content', 'metadata': 'extra'},
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'tags': <String>[],
        'isFavorite': false,
      };

      // Should not throw an exception during deserialization
      expect(() => ContentItem.fromJson(json), returnsNormally);
      
      final item = ContentItem.fromJson(json);
      expect(() => item.previewText, returnsNormally);
      expect(item.previewText, isNotEmpty);
    });
  });
}
