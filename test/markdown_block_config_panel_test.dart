import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/markdown/models/markdown_block.dart';

void main() {
  group('Independent H1 and H2 Splitting Tests', () {
    test('should allow independent H1 and H2 configuration', () {
      // 测试所有四种组合
      const config1 = BlockRenderConfig(splitByH1: true, splitByH2: false);
      const config2 = BlockRenderConfig(splitByH1: false, splitByH2: true);
      const config3 = BlockRenderConfig(splitByH1: true, splitByH2: true);
      const config4 = BlockRenderConfig(splitByH1: false, splitByH2: false);

      // 验证每种配置都能正确设置
      expect(config1.splitByH1, isTrue);
      expect(config1.splitByH2, isFalse);

      expect(config2.splitByH1, isFalse);
      expect(config2.splitByH2, isTrue);

      expect(config3.splitByH1, isTrue);
      expect(config3.splitByH2, isTrue);

      expect(config4.splitByH1, isFalse);
      expect(config4.splitByH2, isFalse);
    });

    test('should allow independent modification via copyWith', () {
      const originalConfig = BlockRenderConfig(
        splitByH1: true,
        splitByH2: false,
      );

      // 只修改 H2，H1 应该保持不变
      final config1 = originalConfig.copyWith(splitByH2: true);
      expect(config1.splitByH1, isTrue); // 保持原值
      expect(config1.splitByH2, isTrue); // 新值

      // 只修改 H1，H2 应该保持不变
      final config2 = originalConfig.copyWith(splitByH1: false);
      expect(config2.splitByH1, isFalse); // 新值
      expect(config2.splitByH2, isFalse); // 保持原值

      // 同时修改两个
      final config3 = originalConfig.copyWith(
        splitByH1: false,
        splitByH2: true,
      );
      expect(config3.splitByH1, isFalse);
      expect(config3.splitByH2, isTrue);
    });

    test('should maintain independence when toggling values', () {
      // 从默认配置开始
      const initialConfig = BlockRenderConfig();
      expect(initialConfig.splitByH1, isTrue);
      expect(initialConfig.splitByH2, isFalse);

      // 启用 H2，H1 应该保持启用
      final step1 = initialConfig.copyWith(splitByH2: true);
      expect(step1.splitByH1, isTrue);
      expect(step1.splitByH2, isTrue);

      // 禁用 H1，H2 应该保持启用
      final step2 = step1.copyWith(splitByH1: false);
      expect(step2.splitByH1, isFalse);
      expect(step2.splitByH2, isTrue);

      // 重新启用 H1，H2 应该保持启用
      final step3 = step2.copyWith(splitByH1: true);
      expect(step3.splitByH1, isTrue);
      expect(step3.splitByH2, isTrue);

      // 禁用 H2，H1 应该保持启用
      final step4 = step3.copyWith(splitByH2: false);
      expect(step4.splitByH1, isTrue);
      expect(step4.splitByH2, isFalse);
    });
  });
}
