import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:contentpal/traffic_guide/utils/image_dimension_calculator.dart';
import 'package:contentpal/traffic_guide/models/traffic_guide_models.dart';
import 'package:contentpal/traffic_guide/models/image_template.dart';

void main() {
  group('ImageDimensionCalculator', () {
    late TrafficImageConfig testConfig;
    late ImageTemplate testTemplate;

    setUp(() {
      testConfig = TrafficImageConfig(
        text: 'Test text for image generation',
        fontSize: 48.0,
        fontFamily: 'Arial',
        backgroundColor: '#FFFFFF',
        textColor: '#000000',
      );

      testTemplate = ImageTemplate(
        id: 'test',
        name: 'Test Template',
        description: 'Test template for unit tests',
        type: TemplateType.modern,
        thumbnailPath: 'test.png',
        textLayout: TextLayout.center,
        backgroundType: BackgroundType.solid,
        primaryColors: [Colors.blue, Colors.purple],
        secondaryColors: [Colors.white],
        gradientPresets: [],
        baseFontSize: 48.0,
        fontWeight: FontWeight.bold,
      );
    });

    test('calculateOptimalSize returns reasonable dimensions', () {
      final size = ImageDimensionCalculator.calculateOptimalSize(testConfig);
      
      expect(size.width, greaterThan(0));
      expect(size.height, greaterThan(0));
      expect(size.width, greaterThanOrEqualTo(600));
      expect(size.height, greaterThanOrEqualTo(400));
    });

    test('calculateOptimalSize scales with text length', () {
      final shortTextConfig = testConfig.copyWith(text: 'Short');
      final longTextConfig = testConfig.copyWith(text: 'This is a much longer text that should result in a larger image size to accommodate all the content properly');

      final shortSize = ImageDimensionCalculator.calculateOptimalSize(shortTextConfig);
      final longSize = ImageDimensionCalculator.calculateOptimalSize(longTextConfig);

      expect(longSize.width, greaterThanOrEqualTo(shortSize.width));
    });

    test('calculateTextDimensions returns valid dimensions', () {
      final containerSize = const Size(800, 600);
      final textDimensions = ImageDimensionCalculator.calculateTextDimensions(
        testConfig,
        containerSize,
        template: testTemplate,
      );

      expect(textDimensions.width, greaterThan(0));
      expect(textDimensions.height, greaterThan(0));
      expect(textDimensions.width, lessThanOrEqualTo(containerSize.width * 0.9));
    });

    test('calculateTextScalingFactor returns valid scaling', () {
      final containerSize = const Size(400, 300);
      final scalingFactor = ImageDimensionCalculator.calculateTextScalingFactor(
        testConfig,
        containerSize,
        template: testTemplate,
      );

      expect(scalingFactor, greaterThan(0));
      expect(scalingFactor, lessThanOrEqualTo(1.0));
      expect(scalingFactor, greaterThanOrEqualTo(0.3));
    });

    test('calculatePreviewFontSize scales appropriately', () {
      final previewSize = const Size(400, 300);
      final previewFontSize = ImageDimensionCalculator.calculatePreviewFontSize(
        testConfig,
        previewSize,
        template: testTemplate,
      );

      expect(previewFontSize, greaterThan(0));
      expect(previewFontSize, lessThanOrEqualTo(testConfig.fontSize));
    });

    test('getOptimalAspectRatio returns valid ratio', () {
      final aspectRatio = ImageDimensionCalculator.getOptimalAspectRatio(testConfig);
      
      expect(aspectRatio, greaterThan(0));
      expect(aspectRatio, greaterThan(1.0)); // Width should be greater than height
    });

    test('validateTextFit works correctly', () {
      final smallContainer = const Size(100, 100);
      final largeContainer = const Size(1000, 800);

      final fitsInSmall = ImageDimensionCalculator.validateTextFit(
        testConfig,
        smallContainer,
        template: testTemplate,
      );

      final fitsInLarge = ImageDimensionCalculator.validateTextFit(
        testConfig,
        largeContainer,
        template: testTemplate,
      );

      expect(fitsInLarge, isTrue);
      // Small container might not fit, but that's expected
    });

    test('calculatePreviewPadding scales with container size', () {
      final smallSize = const Size(200, 150);
      final largeSize = const Size(800, 600);
      final optimalSize = const Size(800, 600);

      final smallPadding = ImageDimensionCalculator.calculatePreviewPadding(
        smallSize,
        optimalSize,
      );

      final largePadding = ImageDimensionCalculator.calculatePreviewPadding(
        largeSize,
        optimalSize,
      );

      expect(smallPadding, greaterThan(0));
      expect(largePadding, greaterThan(0));
      expect(largePadding, greaterThanOrEqualTo(smallPadding));
    });
  });
}
