import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/traffic_guide/models/traffic_guide_models.dart';

void main() {
  group('水印文本控制器状态管理测试', () {
    late TextEditingController watermarkController;
    late TrafficImageConfig config;

    setUp(() {
      watermarkController = TextEditingController();
      config = TrafficImageConfig(text: '');
    });

    tearDown(() {
      watermarkController.dispose();
    });

    test('控制器应该与配置对象同步', () {
      // 模拟用户输入水印文本
      const testWatermarkText = '测试水印文本';
      watermarkController.text = testWatermarkText;
      
      // 模拟配置更新
      config = config.copyWith(watermarkText: testWatermarkText);
      
      // 验证同步
      expect(watermarkController.text, equals(testWatermarkText));
      expect(config.watermarkText, equals(testWatermarkText));
    });

    test('清空控制器应该同时清空配置', () {
      // 设置初始值
      const testWatermarkText = '测试水印文本';
      watermarkController.text = testWatermarkText;
      config = config.copyWith(watermarkText: testWatermarkText);
      
      // 清空控制器
      watermarkController.clear();
      config = config.copyWith(watermarkText: '');
      
      // 验证都被清空
      expect(watermarkController.text, isEmpty);
      expect(config.watermarkText, isEmpty);
    });

    test('配置对象更新时控制器应该同步', () {
      // 模拟从配置对象更新控制器
      const testWatermarkText = '从配置更新的文本';
      config = config.copyWith(watermarkText: testWatermarkText);
      
      // 模拟同步方法
      if (watermarkController.text != config.watermarkText) {
        watermarkController.text = config.watermarkText;
      }
      
      // 验证同步成功
      expect(watermarkController.text, equals(testWatermarkText));
    });

    test('水印开关状态应该正确保持', () {
      // 初始状态
      expect(config.addWatermark, isFalse);
      expect(config.watermarkText, isEmpty);
      
      // 启用水印
      config = config.copyWith(addWatermark: true);
      expect(config.addWatermark, isTrue);
      
      // 添加水印文本
      const testWatermarkText = '测试水印';
      config = config.copyWith(watermarkText: testWatermarkText);
      watermarkController.text = testWatermarkText;
      
      expect(config.watermarkText, equals(testWatermarkText));
      expect(watermarkController.text, equals(testWatermarkText));
      
      // 禁用水印应该清空文本
      config = config.copyWith(addWatermark: false, watermarkText: '');
      watermarkController.clear();
      
      expect(config.addWatermark, isFalse);
      expect(config.watermarkText, isEmpty);
      expect(watermarkController.text, isEmpty);
    });

    test('TrafficImageConfig copyWith 方法应该正确工作', () {
      const originalText = '原始文本';
      const originalWatermark = '原始水印';
      
      config = TrafficImageConfig(
        text: originalText,
        watermarkText: originalWatermark,
        addWatermark: true,
      );
      
      // 只更新水印文本
      const newWatermark = '新水印文本';
      final updatedConfig = config.copyWith(watermarkText: newWatermark);
      
      expect(updatedConfig.text, equals(originalText)); // 原始文本不变
      expect(updatedConfig.watermarkText, equals(newWatermark)); // 水印文本更新
      expect(updatedConfig.addWatermark, isTrue); // 水印开关不变
    });

    test('多次配置更新应该保持一致性', () {
      // 第一次更新
      config = config.copyWith(addWatermark: true);
      expect(config.addWatermark, isTrue);
      
      // 第二次更新
      const watermarkText1 = '第一个水印';
      config = config.copyWith(watermarkText: watermarkText1);
      watermarkController.text = watermarkText1;
      
      expect(config.watermarkText, equals(watermarkText1));
      expect(watermarkController.text, equals(watermarkText1));
      
      // 第三次更新
      const watermarkText2 = '第二个水印';
      config = config.copyWith(watermarkText: watermarkText2);
      watermarkController.text = watermarkText2;
      
      expect(config.watermarkText, equals(watermarkText2));
      expect(watermarkController.text, equals(watermarkText2));
      
      // 最终状态验证
      expect(config.addWatermark, isTrue);
      expect(config.watermarkText, equals(watermarkText2));
      expect(watermarkController.text, equals(watermarkText2));
    });
  });
}
