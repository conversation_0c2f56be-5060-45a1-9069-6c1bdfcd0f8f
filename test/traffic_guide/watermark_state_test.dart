import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/traffic_guide/screens/advanced_image_generator_screen.dart';

void main() {
  group('AdvancedImageGeneratorScreen Watermark State Tests', () {
    testWidgets('水印文本在标签页切换时应该保持状态', (WidgetTester tester) async {
      // 构建应用
      await tester.pumpWidget(
        MaterialApp(
          home: const AdvancedImageGeneratorScreen(),
        ),
      );

      // 等待界面加载完成
      await tester.pumpAndSettle();

      // 切换到"视觉效果"标签页
      await tester.tap(find.text('视觉效果'));
      await tester.pumpAndSettle();

      // 启用水印功能
      final watermarkCheckbox = find.byType(Checkbox);
      await tester.tap(watermarkCheckbox);
      await tester.pumpAndSettle();

      // 输入水印文本
      const testWatermarkText = '测试水印文本';
      final watermarkTextField = find.byWidgetPredicate(
        (widget) => widget is TextField && 
                   widget.decoration?.labelText == '水印文本',
      );
      
      expect(watermarkTextField, findsOneWidget);
      await tester.enterText(watermarkTextField, testWatermarkText);
      await tester.pumpAndSettle();

      // 切换到"文本设置"标签页
      await tester.tap(find.text('文本设置'));
      await tester.pumpAndSettle();

      // 切换回"视觉效果"标签页
      await tester.tap(find.text('视觉效果'));
      await tester.pumpAndSettle();

      // 验证水印文本是否保持
      final watermarkTextFieldAfterSwitch = find.byWidgetPredicate(
        (widget) => widget is TextField && 
                   widget.decoration?.labelText == '水印文本',
      );
      
      expect(watermarkTextFieldAfterSwitch, findsOneWidget);
      
      // 获取TextField的控制器文本
      final textField = tester.widget<TextField>(watermarkTextFieldAfterSwitch);
      expect(textField.controller?.text, equals(testWatermarkText));
    });

    testWidgets('水印开关状态在标签页切换时应该保持', (WidgetTester tester) async {
      // 构建应用
      await tester.pumpWidget(
        MaterialApp(
          home: const AdvancedImageGeneratorScreen(),
        ),
      );

      // 等待界面加载完成
      await tester.pumpAndSettle();

      // 切换到"视觉效果"标签页
      await tester.tap(find.text('视觉效果'));
      await tester.pumpAndSettle();

      // 启用水印功能
      final watermarkCheckbox = find.byType(Checkbox);
      await tester.tap(watermarkCheckbox);
      await tester.pumpAndSettle();

      // 验证复选框已选中
      Checkbox checkbox = tester.widget<Checkbox>(watermarkCheckbox);
      expect(checkbox.value, isTrue);

      // 切换到"模板选择"标签页
      await tester.tap(find.text('模板选择'));
      await tester.pumpAndSettle();

      // 切换回"视觉效果"标签页
      await tester.tap(find.text('视觉效果'));
      await tester.pumpAndSettle();

      // 验证复选框状态保持
      final watermarkCheckboxAfterSwitch = find.byType(Checkbox);
      checkbox = tester.widget<Checkbox>(watermarkCheckboxAfterSwitch);
      expect(checkbox.value, isTrue);
    });

    testWidgets('禁用水印时应该清空水印文本', (WidgetTester tester) async {
      // 构建应用
      await tester.pumpWidget(
        MaterialApp(
          home: const AdvancedImageGeneratorScreen(),
        ),
      );

      // 等待界面加载完成
      await tester.pumpAndSettle();

      // 切换到"视觉效果"标签页
      await tester.tap(find.text('视觉效果'));
      await tester.pumpAndSettle();

      // 启用水印功能
      final watermarkCheckbox = find.byType(Checkbox);
      await tester.tap(watermarkCheckbox);
      await tester.pumpAndSettle();

      // 输入水印文本
      const testWatermarkText = '测试水印文本';
      final watermarkTextField = find.byWidgetPredicate(
        (widget) => widget is TextField && 
                   widget.decoration?.labelText == '水印文本',
      );
      
      await tester.enterText(watermarkTextField, testWatermarkText);
      await tester.pumpAndSettle();

      // 禁用水印功能
      await tester.tap(watermarkCheckbox);
      await tester.pumpAndSettle();

      // 重新启用水印功能
      await tester.tap(watermarkCheckbox);
      await tester.pumpAndSettle();

      // 验证水印文本已被清空
      final watermarkTextFieldAfterDisable = find.byWidgetPredicate(
        (widget) => widget is TextField && 
                   widget.decoration?.labelText == '水印文本',
      );
      
      final textField = tester.widget<TextField>(watermarkTextFieldAfterDisable);
      expect(textField.controller?.text, isEmpty);
    });
  });
}
