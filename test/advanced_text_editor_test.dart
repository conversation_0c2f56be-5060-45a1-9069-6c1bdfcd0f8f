import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/text_cards/widgets/advanced_text_editor.dart';
import 'package:contentpal/text_cards/models/enhanced_card_template.dart';

void main() {
  group('AdvancedTextEditor Tests', () {
    late EnhancedCardTemplate testTemplate;

    setUp(() {
      testTemplate = EnhancedCardTemplate(
        id: 'test',
        name: 'Test Template',
        category: 'test',
        description: 'Test template for testing',
        backgroundGradient: const LinearGradient(
          colors: [Colors.white, Colors.grey],
        ),
        textColor: Colors.black,
        titleColor: Colors.black,
        accentColor: Colors.blue,
        fontFamily: 'SourceHanSansCN',
        contentFontSize: 16.0,
        contentFontWeight: FontWeight.w400,
        textAlign: TextAlign.left,
        lineHeight: 1.5,
        padding: 16.0,
      );
    });

    testWidgets('should create advanced text editor with template', (WidgetTester tester) async {
      String currentText = 'Initial text';
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdvancedTextEditor(
              initialText: currentText,
              template: testTemplate,
              onTextChanged: (text) {
                currentText = text;
              },
              onStyleChanged: (styles) {

              },
            ),
          ),
        ),
      );

      // Verify the text field is present
      expect(find.byType(TextField), findsOneWidget);
      
      // Verify initial text is displayed
      expect(find.text('Initial text'), findsOneWidget);
    });

    testWidgets('should show custom context menu when text is selected', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdvancedTextEditor(
              initialText: 'Test text for selection',
              template: testTemplate,
              onTextChanged: (text) {},
              onStyleChanged: (styles) {},
            ),
          ),
        ),
      );

      // Find the text field
      final textField = find.byType(TextField);
      expect(textField, findsOneWidget);

      // Tap to focus
      await tester.tap(textField);
      await tester.pump();

      // The context menu should be customized with our "修改样式" option
      // Note: Actual context menu testing might require more complex setup
    });

    testWidgets('should update text when typing', (WidgetTester tester) async {
      String currentText = 'Initial';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdvancedTextEditor(
              initialText: currentText,
              template: testTemplate,
              onTextChanged: (text) {
                currentText = text;
              },
              onStyleChanged: (styles) {},
            ),
          ),
        ),
      );

      // Find the text field and enter text
      final textField = find.byType(TextField);
      await tester.enterText(textField, 'New text content');
      await tester.pump();

      // Verify the callback was called
      expect(currentText, equals('New text content'));
    });

    testWidgets('should apply template styles correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdvancedTextEditor(
              initialText: 'Test text',
              template: testTemplate,
              onTextChanged: (text) {},
              onStyleChanged: (styles) {},
            ),
          ),
        ),
      );

      // Verify the text field uses template styles
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.style?.fontSize, equals(16.0));
      expect(textField.style?.color, equals(Colors.black));
      expect(textField.textAlign, equals(TextAlign.left));
    });
  });
}
