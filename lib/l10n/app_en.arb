{"@@locale": "en", "@@last_modified": "2024-01-15T10:00:00.000Z", "appName": "ContentPal", "@appName": {"description": "The name of the application"}, "appNameChinese": "内容君", "@appNameChinese": {"description": "The Chinese name of the application"}, "appDescription": "Professional content processing tool that makes content creation easier", "@appDescription": {"description": "Description of the application"}, "home": "Home", "@home": {"description": "Home page title"}, "settings": "Settings", "@settings": {"description": "Settings page title"}, "language": "Language", "@language": {"description": "Language setting label"}, "theme": "Theme", "@theme": {"description": "Theme setting label"}, "lightTheme": "Light", "@lightTheme": {"description": "Light theme option"}, "darkTheme": "Dark", "@darkTheme": {"description": "Dark theme option"}, "systemTheme": "System", "@systemTheme": {"description": "System theme option"}, "markdown": "<PERSON><PERSON>", "@markdown": {"description": "Markdown module name"}, "textCards": "Text Cards", "@textCards": {"description": "Text Cards module name"}, "pdf": "PDF", "@pdf": {"description": "PDF module name"}, "voice": "Voice", "@voice": {"description": "Voice module name"}, "html": "HTML", "@html": {"description": "HTML module name"}, "svg": "SVG", "@svg": {"description": "SVG module name"}, "content": "Content", "@content": {"description": "Content module name"}, "trafficGuide": "Traffic Guide", "@trafficGuide": {"description": "Traffic Guide module name"}, "create": "Create", "@create": {"description": "Create button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "save": "Save", "@save": {"description": "Save button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "yes": "Yes", "@yes": {"description": "Yes button text"}, "no": "No", "@no": {"description": "No button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Error message prefix"}, "success": "Success", "@success": {"description": "Success message prefix"}, "warning": "Warning", "@warning": {"description": "Warning message prefix"}, "info": "Info", "@info": {"description": "Info message prefix"}, "search": "Search", "@search": {"description": "Search placeholder text"}, "searchHint": "Enter search terms...", "@searchHint": {"description": "Search input hint text"}, "noResults": "No results found", "@noResults": {"description": "No search results message"}, "tryAgain": "Try Again", "@tryAgain": {"description": "Try again button text"}, "refresh": "Refresh", "@refresh": {"description": "Refresh button text"}, "share": "Share", "@share": {"description": "Share button text"}, "export": "Export", "@export": {"description": "Export button text"}, "import": "Import", "@import": {"description": "Import button text"}, "copy": "Copy", "@copy": {"description": "Copy button text"}, "paste": "Paste", "@paste": {"description": "Paste button text"}, "cut": "Cut", "@cut": {"description": "Cut button text"}, "undo": "Undo", "@undo": {"description": "Undo button text"}, "redo": "Redo", "@redo": {"description": "Redo button text"}, "selectAll": "Select All", "@selectAll": {"description": "Select all button text"}, "close": "Close", "@close": {"description": "Close button text"}, "back": "Back", "@back": {"description": "Back button text"}, "next": "Next", "@next": {"description": "Next button text"}, "previous": "Previous", "@previous": {"description": "Previous button text"}, "done": "Done", "@done": {"description": "Done button text"}, "finish": "Finish", "@finish": {"description": "Finish button text"}, "skip": "<PERSON><PERSON>", "@skip": {"description": "Skip button text"}, "continueAction": "Continue", "@continueAction": {"description": "Continue button text"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "reset": "Reset", "@reset": {"description": "Reset button text"}, "clear": "Clear", "@clear": {"description": "Clear button text"}, "apply": "Apply", "@apply": {"description": "Apply button text"}, "preview": "Preview", "@preview": {"description": "Preview button text"}, "download": "Download", "@download": {"description": "Download button text"}, "upload": "Upload", "@upload": {"description": "Upload button text"}, "file": "File", "@file": {"description": "File label"}, "folder": "Folder", "@folder": {"description": "Folder label"}, "name": "Name", "@name": {"description": "Name field label"}, "title": "Title", "@title": {"description": "Title field label"}, "description": "Description", "@description": {"description": "Description field label"}, "size": "Size", "@size": {"description": "Size field label"}, "date": "Date", "@date": {"description": "Date field label"}, "time": "Time", "@time": {"description": "Time field label"}, "type": "Type", "@type": {"description": "Type field label"}, "status": "Status", "@status": {"description": "Status field label"}, "version": "Version", "@version": {"description": "Version field label"}, "author": "Author", "@author": {"description": "Author field label"}, "tags": "Tags", "@tags": {"description": "Tags field label"}, "category": "Category", "@category": {"description": "Category field label"}, "priority": "Priority", "@priority": {"description": "Priority field label"}, "high": "High", "@high": {"description": "High priority"}, "medium": "Medium", "@medium": {"description": "Medium priority"}, "low": "Low", "@low": {"description": "Low priority"}, "enabled": "Enabled", "@enabled": {"description": "Enabled status"}, "disabled": "Disabled", "@disabled": {"description": "Disabled status"}, "online": "Online", "@online": {"description": "Online status"}, "offline": "Offline", "@offline": {"description": "Offline status"}, "connected": "Connected", "@connected": {"description": "Connected status"}, "disconnected": "Disconnected", "@disconnected": {"description": "Disconnected status"}, "available": "Available", "@available": {"description": "Available status"}, "unavailable": "Unavailable", "@unavailable": {"description": "Unavailable status"}, "active": "Active", "@active": {"description": "Active status"}, "inactive": "Inactive", "@inactive": {"description": "Inactive status"}, "public": "Public", "@public": {"description": "Public visibility"}, "private": "Private", "@private": {"description": "Private visibility"}, "draft": "Draft", "@draft": {"description": "Draft status"}, "published": "Published", "@published": {"description": "Published status"}, "archived": "Archived", "@archived": {"description": "Archived status"}, "pdfProfessionalTool": "PDF Professional Tool", "@pdfProfessionalTool": {"description": "PDF module title"}, "pdfToolDescription": "Powerful PDF processing capabilities that make document management easier", "@pdfToolDescription": {"description": "PDF module description"}, "securityEncryption": "Security Encryption", "@securityEncryption": {"description": "PDF security feature title"}, "passwordProtectionPermissionControl": "Password Protection\nPermission Control", "@passwordProtectionPermissionControl": {"description": "PDF security feature description"}, "intelligentAnnotation": "Intelligent Annotation", "@intelligentAnnotation": {"description": "PDF annotation feature title"}, "highlightMarkingTextAnnotation": "Highlight Marking\nText Annotation", "@highlightMarkingTextAnnotation": {"description": "PDF annotation feature description"}, "quickSearch": "Quick Search", "@quickSearch": {"description": "PDF search feature title"}, "fullTextSearchContentLocation": "Full-text Search\nContent Location", "@fullTextSearchContentLocation": {"description": "PDF search feature description"}, "convenientSharing": "Convenient Sharing", "@convenientSharing": {"description": "PDF sharing feature title"}, "multipleFormatsOneClickExport": "Multiple Formats\nOne-click Export", "@multipleFormatsOneClickExport": {"description": "PDF sharing feature description"}, "welcomeToPdfTool": "Welcome to PDF Professional Tool!", "@welcomeToPdfTool": {"description": "Welcome message for PDF tool"}, "importFirstPdfDocument": "Import First PDF Document", "@importFirstPdfDocument": {"description": "Import PDF button text"}}