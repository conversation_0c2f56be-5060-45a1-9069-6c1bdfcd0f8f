

class VoiceRecord {
  final String id;
  final String title;
  final String filePath;
  final DateTime createdAt;
  final Duration duration;
  String? transcription; // 语音转文本结果

  VoiceRecord({
    required this.id,
    required this.title,
    required this.filePath,
    required this.createdAt,
    required this.duration,
    this.transcription,
  });

  // 从JSON转换为对象
  factory VoiceRecord.fromJson(Map<String, dynamic> json) {
    return VoiceRecord(
      id: json['id'],
      title: json['title'],
      filePath: json['filePath'],
      createdAt: DateTime.parse(json['createdAt']),
      duration: Duration(milliseconds: json['durationMs']),
      transcription: json['transcription'],
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'filePath': filePath,
      'createdAt': createdAt.toIso8601String(),
      'durationMs': duration.inMilliseconds,
      'transcription': transcription,
    };
  }

  // 更新转录文本
  VoiceRecord copyWithTranscription(String? transcription) {
    return VoiceRecord(
      id: id,
      title: title,
      filePath: filePath,
      createdAt: createdAt,
      duration: duration,
      transcription: transcription,
    );
  }

  // 更新标题
  VoiceRecord copyWithTitle(String title) {
    return VoiceRecord(
      id: id,
      title: title,
      filePath: filePath,
      createdAt: createdAt,
      duration: duration,
      transcription: transcription,
    );
  }

  @override
  String toString() {
    return 'VoiceRecord{id: $id, title: $title, duration: $duration, hasTranscription: ${transcription != null}}';
  }
}
