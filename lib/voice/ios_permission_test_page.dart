import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'dart:io';

class IosPermissionTestPage extends StatefulWidget {
  const IosPermissionTestPage({super.key});

  @override
  State<IosPermissionTestPage> createState() => _IosPermissionTestPageState();
}

class _IosPermissionTestPageState extends State<IosPermissionTestPage> {
  final _logMessages = <String>[];

  @override
  void initState() {
    super.initState();
    _logMessages.add("页面已加载");
    _checkInitialPermissions();
  }

  Future<void> _checkInitialPermissions() async {
    if (Platform.isIOS) {
      final micStatus = await Permission.microphone.status;
      final speechStatus = await Permission.speech.status;

      _addLog("初始麦克风权限状态: $micStatus");
      _addLog("初始语音识别权限状态: $speechStatus");
    } else {
      _addLog("非iOS平台，不检查权限");
    }
  }

  void _addLog(String message) {
    debugPrint(message);
    setState(() {
      _logMessages.add(message);
    });
  }

  // 测试麦克风权限请求
  Future<void> _testMicrophonePermission() async {
    _addLog("请求麦克风权限...");
    final status = await Permission.microphone.request();
    _addLog("麦克风权限状态: $status");
  }

  // 测试语音识别权限请求
  Future<void> _testSpeechRecognitionPermission() async {
    _addLog("请求语音识别权限...");
    final status = await Permission.speech.request();
    _addLog("语音识别权限状态: $status");
  }

  // 测试录音功能
  Future<void> _testRecorder() async {
    _addLog("测试录音功能...");
    FlutterSoundRecorder? recorder;

    try {
      recorder = FlutterSoundRecorder();
      _addLog("录音机实例已创建");

      await recorder.openRecorder();
      _addLog("录音机已初始化");

      await Future.delayed(const Duration(seconds: 1));
      _addLog("录音机测试完成");

      await recorder.closeRecorder();
      _addLog("录音机已关闭");
    } catch (e) {
      _addLog("录音机错误: $e");
    }
  }

  // 测试语音识别
  Future<void> _testSpeechToText() async {
    _addLog("测试语音识别...");
    stt.SpeechToText speech = stt.SpeechToText();

    try {
      bool available = await speech.initialize(
        onError: (error) => _addLog("语音识别错误: $error"),
        onStatus: (status) => _addLog("语音识别状态: $status"),
      );

      _addLog("语音识别初始化: ${available ? '成功' : '失败'}");

      if (available) {
        _addLog("开始监听...");
        await speech.listen(
          onResult: (result) => _addLog("识别结果: ${result.recognizedWords}"),
        );

        await Future.delayed(const Duration(seconds: 3));
        await speech.stop();
        _addLog("停止监听");
      }
    } catch (e) {
      _addLog("语音识别测试错误: $e");
    }
  }

  // 打开应用设置
  void _openSettings() {
    _addLog("打开应用设置");
    openAppSettings();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('iOS权限测试')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              '直接测试iOS权限',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // 权限测试按钮
            ElevatedButton(
              onPressed: _testMicrophonePermission,
              child: const Text('请求麦克风权限'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _testSpeechRecognitionPermission,
              child: const Text('请求语音识别权限'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _testRecorder,
              child: const Text('测试录音功能'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _testSpeechToText,
              child: const Text('测试语音识别'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _openSettings,
              child: const Text('打开应用设置'),
            ),

            const SizedBox(height: 24),

            // 日志显示
            const Text(
              '操作日志:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children:
                        _logMessages.map((log) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(log),
                          );
                        }).toList(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
