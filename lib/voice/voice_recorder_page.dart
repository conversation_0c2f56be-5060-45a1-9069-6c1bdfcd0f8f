import 'dart:io';

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

import '../services/audio_recorder_service.dart';
import '../services/speech_service.dart';
import '../services/voice_record_storage_service.dart';

class VoiceRecorderPage extends StatefulWidget {
  const VoiceRecorderPage({super.key});

  @override
  State<VoiceRecorderPage> createState() => _VoiceRecorderPageState();
}

class _VoiceRecorderPageState extends State<VoiceRecorderPage>
    with SingleTickerProviderStateMixin {
  final SpeechService _speechService = SpeechService();
  final AudioRecorderService _recorderService = AudioRecorderService();
  final VoiceRecordStorageService _storageService = VoiceRecordStorageService();

  bool _isInitialized = false;
  bool _isInitializing = true;
  String _errorMessage = '';
  String _transcription = '';
  final TextEditingController _titleController = TextEditingController();
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    // 推迟初始化，让UI先呈现
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _delayedInitialize();
    });
  }

  // 延迟初始化，避免在构建过程中请求权限
  Future<void> _delayedInitialize() async {
    // 简单初始化，不立即请求权限
    setState(() {
      _isInitializing = true;
      _errorMessage = '';
    });

    // 先检查是否已经有权限 - 使用录音服务的权限检查方法
    bool hasRecordingPermission = await _recorderService.hasPermission();
    debugPrint("语音录制页面初始化: 权限检查结果 = $hasRecordingPermission");

    if (hasRecordingPermission) {
      // 已有权限，直接初始化
      debugPrint("已有所需权限，开始初始化");
      _initialize();
    } else {
      // 没有权限，显示权限说明和请求按钮
      setState(() {
        _isInitializing = false;
        _isInitialized = false;
        _errorMessage = '需要麦克风${Platform.isIOS ? '和语音识别' : ''}权限才能使用录音功能';
      });
    }
  }

  Future<void> _initialize() async {
    if (!mounted) return;

    try {
      setState(() {
        _isInitializing = true;
        _errorMessage = '';
      });

      // 初始化存储服务
      await _storageService.init();

      // 检查是否有权限
      bool hasMicPermission = await _recorderService.hasPermission();

      if (!hasMicPermission) {
        bool permissionGranted = await _recorderService.checkPermission();
        if (!permissionGranted) {
          setState(() {
            _isInitializing = false;
            _errorMessage = '需要麦克风${Platform.isIOS ? '和语音识别' : ''}权限才能进行录音';
          });

          // 检查是否是权限被永久拒绝
          PermissionStatus micStatus = await Permission.microphone.status;
          bool isPermanentlyDenied = micStatus.isPermanentlyDenied;

          if (Platform.isAndroid) {
            PermissionStatus audioStatus = await Permission.audio.status;
            isPermanentlyDenied =
                isPermanentlyDenied || audioStatus.isPermanentlyDenied;
          }

          debugPrint("权限检查: 是否永久拒绝 = $isPermanentlyDenied");

          if (isPermanentlyDenied) {
            if (Platform.isIOS) {
              _showIosPermissionGuide();
            } else {
              _showPermanentlyDeniedDialog();
            }
          } else {
            _showErrorDialog(_errorMessage);
          }
          return;
        }
      }

      // 初始化录音服务
      await _recorderService.init();

      // 初始化语音识别服务
      final speechInitialized = await _speechService.initSpeech();
      if (!speechInitialized) {
        setState(() {
          _isInitializing = false;
          _errorMessage =
              '语音识别初始化失败，请确保允许使用麦克风${Platform.isIOS ? '和语音识别' : ''}';
        });
        _showErrorDialog(_errorMessage);
        return;
      }

      // 设置监听器
      _speechService.resultStream.listen((result) {
        if (mounted) {
          setState(() {
            _transcription = result;
          });
        }
      });

      _recorderService.isRecording.addListener(() {
        if (mounted) {
          setState(() {});
        }
      });

      _recorderService.recordingDuration.addListener(() {
        if (mounted) {
          setState(() {});
        }
      });

      if (mounted) {
        setState(() {
          _isInitialized = true;
          _isInitializing = false;
        });
      }
    } catch (e) {
      debugPrint('初始化失败: $e');
      if (mounted) {
        setState(() {
          _errorMessage = '初始化失败: $e';
          _isInitializing = false;
        });
        _showErrorDialog(_errorMessage);
      }
    }
  }

  // 检查并请求麦克风权限 - 显式请求
  Future<void> _requestMicrophonePermission() async {
    try {
      debugPrint("===== 开始麦克风权限请求流程 =====");
      if (Platform.isIOS) {
        // iOS平台针对权限请求优化
        debugPrint("iOS: 检查当前权限状态");

        // 检查麦克风权限状态
        PermissionStatus micStatus = await Permission.microphone.status;
        debugPrint("iOS: 当前麦克风权限状态: $micStatus");

        // 检查语音识别权限状态
        PermissionStatus speechStatus = await Permission.speech.status;
        debugPrint("iOS: 当前语音识别权限状态: $speechStatus");

        // 无论状态如何，都尝试请求权限（在iOS上即使是permanentlyDenied也可能能够重新触发请求）
        if (!micStatus.isGranted) {
          micStatus = await Permission.microphone.request();
          debugPrint("iOS: 请求后麦克风权限状态: $micStatus");
        }

        if (!speechStatus.isGranted) {
          speechStatus = await Permission.speech.request();
          debugPrint("iOS: 请求后语音识别权限状态: $speechStatus");
        }

        // 如果权限仍未授予，显示设置指南
        if (!micStatus.isGranted || !speechStatus.isGranted) {
          _showIosPermissionGuide();
          return;
        }

        // 重新初始化
        setState(() {
          _isInitializing = true;
        });
        _initialize();
        return;
      }

      // Android逻辑保持不变
      // 先检查当前权限状态
      PermissionStatus status = await Permission.microphone.status;
      debugPrint("当前麦克风权限状态: $status");

      // 如果不是已授权且不是永久拒绝状态，则请求权限
      if (!status.isGranted && !status.isPermanentlyDenied) {
        status = await Permission.microphone.request();
        debugPrint("请求后麦克风权限状态: $status");
      }

      // 如果是Android，还需要检查处理音频权限
      if (Platform.isAndroid) {
        PermissionStatus audioStatus = await Permission.audio.status;
        debugPrint("当前音频权限状态: $audioStatus");

        if (!audioStatus.isGranted && !audioStatus.isPermanentlyDenied) {
          audioStatus = await Permission.audio.request();
          debugPrint("请求后音频权限状态: $audioStatus");
        }

        // 如果任一权限被永久拒绝，则弹出设置对话框
        if (status.isPermanentlyDenied || audioStatus.isPermanentlyDenied) {
          _showPermanentlyDeniedDialog();
          return;
        }
      } else if (status.isPermanentlyDenied) {
        // 非Android平台，如果麦克风权限被永久拒绝
        _showPermanentlyDeniedDialog();
        return;
      }

      if (status.isGranted) {
        // 重新初始化
        setState(() {
          _isInitializing = true;
        });
        _initialize();
      } else {
        // 其他拒绝状态
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('没有麦克风权限，无法进行录音')));
      }
    } catch (e) {
      debugPrint('请求权限失败: $e');
    }
  }

  // 针对iOS的权限设置引导对话框
  void _showIosPermissionGuide() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('需要权限'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('请按照以下步骤开启权限:'),
                const SizedBox(height: 8),
                const Text('1. 点击"去设置"按钮'),
                const SizedBox(height: 4),
                const Text('2. 在设置中点击"隐私与安全性"'),
                const SizedBox(height: 4),
                const Text('3. 分别点击"麦克风"和"语音识别"'),
                const SizedBox(height: 4),
                const Text('4. 在列表中找到"ContentPal"并开启权限'),
                const SizedBox(height: 12),
                const Text(
                  '注意: 如果看不到App，请回到应用中重新点击"请求权限"按钮，然后再次查看设置',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  openAppSettings();
                },
                child: const Text('去设置'),
              ),
            ],
          ),
    );
  }

  // 显示永久拒绝权限对话框
  void _showPermanentlyDeniedDialog() {
    // Android平台使用此对话框
    if (Platform.isIOS) {
      _showIosPermissionGuide(); // iOS使用更详细的引导
      return;
    }

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('需要权限'),
            content: const Text('请到设置中开启麦克风权限，以便进行录音'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  openAppSettings();
                },
                child: const Text('去设置'),
              ),
            ],
          ),
    );
  }

  // 显示错误对话框
  void _showErrorDialog(String message) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('初始化错误'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _requestMicrophonePermission();
              },
              child: const Text('重新请求权限'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings(); // 打开应用设置页面
              },
              child: const Text('去设置'),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _titleController.dispose();

    // 确保停止所有服务
    _speechService.stopListening();

    // 如果正在录音，停止录音
    if (_recorderService.isRecording.value) {
      _recorderService.stopRecording();
    }

    super.dispose();
  }

  // 开始录音并同时转写
  Future<void> _startRecording() async {
    setState(() {
      _transcription = '';
    });

    try {
      // 先开始录音
      await _recorderService.startRecording();
      debugPrint('录音开始成功');

      // 再开始语音识别
      await _speechService.startListening(localeId: 'zh_CN');
      debugPrint('语音识别开始成功');
    } catch (e) {
      debugPrint('开始录音失败: $e');

      // 重置状态
      _recorderService.isRecording.value = false;

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('开始录音失败: $e')));

      // 如果是权限问题，显示对话框
      if (e.toString().contains('权限')) {
        _showErrorDialog(e.toString());
      }
    }
  }

  // 停止录音并保存
  Future<void> _stopRecording() async {
    try {
      debugPrint('尝试停止语音识别...');
      // 停止语音识别
      await _speechService.stopListening();

      debugPrint('尝试停止录音...');
      // 停止录音
      final filePath = await _recorderService.stopRecording();
      debugPrint('录音已停止，文件路径: $filePath');

      if (filePath != null) {
        // 保存录音记录
        await _saveRecord(filePath);
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('录音文件无效，请重试')));
      }
    } catch (e) {
      debugPrint('停止录音失败: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('停止录音失败: $e')));
    }
  }

  // 保存录音记录
  Future<void> _saveRecord(String filePath) async {
    if (!mounted) return;

    // 检查文件是否存在
    final file = File(filePath);
    if (!await file.exists()) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('录音文件不存在，可能录音失败')));
      return;
    }

    // 显示对话框以输入标题
    _titleController.text = '录音 ${DateTime.now().toString().substring(0, 19)}';
    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('保存录音'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: '标题',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  '时长: ${_formatDuration(_recorderService.recordingDuration.value)}',
                ),
                const SizedBox(height: 8),
                if (_transcription.isNotEmpty)
                  Text(
                    '转录: $_transcription',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('保存'),
              ),
            ],
          ),
    );

    if (result == true) {
      // 保存记录
      try {
        // 直接将录音和转录一起保存
        final recordId = await _storageService.addRecord(
          filePath,
          _recorderService.recordingDuration.value,
          title: _titleController.text,
          transcription: _transcription.isNotEmpty ? _transcription : null,
        );

        debugPrint('录音记录已添加，ID: $recordId');

        // 显示成功消息
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  _transcription.isNotEmpty
                      ? const Text('录音和转录文本已保存')
                      : const Text('录音已保存'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } catch (e) {
        debugPrint('保存录音记录失败: $e');
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('保存录音记录失败: $e')));
        }
      }
    } else {
      // 取消保存，删除文件
      try {
        if (await file.exists()) {
          await file.delete();
          debugPrint('录音文件已删除');
        }
      } catch (e) {
        debugPrint('删除录音文件失败: $e');
      }
    }
  }

  // 格式化时长
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    final milliseconds = duration.inMilliseconds.remainder(1000) ~/ 10;
    final millisecondsStr = milliseconds.toString().padLeft(2, '0');
    return '$minutes:$seconds.$millisecondsStr';
  }

  @override
  Widget build(BuildContext context) {
    final isRecording = _recorderService.isRecording.value;
    final recordingDuration = _recorderService.recordingDuration.value;

    return Scaffold(
      appBar: AppBar(
        title: const Text('语音录制'),
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      body:
          _isInitializing
              ? const Center(child: CircularProgressIndicator())
              : !_isInitialized
              ? _buildPermissionRequest()
              : _buildRecordingUI(isRecording, recordingDuration),
    );
  }

  Widget _buildPermissionRequest() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.mic_off_rounded, size: 100, color: Colors.red.shade400),
            const SizedBox(height: 24),
            Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 18,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 40),
            ElevatedButton.icon(
              onPressed: _requestMicrophonePermission,
              icon: const Icon(Icons.mic),
              label: const Text('请求权限'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                textStyle: const TextStyle(fontSize: 16),
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
            const SizedBox(height: 16),
            OutlinedButton.icon(
              onPressed: () {
                openAppSettings();
              },
              icon: const Icon(Icons.settings),
              label: const Text('打开设置'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                textStyle: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingUI(bool isRecording, Duration recordingDuration) {
    return Column(
      children: [
        // 上部分：实时转录区域
        Expanded(
          flex: 3,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(24),
                bottomRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      isRecording ? '正在录音...' : '准备好开始录音',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    if (isRecording)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            AnimatedBuilder(
                              animation: _animationController,
                              builder: (context, child) {
                                return Opacity(
                                  opacity: _animationController.value,
                                  child: Container(
                                    width: 8,
                                    height: 8,
                                    decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.red,
                                    ),
                                  ),
                                );
                              },
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _formatDuration(recordingDuration),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child:
                        isRecording
                            ? SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (_transcription.isNotEmpty)
                                    Text(
                                      _transcription,
                                      style: const TextStyle(
                                        fontSize: 18,
                                        height: 1.5,
                                      ),
                                    )
                                  else
                                    Center(
                                      child: Text(
                                        '请开始说话...',
                                        style: TextStyle(
                                          fontSize: 18,
                                          color: Colors.grey.shade600,
                                          height: 1.5,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            )
                            : Center(
                              child: Text(
                                '点击下方按钮开始录音\n语音将实时转为文字',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey.shade600,
                                  height: 1.5,
                                ),
                              ),
                            ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // 下部分：录音控制区域
        Expanded(
          flex: 2,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 使用更大的触摸面积和更明确的视觉反馈
                GestureDetector(
                  onTap: () {
                    if (isRecording) {
                      _stopRecording();
                    } else {
                      _startRecording();
                    }
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          isRecording
                              ? Colors.red
                              : Theme.of(context).colorScheme.primary,
                      boxShadow: [
                        BoxShadow(
                          color: (isRecording
                                  ? Colors.red
                                  : Theme.of(context).colorScheme.primary)
                              .withValues(alpha: 0.3),
                          blurRadius: 12,
                          spreadRadius: 4,
                        ),
                      ],
                    ),
                    child: Icon(
                      isRecording ? Icons.stop : Icons.mic,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  isRecording ? '点击停止' : '点击开始',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (isRecording)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      '语音识别中，实时转录显示在上方',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
