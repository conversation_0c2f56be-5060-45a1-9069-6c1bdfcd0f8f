import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../config/app_theme.dart';
import '../services/speech_service.dart';
import '../services/audio_recorder_service.dart';
import '../services/voice_record_storage_service.dart';
import '../services/transcription_service.dart';

class TranscriptionPage extends StatefulWidget {
  const TranscriptionPage({super.key});

  @override
  State<TranscriptionPage> createState() => _TranscriptionPageState();
}

class _TranscriptionPageState extends State<TranscriptionPage>
    with TickerProviderStateMixin {
  final SpeechService _speechService = SpeechService();
  final AudioRecorderService _recorderService = AudioRecorderService();
  final VoiceRecordStorageService _storageService = VoiceRecordStorageService();
  final TranscriptionService _transcriptionService = TranscriptionService();

  late AnimationController _animationController;
  late AnimationController _waveController;

  // 转录模式
  TranscriptionMode _currentMode = TranscriptionMode.realtime;

  // 实时转录状态
  bool _isListening = false;
  String _realtimeTranscription = '';
  String _finalTranscription = '';

  // 文件转录状态
  bool _isProcessingFile = false;
  String _selectedFilePath = '';
  String _fileTranscription = '';

  // 语言设置
  String _selectedLanguage = 'zh_CN';
  final Map<String, String> _supportedLanguages = {
    'zh_CN': '中文（简体）',
    'zh_TW': '中文（繁体）',
    'en_US': 'English (US)',
    'ja_JP': '日本語',
    'ko_KR': '한국어',
  };

  // 转录设置
  TranscriptionSettings _transcriptionSettings = TranscriptionSettings();

  // 文本编辑
  final TextEditingController _textController = TextEditingController();
  final TextEditingController _titleController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeServices();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _waveController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 波形动画已移除，使用简单的重复动画
  }

  Future<void> _initializeServices() async {
    try {
      await _storageService.init();
      await _recorderService.init();
      await _speechService.initSpeech();
      await _transcriptionService.initialize();

      // 监听语音识别结果
      _speechService.resultStream.listen((result) {
        if (mounted) {
          setState(() {
            _realtimeTranscription = result;
          });
        }
      });

      // 监听转录服务结果
      _transcriptionService.resultStream.listen((result) {
        if (mounted) {
          setState(() {
            _realtimeTranscription = result;
          });
        }
      });

      // 监听转录状态
      _transcriptionService.status.addListener(() {
        if (mounted) {
          setState(() {});
        }
      });

      // 监听录音状态
      _recorderService.isRecording.addListener(() {
        if (mounted) {
          setState(() {});
        }
      });
    } catch (e) {
      debugPrint('初始化服务失败: $e');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _waveController.dispose();
    _textController.dispose();
    _titleController.dispose();

    // 确保停止所有服务
    _speechService.stopListening();
    _transcriptionService.cancelTranscription();

    // 如果正在录音，停止录音
    if (_recorderService.isRecording.value) {
      _recorderService.stopRecording();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: const Text('智能转录'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // 模式选择器
          _buildModeSelector(),

          // 主要内容区域
          Expanded(child: _buildMainContent()),

          // 底部操作栏
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildModeSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children:
            TranscriptionMode.values.map((mode) {
              final isSelected = _currentMode == mode;
              return Expanded(
                child: GestureDetector(
                  onTap: () => _switchMode(mode),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? AppTheme.primaryColor
                              : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _getModeTitle(mode),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color:
                            isSelected
                                ? Colors.white
                                : AppTheme.textMediumColor,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildMainContent() {
    switch (_currentMode) {
      case TranscriptionMode.realtime:
        return _buildRealtimeTranscription();
      case TranscriptionMode.file:
        return _buildFileTranscription();
      case TranscriptionMode.batch:
        return _buildBatchTranscription();
    }
  }

  Widget _buildRealtimeTranscription() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 语言选择器
          _buildLanguageSelector(),

          const SizedBox(height: 16),

          // 转录显示区域
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.mic,
                        color:
                            _isListening
                                ? AppTheme.primaryColor
                                : AppTheme.textLightColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _isListening ? '正在转录...' : '准备就绪',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color:
                              _isListening
                                  ? AppTheme.primaryColor
                                  : AppTheme.textMediumColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (_realtimeTranscription.isNotEmpty)
                            Text(
                              _realtimeTranscription,
                              style: const TextStyle(
                                fontSize: 18,
                                height: 1.6,
                                color: AppTheme.textDarkColor,
                              ),
                            )
                          else if (_finalTranscription.isNotEmpty)
                            Text(
                              _finalTranscription,
                              style: const TextStyle(
                                fontSize: 18,
                                height: 1.6,
                                color: AppTheme.textDarkColor,
                              ),
                            )
                          else
                            Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.mic_none,
                                    size: 64,
                                    color: AppTheme.textLightColor,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    '点击下方按钮开始实时转录',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: AppTheme.textMediumColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 录音控制按钮
          _buildRecordingControls(),
        ],
      ),
    );
  }

  Widget _buildFileTranscription() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 文件选择区域
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(Icons.audio_file, size: 48, color: AppTheme.primaryColor),
                const SizedBox(height: 16),
                Text(
                  _selectedFilePath.isEmpty ? '选择音频文件进行转录' : '已选择文件',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                if (_selectedFilePath.isNotEmpty)
                  Text(
                    _selectedFilePath.split('/').last,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textMediumColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _isProcessingFile ? null : _selectAudioFile,
                  icon: const Icon(Icons.folder_open),
                  label: Text(_selectedFilePath.isEmpty ? '选择文件' : '重新选择'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 转录结果显示
          if (_fileTranscription.isNotEmpty)
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '转录结果',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Text(
                          _fileTranscription,
                          style: const TextStyle(fontSize: 16, height: 1.6),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBatchTranscription() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Icon(
                  Icons.batch_prediction,
                  size: 48,
                  color: AppTheme.primaryColor,
                ),
                const SizedBox(height: 16),
                const Text(
                  '批量转录功能',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  '选择多个音频文件进行批量转录',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textMediumColor,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _selectBatchFiles,
                  icon: const Icon(Icons.folder_open),
                  label: const Text('选择多个文件'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 批量处理进度
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '批量处理进度',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),
                  Expanded(
                    child: Center(
                      child: Text(
                        '批量转录功能开发中...',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedLanguage,
          isExpanded: true,
          icon: const Icon(Icons.arrow_drop_down),
          items:
              _supportedLanguages.entries.map((entry) {
                return DropdownMenuItem<String>(
                  value: entry.key,
                  child: Text(entry.value),
                );
              }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedLanguage = value;
              });
            }
          },
        ),
      ),
    );
  }

  Widget _buildRecordingControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 清除按钮
        IconButton(
          onPressed: _clearTranscription,
          icon: const Icon(Icons.clear),
          style: IconButton.styleFrom(
            backgroundColor: Colors.grey.shade100,
            foregroundColor: AppTheme.textMediumColor,
          ),
        ),

        // 录音按钮
        GestureDetector(
          onTapDown: (_) => _startRecording(),
          onTapUp: (_) => _stopRecording(),
          onTapCancel: () => _stopRecording(),
          child: AnimatedBuilder(
            animation: _waveController,
            builder: (context, child) {
              return Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color:
                      _isListening
                          ? AppTheme.primaryColor
                          : Colors.grey.shade300,
                  shape: BoxShape.circle,
                  boxShadow:
                      _isListening
                          ? [
                            BoxShadow(
                              color: AppTheme.primaryColor.withValues(
                                alpha: 0.3,
                              ),
                              blurRadius: 20,
                              spreadRadius: 5,
                            ),
                          ]
                          : null,
                ),
                child: Icon(
                  _isListening ? Icons.stop : Icons.mic,
                  color: _isListening ? Colors.white : AppTheme.textMediumColor,
                  size: 32,
                ),
              );
            },
          ),
        ),

        // 保存按钮
        IconButton(
          onPressed: _saveTranscription,
          icon: const Icon(Icons.save),
          style: IconButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _exportTranscription,
              icon: const Icon(Icons.download),
              label: const Text('导出'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _shareTranscription,
              icon: const Icon(Icons.share),
              label: const Text('分享'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 模式切换
  void _switchMode(TranscriptionMode mode) {
    setState(() {
      _currentMode = mode;
    });

    // 停止当前录音
    if (_isListening) {
      _stopRecording();
    }
  }

  String _getModeTitle(TranscriptionMode mode) {
    switch (mode) {
      case TranscriptionMode.realtime:
        return '实时转录';
      case TranscriptionMode.file:
        return '文件转录';
      case TranscriptionMode.batch:
        return '批量转录';
    }
  }

  // 开始录音
  Future<void> _startRecording() async {
    try {
      // 检查权限
      bool hasPermission = await _recorderService.hasPermission();
      if (!hasPermission) {
        bool granted = await _recorderService.checkPermission();
        if (!granted) {
          _showPermissionDialog();
          return;
        }
      }

      // 重置转录文本
      setState(() {
        _realtimeTranscription = '';
        _finalTranscription = '';
      });

      // 先开始录音
      await _recorderService.startRecording();
      debugPrint('录音开始成功');

      // 再开始转录
      await _transcriptionService.startRealtimeTranscription(
        language: _selectedLanguage,
        settings: _transcriptionSettings,
      );
      debugPrint('转录开始成功');

      setState(() {
        _isListening = true;
      });

      _waveController.repeat();
    } catch (e) {
      debugPrint('开始录音失败: $e');
      // 重置状态
      setState(() {
        _isListening = false;
      });
      _waveController.stop();

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('开始录音失败: $e')));
    }
  }

  // 停止录音
  Future<void> _stopRecording() async {
    try {
      setState(() {
        _isListening = false;
      });
      _waveController.stop();

      // 先停止转录
      await _transcriptionService.stopRealtimeTranscription();
      debugPrint('转录已停止');

      // 再停止录音
      final filePath = await _recorderService.stopRecording();
      debugPrint('录音已停止，文件路径: $filePath');

      // 保存最终转录结果
      setState(() {
        _finalTranscription = _realtimeTranscription;
      });

      if (filePath != null) {
        // 可以在这里保存录音文件
        debugPrint('录音文件已保存: $filePath');

        // 显示成功消息
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('录音完成'), duration: Duration(seconds: 2)),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('录音失败，请重试'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('停止录音失败: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('停止录音失败: $e')));
    }
  }

  // 选择音频文件
  Future<void> _selectAudioFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null) {
        setState(() {
          _selectedFilePath = result.files.single.path!;
          _fileTranscription = '';
        });

        // 开始处理文件
        _processAudioFile();
      }
    } catch (e) {
      debugPrint('选择文件失败: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('选择文件失败: $e')));
    }
  }

  // 处理音频文件
  Future<void> _processAudioFile() async {
    if (_selectedFilePath.isEmpty) return;

    setState(() {
      _isProcessingFile = true;
    });

    try {
      // 使用转录服务处理音频文件
      final transcription = await _transcriptionService.transcribeAudioFile(
        filePath: _selectedFilePath,
        language: _selectedLanguage,
        settings: _transcriptionSettings,
      );

      setState(() {
        _fileTranscription = transcription ?? '转录失败，请重试';
        _isProcessingFile = false;
      });
    } catch (e) {
      debugPrint('处理音频文件失败: $e');
      setState(() {
        _isProcessingFile = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('处理音频文件失败: $e')));
    }
  }

  // 选择批量文件
  Future<void> _selectBatchFiles() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: true,
      );

      if (result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('已选择 ${result.files.length} 个文件')),
        );
        // 批量处理逻辑
      }
    } catch (e) {
      debugPrint('选择批量文件失败: $e');
    }
  }

  // 清除转录内容
  void _clearTranscription() {
    setState(() {
      _realtimeTranscription = '';
      _finalTranscription = '';
      _fileTranscription = '';
    });
  }

  // 保存转录结果
  Future<void> _saveTranscription() async {
    final transcription = _getCurrentTranscription();
    if (transcription.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('没有可保存的转录内容')));
      return;
    }

    _titleController.text = '转录 ${DateTime.now().toString().substring(0, 19)}';

    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('保存转录结果'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: '标题',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  '转录内容预览:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textMediumColor,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 100,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: SingleChildScrollView(
                    child: Text(
                      transcription.length > 200
                          ? '${transcription.substring(0, 200)}...'
                          : transcription,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('保存'),
              ),
            ],
          ),
    );

    if (result == true) {
      try {
        // 保存到语音记录
        final recordId = await _storageService.addRecord(
          '', // 空文件路径，因为这是纯文本转录
          Duration.zero,
          title: _titleController.text,
          transcription: transcription,
        );

        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('转录结果已保存')));

        debugPrint('转录记录已保存，ID: $recordId');
      } catch (e) {
        debugPrint('保存转录结果失败: $e');
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('保存失败: $e')));
      }
    }
  }

  // 获取当前转录内容
  String _getCurrentTranscription() {
    switch (_currentMode) {
      case TranscriptionMode.realtime:
        return _finalTranscription.isNotEmpty
            ? _finalTranscription
            : _realtimeTranscription;
      case TranscriptionMode.file:
        return _fileTranscription;
      case TranscriptionMode.batch:
        return '';
    }
  }

  // 导出转录结果
  void _exportTranscription() {
    final transcription = _getCurrentTranscription();
    if (transcription.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('没有可导出的转录内容')));
      return;
    }

    // TODO: 实现导出功能
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('导出功能开发中...')));
  }

  // 分享转录结果
  void _shareTranscription() {
    final transcription = _getCurrentTranscription();
    if (transcription.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('没有可分享的转录内容')));
      return;
    }

    // TODO: 实现分享功能
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('分享功能开发中...')));
  }

  // 显示设置对话框
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('转录设置'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SwitchListTile(
                  title: const Text('启用标点符号'),
                  subtitle: const Text('自动添加标点符号'),
                  value: _transcriptionSettings.enablePunctuation,
                  onChanged: (value) {
                    setState(() {
                      _transcriptionSettings = _transcriptionSettings.copyWith(
                        enablePunctuation: value,
                      );
                    });
                  },
                ),
                SwitchListTile(
                  title: const Text('说话人检测'),
                  subtitle: const Text('识别不同的说话人'),
                  value: _transcriptionSettings.enableSpeakerDetection,
                  onChanged: (value) {
                    setState(() {
                      _transcriptionSettings = _transcriptionSettings.copyWith(
                        enableSpeakerDetection: value,
                      );
                    });
                  },
                ),
                ListTile(
                  title: const Text('置信度阈值'),
                  subtitle: Slider(
                    value: _transcriptionSettings.confidenceThreshold,
                    min: 0.0,
                    max: 1.0,
                    divisions: 10,
                    label:
                        '${(_transcriptionSettings.confidenceThreshold * 100).round()}%',
                    onChanged: (value) {
                      setState(() {
                        _transcriptionSettings = _transcriptionSettings
                            .copyWith(confidenceThreshold: value);
                      });
                    },
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  // 显示权限对话框
  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('需要权限'),
            content: const Text('语音转录功能需要麦克风权限。请在设置中允许访问麦克风。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  openAppSettings();
                },
                child: const Text('打开设置'),
              ),
            ],
          ),
    );
  }
}

// 转录模式枚举
enum TranscriptionMode {
  realtime, // 实时转录
  file, // 文件转录
  batch, // 批量转录
}
