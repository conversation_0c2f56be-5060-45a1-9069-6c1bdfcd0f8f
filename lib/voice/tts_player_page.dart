import 'package:flutter/material.dart';
import '../services/tts_service.dart';
import 'dart:async';

class TtsPlayerPage extends StatefulWidget {
  final bool showAppBar;

  const TtsPlayerPage({super.key, this.showAppBar = true});

  @override
  State<TtsPlayerPage> createState() => _TtsPlayerPageState();
}

class _TtsPlayerPageState extends State<TtsPlayerPage> {
  final TtsService _ttsService = TtsService();
  final TextEditingController _textController = TextEditingController();

  final List<String> _playlist = [];
  int _currentPlayingIndex = -1;
  bool _isInitialized = false;
  double _rate = 0.5; // 语速
  double _pitch = 1.0; // 音调
  double _volume = 1.0; // 音量
  String _currentLanguage = 'zh-CN';
  List<String> _availableLanguages = [];

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      await _ttsService.initTts();

      // 加载可用语言
      _availableLanguages = await _ttsService.getAvailableLanguages();
      if (!_availableLanguages.contains(_currentLanguage)) {
        _currentLanguage =
            _availableLanguages.isNotEmpty
                ? _availableLanguages.first
                : 'zh-CN';
      }

      // 监听TTS状态
      _ttsService.stateNotifier.addListener(() {
        if (mounted) {
          setState(() {
            if (_ttsService.stateNotifier.value == TtsState.stopped &&
                _currentPlayingIndex >= 0 &&
                _currentPlayingIndex < _playlist.length - 1) {
              // 如果播放完毕且不是最后一个，播放下一个
              _playNext();
            }
          });
        }
      });

      // 监听TTS进度
      _ttsService.progressNotifier.addListener(() {
        if (mounted) {
          setState(() {});
        }
      });

      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('初始化失败: $e')));
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  // 添加文本到播放列表
  void _addToPlaylist() {
    final text = _textController.text.trim();
    if (text.isNotEmpty) {
      setState(() {
        _playlist.add(text);
        _textController.clear();
      });

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('已添加到播放列表')));
    }
  }

  // 移除播放列表项
  void _removeFromPlaylist(int index) {
    setState(() {
      _playlist.removeAt(index);
      if (_currentPlayingIndex >= index) {
        _currentPlayingIndex--;
      }
    });
  }

  // 播放
  Future<void> _play(int index) async {
    if (index < 0 || index >= _playlist.length) return;

    // 设置当前播放索引
    setState(() {
      _currentPlayingIndex = index;
    });

    // 播放文本
    await _ttsService.speak(_playlist[index]);
  }

  // 停止播放
  Future<void> _stop() async {
    await _ttsService.stop();
    setState(() {
      _currentPlayingIndex = -1;
    });
  }

  // 播放下一个
  Future<void> _playNext() async {
    if (_currentPlayingIndex >= 0 &&
        _currentPlayingIndex < _playlist.length - 1) {
      await _play(_currentPlayingIndex + 1);
    }
  }

  // 播放所有
  Future<void> _playAll() async {
    if (_playlist.isEmpty) return;
    await _play(0);
  }

  // 更新语速
  Future<void> _updateRate(double value) async {
    await _ttsService.setSpeechRate(value);
    setState(() {
      _rate = value;
    });
  }

  // 更新音调
  Future<void> _updatePitch(double value) async {
    await _ttsService.setPitch(value);
    setState(() {
      _pitch = value;
    });
  }

  // 更新音量
  Future<void> _updateVolume(double value) async {
    await _ttsService.setVolume(value);
    setState(() {
      _volume = value;
    });
  }

  // 更新语言
  Future<void> _updateLanguage(String language) async {
    await _ttsService.setLanguage(language);
    setState(() {
      _currentLanguage = language;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isPlaying = _ttsService.stateNotifier.value == TtsState.playing;

    final content =
        _isInitialized
            ? _buildTtsPlayerContent(isPlaying)
            : const Center(child: CircularProgressIndicator());

    if (widget.showAppBar) {
      return Scaffold(appBar: AppBar(title: const Text('文本朗读')), body: content);
    } else {
      return content;
    }
  }

  Widget _buildTtsPlayerContent(bool isPlaying) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 输入文本
          TextField(
            controller: _textController,
            maxLines: 3,
            decoration: InputDecoration(
              labelText: '输入要朗读的文本',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              hintText: '输入要朗读的文本，点击"添加"按钮添加到播放列表',
              suffixIcon: IconButton(
                icon: const Icon(Icons.add),
                onPressed: _addToPlaylist,
                tooltip: '添加到播放列表',
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 参数设置区域
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '朗读设置',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // 语速设置
                  Row(
                    children: [
                      const Icon(Icons.speed, size: 20),
                      const SizedBox(width: 8),
                      const Text('语速:'),
                      Expanded(
                        child: Slider(
                          value: _rate,
                          min: 0.0,
                          max: 1.0,
                          divisions: 10,
                          label: _rate.toStringAsFixed(1),
                          onChanged: _updateRate,
                        ),
                      ),
                      Text(
                        _rate.toStringAsFixed(1),
                        style: const TextStyle(fontWeight: FontWeight.w500),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),

                  // 音调设置
                  Row(
                    children: [
                      const Icon(Icons.tune, size: 20),
                      const SizedBox(width: 8),
                      const Text('音调:'),
                      Expanded(
                        child: Slider(
                          value: _pitch,
                          min: 0.5,
                          max: 2.0,
                          divisions: 15,
                          label: _pitch.toStringAsFixed(1),
                          onChanged: _updatePitch,
                        ),
                      ),
                      Text(
                        _pitch.toStringAsFixed(1),
                        style: const TextStyle(fontWeight: FontWeight.w500),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),

                  // 音量设置
                  Row(
                    children: [
                      const Icon(Icons.volume_up, size: 20),
                      const SizedBox(width: 8),
                      const Text('音量:'),
                      Expanded(
                        child: Slider(
                          value: _volume,
                          min: 0.0,
                          max: 1.0,
                          divisions: 10,
                          label: _volume.toStringAsFixed(1),
                          onChanged: _updateVolume,
                        ),
                      ),
                      Text(
                        _volume.toStringAsFixed(1),
                        style: const TextStyle(fontWeight: FontWeight.w500),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),

                  // 语言选择
                  if (_availableLanguages.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Row(
                        children: [
                          const Icon(Icons.language, size: 20),
                          const SizedBox(width: 8),
                          const Text('语言:'),
                          const SizedBox(width: 16),
                          Expanded(
                            child: DropdownButton<String>(
                              value: _currentLanguage,
                              isExpanded: true,
                              items:
                                  _availableLanguages
                                      .map(
                                        (lang) => DropdownMenuItem(
                                          value: lang,
                                          child: Text(lang),
                                        ),
                                      )
                                      .toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  _updateLanguage(value);
                                }
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 播放列表区域
          Expanded(
            child: Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '播放列表',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        if (_playlist.isNotEmpty)
                          IconButton(
                            icon: Icon(
                              isPlaying ? Icons.pause : Icons.play_arrow,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            onPressed: isPlaying ? _stop : _playAll,
                            tooltip: isPlaying ? '停止' : '播放全部',
                          ),
                      ],
                    ),
                  ),

                  const Divider(),

                  Expanded(
                    child:
                        _playlist.isEmpty
                            ? Center(
                              child: Text(
                                '播放列表为空',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 16,
                                ),
                              ),
                            )
                            : ListView.builder(
                              itemCount: _playlist.length,
                              itemBuilder: (context, index) {
                                final isCurrentPlaying =
                                    index == _currentPlayingIndex;

                                return ListTile(
                                  title: Text(
                                    _playlist[index],
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      fontWeight:
                                          isCurrentPlaying
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                      color:
                                          isCurrentPlaying
                                              ? Theme.of(
                                                context,
                                              ).colorScheme.primary
                                              : null,
                                    ),
                                  ),
                                  leading: CircleAvatar(
                                    backgroundColor:
                                        isCurrentPlaying
                                            ? Theme.of(
                                              context,
                                            ).colorScheme.primary
                                            : Theme.of(
                                              context,
                                            ).colorScheme.primaryContainer,
                                    child: Icon(
                                      isCurrentPlaying
                                          ? Icons.volume_up
                                          : Icons.text_snippet,
                                      color:
                                          isCurrentPlaying
                                              ? Theme.of(
                                                context,
                                              ).colorScheme.onPrimary
                                              : Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                      size: 20,
                                    ),
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: Icon(
                                          isCurrentPlaying
                                              ? Icons.pause
                                              : Icons.play_arrow,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                        ),
                                        onPressed:
                                            isCurrentPlaying
                                                ? _stop
                                                : () => _play(index),
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.delete_outline),
                                        onPressed:
                                            () => _removeFromPlaylist(index),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
