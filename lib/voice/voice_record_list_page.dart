import 'dart:io';
import 'package:flutter/material.dart';
import '../services/voice_record_storage_service.dart';
import '../services/audio_player_service.dart';
import '../models/voice_record.dart';
import 'voice_recorder_page.dart';
import 'voice_record_detail_page.dart';

class VoiceRecordListPage extends StatefulWidget {
  final bool showAppBar;

  const VoiceRecordListPage({super.key, this.showAppBar = true});

  @override
  State<VoiceRecordListPage> createState() => _VoiceRecordListPageState();
}

class _VoiceRecordListPageState extends State<VoiceRecordListPage> {
  final VoiceRecordStorageService _storageService = VoiceRecordStorageService();
  final AudioPlayerService _playerService = AudioPlayerService();

  bool _isInitialized = false;
  int? _playingIndex;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      await _storageService.init();
      await _playerService.init();

      _storageService.recordsNotifier.addListener(() {
        if (mounted) {
          setState(() {});
        }
      });

      // 监听播放状态变化
      _playerService.playerStateNotifier.addListener(() {
        if (mounted && !_playerService.playerStateNotifier.value.playing) {
          setState(() {
            _playingIndex = null;
          });
        }
      });

      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('初始化失败: $e')));
    }
  }

  // 格式化时长
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  // 格式化日期
  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return '今天 ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return '昨天 ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else {
      return '${dateTime.year}-${dateTime.month}-${dateTime.day}';
    }
  }

  // 播放所有录音
  Future<void> _playAllRecordings() async {
    final records = _storageService.recordsNotifier.value;
    if (records.isEmpty) return;

    final filePaths = records.map((record) => record.filePath).toList();
    await _playerService.loadPlaylist(filePaths);
    await _playerService.play();

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('正在播放所有录音')));
  }

  @override
  Widget build(BuildContext context) {
    final records = _storageService.recordsNotifier.value;

    if (!_isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    final content =
        records.isEmpty ? _buildEmptyState() : _buildRecordList(records);

    if (widget.showAppBar) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('我的录音'),
          actions: [
            if (records.isNotEmpty)
              IconButton(
                icon: const Icon(Icons.play_arrow),
                onPressed: _playAllRecordings,
                tooltip: '播放所有',
              ),
          ],
        ),
        body: content,
      );
    } else {
      return content;
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.mic_off, size: 100, color: Colors.grey.shade300),
          const SizedBox(height: 24),
          Text(
            '暂无语音记录',
            style: TextStyle(
              fontSize: 20,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击下方按钮开始录制您的第一条语音',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade500),
          ),
          const SizedBox(height: 40),
          ElevatedButton.icon(
            icon: const Icon(Icons.mic),
            label: const Text('开始录制'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              textStyle: const TextStyle(fontSize: 16),
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const VoiceRecorderPage(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildRecordList(List<VoiceRecord> records) {
    return ListView.builder(
      itemCount: records.length,
      padding: const EdgeInsets.only(bottom: 80),
      itemBuilder: (context, index) {
        final record = records[index];
        final fileExists = File(record.filePath).existsSync();
        final isPlaying = _playingIndex == index;

        return Dismissible(
          key: Key(record.id),
          background: Container(
            color: Colors.red.shade700,
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.only(right: 20),
            child: const Icon(
              Icons.delete_outline,
              color: Colors.white,
              size: 32,
            ),
          ),
          direction: DismissDirection.endToStart,
          confirmDismiss: (direction) async {
            return await showDialog<bool>(
                  context: context,
                  builder:
                      (context) => AlertDialog(
                        title: const Text('确认删除'),
                        content: const Text('确定要删除这条语音记录吗？'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(false),
                            child: const Text('取消'),
                          ),
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(true),
                            child: const Text('删除'),
                          ),
                        ],
                      ),
                ) ??
                false;
          },
          onDismissed: (direction) {
            _storageService.deleteRecord(record.id);
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(const SnackBar(content: Text('记录已删除')));
          },
          child: Card(
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap:
                  fileExists
                      ? () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) =>
                                    VoiceRecordDetailPage(recordId: record.id),
                          ),
                        );
                      }
                      : null,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color:
                                fileExists
                                    ? isPlaying
                                        ? Theme.of(context).colorScheme.primary
                                        : Theme.of(
                                          context,
                                        ).colorScheme.primaryContainer
                                    : Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            isPlaying ? Icons.pause : Icons.mic,
                            color:
                                fileExists
                                    ? isPlaying
                                        ? Theme.of(
                                          context,
                                        ).colorScheme.onPrimary
                                        : Theme.of(context).colorScheme.primary
                                    : Colors.grey.shade500,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                record.title,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  Icon(
                                    Icons.timer_outlined,
                                    size: 14,
                                    color: Colors.grey.shade600,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _formatDuration(record.duration),
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Icon(
                                    Icons.calendar_today_outlined,
                                    size: 14,
                                    color: Colors.grey.shade600,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _formatDate(record.createdAt),
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: Icon(
                            isPlaying ? Icons.pause : Icons.play_arrow,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          onPressed:
                              fileExists
                                  ? () async {
                                    if (isPlaying) {
                                      await _playerService.pause();
                                      setState(() {
                                        _playingIndex = null;
                                      });
                                    } else {
                                      await _playerService.clearPlaylist();
                                      await _playerService.addToPlaylist(
                                        record.filePath,
                                      );
                                      await _playerService.play();
                                      setState(() {
                                        _playingIndex = index;
                                      });
                                    }
                                  }
                                  : null,
                        ),
                      ],
                    ),
                    if (record.transcription != null &&
                        record.transcription!.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 12, left: 60),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '转录内容:',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                record.transcription!,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade900,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
