import 'dart:io';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import '../services/voice_record_storage_service.dart';
import '../services/tts_service.dart';
import '../models/voice_record.dart';

class VoiceRecordDetailPage extends StatefulWidget {
  final String recordId;

  const VoiceRecordDetailPage({super.key, required this.recordId});

  @override
  State<VoiceRecordDetailPage> createState() => _VoiceRecordDetailPageState();
}

class _VoiceRecordDetailPageState extends State<VoiceRecordDetailPage> {
  final VoiceRecordStorageService _storageService = VoiceRecordStorageService();
  final TtsService _ttsService = TtsService();
  final AudioPlayer _audioPlayer = AudioPlayer();

  late VoiceRecord _record;
  bool _isLoading = true;
  bool _isPlaying = false;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  bool _fileExists = false;
  bool _fileHasError = false;

  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _transcriptionController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      debugPrint('详情页面初始化开始，ID: ${widget.recordId}');
      setState(() {
        _isLoading = true;
      });

      await _storageService.init();
      await _ttsService.initTts();

      _storageService.recordsNotifier.addListener(_updateRecord);

      // 先获取记录数据
      _updateRecord();

      // 检查文件是否存在
      await _checkFileExists();

      // 先完成UI加载
      setState(() {
        _isLoading = false;
      });

      // 修复录音文件错误 - 如果文件大小过小，仍然显示但添加警告
      if (_fileHasError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('录音文件可能已损坏，无法播放'),
            duration: Duration(seconds: 3),
          ),
        );
      }

      // 如果文件存在且没有错误，延迟加载音频，以避免阻塞UI
      if (_fileExists && !_fileHasError) {
        debugPrint('文件存在且正常，延迟加载音频文件: ${_record.filePath}');

        // 使用Future.delayed让UI先渲染完成
        Future.delayed(Duration.zero, () async {
          try {
            // 释放之前的资源
            await _audioPlayer.stop();

            debugPrint('正在加载音频文件...');
            // 加载音频源
            await _audioPlayer.setAudioSource(
              AudioSource.uri(Uri.file(_record.filePath)),
              preload: true,
            );

            // 获取加载后的持续时间
            if (_audioPlayer.duration != null) {
              _duration = _audioPlayer.duration!;
              debugPrint('音频加载成功，时长: $_duration');
            } else {
              debugPrint('等待音频时长...');
              // 等待音频时长加载完成
              _duration = await _audioPlayer.durationFuture ?? Duration.zero;
              debugPrint('获取到音频时长: $_duration');
            }

            // 检查音频时长是否有效
            if (_duration.inMilliseconds < 100) {
              debugPrint('警告: 音频时长异常短 ($_duration)');
              setState(() {
                _fileHasError = true;
              });

              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text('音频时长异常，可能无法正常播放')));
            }

            if (mounted) {
              setState(() {});
            }
          } catch (e) {
            debugPrint('音频加载失败: $e');
            if (mounted) {
              setState(() {
                _fileHasError = true;
              });

              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text('音频加载失败: $e')));
            }
          }
        });
      }

      // 监听音频播放状态
      _audioPlayer.playerStateStream.listen((state) {
        debugPrint('播放状态变化: $state');
        if (mounted) {
          setState(() {
            _isPlaying = state.playing;
          });
        }
      });

      // 监听播放进度
      _audioPlayer.positionStream.listen((position) {
        if (mounted) {
          setState(() {
            _position = position;
          });
        }
      });

      // 监听音频总时长
      _audioPlayer.durationStream.listen((duration) {
        debugPrint('检测到音频时长变化: $duration');
        if (duration != null && mounted) {
          setState(() {
            _duration = duration;
          });
        }
      });
    } catch (e) {
      debugPrint('初始化失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('初始化失败: $e')));
      }
    }
  }

  void _updateRecord() {
    final record = _storageService.getRecord(widget.recordId);
    if (record != null) {
      setState(() {
        _record = record;
        _titleController.text = record.title;
        if (record.transcription != null) {
          _transcriptionController.text = record.transcription!;
        }
      });
    }
  }

  Future<void> _checkFileExists() async {
    final record = _storageService.getRecord(widget.recordId);
    if (record != null) {
      final file = File(record.filePath);
      final exists = await file.exists();

      debugPrint('检查文件存在性: ${record.filePath}');
      debugPrint('文件是否存在: $exists');

      if (!exists) {
        debugPrint('文件不存在，检查目录是否存在...');
        final directory = Directory(file.parent.path);
        final dirExists = await directory.exists();
        debugPrint('目录是否存在: $dirExists');

        if (dirExists) {
          // 列出目录中的文件
          final files = await directory.list().toList();
          debugPrint('目录中的文件: ${files.map((e) => e.path).join(', ')}');
        }

        setState(() {
          _fileExists = false;
          _fileHasError = false;
        });
      } else {
        // 文件存在，检查文件大小
        try {
          final fileSize = await file.length();
          debugPrint('音频文件大小: $fileSize 字节');

          setState(() {
            _fileExists = true;

            // 如果文件过小，标记为有错误但仍然显示
            if (fileSize < 100) {
              debugPrint('警告: 文件过小，可能已损坏');
              _fileHasError = true;
            } else {
              _fileHasError = false;
            }
          });
        } catch (e) {
          debugPrint('检查文件大小失败: $e');
          setState(() {
            _fileExists = exists;
            _fileHasError = true; // 出错时标记为有问题
          });
        }
      }
    } else {
      debugPrint('无法获取录音记录，ID: ${widget.recordId}');
      setState(() {
        _fileExists = false;
        _fileHasError = false;
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _transcriptionController.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  // 播放/暂停录音
  Future<void> _togglePlayback() async {
    try {
      debugPrint(
        '当前播放状态: $_isPlaying, 音频文件: ${_record.filePath}, 时长: $_duration',
      );

      if (_isPlaying) {
        debugPrint('正在暂停播放');
        await _audioPlayer.pause();
      } else {
        // 如果音频从未被加载或当前音频时长为零，重新加载
        if (_audioPlayer.duration == null ||
            _audioPlayer.duration == Duration.zero) {
          debugPrint('重新加载音频文件');
          try {
            // 重新检查文件是否存在
            final file = File(_record.filePath);
            if (!await file.exists()) {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text('音频文件不存在，无法播放')));
              return;
            }

            await _audioPlayer.setAudioSource(
              AudioSource.uri(Uri.file(_record.filePath)),
              preload: true,
            );

            // 等待加载完成并获取时长
            _duration = await _audioPlayer.durationFuture ?? Duration.zero;
            debugPrint('重新加载后的音频时长: $_duration');

            if (_duration.inMilliseconds == 0) {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text('音频文件可能已损坏，无法获取时长')));
              return;
            }

            // 更新UI
            if (mounted) setState(() {});
          } catch (e) {
            debugPrint('加载音频失败: $e');
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text('加载音频失败: $e')));
            return;
          }
        }

        debugPrint('开始播放');
        await _audioPlayer.play();
      }
    } catch (e) {
      debugPrint('播放操作失败: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('播放失败: $e')));
    }
  }

  // 播放文本
  Future<void> _speakText() async {
    if (_transcriptionController.text.isNotEmpty) {
      await _ttsService.speak(_transcriptionController.text);
    }
  }

  // 保存标题
  Future<void> _saveTitle() async {
    final newTitle = _titleController.text.trim();
    if (newTitle.isNotEmpty && newTitle != _record.title) {
      await _storageService.updateTitle(widget.recordId, newTitle);
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('标题已保存')));
    }
  }

  // 保存转录文本
  Future<void> _saveTranscription() async {
    final newTranscription = _transcriptionController.text.trim();
    if (newTranscription != _record.transcription) {
      await _storageService.updateTranscription(
        widget.recordId,
        newTranscription,
      );
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('转录文本已保存')));
    }
  }

  // 格式化时长
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('语音详情')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('语音详情'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () async {
              await _saveTitle();
              await _saveTranscription();
            },
            tooltip: '保存所有更改',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题编辑
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: '标题',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.edit),
              ),
              onEditingComplete: _saveTitle,
            ),
            const SizedBox(height: 24),

            // 音频播放器
            if (_fileExists)
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // 文件有错误时显示警告
                      if (_fileHasError)
                        Container(
                          margin: EdgeInsets.only(bottom: 12),
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.yellow.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.orange),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.warning_amber_rounded,
                                color: Colors.orange,
                              ),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '录音文件可能已损坏，播放可能失败',
                                  style: TextStyle(
                                    color: Colors.orange.shade800,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _formatDuration(_position),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            _formatDuration(_duration),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                      Slider(
                        value: _position.inSeconds.toDouble(),
                        max:
                            _duration.inSeconds > 0
                                ? _duration.inSeconds.toDouble()
                                : 1.0,
                        onChanged:
                            _fileHasError
                                ? null
                                : (value) {
                                  if (_duration.inSeconds > 0) {
                                    _audioPlayer.seek(
                                      Duration(seconds: value.toInt()),
                                    );
                                  }
                                },
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _fileHasError ? null : _togglePlayback,
                              borderRadius: BorderRadius.circular(30),
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color:
                                      _fileHasError
                                          ? Colors.grey.withValues(alpha: 0.1)
                                          : Theme.of(
                                            context,
                                          ).primaryColor.withValues(alpha: 0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  _isPlaying ? Icons.pause : Icons.play_arrow,
                                  size: 40,
                                  color:
                                      _fileHasError
                                          ? Colors.grey
                                          : Theme.of(context).primaryColor,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (_position.inSeconds > 0 || _isPlaying)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            _isPlaying ? '正在播放...' : '已暂停',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              )
            else
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        '音频文件不存在或已损坏',
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '文件路径: ${_record.filePath}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      OutlinedButton.icon(
                        onPressed: () async {
                          // 重新检查文件是否存在
                          await _checkFileExists();
                          // 显示调试信息
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                '已重新检查文件状态: ${_fileExists ? '文件存在' : '文件不存在'}${_fileHasError ? '，但文件可能已损坏' : ''}',
                              ),
                            ),
                          );
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('重新检查'),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // 转录文本编辑
            Row(
              children: [
                const Expanded(
                  child: Text(
                    '语音转录',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                OutlinedButton.icon(
                  onPressed:
                      _transcriptionController.text.isNotEmpty
                          ? _speakText
                          : null,
                  icon: const Icon(Icons.volume_up),
                  label: const Text('朗读文本'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _transcriptionController,
              decoration: const InputDecoration(
                hintText: '暂无转录文本',
                border: OutlineInputBorder(),
              ),
              maxLines: 10,
              onEditingComplete: _saveTranscription,
            ),
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: _saveTranscription,
                child: const Text('保存转录文本'),
              ),
            ),

            // 创建日期信息
            const SizedBox(height: 16),
            Text(
              '创建时间: ${_record.createdAt.toString().substring(0, 19)}',
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
