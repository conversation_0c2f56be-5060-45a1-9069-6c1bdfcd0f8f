import 'package:flutter/material.dart';

import '../config/app_theme.dart';
import '../services/smart_transcription_service.dart';

/// 智能转录页面
class SmartTranscriptionPage extends StatefulWidget {
  const SmartTranscriptionPage({super.key});

  @override
  State<SmartTranscriptionPage> createState() => _SmartTranscriptionPageState();
}

class _SmartTranscriptionPageState extends State<SmartTranscriptionPage>
    with TickerProviderStateMixin {
  final SmartTranscriptionService _service = SmartTranscriptionService();

  bool _isInitialized = false;
  bool _isInitializing = true;
  String _transcriptionText = '';
  String _selectedLanguage = 'zh_CN';
  String _errorMessage = '';

  final Map<String, String> _supportedLanguages = {
    'zh_CN': '中文（简体）',
    'en_US': 'English (US)',
    'ja_JP': '日本語',
    'ko_KR': '한국어',
  };

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    try {
      setState(() {
        _isInitializing = true;
        _errorMessage = '';
      });

      final success = await _service.initialize();

      if (success) {
        _setupStreams();
        setState(() {
          _isInitialized = true;
          _isInitializing = false;
        });
      } else {
        setState(() {
          _isInitialized = false;
          _isInitializing = false;
          _errorMessage = '初始化失败，请检查麦克风权限';
        });
      }
    } catch (e) {
      setState(() {
        _isInitialized = false;
        _isInitializing = false;
        _errorMessage = '初始化异常: $e';
      });
    }
  }

  void _setupStreams() {
    // 监听转录结果
    _service.resultStream.listen((text) {
      setState(() {
        _transcriptionText = text;
        _errorMessage = '';
      });
    });

    // 监听错误信息
    _service.errorStream.listen((error) {
      setState(() {
        _errorMessage = error;
      });
    });

    // 监听状态变化
    _service.state.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _service.dispose();
    super.dispose();
  }

  Future<void> _startTranscription() async {
    if (!_isInitialized) {
      final success = await _service.initialize();
      if (!success) {
        setState(() {
          _errorMessage = '服务初始化失败';
        });
        return;
      }
    }

    final success = await _service.startRealtimeTranscription(
      language: _selectedLanguage,
    );

    if (!success) {
      setState(() {
        _errorMessage = '启动转录失败';
      });
    }
  }

  Future<void> _stopTranscription() async {
    await _service.stopRealtimeTranscription();
  }

  void _clearTranscription() {
    _service.clearResults();
    setState(() {
      _transcriptionText = '';
      _errorMessage = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: const Text('智能转录'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isInitializing
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // 状态指示器
                  _buildStatusIndicator(),

                  // 语言选择
                  _buildLanguageSelector(),

                  // 转录结果显示
                  Expanded(child: _buildTranscriptionDisplay()),

                  // 错误显示
                  if (_errorMessage.isNotEmpty) _buildErrorDisplay(),

                  // 控制按钮
                  _buildControlButtons(),
                ],
              ),
    );
  }

  Widget _buildStatusIndicator() {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (_service.state.value) {
      case TranscriptionState.idle:
        statusColor = Colors.grey;
        statusText = '准备就绪';
        statusIcon = Icons.mic_off;
        break;
      case TranscriptionState.transcribing:
        statusColor = Colors.green;
        statusText = '正在转录...';
        statusIcon = Icons.mic;
        break;
      case TranscriptionState.completed:
        statusColor = Colors.blue;
        statusText = '转录完成';
        statusIcon = Icons.check_circle;
        break;
      case TranscriptionState.error:
        statusColor = Colors.red;
        statusText = '转录出错';
        statusIcon = Icons.error;
        break;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      color: statusColor,
      child: Row(
        children: [
          Icon(statusIcon, color: Colors.white, size: 24),
          const SizedBox(width: 12),
          Text(
            statusText,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          const Icon(Icons.language, color: AppTheme.primaryColor),
          const SizedBox(width: 12),
          const Text(
            '语言：',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppTheme.textDarkColor,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: DropdownButton<String>(
              value: _selectedLanguage,
              isExpanded: true,
              underline: const SizedBox(),
              items:
                  _supportedLanguages.entries.map((entry) {
                    return DropdownMenuItem<String>(
                      value: entry.key,
                      child: Text(entry.value),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedLanguage = value;
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTranscriptionDisplay() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '转录结果',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Text(
                _transcriptionText.isEmpty
                    ? '点击开始按钮开始转录...'
                    : _transcriptionText,
                style: TextStyle(
                  fontSize: 16,
                  color:
                      _transcriptionText.isEmpty
                          ? AppTheme.textLightColor
                          : AppTheme.textDarkColor,
                  height: 1.5,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorDisplay() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.error, color: Colors.red.shade600, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _errorMessage,
              style: TextStyle(color: Colors.red.shade700, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed:
                      _service.state.value == TranscriptionState.transcribing
                          ? _stopTranscription
                          : _startTranscription,
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _service.state.value == TranscriptionState.transcribing
                            ? Colors.red
                            : AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    _service.state.value == TranscriptionState.transcribing
                        ? '停止转录'
                        : '开始转录',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton(
                  onPressed: _clearTranscription,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.primaryColor,
                    side: BorderSide(color: AppTheme.primaryColor),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    '清除',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
