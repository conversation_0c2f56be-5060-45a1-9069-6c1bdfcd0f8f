import 'package:flutter/material.dart';

import '../markdown/models/markdown_watermark.dart';
import 'app_theme.dart';
import 'chinese_traditional_colors.dart';
import 'constants.dart';

class AppSettings {
  final ThemeMode themeMode;
  final AppThemeType themeType;
  final ChineseTraditionalColorTheme? chineseTraditionalColorTheme; // 中国传统色主题
  final double aiTemperature;
  final bool autoSaveChat;
  final bool privacyEnhanced;
  final bool enableNotifications;
  final String? defaultModelId;
  final bool enableStreamingResponse;
  final int requestTimeout; // 请求超时时间（秒）
  final MarkdownWatermark watermark; // 添加水印设置

  AppSettings({
    this.themeMode = ThemeMode.system,
    this.themeType = AppThemeType.materialYou,
    this.chineseTraditionalColorTheme,
    this.aiTemperature = AppConstants.defaultTemperature,
    this.autoSaveChat = AppConstants.defaultAutoSaveChat,
    this.privacyEnhanced = AppConstants.defaultPrivacyEnhanced,
    this.enableNotifications = AppConstants.defaultNotifications,
    this.defaultModelId,
    this.enableStreamingResponse = AppConstants.defaultStreamingResponse,
    this.requestTimeout = AppConstants.defaultRequestTimeout,
    MarkdownWatermark? watermark,
  }) : watermark = watermark ?? MarkdownWatermark.defaultWatermark();

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    MarkdownWatermark? watermark;
    if (json.containsKey('watermark')) {
      final watermarkJson = json['watermark'] as Map<String, dynamic>;
      watermark = MarkdownWatermark(
        text: watermarkJson['text'] ?? '',
        textColor: Color(watermarkJson['textColor'] ?? 0),
        fontSize: watermarkJson['fontSize']?.toDouble() ?? 12.0,
        fontFamily: watermarkJson['fontFamily'] ?? '',
        fontStyle:
            watermarkJson['fontStyle'] == 1
                ? FontStyle.italic
                : FontStyle.normal,
        fontWeight:
            watermarkJson['fontWeight'] == 1
                ? FontWeight.bold
                : FontWeight.normal,
        isVisible: watermarkJson['isVisible'] ?? false,
        position: WatermarkPosition.values[watermarkJson['position'] ?? 0],
        opacity: watermarkJson['opacity']?.toDouble() ?? 0.1,
      );
    }

    return AppSettings(
      themeMode: ThemeMode.values[json['themeMode'] ?? 2],
      themeType:
          json.containsKey('themeType')
              ? AppThemeType.values[json['themeType']]
              : AppThemeType.materialYou,
      chineseTraditionalColorTheme: ChineseTraditionalColors.fromString(
        json['chineseTraditionalColorTheme'],
      ),
      aiTemperature: json['aiTemperature'] ?? AppConstants.defaultTemperature,
      autoSaveChat: json['autoSaveChat'] ?? AppConstants.defaultAutoSaveChat,
      privacyEnhanced:
          json['privacyEnhanced'] ?? AppConstants.defaultPrivacyEnhanced,
      enableNotifications:
          json['enableNotifications'] ?? AppConstants.defaultNotifications,
      defaultModelId: json['defaultModelId'],
      enableStreamingResponse:
          json['enableStreamingResponse'] ??
          AppConstants.defaultStreamingResponse,
      requestTimeout:
          json['requestTimeout'] ?? AppConstants.defaultRequestTimeout,
      watermark: watermark,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'themeMode': themeMode.index,
      'themeType': themeType.index,
      'chineseTraditionalColorTheme':
          chineseTraditionalColorTheme != null
              ? ChineseTraditionalColors.themeToString(
                chineseTraditionalColorTheme!,
              )
              : null,
      'aiTemperature': aiTemperature,
      'autoSaveChat': autoSaveChat,
      'privacyEnhanced': privacyEnhanced,
      'enableNotifications': enableNotifications,
      'defaultModelId': defaultModelId,
      'enableStreamingResponse': enableStreamingResponse,
      'requestTimeout': requestTimeout,
      'watermark': {
        'text': watermark.text,
        'textColor': watermark.textColor.toARGB32(),
        'fontSize': watermark.fontSize,
        'fontFamily': watermark.fontFamily,
        'fontStyle': watermark.fontStyle == FontStyle.italic ? 1 : 0,
        'fontWeight': watermark.fontWeight == FontWeight.bold ? 1 : 0,
        'isVisible': watermark.isVisible,
        'position': watermark.position.index,
        'opacity': watermark.opacity,
      },
    };
  }

  AppSettings copyWith({
    ThemeMode? themeMode,
    AppThemeType? themeType,
    ChineseTraditionalColorTheme? chineseTraditionalColorTheme,
    double? aiTemperature,
    bool? autoSaveChat,
    bool? privacyEnhanced,
    bool? enableNotifications,
    String? defaultModelId,
    bool? enableStreamingResponse,
    int? requestTimeout,
    MarkdownWatermark? watermark,
  }) {
    return AppSettings(
      themeMode: themeMode ?? this.themeMode,
      themeType: themeType ?? this.themeType,
      chineseTraditionalColorTheme:
          chineseTraditionalColorTheme ?? this.chineseTraditionalColorTheme,
      aiTemperature: aiTemperature ?? this.aiTemperature,
      autoSaveChat: autoSaveChat ?? this.autoSaveChat,
      privacyEnhanced: privacyEnhanced ?? this.privacyEnhanced,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      defaultModelId: defaultModelId ?? this.defaultModelId,
      enableStreamingResponse:
          enableStreamingResponse ?? this.enableStreamingResponse,
      requestTimeout: requestTimeout ?? this.requestTimeout,
      watermark: watermark ?? this.watermark,
    );
  }
}
