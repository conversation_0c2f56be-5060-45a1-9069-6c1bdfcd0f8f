import 'package:flutter/material.dart';

/// 主题类型枚举
enum AppThemeType {
  /// Material You 动态主题
  materialYou,

  /// 莫兰迪色系主题
  morandiTheme,

  /// 极简黑白主题
  monochromeTheme,

  /// 自然色系主题
  natureTheme,

  /// 科技感主题
  techTheme,

  /// 中国传统色主题
  chineseTheme,
}

/// 主题配置
class ThemeConfig {
  final double cornerRadius;
  final double fontSizeScale;
  final double colorSaturation;
  final double contrast;
  final bool isDark;

  const ThemeConfig({
    this.cornerRadius = 1.0,
    this.fontSizeScale = 1.0,
    this.colorSaturation = 1.0,
    this.contrast = 1.0,
    this.isDark = false,
  });
}

class AppTheme {
  // 防止实例化
  AppTheme._();

  // 亮色模式颜色
  static const Color primaryColor = Color(0xFF4F46E5); // 靛蓝色
  static const Color primaryLightColor = Color(0xFF818CF8);
  static const Color secondaryColor = Color(0xFF3B82F6); // 蓝色

  static const Color textDarkColor = Color(0xFF1F2937);
  static const Color textMediumColor = Color(0xFF4B5563);
  static const Color textLightColor = Color(0xFF9CA3AF);

  static const Color bgLightColor = Color(0xFFF9FAFB);
  static const Color bgWhiteColor = Color(0xFFFFFFFF);
  static const Color bgIndigo50 = Color(0xFFEEF2FF);

  static const Color borderColor = Color(0xFFE5E7EB);
  static const Color borderActiveColor = Color(0xFFA5B4FC);

  // 深色模式颜色
  static const Color darkPrimaryColor = Color(0xFF6366F1); // 亮一点的靛蓝色
  static const Color darkPrimaryLightColor = Color(0xFF818CF8);
  static const Color darkSecondaryColor = Color(0xFF60A5FA); // 亮一点的蓝色

  static const Color darkTextColor = Color(0xFFF9FAFB);
  static const Color darkTextMediumColor = Color(0xFFD1D5DB);
  static const Color darkTextLightColor = Color(0xFF9CA3AF);

  static const Color darkBgColor = Color(0xFF111827);
  static const Color darkBgLightColor = Color(0xFF1F2937);
  static const Color darkBgIndigo = Color(0xFF312E81);

  static const Color darkBorderColor = Color(0xFF374151);
  static const Color darkBorderActiveColor = Color(0xFF6366F1);

  // 通用颜色
  static const Color greenLight = Color(0xFFDCFCE7);
  static const Color greenDark = Color(0xFF10B981);

  static const Color redLight = Color(0xFFFEE2E2);
  static const Color redDark = Color(0xFFEF4444);

  static const Color yellowLight = Color(0xFFFEF3C7);
  static const Color yellowDark = Color(0xFFF59E0B);

  static const Color purpleLight = Color(0xFFF3E8FF);
  static const Color purpleDark = Color(0xFF8B5CF6);

  static const Color orangeLight = Color(0xFFFFEDD5);
  static const Color orangeDark = Color(0xFFF97316);

  // 中国传统色
  static const Color chineseRed = Color(0xFFCB1B45); // 茜红 - xiān hóng
  static const Color chineseRedLight = Color(0xFFFFE4E1);
  static const Color chineseBlue = Color(0xFF2E4E7E); // 靛青 - diàn qīng
  static const Color chineseBlueLight = Color(0xFFE6EEFF);
  static const Color chineseYellow = Color(0xFFF7C242); // 缃色 - xiāng sè
  static const Color chineseYellowLight = Color(0xFFFFF8E1);
  static const Color chineseGreen = Color(0xFF1A6840); // 松绿 - sōng lǜ
  static const Color chineseGreenLight = Color(0xFFE8F5E9);
  static const Color chineseCyan = Color(0xFF00A6AC); // 石青 - shí qīng
  static const Color chineseCyanLight = Color(0xFFE0F7FA);
  static const Color chinesePurple = Color(0xFF8B2671); // 紫色 - zǐ sè
  static const Color chinesePurpleLight = Color(0xFFF3E5F5);
  static const Color chineseWhite = Color(0xFFF2ECDE); // 缟 - gǎo
  static const Color chineseBlack = Color(0xFF161823); // 玄青 - xuán qīng

  // 渐变
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryColor, secondaryColor],
  );

  static const LinearGradient blueGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF60A5FA), Color(0xFF3B82F6)],
  );

  static const LinearGradient greenGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF34D399), Color(0xFF10B981)],
  );

  static const LinearGradient purpleGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFFA78BFA), Color(0xFF8B5CF6)],
  );

  static const LinearGradient yellowGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFFFCD34D), Color(0xFFF59E0B)],
  );

  static const LinearGradient orangeGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFFFFBA7C), Color(0xFFF97316)],
  );

  static const LinearGradient redGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFFE53E3E), Color(0xFFD53F8C)],
  );

  static const LinearGradient chineseGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [chineseRed, chinesePurple],
  );

  // 圆角
  static const double borderRadiusXS = 8.0;
  static const double borderRadiusSM = 12.0;
  static const double borderRadiusMD = 16.0;
  static const double borderRadiusLG = 24.0;
  static const double borderRadiusXL = 40.0;

  // 边距
  static const double paddingXS = 4.0;
  static const double paddingSM = 8.0;
  static const double paddingMD = 16.0;
  static const double paddingLG = 24.0;
  static const double paddingXL = 32.0;

  // 莫兰迪色系
  static const Color morandiPrimary = Color(0xFF94A6B4);
  static const Color morandiSecondary = Color(0xFFB4A394);
  static const Color morandiAccent = Color(0xFFB494A6);
  static const Color morandiBg = Color(0xFFF5F5F3);
  static const Color morandiText = Color(0xFF4A4A4A);

  // 极简黑白
  static const Color monoPrimary = Color(0xFF2C2C2C);
  static const Color monoSecondary = Color(0xFF5C5C5C);
  static const Color monoAccent = Color(0xFF8C8C8C);
  static const Color monoBg = Color(0xFFFAFAFA);
  static const Color monoText = Color(0xFF1A1A1A);

  // 自然色系
  static const Color naturePrimary = Color(0xFF7FA480);
  static const Color natureSecondary = Color(0xFFB7C9B7);
  static const Color natureAccent = Color(0xFFD4B7A4);
  static const Color natureBg = Color(0xFFF8F6F3);
  static const Color natureText = Color(0xFF3C4A3C);

  // 科技感
  static const Color techPrimary = Color(0xFF0084FF);
  static const Color techSecondary = Color(0xFF00E5FF);
  static const Color techAccent = Color(0xFF00FFD1);
  static const Color techBg = Color(0xFF0A1929);
  static const Color techText = Color(0xFFE6F3FF);

  // 优化后的中国传统色
  static const Color chinesePrimary = Color(0xFFB4002D); // 枫红
  static const Color chineseSecondary = Color(0xFF247B70); // 玉色
  static const Color chineseAccent = Color(0xFFB68D4C); // 琥珀
  static const Color chineseBg = Color(0xFFF7F4ED); // 玉脂
  static const Color chineseText = Color(0xFF2D2B27); // 墨

  // 亮色主题
  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: primaryColor,
      primary: primaryColor,
      secondary: secondaryColor,
      surface: bgWhiteColor,
    ),
    scaffoldBackgroundColor: bgLightColor,
    textTheme: _getTextTheme(),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMD),
        ),
        elevation: 0,
        padding: const EdgeInsets.symmetric(
          horizontal: paddingLG,
          vertical: paddingMD,
        ),
        textStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: const BorderSide(color: borderColor),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMD),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: paddingLG,
          vertical: paddingMD,
        ),
        textStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      fillColor: bgLightColor,
      filled: true,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
        borderSide: const BorderSide(color: borderColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
        borderSide: const BorderSide(color: borderColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
        borderSide: const BorderSide(color: borderActiveColor),
      ),
      contentPadding: const EdgeInsets.all(paddingMD),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: bgWhiteColor,
      elevation: 0,
      centerTitle: false,
      iconTheme: IconThemeData(color: textDarkColor),
      titleTextStyle: TextStyle(
        color: textDarkColor,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),
    cardTheme: CardThemeData(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
        side: const BorderSide(color: borderColor),
      ),
      color: bgWhiteColor,
    ),
    dividerTheme: const DividerThemeData(
      color: borderColor,
      thickness: 1,
      space: 1,
    ),
  );

  // 深色主题
  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: darkPrimaryColor,
      brightness: Brightness.dark,
      primary: darkPrimaryColor,
      secondary: darkSecondaryColor,
      surface: darkBgLightColor,
    ),
    scaffoldBackgroundColor: darkBgColor,
    textTheme: _getTextTheme(AppThemeType.materialYou, true),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: darkPrimaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMD),
        ),
        elevation: 0,
        padding: const EdgeInsets.symmetric(
          horizontal: paddingLG,
          vertical: paddingMD,
        ),
        textStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: darkPrimaryColor,
        side: const BorderSide(color: darkBorderColor),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMD),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: paddingLG,
          vertical: paddingMD,
        ),
        textStyle: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      fillColor: darkBgLightColor,
      filled: true,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
        borderSide: const BorderSide(color: darkBorderColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
        borderSide: const BorderSide(color: darkBorderColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
        borderSide: const BorderSide(color: darkBorderActiveColor),
      ),
      contentPadding: const EdgeInsets.all(paddingMD),
      hintStyle: const TextStyle(color: darkTextLightColor),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: darkBgLightColor,
      elevation: 0,
      centerTitle: false,
      iconTheme: IconThemeData(color: darkTextColor),
      titleTextStyle: TextStyle(
        color: darkTextColor,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),
    cardTheme: CardThemeData(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
        side: const BorderSide(color: darkBorderColor),
      ),
      color: darkBgLightColor,
    ),
    dividerTheme: const DividerThemeData(
      color: darkBorderColor,
      thickness: 1,
      space: 1,
    ),
    dialogTheme: DialogThemeData(
      backgroundColor: darkBgLightColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
      ),
    ),
    bottomSheetTheme: const BottomSheetThemeData(
      backgroundColor: darkBgLightColor,
    ),
    popupMenuTheme: PopupMenuThemeData(
      color: darkBgLightColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
      ),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return darkPrimaryColor;
        }
        return null;
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return darkPrimaryColor.withValues(alpha: 0.5);
        }
        return null;
      }),
    ),
    checkboxTheme: CheckboxThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return darkPrimaryColor;
        }
        return null;
      }),
    ),
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return darkPrimaryColor;
        }
        return null;
      }),
    ),
  );

  // 蓝色主题 - 亮色模式
  static ThemeData blueLightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: const Color(0xFF1E88E5),
      primary: const Color(0xFF1E88E5),
      secondary: const Color(0xFF42A5F5),
      surface: bgWhiteColor,
    ),
    scaffoldBackgroundColor: const Color(0xFFF5F9FF),
    textTheme: _getTextTheme(),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF1E88E5),
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMD),
        ),
      ),
    ),
  );

  // 蓝色主题 - 深色模式
  static ThemeData blueDarkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: ColorScheme.fromSeed(
      seedColor: const Color(0xFF42A5F5),
      brightness: Brightness.dark,
      primary: const Color(0xFF42A5F5),
      secondary: const Color(0xFF90CAF9),
      surface: const Color(0xFF1A2536),
    ),
    scaffoldBackgroundColor: const Color(0xFF0A1929),
  );

  // 绿色主题 - 亮色模式
  static ThemeData greenLightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: const Color(0xFF2E7D32),
      primary: const Color(0xFF2E7D32),
      secondary: const Color(0xFF66BB6A),
      surface: bgWhiteColor,
    ),
    scaffoldBackgroundColor: const Color(0xFFF5FFF7),
  );

  // 绿色主题 - 深色模式
  static ThemeData greenDarkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: ColorScheme.fromSeed(
      seedColor: const Color(0xFF66BB6A),
      brightness: Brightness.dark,
      primary: const Color(0xFF66BB6A),
      secondary: const Color(0xFFA5D6A7),
      surface: const Color(0xFF1A2E1C),
    ),
    scaffoldBackgroundColor: const Color(0xFF0A1F0F),
  );

  // 紫色主题 - 亮色模式
  static ThemeData purpleLightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: const Color(0xFF7B1FA2),
      primary: const Color(0xFF7B1FA2),
      secondary: const Color(0xFFAB47BC),
      surface: bgWhiteColor,
    ),
    scaffoldBackgroundColor: const Color(0xFFFAF5FF),
  );

  // 紫色主题 - 深色模式
  static ThemeData purpleDarkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: ColorScheme.fromSeed(
      seedColor: const Color(0xFFAB47BC),
      brightness: Brightness.dark,
      primary: const Color(0xFFAB47BC),
      secondary: const Color(0xFFCE93D8),
      surface: const Color(0xFF2A1930),
    ),
    scaffoldBackgroundColor: const Color(0xFF170A1E),
  );

  // 中国传统色主题 - 亮色模式
  static ThemeData chineseLightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: chinesePrimary,
      primary: chinesePrimary,
      secondary: chineseCyan,
      surface: chineseWhite,
    ),
    scaffoldBackgroundColor: const Color(0xFFF9F6F0),
    textTheme: _getTextTheme(),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: chinesePrimary,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMD),
        ),
      ),
    ),
    cardTheme: CardThemeData(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
        side: const BorderSide(color: Color(0xFFE8E0D5)),
      ),
      color: chineseWhite,
    ),
  );

  // 中国传统色主题 - 深色模式
  static ThemeData chineseDarkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: ColorScheme.fromSeed(
      seedColor: chinesePrimary,
      brightness: Brightness.dark,
      primary: chinesePrimary,
      secondary: chineseCyan,
      surface: const Color(0xFF2A2A35),
    ),
    scaffoldBackgroundColor: chineseBlack,
    textTheme: _getTextTheme(),
    cardTheme: CardThemeData(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMD),
        side: const BorderSide(color: Color(0xFF3A3A45)),
      ),
      color: const Color(0xFF2A2A35),
    ),
  );

  /// 获取安全的文本主题
  static TextTheme _getTextTheme([
    AppThemeType themeType = AppThemeType.materialYou,
    bool isDark = false,
  ]) {
    // 使用系统默认字体
    final Color primaryTextColor = isDark ? darkTextColor : textDarkColor;
    final Color mediumTextColor =
        isDark ? darkTextMediumColor : textMediumColor;
    final Color lightTextColor = isDark ? darkTextLightColor : textLightColor;

    return TextTheme(
      displayLarge: TextStyle(color: primaryTextColor),
      displayMedium: TextStyle(color: primaryTextColor),
      displaySmall: TextStyle(color: primaryTextColor),
      headlineLarge: TextStyle(color: primaryTextColor),
      headlineMedium: TextStyle(color: primaryTextColor),
      headlineSmall: TextStyle(color: primaryTextColor),
      titleLarge: TextStyle(color: primaryTextColor),
      titleMedium: TextStyle(color: primaryTextColor),
      titleSmall: TextStyle(color: primaryTextColor),
      bodyLarge: TextStyle(color: primaryTextColor),
      bodyMedium: TextStyle(color: primaryTextColor),
      bodySmall: TextStyle(color: mediumTextColor),
      labelLarge: TextStyle(color: primaryTextColor),
      labelMedium: TextStyle(color: mediumTextColor),
      labelSmall: TextStyle(color: lightTextColor),
    );
  }

  /// 获取主题
  static ThemeData getTheme(AppThemeType themeType, bool isDark) {
    switch (themeType) {
      case AppThemeType.materialYou:
        return isDark ? darkTheme : lightTheme;
      case AppThemeType.morandiTheme:
        return morandiTheme(isDark);
      case AppThemeType.monochromeTheme:
        return monochromeTheme(isDark);
      case AppThemeType.natureTheme:
        return natureTheme(isDark);
      case AppThemeType.techTheme:
        return techTheme(isDark);
      case AppThemeType.chineseTheme:
        return chineseTheme(isDark);
    }
  }

  // 适配暗黑模式的颜色
  static Color adaptDarkColor(
    Color lightColor,
    Color darkColor,
    bool isDarkMode,
  ) {
    return isDarkMode ? darkColor : lightColor;
  }

  // 获取主题名称
  static String getThemeName(AppThemeType type) {
    switch (type) {
      case AppThemeType.materialYou:
        return 'Material You';
      case AppThemeType.morandiTheme:
        return '莫兰迪风格';
      case AppThemeType.monochromeTheme:
        return '极简黑白';
      case AppThemeType.natureTheme:
        return '自然色系';
      case AppThemeType.techTheme:
        return '科技感';
      case AppThemeType.chineseTheme:
        return '中国传统色';
    }
  }

  /// 获取主题图标
  static IconData getThemeIcon(AppThemeType type) {
    switch (type) {
      case AppThemeType.materialYou:
        return Icons.palette_outlined;
      case AppThemeType.morandiTheme:
        return Icons.opacity;
      case AppThemeType.monochromeTheme:
        return Icons.contrast;
      case AppThemeType.natureTheme:
        return Icons.eco_outlined;
      case AppThemeType.techTheme:
        return Icons.memory;
      case AppThemeType.chineseTheme:
        return Icons.brush;
    }
  }

  /// 获取主题描述
  static String getThemeDescription(AppThemeType type) {
    switch (type) {
      case AppThemeType.materialYou:
        return '根据壁纸自动提取的动态主题';
      case AppThemeType.morandiTheme:
        return '柔和优雅的莫兰迪色系';
      case AppThemeType.monochromeTheme:
        return '简约纯粹的黑白配色';
      case AppThemeType.natureTheme:
        return '舒适自然的生态色系';
      case AppThemeType.techTheme:
        return '充满未来感的科技色彩';
      case AppThemeType.chineseTheme:
        return '传统与现代结合的东方美学';
    }
  }

  /// 获取主题颜色
  static Color getThemeColor(AppThemeType type) {
    switch (type) {
      case AppThemeType.materialYou:
        return const Color(0xFF6750A4);
      case AppThemeType.morandiTheme:
        return morandiPrimary;
      case AppThemeType.monochromeTheme:
        return monoPrimary;
      case AppThemeType.natureTheme:
        return naturePrimary;
      case AppThemeType.techTheme:
        return techPrimary;
      case AppThemeType.chineseTheme:
        return chinesePrimary;
    }
  }

  // 中国传统色主题
  static ThemeData chineseTheme(bool isDark) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: chinesePrimary,
        brightness: isDark ? Brightness.dark : Brightness.light,
        primary: chinesePrimary,
        secondary: chineseSecondary,
      ),
      scaffoldBackgroundColor: isDark ? chineseBlack : chineseBg,
      textTheme: _getTextTheme(AppThemeType.chineseTheme, isDark),
      cardTheme: CardThemeData(
        color: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: chinesePrimary.withAlpha(20)),
        ),
      ),
    );
  }

  // 莫兰迪风格主题
  static ThemeData morandiTheme(bool isDark) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: morandiPrimary,
        brightness: isDark ? Brightness.dark : Brightness.light,
        primary: morandiPrimary,
        secondary: morandiSecondary,
      ),
      scaffoldBackgroundColor: isDark ? const Color(0xFF2E2E2E) : morandiBg,
      textTheme: _getTextTheme(AppThemeType.morandiTheme, isDark),
      cardTheme: CardThemeData(
        color: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: morandiPrimary.withAlpha(20)),
        ),
      ),
    );
  }

  // 极简黑白主题
  static ThemeData monochromeTheme(bool isDark) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: monoPrimary,
        brightness: isDark ? Brightness.dark : Brightness.light,
        primary: monoPrimary,
        secondary: monoSecondary,
      ),
      scaffoldBackgroundColor: isDark ? const Color(0xFF121212) : monoBg,
      textTheme: _getTextTheme(AppThemeType.monochromeTheme, isDark),
      cardTheme: CardThemeData(
        color: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: const BorderSide(color: Color(0xFFEEEEEE)),
        ),
      ),
    );
  }

  // 自然色系主题
  static ThemeData natureTheme(bool isDark) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: naturePrimary,
        brightness: isDark ? Brightness.dark : Brightness.light,
        primary: naturePrimary,
        secondary: natureSecondary,
      ),
      scaffoldBackgroundColor: isDark ? const Color(0xFF1C2A1C) : natureBg,
      textTheme: _getTextTheme(AppThemeType.natureTheme, isDark),
      cardTheme: CardThemeData(
        color: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: naturePrimary.withAlpha(20)),
        ),
      ),
    );
  }

  // 科技感主题
  static ThemeData techTheme(bool isDark) {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: techPrimary,
        brightness: isDark ? Brightness.dark : Brightness.light,
        primary: techPrimary,
        secondary: techSecondary,
      ),
      scaffoldBackgroundColor: isDark ? techBg : Colors.white,
      textTheme: _getTextTheme(AppThemeType.techTheme, isDark),
      cardTheme: CardThemeData(
        color: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: techPrimary.withAlpha(20)),
        ),
      ),
    );
  }

  static ThemeData getDefaultTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppTheme.primaryColor,
        brightness: Brightness.light,
      ),
      scaffoldBackgroundColor: AppTheme.bgLightColor,
      appBarTheme: const AppBarTheme(
        backgroundColor: AppTheme.bgWhiteColor,
        elevation: 0,
        iconTheme: IconThemeData(color: AppTheme.textDarkColor),
        titleTextStyle: TextStyle(
          color: AppTheme.textDarkColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      cardTheme: CardThemeData(
        color: AppTheme.bgWhiteColor,
        elevation: 0.5,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.paddingMD,
            vertical: AppTheme.paddingSM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(foregroundColor: AppTheme.primaryColor),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppTheme.primaryColor,
          side: const BorderSide(color: AppTheme.primaryColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppTheme.bgIndigo50,
        contentPadding: const EdgeInsets.all(AppTheme.paddingMD),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          borderSide: const BorderSide(color: AppTheme.borderActiveColor),
        ),
        hintStyle: const TextStyle(color: AppTheme.textLightColor),
      ),
    );
  }

  static ThemeData getDartTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppTheme.darkPrimaryColor,
        brightness: Brightness.dark,
      ),
      scaffoldBackgroundColor: AppTheme.darkBgColor,
      appBarTheme: const AppBarTheme(
        backgroundColor: AppTheme.darkBgLightColor,
        elevation: 0,
        iconTheme: IconThemeData(color: AppTheme.darkTextColor),
        titleTextStyle: TextStyle(
          color: AppTheme.darkTextColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      cardTheme: CardThemeData(
        color: AppTheme.darkBgLightColor,
        elevation: 0.5,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.darkPrimaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.paddingMD,
            vertical: AppTheme.paddingSM,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(foregroundColor: AppTheme.darkPrimaryColor),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppTheme.darkPrimaryColor,
          side: const BorderSide(color: AppTheme.darkPrimaryColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppTheme.darkBgLightColor,
        contentPadding: const EdgeInsets.all(AppTheme.paddingMD),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          borderSide: const BorderSide(color: AppTheme.darkBorderActiveColor),
        ),
        hintStyle: const TextStyle(color: AppTheme.darkTextLightColor),
      ),
    );
  }

}
