import 'package:flutter/material.dart';

/// 中国传统色主题类型
enum ChineseTraditionalColorTheme {
  /// 竹青色方案 - 文雅清新
  bambooGreen,

  /// 青黛色方案 - 深邃典雅
  indigoBlue,

  /// 湖蓝色方案 - 温润如玉
  lakeBlue,

  /// 松绿色方案 - 沉稳大气
  pineGreen,

  /// 靛蓝色方案 - 古典雅致
  deepBlue,

  /// 胭脂色方案 - 温暖柔美
  rougeRed,
}

/// 中国传统色配置
class ChineseTraditionalColorConfig {
  final String name;
  final String description;
  final Color primaryColor;
  final Color secondaryColor;
  final Color accentColor;
  final Color backgroundColor;
  final Color surfaceColor;
  final Color textColor;
  final Color subtitleColor;
  final LinearGradient gradient;
  final LinearGradient lightGradient;

  const ChineseTraditionalColorConfig({
    required this.name,
    required this.description,
    required this.primaryColor,
    required this.secondaryColor,
    required this.accentColor,
    required this.backgroundColor,
    required this.surfaceColor,
    required this.textColor,
    required this.subtitleColor,
    required this.gradient,
    required this.lightGradient,
  });
}

/// 中国传统色彩管理器
class ChineseTraditionalColors {
  /// 获取所有传统色主题配置
  static Map<ChineseTraditionalColorTheme, ChineseTraditionalColorConfig>
  get allThemes => {
    ChineseTraditionalColorTheme.bambooGreen: bambooGreenConfig,
    ChineseTraditionalColorTheme.indigoBlue: indigoBlueConfig,
    ChineseTraditionalColorTheme.lakeBlue: lakeBlueConfig,
    ChineseTraditionalColorTheme.pineGreen: pineGreenConfig,
    ChineseTraditionalColorTheme.deepBlue: deepBlueConfig,
    ChineseTraditionalColorTheme.rougeRed: rougeRedConfig,
  };

  /// 竹青色方案 - 文雅清新
  static const ChineseTraditionalColorConfig bambooGreenConfig =
      ChineseTraditionalColorConfig(
        name: '竹青',
        description: '文雅清新，如竹般挺拔',
        primaryColor: Color(0xFF789262), // 竹青
        secondaryColor: Color(0xFF8FA777), // 嫩竹青
        accentColor: Color(0xFFA4B88A), // 浅竹青
        backgroundColor: Color(0xFFF8FAF6), // 竹白
        surfaceColor: Color(0xFFFFFFFF),
        textColor: Color(0xFF2D3E2A), // 深竹绿
        subtitleColor: Color(0xFF5A6B57), // 中竹绿
        gradient: LinearGradient(
          colors: [Color(0xFF789262), Color(0xFF8FA777)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        lightGradient: LinearGradient(
          colors: [Color(0xFFF0F4ED), Color(0xFFE8F0E3)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      );

  /// 青黛色方案 - 深邃典雅
  static const ChineseTraditionalColorConfig indigoBlueConfig =
      ChineseTraditionalColorConfig(
        name: '青黛',
        description: '深邃典雅，如古代文人青衫',
        primaryColor: Color(0xFF1B4B5A), // 青黛
        secondaryColor: Color(0xFF2E6B7A), // 浅青黛
        accentColor: Color(0xFF4A8B9A), // 淡青黛
        backgroundColor: Color(0xFFF6F9FA), // 青白
        surfaceColor: Color(0xFFFFFFFF),
        textColor: Color(0xFF1A2B32), // 深青
        subtitleColor: Color(0xFF3A4B52), // 中青
        gradient: LinearGradient(
          colors: [Color(0xFF1B4B5A), Color(0xFF2E6B7A)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        lightGradient: LinearGradient(
          colors: [Color(0xFFEDF4F6), Color(0xFFE3F0F3)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      );

  /// 湖蓝色方案 - 温润如玉
  static const ChineseTraditionalColorConfig lakeBlueConfig =
      ChineseTraditionalColorConfig(
        name: '湖蓝',
        description: '温润如玉，如湖水般清澈',
        primaryColor: Color(0xFF30A9DE), // 湖蓝
        secondaryColor: Color(0xFF4FC3F7), // 浅湖蓝
        accentColor: Color(0xFF81D4FA), // 淡湖蓝
        backgroundColor: Color(0xFFF7FCFF), // 湖白
        surfaceColor: Color(0xFFFFFFFF),
        textColor: Color(0xFF1A3A4A), // 深湖蓝
        subtitleColor: Color(0xFF3A5A6A), // 中湖蓝
        gradient: LinearGradient(
          colors: [Color(0xFF30A9DE), Color(0xFF4FC3F7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        lightGradient: LinearGradient(
          colors: [Color(0xFFEDF8FF), Color(0xFFE1F5FE)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      );

  /// 松绿色方案 - 沉稳大气
  static const ChineseTraditionalColorConfig pineGreenConfig =
      ChineseTraditionalColorConfig(
        name: '松绿',
        description: '沉稳大气，如松柏般挺拔',
        primaryColor: Color(0xFF057748), // 松绿
        secondaryColor: Color(0xFF16A085), // 翠绿
        accentColor: Color(0xFF48C9B0), // 浅翠绿
        backgroundColor: Color(0xFFF6FAF8), // 松白
        surfaceColor: Color(0xFFFFFFFF),
        textColor: Color(0xFF1A3A2A), // 深松绿
        subtitleColor: Color(0xFF3A5A4A), // 中松绿
        gradient: LinearGradient(
          colors: [Color(0xFF057748), Color(0xFF16A085)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        lightGradient: LinearGradient(
          colors: [Color(0xFFEDF7F3), Color(0xFFE8F5F0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      );

  /// 靛蓝色方案 - 古典雅致
  static const ChineseTraditionalColorConfig deepBlueConfig =
      ChineseTraditionalColorConfig(
        name: '靛蓝',
        description: '古典雅致，传统靛染色彩',
        primaryColor: Color(0xFF177CB0), // 靛蓝
        secondaryColor: Color(0xFF4A90E2), // 浅靛蓝
        accentColor: Color(0xFF74A9F7), // 淡靛蓝
        backgroundColor: Color(0xFFF6F9FC), // 靛白
        surfaceColor: Color(0xFFFFFFFF),
        textColor: Color(0xFF1A2A3A), // 深靛蓝
        subtitleColor: Color(0xFF3A4A5A), // 中靛蓝
        gradient: LinearGradient(
          colors: [Color(0xFF177CB0), Color(0xFF4A90E2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        lightGradient: LinearGradient(
          colors: [Color(0xFFEDF4F9), Color(0xFFE3F0F7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      );

  /// 胭脂色方案 - 温暖柔美
  static const ChineseTraditionalColorConfig rougeRedConfig =
      ChineseTraditionalColorConfig(
        name: '胭脂',
        description: '温暖柔美，如胭脂般娇艳',
        primaryColor: Color(0xFFC93756), // 胭脂
        secondaryColor: Color(0xFFE91E63), // 浅胭脂
        accentColor: Color(0xFFF48FB1), // 淡胭脂
        backgroundColor: Color(0xFFFDF7F8), // 胭脂白
        surfaceColor: Color(0xFFFFFFFF),
        textColor: Color(0xFF3A1A2A), // 深胭脂
        subtitleColor: Color(0xFF5A3A4A), // 中胭脂
        gradient: LinearGradient(
          colors: [Color(0xFFC93756), Color(0xFFE91E63)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        lightGradient: LinearGradient(
          colors: [Color(0xFFFCF0F3), Color(0xFFF9E8ED)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      );

  /// 根据主题类型获取配置
  static ChineseTraditionalColorConfig getConfig(
    ChineseTraditionalColorTheme theme,
  ) {
    return allThemes[theme]!;
  }

  /// 获取主题名称
  static String getThemeName(ChineseTraditionalColorTheme theme) {
    return getConfig(theme).name;
  }

  /// 获取主题描述
  static String getThemeDescription(ChineseTraditionalColorTheme theme) {
    return getConfig(theme).description;
  }

  /// 获取主题图标
  static IconData getThemeIcon(ChineseTraditionalColorTheme theme) {
    switch (theme) {
      case ChineseTraditionalColorTheme.bambooGreen:
        return Icons.eco_outlined;
      case ChineseTraditionalColorTheme.indigoBlue:
        return Icons.water_drop_outlined;
      case ChineseTraditionalColorTheme.lakeBlue:
        return Icons.waves_outlined;
      case ChineseTraditionalColorTheme.pineGreen:
        return Icons.park_outlined;
      case ChineseTraditionalColorTheme.deepBlue:
        return Icons.palette_outlined;
      case ChineseTraditionalColorTheme.rougeRed:
        return Icons.favorite_outline;
    }
  }

  /// 从字符串解析主题
  static ChineseTraditionalColorTheme? fromString(String? value) {
    if (value == null) return null;
    try {
      return ChineseTraditionalColorTheme.values.firstWhere(
        (theme) => theme.toString().split('.').last == value,
      );
    } catch (e) {
      return null;
    }
  }

  /// 转换为字符串
  static String themeToString(ChineseTraditionalColorTheme theme) {
    return theme.toString().split('.').last;
  }
}
