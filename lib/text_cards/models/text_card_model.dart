class TextCardModel {
  final String id;
  final String title;
  final String content;
  final String templateId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? customStyles;
  final List<TextBlock>? blocks;

  const TextCardModel({
    required this.id,
    required this.title,
    required this.content,
    required this.templateId,
    required this.createdAt,
    this.updatedAt,
    this.customStyles,
    this.blocks,
  });

  TextCardModel copyWith({
    String? id,
    String? title,
    String? content,
    String? templateId,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? customStyles,
    List<TextBlock>? blocks,
  }) {
    return TextCardModel(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      templateId: templateId ?? this.templateId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      customStyles: customStyles ?? this.customStyles,
      blocks: blocks ?? this.blocks,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'templateId': templateId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'customStyles': customStyles,
      'blocks': blocks?.map((block) => block.toJson()).toList(),
    };
  }

  factory TextCardModel.fromJson(Map<String, dynamic> json) {
    return TextCardModel(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      templateId: json['templateId'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      customStyles: json['customStyles'],
      blocks:
          json['blocks'] != null
              ? (json['blocks'] as List)
                  .map((block) => TextBlock.fromJson(block))
                  .toList()
              : null,
    );
  }
}

class TextBlock {
  final String id;
  final String content;
  final TextBlockType type;
  final Map<String, dynamic> styles;
  final int order;

  const TextBlock({
    required this.id,
    required this.content,
    required this.type,
    required this.styles,
    required this.order,
  });

  TextBlock copyWith({
    String? id,
    String? content,
    TextBlockType? type,
    Map<String, dynamic>? styles,
    int? order,
  }) {
    return TextBlock(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      styles: styles ?? this.styles,
      order: order ?? this.order,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'type': type.name,
      'styles': styles,
      'order': order,
    };
  }

  factory TextBlock.fromJson(Map<String, dynamic> json) {
    return TextBlock(
      id: json['id'],
      content: json['content'],
      type: TextBlockType.values.firstWhere(
        (type) => type.name == json['type'],
      ),
      styles: json['styles'],
      order: json['order'],
    );
  }
}

enum TextBlockType {
  text,
  title,
  subtitle,
  bullet,
  number,
  checkbox,
  table,
  quote,
  code,
}

class CardTemplate {
  final String id;
  final String name;
  final String preview;
  final Map<String, dynamic> styles;
  final String? backgroundImage;

  const CardTemplate({
    required this.id,
    required this.name,
    required this.preview,
    required this.styles,
    this.backgroundImage,
  });

  static List<CardTemplate> getBuiltInTemplates() {
    return [
      CardTemplate(
        id: 'gradient_blue',
        name: '蓝色渐变',
        preview: '优雅的蓝色渐变背景',
        styles: {
          'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          'textColor': '#ffffff',
          'titleColor': '#ffffff',
          'borderRadius': 16.0,
          'padding': 20.0,
        },
      ),
      CardTemplate(
        id: 'card_shadow',
        name: '阴影卡片',
        preview: '简洁的白色卡片带阴影',
        styles: {
          'background': '#ffffff',
          'textColor': '#333333',
          'titleColor': '#2d3748',
          'borderRadius': 12.0,
          'padding': 24.0,
          'shadow': true,
        },
      ),
      CardTemplate(
        id: 'gradient_purple',
        name: '紫色渐变',
        preview: '神秘的紫色渐变',
        styles: {
          'background': 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
          'textColor': '#4a5568',
          'titleColor': '#2d3748',
          'borderRadius': 16.0,
          'padding': 20.0,
        },
      ),
      CardTemplate(
        id: 'nature_green',
        name: '自然绿',
        preview: '清新的绿色主题',
        styles: {
          'background': 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)',
          'textColor': '#2f855a',
          'titleColor': '#1a202c',
          'borderRadius': 20.0,
          'padding': 24.0,
        },
      ),
      CardTemplate(
        id: 'dark_mode',
        name: '深色模式',
        preview: '优雅的深色主题',
        styles: {
          'background': 'linear-gradient(135deg, #2d3748 0%, #4a5568 100%)',
          'textColor': '#e2e8f0',
          'titleColor': '#ffffff',
          'borderRadius': 16.0,
          'padding': 20.0,
        },
      ),
      CardTemplate(
        id: 'warm_orange',
        name: '温暖橙色',
        preview: '温暖的橙色调',
        styles: {
          'background': 'linear-gradient(135deg, #fed7aa 0%, #f97316 100%)',
          'textColor': '#ffffff',
          'titleColor': '#ffffff',
          'borderRadius': 16.0,
          'padding': 20.0,
        },
      ),
    ];
  }
}
