import 'package:flutter/material.dart';

/// 字体管理器
/// 管理应用中所有可用的中文字体
class FontManager {
  /// 可用的中文字体列表
  static const List<FontFamily> availableFonts = [
    FontFamily(
      name: '系统默认',
      fontFamily: null,
      displayName: '系统默认',
      description: '使用系统默认字体',
      category: FontCategory.system,
      isDefault: true,
    ),
    FontFamily(
      name: 'SourceHanSansCN',
      fontFamily: 'SourceHanSansCN',
      displayName: '思源黑体',
      description: 'Adobe与Google联合开发的开源字体，现代简洁',
      category: FontCategory.sansSerif,
      weights: [FontWeight.w400, FontWeight.w500, FontWeight.w700],
    ),
    FontFamily(
      name: 'AlibabaPuHuiTi',
      fontFamily: 'AlibabaPuHuiTi',
      displayName: '阿里巴巴普惠体',
      description: '阿里巴巴开源字体，温和友好',
      category: FontCategory.sansSerif,
      weights: [FontWeight.w400, FontWeight.w500, FontWeight.w700],
    ),
    FontFamily(
      name: 'HarmonyOS_Sans_SC',
      fontFamily: 'HarmonyOS_Sans_SC',
      displayName: 'HarmonyOS Sans',
      description: '华为鸿蒙系统字体，现代科技感',
      category: FontCategory.sansSerif,
      weights: [FontWeight.w400, FontWeight.w500, FontWeight.w700],
    ),
    FontFamily(
      name: 'MiSans',
      fontFamily: 'MiSans',
      displayName: '小米兰亭',
      description: '小米开源字体，简约现代',
      category: FontCategory.sansSerif,
      weights: [FontWeight.w400, FontWeight.w500, FontWeight.w700],
    ),
  ];

  /// 根据名称获取字体
  static FontFamily? getFontByName(String name) {
    try {
      return availableFonts.firstWhere((font) => font.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 根据分类获取字体
  static List<FontFamily> getFontsByCategory(FontCategory category) {
    return availableFonts.where((font) => font.category == category).toList();
  }

  /// 获取默认字体
  static FontFamily getDefaultFont() {
    return availableFonts.firstWhere((font) => font.isDefault);
  }

  /// 获取推荐字体（用于不同场景）
  static FontFamily getRecommendedFont(FontUsageScenario scenario) {
    switch (scenario) {
      case FontUsageScenario.title:
        return getFontByName('SourceHanSansCN') ?? getDefaultFont();
      case FontUsageScenario.body:
        return getFontByName('AlibabaPuHuiTi') ?? getDefaultFont();
      case FontUsageScenario.quote:
        return getFontByName('HarmonyOS_Sans_SC') ?? getDefaultFont();
      case FontUsageScenario.modern:
        return getFontByName('MiSans') ?? getDefaultFont();
      default:
        return getDefaultFont();
    }
  }

  /// 创建文本样式
  static TextStyle createTextStyle({
    required FontFamily fontFamily,
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
  }) {
    return TextStyle(
      fontFamily: fontFamily.fontFamily,
      fontSize: fontSize ?? 16.0,
      fontWeight: fontWeight ?? FontWeight.w400,
      color: color ?? Colors.black,
      height: height ?? 1.4,
      decoration: decoration ?? TextDecoration.none,
    );
  }

  /// 检查字体是否可用
  static bool isFontAvailable(String fontName) {
    return availableFonts.any((font) => font.name == fontName);
  }
}

/// 字体家族模型
class FontFamily {
  final String name;
  final String? fontFamily;
  final String displayName;
  final String description;
  final FontCategory category;
  final List<FontWeight> weights;
  final bool isDefault;

  const FontFamily({
    required this.name,
    required this.fontFamily,
    required this.displayName,
    required this.description,
    required this.category,
    this.weights = const [FontWeight.w400],
    this.isDefault = false,
  });

  /// 获取支持的字重
  List<FontWeight> getSupportedWeights() {
    return weights;
  }

  /// 检查是否支持指定字重
  bool supportsWeight(FontWeight weight) {
    return weights.contains(weight);
  }

  /// 获取最接近的支持字重
  FontWeight getClosestWeight(FontWeight targetWeight) {
    if (supportsWeight(targetWeight)) {
      return targetWeight;
    }

    // 找到最接近的字重
    FontWeight closest = weights.first;
    int minDiff = (targetWeight.index - closest.index).abs();

    for (final weight in weights) {
      final diff = (targetWeight.index - weight.index).abs();
      if (diff < minDiff) {
        minDiff = diff;
        closest = weight;
      }
    }

    return closest;
  }
}

/// 字体分类
enum FontCategory {
  system('系统字体'),
  sansSerif('无衬线'),
  serif('衬线'),
  monospace('等宽'),
  decorative('装饰');

  const FontCategory(this.displayName);
  final String displayName;
}

/// 字体使用场景
enum FontUsageScenario {
  title('标题'),
  body('正文'),
  quote('引用'),
  modern('现代'),
  academic('学术'),
  creative('创意');

  const FontUsageScenario(this.displayName);
  final String displayName;
}

/// 字体预设样式
class FontPresets {
  /// 标题样式
  static TextStyle titleStyle({
    FontFamily? fontFamily,
    Color? color,
  }) {
    final font = fontFamily ?? FontManager.getRecommendedFont(FontUsageScenario.title);
    return FontManager.createTextStyle(
      fontFamily: font,
      fontSize: 24.0,
      fontWeight: FontWeight.w700,
      color: color ?? Colors.black,
      height: 1.2,
    );
  }

  /// 副标题样式
  static TextStyle subtitleStyle({
    FontFamily? fontFamily,
    Color? color,
  }) {
    final font = fontFamily ?? FontManager.getRecommendedFont(FontUsageScenario.title);
    return FontManager.createTextStyle(
      fontFamily: font,
      fontSize: 18.0,
      fontWeight: FontWeight.w600,
      color: color ?? Colors.black87,
      height: 1.3,
    );
  }

  /// 正文样式
  static TextStyle bodyStyle({
    FontFamily? fontFamily,
    Color? color,
  }) {
    final font = fontFamily ?? FontManager.getRecommendedFont(FontUsageScenario.body);
    return FontManager.createTextStyle(
      fontFamily: font,
      fontSize: 16.0,
      fontWeight: FontWeight.w400,
      color: color ?? Colors.black87,
      height: 1.6,
    );
  }

  /// 引用样式
  static TextStyle quoteStyle({
    FontFamily? fontFamily,
    Color? color,
  }) {
    final font = fontFamily ?? FontManager.getRecommendedFont(FontUsageScenario.quote);
    return FontManager.createTextStyle(
      fontFamily: font,
      fontSize: 16.0,
      fontWeight: FontWeight.w400,
      color: color ?? Colors.black54,
      height: 1.8,
    );
  }

  /// 小字样式
  static TextStyle captionStyle({
    FontFamily? fontFamily,
    Color? color,
  }) {
    final font = fontFamily ?? FontManager.getRecommendedFont(FontUsageScenario.body);
    return FontManager.createTextStyle(
      fontFamily: font,
      fontSize: 12.0,
      fontWeight: FontWeight.w400,
      color: color ?? Colors.black54,
      height: 1.4,
    );
  }
}
