import 'text_card_model.dart';

class DocumentModel {
  final String id;
  final String title;
  final String originalText;
  final List<TextCardModel> cards;
  final String globalTemplateId;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? globalStyles;

  const DocumentModel({
    required this.id,
    required this.title,
    required this.originalText,
    required this.cards,
    required this.globalTemplateId,
    required this.createdAt,
    this.updatedAt,
    this.globalStyles,
  });

  DocumentModel copyWith({
    String? id,
    String? title,
    String? originalText,
    List<TextCardModel>? cards,
    String? globalTemplateId,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? globalStyles,
  }) {
    return DocumentModel(
      id: id ?? this.id,
      title: title ?? this.title,
      originalText: originalText ?? this.originalText,
      cards: cards ?? this.cards,
      globalTemplateId: globalTemplateId ?? this.globalTemplateId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      globalStyles: globalStyles ?? this.globalStyles,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'originalText': originalText,
      'cards': cards.map((card) => card.toJson()).toList(),
      'globalTemplateId': globalTemplateId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'globalStyles': globalStyles,
    };
  }

  factory DocumentModel.fromJson(Map<String, dynamic> json) {
    return DocumentModel(
      id: json['id'],
      title: json['title'],
      originalText: json['originalText'],
      cards:
          (json['cards'] as List)
              .map((card) => TextCardModel.fromJson(card))
              .toList(),
      globalTemplateId: json['globalTemplateId'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      globalStyles: json['globalStyles'],
    );
  }
}

class ExportConfig {
  final ExportSize size;
  final ExportPlatform platform;
  final bool includeWatermark;
  final String? customWatermark;
  final double quality;
  final ExportFormat format;
  final bool includeTitle;
  final bool includeTimestamp;

  const ExportConfig({
    required this.size,
    required this.platform,
    this.includeWatermark = false,
    this.customWatermark,
    this.quality = 1.0,
    this.format = ExportFormat.png,
    this.includeTitle = true,
    this.includeTimestamp = false,
  });

  ExportConfig copyWith({
    ExportSize? size,
    ExportPlatform? platform,
    bool? includeWatermark,
    String? customWatermark,
    double? quality,
    ExportFormat? format,
    bool? includeTitle,
    bool? includeTimestamp,
  }) {
    return ExportConfig(
      size: size ?? this.size,
      platform: platform ?? this.platform,
      includeWatermark: includeWatermark ?? this.includeWatermark,
      customWatermark: customWatermark ?? this.customWatermark,
      quality: quality ?? this.quality,
      format: format ?? this.format,
      includeTitle: includeTitle ?? this.includeTitle,
      includeTimestamp: includeTimestamp ?? this.includeTimestamp,
    );
  }
}

enum ExportSize {
  square1080(1080, 1080, '正方形 (1080x1080)'),
  story1080(1080, 1920, '竖屏故事 (1080x1920)'),
  post1080(1080, 1350, '社交媒体 (1080x1350)'),
  a4Portrait(2480, 3508, 'A4 竖版 (2480x3508)'),
  a4Landscape(3508, 2480, 'A4 横版 (3508x2480)'),
  custom(0, 0, '自定义尺寸');

  const ExportSize(this.width, this.height, this.description);

  final int width;
  final int height;
  final String description;
}

enum ExportPlatform {
  instagram('Instagram'),
  socialCircle('社交朋友圈'),
  socialPlatform('社交平台'),
  douyin('抖音'),
  weibo('微博'),
  general('通用');

  const ExportPlatform(this.description);

  final String description;
}

enum ExportFormat {
  png('PNG'),
  jpg('JPG'),
  pdf('PDF');

  const ExportFormat(this.description);

  final String description;
}
