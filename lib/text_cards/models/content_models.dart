// 内容数据模型 - 支持文本范围选择和样式定制
import 'package:flutter/material.dart';

/// 内容类型枚举
enum ContentType {
  text, // 普通文本
  heading, // 标题
  bulletList, // 无序列表
  numberedList, // 有序列表
  checkbox, // 复选框列表
  table, // 表格
  quote, // 引用
  code, // 代码块
  divider, // 分割线
}

/// 文本样式配置
@immutable
class TextStyleConfig {
  final double fontSize;
  final FontWeight fontWeight;
  final Color color;
  final String? fontFamily;
  final double? letterSpacing;
  final double? lineHeight;
  final TextAlign textAlign;
  final bool italic;
  final bool underline;
  final bool strikethrough;
  final Color? backgroundColor;

  const TextStyleConfig({
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.color = Colors.black,
    this.fontFamily,
    this.letterSpacing,
    this.lineHeight,
    this.textAlign = TextAlign.left,
    this.italic = false,
    this.underline = false,
    this.strikethrough = false,
    this.backgroundColor,
  });

  TextStyleConfig copyWith({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    String? fontFamily,
    double? letterSpacing,
    double? lineHeight,
    TextAlign? textAlign,
    bool? italic,
    bool? underline,
    bool? strikethrough,
    Color? backgroundColor,
  }) {
    return TextStyleConfig(
      fontSize: fontSize ?? this.fontSize,
      fontWeight: fontWeight ?? this.fontWeight,
      color: color ?? this.color,
      fontFamily: fontFamily ?? this.fontFamily,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      lineHeight: lineHeight ?? this.lineHeight,
      textAlign: textAlign ?? this.textAlign,
      italic: italic ?? this.italic,
      underline: underline ?? this.underline,
      strikethrough: strikethrough ?? this.strikethrough,
      backgroundColor: backgroundColor ?? this.backgroundColor,
    );
  }

  TextStyle toTextStyle() {
    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      fontFamily: fontFamily,
      letterSpacing: letterSpacing,
      height: lineHeight,
      fontStyle: italic ? FontStyle.italic : FontStyle.normal,
      decoration: _getTextDecoration(),
      backgroundColor: backgroundColor,
    );
  }

  TextDecoration _getTextDecoration() {
    List<TextDecoration> decorations = [];
    if (underline) decorations.add(TextDecoration.underline);
    if (strikethrough) decorations.add(TextDecoration.lineThrough);

    if (decorations.isEmpty) return TextDecoration.none;
    if (decorations.length == 1) return decorations.first;
    return TextDecoration.combine(decorations);
  }

  Map<String, dynamic> toJson() {
    return {
      'fontSize': fontSize,
      'fontWeight': fontWeight.index,
      'color': color.toARGB32(),
      'fontFamily': fontFamily,
      'letterSpacing': letterSpacing,
      'lineHeight': lineHeight,
      'textAlign': textAlign.index,
      'italic': italic,
      'underline': underline,
      'strikethrough': strikethrough,
      'backgroundColor': backgroundColor?.toARGB32(),
    };
  }

  factory TextStyleConfig.fromJson(Map<String, dynamic> json) {
    return TextStyleConfig(
      fontSize: json['fontSize']?.toDouble() ?? 16.0,
      fontWeight: FontWeight.values[json['fontWeight'] ?? 3],
      color: Color(json['color'] ?? Colors.black.toARGB32()),
      fontFamily: json['fontFamily'],
      letterSpacing: json['letterSpacing']?.toDouble(),
      lineHeight: json['lineHeight']?.toDouble(),
      textAlign: TextAlign.values[json['textAlign'] ?? 0],
      italic: json['italic'] ?? false,
      underline: json['underline'] ?? false,
      strikethrough: json['strikethrough'] ?? false,
      backgroundColor:
          json['backgroundColor'] != null
              ? Color(json['backgroundColor'])
              : null,
    );
  }
}

/// 文本样式范围 - 定义文本的某个范围应用特定样式
@immutable
class TextStyleRange {
  final int start; // 开始位置
  final int end; // 结束位置
  final TextStyleConfig style; // 应用的样式

  const TextStyleRange({
    required this.start,
    required this.end,
    required this.style,
  });

  TextStyleRange copyWith({int? start, int? end, TextStyleConfig? style}) {
    return TextStyleRange(
      start: start ?? this.start,
      end: end ?? this.end,
      style: style ?? this.style,
    );
  }

  /// 检查是否包含指定位置
  bool contains(int position) {
    return position >= start && position < end;
  }

  /// 检查是否与另一个范围重叠
  bool overlapsWith(TextStyleRange other) {
    return !(end <= other.start || start >= other.end);
  }

  Map<String, dynamic> toJson() {
    return {'start': start, 'end': end, 'style': style.toJson()};
  }

  factory TextStyleRange.fromJson(Map<String, dynamic> json) {
    return TextStyleRange(
      start: json['start'],
      end: json['end'],
      style: TextStyleConfig.fromJson(json['style']),
    );
  }
}

/// 富文本内容块 - 支持文本范围样式定制
@immutable
class RichTextBlock {
  final String id;
  final String content; // 原始文本内容
  final List<TextStyleRange> styleRanges; // 样式范围列表
  final TextStyleConfig defaultStyle; // 默认样式

  const RichTextBlock({
    required this.id,
    required this.content,
    this.styleRanges = const [],
    this.defaultStyle = const TextStyleConfig(),
  });

  RichTextBlock copyWith({
    String? id,
    String? content,
    List<TextStyleRange>? styleRanges,
    TextStyleConfig? defaultStyle,
  }) {
    return RichTextBlock(
      id: id ?? this.id,
      content: content ?? this.content,
      styleRanges: styleRanges ?? this.styleRanges,
      defaultStyle: defaultStyle ?? this.defaultStyle,
    );
  }

  /// 添加样式范围
  RichTextBlock addStyleRange(TextStyleRange range) {
    final newRanges = List<TextStyleRange>.from(styleRanges);

    // 移除重叠的范围
    newRanges.removeWhere((existing) => existing.overlapsWith(range));

    // 添加新范围
    newRanges.add(range);

    // 按开始位置排序
    newRanges.sort((a, b) => a.start.compareTo(b.start));

    return copyWith(styleRanges: newRanges);
  }

  /// 移除指定范围的样式
  RichTextBlock removeStyleRange(int start, int end) {
    final newRanges =
        styleRanges
            .where((range) => !(range.start >= start && range.end <= end))
            .toList();

    return copyWith(styleRanges: newRanges);
  }

  /// 获取指定位置的样式
  TextStyleConfig getStyleAt(int position) {
    for (final range in styleRanges) {
      if (range.contains(position)) {
        return range.style;
      }
    }
    return defaultStyle;
  }

  /// 转换为RichText所需的TextSpan
  TextSpan toTextSpan() {
    if (styleRanges.isEmpty) {
      return TextSpan(text: content, style: defaultStyle.toTextStyle());
    }

    final List<TextSpan> spans = [];
    int currentIndex = 0;

    for (final range in styleRanges) {
      // 添加范围前的默认样式文本
      if (currentIndex < range.start) {
        spans.add(
          TextSpan(
            text: content.substring(currentIndex, range.start),
            style: defaultStyle.toTextStyle(),
          ),
        );
      }

      // 添加样式范围文本
      spans.add(
        TextSpan(
          text: content.substring(range.start, range.end),
          style: range.style.toTextStyle(),
        ),
      );

      currentIndex = range.end;
    }

    // 添加剩余的默认样式文本
    if (currentIndex < content.length) {
      spans.add(
        TextSpan(
          text: content.substring(currentIndex),
          style: defaultStyle.toTextStyle(),
        ),
      );
    }

    return TextSpan(children: spans);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'styleRanges': styleRanges.map((range) => range.toJson()).toList(),
      'defaultStyle': defaultStyle.toJson(),
    };
  }

  factory RichTextBlock.fromJson(Map<String, dynamic> json) {
    return RichTextBlock(
      id: json['id'],
      content: json['content'],
      styleRanges:
          (json['styleRanges'] as List?)
              ?.map((rangeJson) => TextStyleRange.fromJson(rangeJson))
              .toList() ??
          [],
      defaultStyle: TextStyleConfig.fromJson(json['defaultStyle']),
    );
  }
}

/// 卡片配置 - 控制整体卡片样式
@immutable
class CardConfig {
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double? width;
  final double? height;
  final BoxShadow? shadow;

  const CardConfig({
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 12.0,
    this.padding = const EdgeInsets.all(20),
    this.margin = const EdgeInsets.all(8),
    this.width,
    this.height,
    this.shadow,
  });

  CardConfig copyWith({
    Color? backgroundColor,
    Color? borderColor,
    double? borderWidth,
    double? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    BoxShadow? shadow,
  }) {
    return CardConfig(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      borderRadius: borderRadius ?? this.borderRadius,
      padding: padding ?? this.padding,
      margin: margin ?? this.margin,
      width: width ?? this.width,
      height: height ?? this.height,
      shadow: shadow ?? this.shadow,
    );
  }
}

/// 渲染卡片 - 包含富文本内容和配置
@immutable
class RenderCard {
  final String id;
  final String title;
  final RichTextBlock content; // 改为单个富文本块
  final CardConfig config;
  final DateTime createdAt;
  final DateTime updatedAt;

  const RenderCard({
    required this.id,
    required this.title,
    required this.content,
    required this.config,
    required this.createdAt,
    required this.updatedAt,
  });

  RenderCard copyWith({
    String? id,
    String? title,
    RichTextBlock? content,
    CardConfig? config,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RenderCard(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      config: config ?? this.config,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content.toJson(),
      'config': {
        'backgroundColor': config.backgroundColor.toARGB32(),
        'borderColor': config.borderColor.toARGB32(),
        'borderWidth': config.borderWidth,
        'borderRadius': config.borderRadius,
        'width': config.width,
        'height': config.height,
      },
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory RenderCard.fromJson(Map<String, dynamic> json) {
    final configJson = json['config'];
    return RenderCard(
      id: json['id'],
      title: json['title'],
      content: RichTextBlock.fromJson(json['content']),
      config: CardConfig(
        backgroundColor: Color(configJson['backgroundColor']),
        borderColor: Color(configJson['borderColor']),
        borderWidth: configJson['borderWidth']?.toDouble() ?? 1.0,
        borderRadius: configJson['borderRadius']?.toDouble() ?? 12.0,
        width: configJson['width']?.toDouble(),
        height: configJson['height']?.toDouble(),
      ),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

/// 内容解析器 - 将纯文本转换为富文本块
class ContentParser {
  /// 将文本解析为富文本块（不再按行分割）
  static RichTextBlock parseTextToRichBlock(String text, {String? id}) {
    return RichTextBlock(
      id: id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      content: text.trim(),
      styleRanges: const [],
      defaultStyle: const TextStyleConfig(),
    );
  }

  /// 将多段文本解析为多个富文本块
  static List<RichTextBlock> parseTextSections(List<String> sections) {
    return sections.asMap().entries.map((entry) {
      final index = entry.key;
      final text = entry.value;
      return parseTextToRichBlock(text, id: 'section_$index');
    }).toList();
  }
}
