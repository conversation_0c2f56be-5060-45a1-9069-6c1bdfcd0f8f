import 'package:flutter/material.dart';

/// 增强的卡片模板
/// 提供更丰富的样式选项和现代化设计
class EnhancedCardTemplate {
  final String id;
  final String name;
  final String category;
  final String description;
  final LinearGradient backgroundGradient;
  final Color textColor;
  final Color titleColor;
  final Color accentColor;
  final double borderRadius;
  final double padding;
  final bool hasShadow;
  final Map<String, dynamic> styles;

  // 新增字体和布局属性
  final String fontFamily;
  final double titleFontSize;
  final double contentFontSize;
  final FontWeight titleFontWeight;
  final FontWeight contentFontWeight;
  final double lineHeight;
  final TextAlign textAlign;
  final EdgeInsets contentPadding;
  final bool hasDecorationElements;
  final String layoutStyle; // 'standard', 'minimal', 'decorative', 'modern'

  const EnhancedCardTemplate({
    required this.id,
    required this.name,
    required this.category,
    required this.description,
    required this.backgroundGradient,
    required this.textColor,
    required this.titleColor,
    required this.accentColor,
    this.borderRadius = 16.0,
    this.padding = 20.0,
    this.hasShadow = true,
    this.styles = const {},
    this.fontFamily = 'System',
    this.titleFontSize = 18.0,
    this.contentFontSize = 14.0,
    this.titleFontWeight = FontWeight.w600,
    this.contentFontWeight = FontWeight.w400,
    this.lineHeight = 1.4,
    this.textAlign = TextAlign.left,
    this.contentPadding = const EdgeInsets.all(20),
    this.hasDecorationElements = false,
    this.layoutStyle = 'standard',
  });

  /// 获取现代化模板集合（社交风格）
  static List<EnhancedCardTemplate> getModernTemplates() {
    return [
      // 经典白底模板
      EnhancedCardTemplate(
        id: 'classic_style',
        name: '经典风格',
        category: 'classic',
        description: '经典白底风格，简约清新',
        backgroundGradient: const LinearGradient(
          colors: [Color(0xFFFFFFFF), Color(0xFFFFFBF8)],
        ),
        textColor: const Color(0xFF333333),
        titleColor: const Color(0xFF1A1A1A),
        accentColor: const Color(0xFFFF2442),
        borderRadius: 16.0,
        titleFontSize: 22.0,
        contentFontSize: 16.0,
        titleFontWeight: FontWeight.w700,
        contentFontWeight: FontWeight.w400,
        lineHeight: 1.6,
        layoutStyle: 'classic_style',
        fontFamily: 'SourceHanSansCN',
        hasDecorationElements: true,
      ),

      // 渐变风格模板
      EnhancedCardTemplate(
        id: 'gradient_style',
        name: '渐变风格',
        category: 'gradient',
        description: '时尚渐变背景，活力十足',
        backgroundGradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFF6B9D), Color(0xFFFF8E8E), Color(0xFFFFB4B4)],
        ),
        textColor: Colors.white,
        titleColor: Colors.white,
        accentColor: const Color(0xFFFFE5E5),
        borderRadius: 20.0,
        titleFontSize: 24.0,
        contentFontSize: 17.0,
        titleFontWeight: FontWeight.w800,
        contentFontWeight: FontWeight.w500,
        lineHeight: 1.5,
        layoutStyle: 'gradient_style',
        fontFamily: 'AlibabaPuHuiTi',
        hasDecorationElements: true,
      ),

      // 阅读风格
      EnhancedCardTemplate(
        id: 'reading_style',
        name: '阅读风格',
        category: 'reading',
        description: '护眼阅读风格，舒适温和',
        backgroundGradient: const LinearGradient(
          colors: [Color(0xFFF7F3E9), Color(0xFFF5F1E8)],
        ),
        textColor: const Color(0xFF3C3C3C),
        titleColor: const Color(0xFF2C2C2C),
        accentColor: const Color(0xFF07C160),
        borderRadius: 12.0,
        titleFontSize: 20.0,
        contentFontSize: 16.0,
        titleFontWeight: FontWeight.w600,
        contentFontWeight: FontWeight.w400,
        lineHeight: 1.8,
        layoutStyle: 'reading',
        fontFamily: 'HarmonyOS_Sans_SC',
        hasShadow: false,
      ),

      // 知识卡片风格
      EnhancedCardTemplate(
        id: 'knowledge_card',
        name: '知识卡片',
        category: 'knowledge',
        description: '知识分享专用，学术风格',
        backgroundGradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
        ),
        textColor: Colors.white,
        titleColor: Colors.white,
        accentColor: const Color(0xFFE2E8F0),
        borderRadius: 16.0,
        titleFontSize: 22.0,
        contentFontSize: 16.0,
        titleFontWeight: FontWeight.w700,
        contentFontWeight: FontWeight.w400,
        lineHeight: 1.7,
        layoutStyle: 'knowledge',
        fontFamily: 'SourceHanSansCN',
        hasDecorationElements: true,
      ),

      // 语录卡片
      EnhancedCardTemplate(
        id: 'quote_card',
        name: '语录卡片',
        category: 'quote',
        description: '经典语录风格，优雅大气',
        backgroundGradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF2C3E50), Color(0xFF34495E)],
        ),
        textColor: const Color(0xFFECF0F1),
        titleColor: const Color(0xFFE74C3C),
        accentColor: const Color(0xFFE67E22),
        borderRadius: 18.0,
        titleFontSize: 20.0,
        contentFontSize: 18.0,
        titleFontWeight: FontWeight.w600,
        contentFontWeight: FontWeight.w400,
        lineHeight: 1.8,
        layoutStyle: 'quote',
        fontFamily: 'AlibabaPuHuiTi',
        hasDecorationElements: true,
      ),

      // 清新自然
      EnhancedCardTemplate(
        id: 'fresh_nature',
        name: '清新自然',
        category: 'nature',
        description: '清新自然风格，舒缓护眼',
        backgroundGradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF56CCF2), Color(0xFF2F80ED)],
        ),
        textColor: Colors.white,
        titleColor: Colors.white,
        accentColor: const Color(0xFFBBDEFB),
        borderRadius: 20.0,
        titleFontSize: 21.0,
        contentFontSize: 16.0,
        titleFontWeight: FontWeight.w700,
        contentFontWeight: FontWeight.w400,
        lineHeight: 1.6,
        layoutStyle: 'nature',
        fontFamily: 'MiSans',
        hasDecorationElements: true,
      ),

      // 深色主题
      EnhancedCardTemplate(
        id: 'dark_elegant',
        name: '深色雅致',
        category: 'dark',
        description: '优雅深色主题，护眼舒适',
        backgroundGradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF2D3748), Color(0xFF1A202C)],
        ),
        textColor: const Color(0xFFE2E8F0),
        titleColor: const Color(0xFFEDF2F7),
        accentColor: const Color(0xFF63B3ED),
        borderRadius: 16.0,
        titleFontSize: 19.0,
        contentFontSize: 15.0,
        titleFontWeight: FontWeight.w600,
        contentFontWeight: FontWeight.w400,
        lineHeight: 1.5,
        layoutStyle: 'standard',
      ),

      // 学术风格
      EnhancedCardTemplate(
        id: 'academic_serif',
        name: '学术衬线',
        category: 'academic',
        description: '经典学术风格，适合知识分享',
        backgroundGradient: const LinearGradient(
          colors: [Color(0xFFF7FAFC), Color(0xFFEDF2F7)],
        ),
        textColor: const Color(0xFF2D3748),
        titleColor: const Color(0xFF1A202C),
        accentColor: const Color(0xFF3182CE),
        borderRadius: 8.0,
        titleFontSize: 20.0,
        contentFontSize: 16.0,
        titleFontWeight: FontWeight.w700,
        contentFontWeight: FontWeight.w400,
        lineHeight: 1.7,
        textAlign: TextAlign.justify,
        layoutStyle: 'academic',
        hasShadow: false,
      ),

      // 创意彩虹
      EnhancedCardTemplate(
        id: 'creative_rainbow',
        name: '创意彩虹',
        category: 'creative',
        description: '多彩创意风格，激发想象力',
        backgroundGradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFF6B6B),
            Color(0xFF4ECDC4),
            Color(0xFF45B7D1),
            Color(0xFF96CEB4),
          ],
        ),
        textColor: Colors.white,
        titleColor: Colors.white,
        accentColor: const Color(0xFFFECE2F),
        borderRadius: 24.0,
        titleFontSize: 21.0,
        contentFontSize: 15.0,
        titleFontWeight: FontWeight.w800,
        contentFontWeight: FontWeight.w500,
        lineHeight: 1.4,
        layoutStyle: 'creative',
        hasDecorationElements: true,
      ),

      // 自然绿色
      EnhancedCardTemplate(
        id: 'nature_green',
        name: '自然绿',
        category: 'nature',
        description: '清新自然风格，舒缓护眼',
        backgroundGradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
        ),
        textColor: Colors.white,
        titleColor: Colors.white,
        accentColor: const Color(0xFF81C784),
        borderRadius: 18.0,
        titleFontSize: 19.0,
        contentFontSize: 15.0,
        titleFontWeight: FontWeight.w600,
        contentFontWeight: FontWeight.w400,
        lineHeight: 1.5,
        layoutStyle: 'nature',
      ),

      // 温暖橙色
      EnhancedCardTemplate(
        id: 'warm_orange',
        name: '温暖橙',
        category: 'warm',
        description: '温暖活力风格，积极向上',
        backgroundGradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFF9800), Color(0xFFE65100)],
        ),
        textColor: Colors.white,
        titleColor: Colors.white,
        accentColor: const Color(0xFFFFCC02),
        borderRadius: 16.0,
        titleFontSize: 20.0,
        contentFontSize: 15.0,
        titleFontWeight: FontWeight.w700,
        contentFontWeight: FontWeight.w400,
        lineHeight: 1.4,
        layoutStyle: 'warm',
      ),

      // 科技蓝
      EnhancedCardTemplate(
        id: 'tech_blue',
        name: '科技蓝',
        category: 'tech',
        description: '科技感十足，未来风格',
        backgroundGradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF0D47A1), Color(0xFF1565C0)],
        ),
        textColor: const Color(0xFFE3F2FD),
        titleColor: Colors.white,
        accentColor: const Color(0xFF42A5F5),
        borderRadius: 12.0,
        titleFontSize: 18.0,
        contentFontSize: 14.0,
        titleFontWeight: FontWeight.w600,
        contentFontWeight: FontWeight.w300,
        lineHeight: 1.6,
        layoutStyle: 'tech',
        hasDecorationElements: true,
      ),

      // 优雅紫色
      EnhancedCardTemplate(
        id: 'elegant_purple',
        name: '优雅紫',
        category: 'elegant',
        description: '高贵优雅风格，彰显品味',
        backgroundGradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF9C27B0), Color(0xFF673AB7)],
        ),
        textColor: Colors.white,
        titleColor: Colors.white,
        accentColor: const Color(0xFFBA68C8),
        borderRadius: 20.0,
        titleFontSize: 19.0,
        contentFontSize: 15.0,
        titleFontWeight: FontWeight.w600,
        contentFontWeight: FontWeight.w400,
        lineHeight: 1.5,
        layoutStyle: 'elegant',
      ),

      // 复古风格
      EnhancedCardTemplate(
        id: 'vintage_sepia',
        name: '复古棕',
        category: 'vintage',
        description: '怀旧复古风格，经典永恒',
        backgroundGradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF8D6E63), Color(0xFF5D4037)],
        ),
        textColor: const Color(0xFFFFF8E1),
        titleColor: const Color(0xFFFFE0B2),
        accentColor: const Color(0xFFFFAB40),
        borderRadius: 8.0,
        titleFontSize: 18.0,
        contentFontSize: 14.0,
        titleFontWeight: FontWeight.w600,
        contentFontWeight: FontWeight.w400,
        lineHeight: 1.6,
        layoutStyle: 'vintage',
      ),
    ];
  }

  /// 根据分类获取模板
  static List<EnhancedCardTemplate> getTemplatesByCategory(String category) {
    return getModernTemplates()
        .where((template) => template.category == category)
        .toList();
  }

  /// 获取所有分类
  static List<String> getAllCategories() {
    return getModernTemplates()
        .map((template) => template.category)
        .toSet()
        .toList();
  }

  /// 根据ID获取模板
  static EnhancedCardTemplate? getTemplateById(String id) {
    try {
      return getModernTemplates().firstWhere((template) => template.id == id);
    } catch (e) {
      return null;
    }
  }
}
