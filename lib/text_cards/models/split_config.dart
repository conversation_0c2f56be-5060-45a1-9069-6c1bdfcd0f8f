/// 智能拆分配置模型
class SplitConfig {
  final SplitMode mode;
  final String customSeparator;
  final int maxLength;
  final bool autoDetectTitles;
  final bool preserveFormatting;
  final bool smartMerge;
  final double titleThreshold;

  const SplitConfig({
    this.mode = SplitMode.paragraph,
    this.customSeparator = '',
    this.maxLength = 500,
    this.autoDetectTitles = true,
    this.preserveFormatting = true,
    this.smartMerge = false,
    this.titleThreshold = 0.7,
  });

  SplitConfig copyWith({
    SplitMode? mode,
    String? customSeparator,
    int? maxLength,
    bool? autoDetectTitles,
    bool? preserveFormatting,
    bool? smartMerge,
    double? titleThreshold,
  }) {
    return SplitConfig(
      mode: mode ?? this.mode,
      customSeparator: customSeparator ?? this.customSeparator,
      maxLength: maxLength ?? this.maxLength,
      autoDetectTitles: autoDetectTitles ?? this.autoDetectTitles,
      preserveFormatting: preserveFormatting ?? this.preserveFormatting,
      smartMerge: smartMerge ?? this.smartMerge,
      titleThreshold: titleThreshold ?? this.titleThreshold,
    );
  }
}

/// 拆分模式枚举
enum SplitMode {
  paragraph('段落拆分', '按段落自动分割文本'),
  sentence('句子拆分', '按句子分割文本'),
  length('长度拆分', '按指定长度分割文本'),
  custom('自定义分隔符', '使用自定义分隔符分割'),
  smart('智能拆分', '基于语义和结构智能分割');

  const SplitMode(this.displayName, this.description);

  final String displayName;
  final String description;
}

/// 拆分结果项
class SplitResultItem {
  final String id;
  final String content;
  final String? title;
  final int originalIndex;
  final bool isTitle;
  final SplitItemType type;

  const SplitResultItem({
    required this.id,
    required this.content,
    this.title,
    required this.originalIndex,
    this.isTitle = false,
    this.type = SplitItemType.content,
  });

  SplitResultItem copyWith({
    String? id,
    String? content,
    String? title,
    int? originalIndex,
    bool? isTitle,
    SplitItemType? type,
  }) {
    return SplitResultItem(
      id: id ?? this.id,
      content: content ?? this.content,
      title: title ?? this.title,
      originalIndex: originalIndex ?? this.originalIndex,
      isTitle: isTitle ?? this.isTitle,
      type: type ?? this.type,
    );
  }
}

/// 拆分项类型
enum SplitItemType {
  content('内容'),
  title('标题'),
  list('列表'),
  quote('引用'),
  code('代码');

  const SplitItemType(this.displayName);

  final String displayName;
}
