// 内容渲染模块路由配置
import 'package:flutter/material.dart';
import 'content_rendering_home.dart';
import 'content_editor_page.dart';
import 'visual_renderer_page.dart';
import 'export_system_page.dart';
import 'models/content_models.dart';

class ContentRenderingRoutes {
  static const String home = '/content-rendering';
  static const String editor = '/content-editor';
  static const String visualRenderer = '/visual-renderer';
  static const String exportSystem = '/export';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case home:
        return MaterialPageRoute(builder: (_) => const ContentRenderingHome());

      case editor:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder:
              (_) => ContentEditorPage(
                existingContent: args?['content'],
                title: args?['title'],
              ),
        );

      case visualRenderer:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder:
              (_) => VisualRendererPage(
                content: args['content'],
                sections: args['sections'],
                title: args['title'],
                mode: args['mode'],
              ),
        );

      case exportSystem:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder:
              (_) => ExportSystemPage(
                cards: args['cards'] as List<RenderCard>,
                title: args['title'],
              ),
        );

      default:
        return MaterialPageRoute(
          builder: (_) => const Scaffold(body: Center(child: Text('页面未找到'))),
        );
    }
  }
}

// 导航辅助类
class ContentRenderingNavigator {
  static void toHome(BuildContext context) {
    Navigator.of(context).pushNamed(ContentRenderingRoutes.home);
  }

  static void toEditor(BuildContext context, {String? content, String? title}) {
    Navigator.of(context).pushNamed(
      ContentRenderingRoutes.editor,
      arguments: {'content': content, 'title': title},
    );
  }

  static void toVisualRenderer(
    BuildContext context, {
    String? content,
    List<String>? sections,
    required String title,
    required String mode,
  }) {
    Navigator.of(context).pushNamed(
      ContentRenderingRoutes.visualRenderer,
      arguments: {
        'content': content,
        'sections': sections,
        'title': title,
        'mode': mode,
      },
    );
  }

  static void toExportSystem(
    BuildContext context, {
    required List<RenderCard> cards,
    required String title,
  }) {
    Navigator.of(context).pushNamed(
      ContentRenderingRoutes.exportSystem,
      arguments: {'cards': cards, 'title': title},
    );
  }
}
