// 导出系统 - 支持单个卡片和批量导出
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';
import 'package:saver_gallery/saver_gallery.dart';
import 'package:share_plus/share_plus.dart';

import 'models/content_models.dart';

class ExportSystemPage extends StatefulWidget {
  final List<RenderCard> cards;
  final String title;

  const ExportSystemPage({super.key, required this.cards, required this.title});

  @override
  State<ExportSystemPage> createState() => _ExportSystemPageState();
}

class _ExportSystemPageState extends State<ExportSystemPage> {
  final List<GlobalKey> _cardKeys = [];
  bool _isExporting = false;
  int _currentExportIndex = 0;

  @override
  void initState() {
    super.initState();
    _cardKeys.addAll(
      List.generate(widget.cards.length, (index) => GlobalKey()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('导出 - ${widget.title}'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _isExporting ? null : _exportAndShare,
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isExporting ? null : _saveToGallery,
          ),
        ],
      ),
      body: Column(
        children: [
          // 导出状态
          if (_isExporting) _buildExportProgress(),

          // 导出选项
          _buildExportOptions(),

          // 卡片预览列表
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: widget.cards.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 24),
                  child: _buildExportableCard(widget.cards[index], index),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExportProgress() {
    final progress = _currentExportIndex / widget.cards.length;
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.blue[50],
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.file_download, color: Colors.blue[700]),
              const SizedBox(width: 8),
              Text(
                '正在导出... $_currentExportIndex/${widget.cards.length}',
                style: TextStyle(
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.blue[100],
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[700]!),
          ),
        ],
      ),
    );
  }

  Widget _buildExportOptions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '导出选项',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isExporting ? null : _exportAndShare,
                  icon: const Icon(Icons.share),
                  label: const Text('分享'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isExporting ? null : _saveToGallery,
                  icon: const Icon(Icons.save),
                  label: const Text('保存到相册'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.cards.length == 1
                ? '将导出 1 张卡片图片'
                : '将导出 ${widget.cards.length} 张卡片图片',
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildExportableCard(RenderCard card, int index) {
    return RepaintBoundary(
      key: _cardKeys[index],
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.grey[200]!),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // 卡片标题栏
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    '第 ${index + 1} 张',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  const Spacer(),
                  Text(
                    card.title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            // 实际的卡片内容
            _buildCardContent(card),
          ],
        ),
      ),
    );
  }

  Widget _buildCardContent(RenderCard card) {
    return Container(
      width: double.infinity,
      margin: card.config.margin,
      padding: card.config.padding,
      decoration: BoxDecoration(
        color: card.config.backgroundColor,
        border: Border.all(
          color: card.config.borderColor,
          width: card.config.borderWidth,
        ),
        borderRadius: BorderRadius.circular(card.config.borderRadius),
        boxShadow: card.config.shadow != null ? [card.config.shadow!] : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 卡片标题
          if (card.title.isNotEmpty) ...[
            Text(
              card.title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 富文本内容
          RichText(text: card.content.toTextSpan(), textAlign: TextAlign.left),
        ],
      ),
    );
  }

  Future<void> _exportAndShare() async {
    setState(() {
      _isExporting = true;
      _currentExportIndex = 0;
    });

    try {
      final imageFiles = await _captureCards();

      if (imageFiles.isNotEmpty) {
        await SharePlus.instance.share(
          ShareParams(
            files: imageFiles.map((file) => XFile(file.path)).toList(),
            text: '来自 ContentPal 的 ${widget.title}',
          )
        );
      }
    } catch (e) {
      _showError('分享失败: $e');
    } finally {
      setState(() {
        _isExporting = false;
        _currentExportIndex = 0;
      });
    }
  }

  Future<void> _saveToGallery() async {
    setState(() {
      _isExporting = true;
      _currentExportIndex = 0;
    });

    try {
      final imageFiles = await _captureCards();

      int savedCount = 0;
      for (final file in imageFiles) {
        final result = await SaverGallery.saveFile(
          filePath: file.path,
          fileName:
              'contentpal_${DateTime.now().millisecondsSinceEpoch}_$savedCount.png',
          skipIfExists: false,
        );

        if (result.isSuccess) {
          savedCount++;
        }
      }

      if (savedCount > 0) {
        _showSuccess('已保存 $savedCount 张卡片到相册');
      } else {
        _showError('保存失败');
      }
    } catch (e) {
      _showError('保存失败: $e');
    } finally {
      setState(() {
        _isExporting = false;
        _currentExportIndex = 0;
      });
    }
  }

  Future<List<File>> _captureCards() async {
    final tempDir = await getTemporaryDirectory();
    final files = <File>[];

    for (int i = 0; i < _cardKeys.length; i++) {
      setState(() {
        _currentExportIndex = i + 1;
      });

      try {
        final boundary =
            _cardKeys[i].currentContext?.findRenderObject()
                as RenderRepaintBoundary?;

        if (boundary != null) {
          final image = await boundary.toImage(pixelRatio: 3.0);
          final byteData = await image.toByteData(
            format: ui.ImageByteFormat.png,
          );
          final bytes = byteData!.buffer.asUint8List();

          final file = File(
            '${tempDir.path}/card_${i}_${DateTime.now().millisecondsSinceEpoch}.png',
          );
          await file.writeAsBytes(bytes);
          files.add(file);
        }
      } catch (e) {
        debugPrint('捕获卡片 $i 失败: $e');
      }
    }

    return files;
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
