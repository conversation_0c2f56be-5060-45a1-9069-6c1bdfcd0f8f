import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../models/document_model.dart';
import '../widgets/text_card_widget.dart';
import 'card_renderer.dart';

class CardExportPage extends StatefulWidget {
  final DocumentModel document;
  final bool isSingleCard;

  const CardExportPage({
    super.key,
    required this.document,
    this.isSingleCard = false,
  });

  @override
  State<CardExportPage> createState() => _CardExportPageState();
}

class _CardExportPageState extends State<CardExportPage> {
  ExportConfig _config = const ExportConfig(
    size: ExportSize.square1080,
    platform: ExportPlatform.general,
  );

  bool _isExporting = false;
  bool _showPreview = true;

  void _updateConfig(ExportConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
  }

  void _exportCards() async {
    setState(() => _isExporting = true);

    try {
      final renderer = CardRenderer();

      if (widget.isSingleCard) {
        // 导出单个卡片
        final card = widget.document.cards.first;
        await renderer.exportSingleCard(card, _config);
      } else {
        // 导出整个文档
        await renderer.exportDocument(widget.document, _config);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ 导出成功！已保存到相册'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('❌ 导出失败: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isExporting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: Text(
          widget.isSingleCard ? '导出卡片' : '导出文档',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textDarkColor,
          ),
        ),
        backgroundColor: AppTheme.bgWhiteColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _showPreview ? Icons.visibility_off : Icons.visibility,
              color: AppTheme.textDarkColor,
            ),
            onPressed: () {
              setState(() {
                _showPreview = !_showPreview;
              });
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          if (_showPreview) ...[
            // 预览区域
            Container(
              color: AppTheme.bgWhiteColor,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '预览',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textDarkColor,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildPreview(),
                ],
              ),
            ),
            const Divider(height: 1),
          ],

          // 配置区域
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSizeSection(),
                  const SizedBox(height: 24),
                  _buildPlatformSection(),
                  const SizedBox(height: 24),
                  _buildFormatSection(),
                  const SizedBox(height: 24),
                  _buildWatermarkSection(),
                  const SizedBox(height: 24),
                  _buildAdvancedSection(),
                ],
              ),
            ),
          ),

          // 导出按钮
          Container(
            color: AppTheme.bgWhiteColor,
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isExporting ? null : _exportCards,
                icon:
                    _isExporting
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : const Icon(Icons.download),
                label: Text(_isExporting ? '导出中...' : '开始导出'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  textStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreview() {
    final aspectRatio = _config.size.width / _config.size.height;

    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.textLightColor.withValues(alpha: 0.3),
        ),
      ),
      child: Center(
        child: AspectRatio(
          aspectRatio: aspectRatio,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  offset: const Offset(0, 2),
                  blurRadius: 4,
                ),
              ],
            ),
            child:
                widget.isSingleCard
                    ? _buildSingleCardPreview()
                    : _buildDocumentPreview(),
          ),
        ),
      ),
    );
  }

  Widget _buildSingleCardPreview() {
    final card = widget.document.cards.first;
    return Padding(
      padding: const EdgeInsets.all(8),
      child: TextCardWidget(card: card),
    );
  }

  Widget _buildDocumentPreview() {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          // 文档标题
          Text(
            widget.document.title,
            style: const TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          // 卡片预览
          Expanded(
            child: ListView.builder(
              itemCount: widget.document.cards.length.clamp(0, 3),
              itemBuilder: (context, index) {
                final card = widget.document.cards[index];
                return Container(
                  height: 20,
                  margin: const EdgeInsets.only(bottom: 2),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: Row(
                      children: [
                        Text(
                          card.title,
                          style: const TextStyle(fontSize: 6),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          if (widget.document.cards.length > 3)
            Text(
              '+ ${widget.document.cards.length - 3} 更多',
              style: const TextStyle(
                fontSize: 6,
                color: AppTheme.textLightColor,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSizeSection() {
    return _ConfigSection(
      title: '导出尺寸',
      child: Column(
        children:
            ExportSize.values.map((size) {
              return RadioListTile<ExportSize>(
                title: Text(size.description),
                subtitle:
                    size != ExportSize.custom
                        ? Text('${size.width} × ${size.height}')
                        : null,
                value: size,
                groupValue: _config.size,
                onChanged: (value) {
                  if (value != null) {
                    _updateConfig(_config.copyWith(size: value));
                  }
                },
              );
            }).toList(),
      ),
    );
  }

  Widget _buildPlatformSection() {
    return _ConfigSection(
      title: '目标平台',
      child: Column(
        children:
            ExportPlatform.values.map((platform) {
              return RadioListTile<ExportPlatform>(
                title: Text(platform.description),
                value: platform,
                groupValue: _config.platform,
                onChanged: (value) {
                  if (value != null) {
                    _updateConfig(_config.copyWith(platform: value));
                  }
                },
              );
            }).toList(),
      ),
    );
  }

  Widget _buildFormatSection() {
    return _ConfigSection(
      title: '文件格式',
      child: Column(
        children:
            ExportFormat.values.map((format) {
              return RadioListTile<ExportFormat>(
                title: Text(format.description),
                value: format,
                groupValue: _config.format,
                onChanged: (value) {
                  if (value != null) {
                    _updateConfig(_config.copyWith(format: value));
                  }
                },
              );
            }).toList(),
      ),
    );
  }

  Widget _buildWatermarkSection() {
    return _ConfigSection(
      title: '水印设置',
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('添加水印'),
            subtitle: const Text('在图片角落添加应用水印'),
            value: _config.includeWatermark,
            onChanged: (value) {
              _updateConfig(_config.copyWith(includeWatermark: value));
            },
          ),
          if (_config.includeWatermark)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                decoration: const InputDecoration(
                  labelText: '自定义水印文字',
                  hintText: '留空使用默认水印',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  _updateConfig(
                    _config.copyWith(
                      customWatermark: value.isEmpty ? null : value,
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAdvancedSection() {
    return _ConfigSection(
      title: '高级选项',
      child: Column(
        children: [
          SwitchListTile(
            title: const Text('包含标题'),
            subtitle: const Text('在导出的图片中显示标题'),
            value: _config.includeTitle,
            onChanged: (value) {
              _updateConfig(_config.copyWith(includeTitle: value));
            },
          ),
          SwitchListTile(
            title: const Text('包含时间戳'),
            subtitle: const Text('在图片中显示创建时间'),
            value: _config.includeTimestamp,
            onChanged: (value) {
              _updateConfig(_config.copyWith(includeTimestamp: value));
            },
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Expanded(
                  child: Text('图片质量', style: TextStyle(fontSize: 16)),
                ),
                Expanded(
                  flex: 2,
                  child: Slider(
                    value: _config.quality,
                    min: 0.1,
                    max: 1.0,
                    divisions: 9,
                    label: '${(_config.quality * 100).round()}%',
                    onChanged: (value) {
                      _updateConfig(_config.copyWith(quality: value));
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ConfigSection extends StatelessWidget {
  final String title;
  final Widget child;

  const _ConfigSection({required this.title, required this.child});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textDarkColor,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.bgWhiteColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.textLightColor.withValues(alpha: 0.2),
            ),
          ),
          child: child,
        ),
      ],
    );
  }
}
