import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../models/document_model.dart';
import '../models/text_card_model.dart';
import '../widgets/text_card_widget.dart';

class CardRenderer {
  Future<void> exportSingleCard(TextCardModel card, ExportConfig config) async {
    try {
      // 创建渲染上下文
      final widget = _buildCardForExport(card, config);
      final image = await _renderWidget(widget, config);

      // 保存到相册
      await _saveToGallery(
        image,
        '${card.title}_${DateTime.now().millisecondsSinceEpoch}',
        config,
      );
    } catch (e) {
      throw Exception('导出卡片失败: $e');
    }
  }

  Future<void> exportDocument(
    DocumentModel document,
    ExportConfig config,
  ) async {
    try {
      if (document.cards.length == 1) {
        // 单卡片文档
        await exportSingleCard(document.cards.first, config);
      } else {
        // 多卡片文档 - 可以选择导出为单张长图或多张图片
        await _exportMultipleCards(document, config);
      }
    } catch (e) {
      throw Exception('导出文档失败: $e');
    }
  }

  Future<void> _exportMultipleCards(
    DocumentModel document,
    ExportConfig config,
  ) async {
    // 这里实现多种导出策略
    switch (config.format) {
      case ExportFormat.pdf:
        await _exportAsPDF(document, config);
        break;
      default:
        // 导出为单张长图
        await _exportAsLongImage(document, config);
        break;
    }
  }

  Future<void> _exportAsLongImage(
    DocumentModel document,
    ExportConfig config,
  ) async {
    final widget = _buildDocumentForExport(document, config);
    final image = await _renderWidget(widget, config);
    await _saveToGallery(
      image,
      '${document.title}_${DateTime.now().millisecondsSinceEpoch}',
      config,
    );
  }

  Future<void> _exportAsPDF(DocumentModel document, ExportConfig config) async {
    // PDF导出逻辑 - 这里暂时用图片代替
    await _exportAsLongImage(document, config);
  }

  Widget _buildCardForExport(TextCardModel card, ExportConfig config) {
    return Container(
      width: config.size.width.toDouble(),
      height: config.size.height.toDouble(),
      color: Colors.white,
      child: Stack(
        children: [
          // 主要内容
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.all(40),
              child: TextCardWidget(card: card),
            ),
          ),

          // 水印
          if (config.includeWatermark)
            Positioned(
              bottom: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  config.customWatermark ?? 'ContentPal',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.black.withValues(alpha: 0.5),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),

          // 时间戳
          if (config.includeTimestamp)
            Positioned(
              bottom: 20,
              left: 20,
              child: Text(
                _formatDateTime(card.createdAt),
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.black.withValues(alpha: 0.4),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDocumentForExport(DocumentModel document, ExportConfig config) {
    return Container(
      width: config.size.width.toDouble(),
      color: Colors.white,
      child: Column(
        children: [
          // 文档标题
          if (config.includeTitle)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(40),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    document.title,
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1a202c),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '共 ${document.cards.length} 个卡片',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black.withValues(alpha: 0.6),
                    ),
                  ),
                  if (config.includeTimestamp) ...[
                    const SizedBox(height: 4),
                    Text(
                      _formatDateTime(document.createdAt),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.black.withValues(alpha: 0.4),
                      ),
                    ),
                  ],
                ],
              ),
            ),

          // 卡片列表
          ...document.cards.asMap().entries.map((entry) {
            final index = entry.key;
            final card = entry.value;

            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
              child: Column(
                children: [
                  // 卡片序号
                  Row(
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: const BoxDecoration(
                          color: Color(0xFF667eea),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            '${index + 1}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Container(
                          height: 1,
                          color: Colors.black.withValues(alpha: 0.1),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // 卡片内容
                  SizedBox(
                    height: 300, // 固定高度
                    child: TextCardWidget(card: card),
                  ),
                ],
              ),
            );
          }),

          // 底部水印
          if (config.includeWatermark)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(40),
              child: Center(
                child: Text(
                  config.customWatermark ?? 'Created with ContentPal',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.black.withValues(alpha: 0.3),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<Uint8List> _renderWidget(Widget widget, ExportConfig config) async {
    // 由于Flutter渲染的复杂性，这里直接返回占位图片
    // 实际项目中建议使用以下第三方包：
    // 1. screenshot: 用于截屏widget
    // 2. flutter_to_pdf: 用于导出PDF
    // 3. image_gallery_saver: 用于保存到相册
    return _createPlaceholderImage(config);
  }

  Future<Uint8List> _createPlaceholderImage(ExportConfig config) async {
    // 创建一个简单的占位图片
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint()..color = Colors.white;

    canvas.drawRect(
      Rect.fromLTWH(
        0,
        0,
        config.size.width.toDouble(),
        config.size.height.toDouble(),
      ),
      paint,
    );

    // 绘制文本
    final textPainter = TextPainter(
      text: const TextSpan(
        text: '卡片导出功能\n需要添加相关依赖',
        style: TextStyle(
          color: Colors.black,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (config.size.width - textPainter.width) / 2,
        (config.size.height - textPainter.height) / 2,
      ),
    );

    final picture = recorder.endRecording();
    final image = await picture.toImage(config.size.width, config.size.height);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    return byteData!.buffer.asUint8List();
  }

  Future<void> _saveToGallery(
    Uint8List imageBytes,
    String filename,
    ExportConfig config,
  ) async {
    try {
      // 这里需要使用 image_gallery_saver 或 gal 等包来保存到相册
      // 由于包依赖的限制，这里只是模拟保存过程

      // 模拟保存延迟
      await Future.delayed(const Duration(seconds: 1));

      // 在实际项目中，应该这样实现：
      // if (config.format == ExportFormat.png) {
      //   await ImageGallerySaver.saveImage(imageBytes, name: filename);
      // } else if (config.format == ExportFormat.jpg) {
      //   await ImageGallerySaver.saveImage(imageBytes, name: filename, isReturnImagePathOfIOS: true);
      // }

      debugPrint('图片已保存: $filename.${config.format.name}');
    } catch (e) {
      throw Exception('保存到相册失败: $e');
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}.${dateTime.month.toString().padLeft(2, '0')}.${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
