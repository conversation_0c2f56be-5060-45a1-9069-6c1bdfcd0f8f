import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import 'models/text_card_model.dart';
import 'models/document_model.dart';
import 'widgets/card_template_selector.dart';
import 'widgets/text_splitter_widget.dart';
import 'document_cards_page.dart';

class DocumentSplitterPage extends StatefulWidget {
  final DocumentModel? document;

  const DocumentSplitterPage({super.key, this.document});

  @override
  State<DocumentSplitterPage> createState() => _DocumentSplitterPageState();
}

class _DocumentSplitterPageState extends State<DocumentSplitterPage> {
  late TextEditingController _titleController;
  late TextEditingController _contentController;
  late CardTemplate _selectedTemplate;

  List<TextCardModel> _previewCards = [];
  bool _isLoading = false;
  int _currentStep = 0; // 0: 输入, 1: 预览

  // 分隔符
  static const String _cardSeparator = '---CARD---';

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _contentController = TextEditingController();
    _selectedTemplate = CardTemplate.getBuiltInTemplates().first;

    if (widget.document != null) {
      _loadDocument();
    } else {
      // 示例文本
      _contentController.text = '''什么是历史唯物主义？

**历史唯物主义**认为：历史的根本动力，不是*思想*、*道德*、*英雄个人*，而是<mark>生产关系与生产力之间的矛盾</mark>。

---CARD---

几个硬定义

• **生产力**：人获取资源的能力，比如*技术*、*工具*、劳动力组织方式。

• **生产关系**：资源归谁所有、谁决定生产、谁分配成果，比如<u>地主-佃农</u>、<u>资本家-工人</u>。

• **经济基础**：就是生产力 + 生产关系。

• **上层建筑**：法律、宗教、意识形态、国家机器，用来<mark>服务和维护经济基础</mark>。''';
      _titleController.text = '历史唯物主义基础概念';
    }

    // 监听文本变化，实时生成预览
    _contentController.addListener(_updatePreview);
  }

  void _loadDocument() {
    final doc = widget.document!;
    _titleController.text = doc.title;

    // 将卡片重新组合为文本
    final textParts = <String>[];
    for (int i = 0; i < doc.cards.length; i++) {
      final card = doc.cards[i];
      if (card.title.isNotEmpty) {
        textParts.add(card.title);
        textParts.add('');
        textParts.add(card.content);
      } else {
        textParts.add(card.content);
      }

      if (i < doc.cards.length - 1) {
        textParts.add('');
        textParts.add(_cardSeparator);
        textParts.add('');
      }
    }

    _contentController.text = textParts.join('\n');

    _selectedTemplate = CardTemplate.getBuiltInTemplates().firstWhere(
      (t) => t.id == doc.globalTemplateId,
    );
    _currentStep = 0; // 回到编辑模式
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _updatePreview() {
    // 实时解析文本并生成预览卡片
    final text = _contentController.text.trim();
    if (text.isEmpty) {
      setState(() {
        _previewCards = [];
      });
      return;
    }

    final cardTexts = text.split(_cardSeparator);
    final cards = <TextCardModel>[];

    for (int i = 0; i < cardTexts.length; i++) {
      final cardText = cardTexts[i].trim();
      if (cardText.isEmpty) continue;

      String title = '';
      String content = '';

      // 分析卡片文本，提取标题和内容
      final lines =
          cardText.split('\n').where((line) => line.trim().isNotEmpty).toList();

      if (lines.isNotEmpty) {
        // 第一行作为标题（如果看起来像标题）
        final firstLine = lines.first.trim();
        if (lines.length > 1 &&
            (firstLine.length < 50 ||
                firstLine.endsWith('？') ||
                firstLine.endsWith('?') ||
                firstLine.endsWith('：') ||
                firstLine.endsWith(':'))) {
          title = firstLine;
          content = lines.skip(1).join('\n').trim();
        } else {
          // 如果第一行不像标题，所有内容作为正文
          content = lines.join('\n').trim();
        }
      }

      if (content.isNotEmpty) {
        cards.add(
          TextCardModel(
            id: '${DateTime.now().millisecondsSinceEpoch}_$i',
            title: title,
            content: content,
            templateId: _selectedTemplate.id,
            createdAt: DateTime.now(),
          ),
        );
      }
    }

    setState(() {
      _previewCards = cards;
    });
  }

  void _insertSeparator() {
    final selection = _contentController.selection;
    final text = _contentController.text;

    // 在光标位置插入分隔符
    final newText = text.replaceRange(
      selection.start,
      selection.end,
      '\n\n$_cardSeparator\n\n',
    );

    _contentController.text = newText;
    _contentController.selection = TextSelection.collapsed(
      offset: selection.start + _cardSeparator.length + 4,
    );
  }

  void _togglePreview() {
    setState(() {
      _currentStep = _currentStep == 0 ? 1 : 0;
    });
  }

  void _selectTemplate() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => CardTemplateSelector(
            onTemplateSelected: (templateId) {
              setState(() {
                _selectedTemplate = CardTemplate.getBuiltInTemplates()
                    .firstWhere((t) => t.id == templateId);
                // 更新所有预览卡片的模板
                _previewCards =
                    _previewCards
                        .map((card) => card.copyWith(templateId: templateId))
                        .toList();
              });
              Navigator.pop(context);
            },
          ),
    );
  }

  void _saveDocument() async {
    if (_titleController.text.trim().isEmpty || _previewCards.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入标题并确保至少有一个卡片')));
      return;
    }

    setState(() => _isLoading = true);

    try {
      final document = DocumentModel(
        id:
            widget.document?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        title: _titleController.text.trim(),
        originalText: _contentController.text,
        cards: _previewCards,
        globalTemplateId: _selectedTemplate.id,
        createdAt: widget.document?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = await Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => DocumentCardsPage(document: document),
        ),
      );

      if (result != null) {
        Navigator.pop(context, result);
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('保存失败: $e')));
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: Text(
          widget.document != null ? '编辑文档' : '创建文档',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textDarkColor,
          ),
        ),
        backgroundColor: AppTheme.bgWhiteColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _currentStep == 0 ? Icons.preview : Icons.edit,
              color: AppTheme.primaryColor,
            ),
            onPressed: _togglePreview,
            tooltip: _currentStep == 0 ? '预览' : '编辑',
          ),
          if (_currentStep == 1)
            TextButton(
              onPressed: _isLoading ? null : _saveDocument,
              child:
                  _isLoading
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text(
                        '保存',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                      ),
            ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // 模式指示器
          Container(
            color: AppTheme.bgWhiteColor,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            child: Row(
              children: [
                Icon(
                  _currentStep == 0 ? Icons.edit : Icons.preview,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  _currentStep == 0 ? '文本编辑模式' : '预览模式',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const Spacer(),
                if (_currentStep == 1)
                  Text(
                    '共 ${_previewCards.length} 个卡片',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textLightColor,
                    ),
                  ),
              ],
            ),
          ),

          const Divider(height: 1),

          // 主内容区域
          Expanded(child: _buildContent()),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_currentStep == 0) {
      return _buildEditMode();
    } else {
      return _buildPreviewMode();
    }
  }

  Widget _buildEditMode() {
    return Column(
      children: [
        // 工具栏
        Container(
          color: AppTheme.bgWhiteColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              ElevatedButton.icon(
                onPressed: _insertSeparator,
                icon: const Icon(Icons.content_cut, size: 16),
                label: const Text('插入分隔符'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  textStyle: const TextStyle(fontSize: 12),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '使用 "$_cardSeparator" 分隔卡片',
                  style: const TextStyle(
                    fontSize: 11,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        const Divider(height: 1),

        // 编辑区域
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                TextField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: '文档标题',
                    hintText: '为这组卡片起个标题...',
                    border: OutlineInputBorder(),
                    filled: true,
                    fillColor: AppTheme.bgWhiteColor,
                  ),
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: TextField(
                    controller: _contentController,
                    decoration: InputDecoration(
                      labelText: '文本内容',
                      hintText:
                          '输入或粘贴长文本...\n\n💡 使用技巧：\n• 在想要分割的地方点击"插入分隔符"按钮\n• 第一行如果是标题会自动识别\n• 点击右上角"预览"查看拆分效果',
                      border: const OutlineInputBorder(),
                      filled: true,
                      fillColor: AppTheme.bgWhiteColor,
                      alignLabelWithHint: true,
                    ),
                    maxLines: null,
                    expands: true,
                    textAlignVertical: TextAlignVertical.top,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewMode() {
    return Column(
      children: [
        // 工具栏
        Container(
          color: AppTheme.bgWhiteColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  _titleController.text.isNotEmpty
                      ? _titleController.text
                      : '未命名文档',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textDarkColor,
                  ),
                ),
              ),
              TextButton.icon(
                onPressed: _selectTemplate,
                icon: const Icon(Icons.palette, size: 16),
                label: const Text('统一样式'),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),

        const Divider(height: 1),

        // 卡片预览列表
        Expanded(
          child:
              _previewCards.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.content_cut,
                          size: 48,
                          color: AppTheme.textLightColor.withValues(alpha: 0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          '还没有卡片',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppTheme.textLightColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '回到编辑模式添加分隔符来创建卡片',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.textLightColor.withValues(
                              alpha: 0.7,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                  : TextSplitterWidget(
                    cards: _previewCards,
                    onCardsChanged: (cards) {
                      setState(() {
                        _previewCards = cards;
                      });
                    },
                    globalTemplate: _selectedTemplate,
                  ),
        ),
      ],
    );
  }
}
