import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import 'models/document_model.dart';
import 'models/text_card_model.dart';
import 'widgets/text_card_widget.dart';
import 'widgets/card_template_selector.dart';
import 'export/card_export_page.dart';
import 'document_splitter_page.dart';

class DocumentCardsPage extends StatefulWidget {
  final DocumentModel document;

  const DocumentCardsPage({super.key, required this.document});

  @override
  State<DocumentCardsPage> createState() => _DocumentCardsPageState();
}

class _DocumentCardsPageState extends State<DocumentCardsPage> {
  late DocumentModel _document;
  bool _isGridView = true;

  @override
  void initState() {
    super.initState();
    _document = widget.document;
  }

  void _changeGlobalTemplate() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => CardTemplateSelector(
            onTemplateSelected: (templateId) {
              setState(() {
                // 更新全局模板
                _document = _document.copyWith(
                  globalTemplateId: templateId,
                  updatedAt: DateTime.now(),
                );
                // 更新所有卡片的模板
                final updatedCards =
                    _document.cards
                        .map((card) => card.copyWith(templateId: templateId))
                        .toList();
                _document = _document.copyWith(cards: updatedCards);
              });
              Navigator.pop(context);
            },
          ),
    );
  }

  void _editDocument() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DocumentSplitterPage(document: _document),
      ),
    );

    if (result != null && result is DocumentModel) {
      setState(() {
        _document = result;
      });
    }
  }

  void _exportCards() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CardExportPage(document: _document),
      ),
    );
  }

  void _exportSingleCard(TextCardModel card) {
    // 创建只包含单个卡片的临时文档
    final tempDocument = DocumentModel(
      id: 'temp_${card.id}',
      title: card.title,
      originalText: card.content,
      cards: [card],
      globalTemplateId: card.templateId,
      createdAt: DateTime.now(),
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                CardExportPage(document: tempDocument, isSingleCard: true),
      ),
    );
  }

  void _showCardOptions(TextCardModel card) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppTheme.textLightColor.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  card.title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textDarkColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 20),
                ListTile(
                  leading: const Icon(
                    Icons.image,
                    color: AppTheme.primaryColor,
                  ),
                  title: const Text('导出为图片'),
                  subtitle: const Text('保存这个卡片为图片'),
                  onTap: () {
                    Navigator.pop(context);
                    _exportSingleCard(card);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.edit, color: AppTheme.primaryColor),
                  title: const Text('编辑卡片'),
                  subtitle: const Text('修改标题和内容'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: 实现单卡片编辑
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text('删除卡片'),
                  subtitle: const Text('从文档中移除'),
                  onTap: () {
                    Navigator.pop(context);
                    _deleteCard(card);
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _deleteCard(TextCardModel card) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('删除卡片'),
            content: Text('确定要删除卡片"${card.title}"吗？此操作无法撤销。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    final updatedCards =
                        _document.cards.where((c) => c.id != card.id).toList();
                    _document = _document.copyWith(
                      cards: updatedCards,
                      updatedAt: DateTime.now(),
                    );
                  });
                  Navigator.pop(context);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('删除'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: Text(
          _document.title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textDarkColor,
          ),
        ),
        backgroundColor: AppTheme.bgWhiteColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _isGridView ? Icons.view_list : Icons.grid_view,
              color: AppTheme.textDarkColor,
            ),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: AppTheme.textDarkColor),
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _editDocument();
                  break;
                case 'template':
                  _changeGlobalTemplate();
                  break;
                case 'export':
                  _exportCards();
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('编辑文档'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'template',
                    child: Row(
                      children: [
                        Icon(Icons.palette, size: 20),
                        SizedBox(width: 8),
                        Text('统一样式'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'export',
                    child: Row(
                      children: [
                        Icon(Icons.image, size: 20),
                        SizedBox(width: 8),
                        Text('导出图片'),
                      ],
                    ),
                  ),
                ],
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // 文档信息栏
          Container(
            color: AppTheme.bgWhiteColor,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '共 ${_document.cards.length} 个卡片',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.textDarkColor,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '模板：${CardTemplate.getBuiltInTemplates().firstWhere((t) => t.id == _document.globalTemplateId).name}',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textLightColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _formatTime(_document.updatedAt ?? _document.createdAt),
                    style: const TextStyle(
                      fontSize: 11,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // 卡片列表
          Expanded(
            child:
                _document.cards.isEmpty
                    ? _buildEmptyState()
                    : _isGridView
                    ? _buildGridView()
                    : _buildListView(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: 'document_cards_fab',
        backgroundColor: AppTheme.primaryColor,
        onPressed: _exportCards,
        child: const Icon(Icons.image, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.text_snippet_outlined,
            size: 80,
            color: AppTheme.textLightColor,
          ),
          const SizedBox(height: 16),
          const Text(
            '文档中没有卡片',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '编辑文档来添加卡片',
            style: TextStyle(fontSize: 14, color: AppTheme.textLightColor),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _editDocument,
            icon: const Icon(Icons.edit),
            label: const Text('编辑文档'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 1,
          childAspectRatio: 1.5,
          mainAxisSpacing: 16,
        ),
        itemCount: _document.cards.length,
        itemBuilder: (context, index) {
          final card = _document.cards[index];
          return TextCardWidget(
            card: card,
            onTap: () => _showCardOptions(card),
            onDelete: () => _deleteCard(card),
          );
        },
      ),
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _document.cards.length,
      itemBuilder: (context, index) {
        final card = _document.cards[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: TextCardWidget(
            card: card,
            isCompact: true,
            onTap: () => _showCardOptions(card),
            onDelete: () => _deleteCard(card),
          ),
        );
      },
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
