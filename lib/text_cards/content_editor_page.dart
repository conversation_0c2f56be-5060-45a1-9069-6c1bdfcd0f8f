// 内容编辑器 - 专注于纯文本编辑和内容拆分
import 'package:flutter/material.dart';

import 'models/content_models.dart';
import 'visual_renderer_page.dart';

class ContentEditorPage extends StatefulWidget {
  final String? existingContent;
  final String? title;

  const ContentEditorPage({super.key, this.existingContent, this.title});

  @override
  State<ContentEditorPage> createState() => _ContentEditorPageState();
}

class _ContentEditorPageState extends State<ContentEditorPage> {
  late TextEditingController _titleController;
  late TextEditingController _contentController;
  final List<String> _splitMarkers = [];
  bool _isPreviewMode = false;
  RichTextBlock? _previewBlock;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.title ?? '');
    _contentController = TextEditingController(
      text: widget.existingContent ?? '',
    );
    _updatePreview();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _updatePreview() {
    final content = _contentController.text;
    _previewBlock = ContentParser.parseTextToRichBlock(content);
    setState(() {});
  }

  List<String> _splitContentIntoSections() {
    final content = _contentController.text;
    if (_splitMarkers.isEmpty) {
      return [content];
    }

    final sections = <String>[];
    final lines = content.split('\n');
    List<String> currentSection = [];

    for (final line in lines) {
      if (_splitMarkers.contains(line.trim())) {
        if (currentSection.isNotEmpty) {
          sections.add(currentSection.join('\n'));
          currentSection = [];
        }
      } else {
        currentSection.add(line);
      }
    }

    if (currentSection.isNotEmpty) {
      sections.add(currentSection.join('\n'));
    }

    return sections.where((section) => section.trim().isNotEmpty).toList();
  }

  void _addSplitMarker() {
    showDialog(
      context: context,
      builder:
          (context) => _SplitMarkerDialog(
            onMarkerSelected: (marker) {
              final cursorPos = _contentController.selection.baseOffset;
              final text = _contentController.text;
              final newText =
                  '${text.substring(0, cursorPos)}\n$marker\n${text.substring(cursorPos)}';

              _contentController.text = newText;
              _contentController.selection = TextSelection.fromPosition(
                TextPosition(offset: cursorPos + marker.length + 2),
              );

              if (!_splitMarkers.contains(marker)) {
                _splitMarkers.add(marker);
              }

              _updatePreview();
            },
          ),
    );
  }

  void _proceedToRenderer() {
    final sections = _splitContentIntoSections();
    final title = _titleController.text.trim();

    if (sections.length == 1) {
      // 单个卡片
      Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (context) => VisualRendererPage(
                content: sections.first,
                title: title.isEmpty ? '未命名卡片' : title,
                mode: 'single',
              ),
        ),
      );
    } else {
      // 多个卡片
      Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (context) => VisualRendererPage(
                sections: sections,
                title: title.isEmpty ? '未命名文档' : title,
                mode: 'multiple',
              ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('内容编辑器'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_isPreviewMode ? Icons.edit : Icons.visibility),
            onPressed: () {
              setState(() {
                _isPreviewMode = !_isPreviewMode;
                if (_isPreviewMode) {
                  _updatePreview();
                }
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.cut),
            onPressed: _addSplitMarker,
            tooltip: '添加拆分标记',
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward),
            onPressed: _proceedToRenderer,
            tooltip: '进入渲染器',
          ),
        ],
      ),
      body: Column(
        children: [
          // 标题输入
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
            ),
            child: TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                hintText: '输入标题（可选）',
                border: InputBorder.none,
                hintStyle: TextStyle(color: Colors.grey),
              ),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
          ),

          // 分割线状态
          if (_splitMarkers.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: Colors.blue[50],
              child: Row(
                children: [
                  Icon(Icons.info_outline, size: 16, color: Colors.blue[700]),
                  const SizedBox(width: 8),
                  Text(
                    '已设置 ${_splitMarkers.length} 个拆分标记，将生成 ${_splitContentIntoSections().length} 个卡片',
                    style: TextStyle(color: Colors.blue[700], fontSize: 12),
                  ),
                ],
              ),
            ),

          // 主要内容区域
          Expanded(
            child: _isPreviewMode ? _buildPreviewArea() : _buildEditArea(),
          ),
        ],
      ),
    );
  }

  Widget _buildEditArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _contentController,
        maxLines: null,
        expands: true,
        textAlignVertical: TextAlignVertical.top,
        decoration: const InputDecoration(
          hintText:
              '在这里输入或粘贴您的内容...\n\n提示：\n- 使用 # 创建标题\n- 使用 - 或 * 创建列表\n- 使用 > 创建引用\n- 点击拆分按钮在光标位置添加拆分标记',
          border: InputBorder.none,
          hintStyle: TextStyle(color: Colors.grey),
        ),
        style: const TextStyle(fontSize: 16, height: 1.5),
        onChanged: (_) => _updatePreview(),
      ),
    );
  }

  Widget _buildPreviewArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '文本预览',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[200]!),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              child:
                  _previewBlock != null
                      ? RichText(
                        text: _previewBlock!.toTextSpan(),
                        textAlign: TextAlign.left,
                      )
                      : Text(
                        '输入内容后将显示预览',
                        style: TextStyle(color: Colors.grey[500]),
                      ),
            ),
          ),
        ],
      ),
    );
  }
}

class _SplitMarkerDialog extends StatefulWidget {
  final Function(String) onMarkerSelected;

  const _SplitMarkerDialog({required this.onMarkerSelected});

  @override
  State<_SplitMarkerDialog> createState() => __SplitMarkerDialogState();
}

class __SplitMarkerDialogState extends State<_SplitMarkerDialog> {
  final TextEditingController _customController = TextEditingController();

  final List<String> _predefinedMarkers = [
    '--- 分割线 ---',
    '=== 新卡片 ===',
    '### 分段 ###',
    '>>> 下一部分 >>>',
    '*** 拆分点 ***',
  ];

  @override
  void dispose() {
    _customController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('选择拆分标记'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('预定义标记:', style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            ...(_predefinedMarkers.map(
              (marker) => ListTile(
                title: Text(marker),
                onTap: () {
                  Navigator.of(context).pop();
                  widget.onMarkerSelected(marker);
                },
                dense: true,
              ),
            )),
            const SizedBox(height: 16),
            const Text('自定义标记:', style: TextStyle(fontWeight: FontWeight.w500)),
            const SizedBox(height: 8),
            TextField(
              controller: _customController,
              decoration: const InputDecoration(
                hintText: '输入自定义拆分标记',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            final custom = _customController.text.trim();
            if (custom.isNotEmpty) {
              Navigator.of(context).pop();
              widget.onMarkerSelected(custom);
            }
          },
          child: const Text('使用自定义'),
        ),
      ],
    );
  }
}
