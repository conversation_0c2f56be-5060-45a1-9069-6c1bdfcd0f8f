import 'dart:convert';
import 'package:flutter/material.dart';
import '../content/content_home_page.dart';
import '../models/content_item.dart';
import '../services/content_service.dart';
import 'widgets/modern_card_creator.dart';
import 'widgets/template_gallery.dart';
import 'widgets/smart_text_splitter.dart';
import 'models/enhanced_card_template.dart';

/// 重新设计的文本卡片主页
/// 核心理念：纯创建入口 + 统一内容管理 + 丰富模板
class TextCardsHomePage extends StatefulWidget {
  const TextCardsHomePage({super.key});

  @override
  State<TextCardsHomePage> createState() => _TextCardsHomePageState();
}

class _TextCardsHomePageState extends State<TextCardsHomePage>
    with TickerProviderStateMixin {
  final ContentService _contentService = ContentService();
  late AnimationController _heroAnimationController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeContentService();
  }

  void _initializeAnimations() {
    _heroAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _heroAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _heroAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // 启动入场动画
    _heroAnimationController.forward();
    Future.delayed(const Duration(milliseconds: 600), () {
      _fabAnimationController.forward();
    });
  }

  Future<void> _initializeContentService() async {
    await _contentService.initialize();
  }

  /// 显示现代风格的卡片创建器
  void _showModernCardCreator() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) =>
              ModernCardCreator(
        onCardCreated: _handleCardCreated,
      ),
    );
  }

  /// 处理单个卡片创建
  Future<void> _handleCardCreated(String content, EnhancedCardTemplate template) async {
    try {
      debugPrint('开始创建卡片，内容长度: ${content.length}, 模板: ${template.name}');

      // 使用统一的创建方法
      await _createSingleCard(content, template);

      debugPrint('卡片创建成功，包含模板信息');

      // 刷新UI状态
      if (mounted) {
        setState(() {
          // 触发UI重建，确保内容库能显示新创建的卡片
        });
      }

      _showSuccessMessage('卡片创建成功！已保存到内容库');

      // 可选：延迟后自动跳转到内容库查看
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          _navigateToContentLibrary();
        }
      });

    } catch (e) {
      debugPrint('创建卡片失败: $e');
      _showErrorMessage('创建失败：$e');
    }
  }

  /// 处理批量创建
  Future<void> _handleBatchCreate(List<String> contents, EnhancedCardTemplate template) async {
    try {
      debugPrint('开始批量创建卡片，数量: ${contents.length}');

      int successCount = 0;
      for (final content in contents) {
        try {
          // 直接创建，不触发单个卡片的导航逻辑
          await _createSingleCard(content, template);
          successCount++;
        } catch (e) {
          debugPrint('创建单个卡片失败: $e');
          // 继续创建其他卡片
        }
      }

      // 刷新UI状态
      if (mounted) {
        setState(() {
          // 触发UI重建
        });
      }

      if (successCount == contents.length) {
        _showSuccessMessage('批量创建成功！共创建 $successCount 张卡片');
      } else {
        _showSuccessMessage('批量创建完成！成功创建 $successCount/${contents.length} 张卡片');
      }

      // 延迟后跳转到内容库
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          _navigateToContentLibrary();
        }
      });

    } catch (e) {
      debugPrint('批量创建失败: $e');
      _showErrorMessage('批量创建失败：$e');
    }
  }

  /// 创建单个卡片（不包含UI导航逻辑）
  Future<void> _createSingleCard(String content, EnhancedCardTemplate template) async {
    // 创建包含完整模板信息的卡片数据
    final cardData = {
      'type': 'text_card',
      'content': {
        'text': content,
        'template': {
          'id': template.id,
          'name': template.name,
          'category': template.category,
          'description': template.description,
          'backgroundGradient': {
            'colors':
                template.backgroundGradient.colors.map((c) => c.toARGB32()).toList(),
            'begin': template.backgroundGradient.begin.toString(),
            'end': template.backgroundGradient.end.toString(),
          },
          'textColor': template.textColor.toARGB32(),
          'titleColor': template.titleColor.toARGB32(),
          'accentColor': template.accentColor.toARGB32(),
          'borderRadius': template.borderRadius,
          'titleFontSize': template.titleFontSize,
          'contentFontSize': template.contentFontSize,
          'titleFontWeight': template.titleFontWeight.index,
          'contentFontWeight': template.contentFontWeight.index,
          'lineHeight': template.lineHeight,
          'layoutStyle': template.layoutStyle,
          'fontFamily': template.fontFamily,
          'padding': template.padding,
          'textAlign': template.textAlign.index,
          'hasShadow': template.hasShadow,
          'hasDecorationElements': template.hasDecorationElements,
        },
        'customStyles': {}, // 用于存储用户自定义的样式
        'createdAt': DateTime.now().toIso8601String(),
        'version': '1.0',
      },
    };

    // 创建内容项
    final contentItem = await _contentService.createTextContent(
      title: _extractTitleFromContent(content),
      type: ContentType.markdown, // 使用markdown类型，通过tags区分
      content: jsonEncode(cardData), // 序列化为JSON字符串
      tags: ['text_card', template.category, template.name],
    );

    debugPrint('文本卡片已保存到内容库，ID: ${contentItem.id}');
  }

  /// 从内容中提取标题
  String _extractTitleFromContent(String content) {
    final lines = content.split('\n');
    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.isNotEmpty && !trimmed.startsWith('#')) {
        return trimmed.length > 30 ? '${trimmed.substring(0, 30)}...' : trimmed;
      }
      if (trimmed.startsWith('#')) {
        return trimmed.replaceAll('#', '').trim();
      }
    }
    return '新建文本卡片';
  }

  /// 显示成功消息
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  /// 显示错误消息
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  /// 导航到内容库
  void _navigateToContentLibrary() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ContentHomePage(),
      ),
    );
  }



  @override
  void dispose() {
    _heroAnimationController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E1A),
      body: CustomScrollView(
        slivers: [
          // 大气的Hero区域
          _buildHeroSection(),

          // 主要创建区域
          SliverFillRemaining(
            child: AnimatedBuilder(
              animation: _heroAnimationController,
              builder: (context, child) {
                return SlideTransition(
                  position: _slideAnimation,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: _buildMainContent(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: _buildAnimatedFAB(),
    );
  }

  /// 构建大气的Hero区域
  Widget _buildHeroSection() {
    return SliverAppBar(
      expandedHeight: 400,
      floating: false,
      pinned: false,
      stretch: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF1A1F3A), // 深蓝紫
                Color(0xFF2D1B69), // 深紫
                Color(0xFF0A0E1A), // 深黑蓝
              ],
              stops: [0.0, 0.6, 1.0],
            ),
          ),
          child: Stack(
            children: [
              // 背景装饰元素
              _buildBackgroundDecorations(),

              // 主要内容
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Spacer(),

                      // 主标题
                      ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [
                            Color(0xFFFFFFFF),
                            Color(0xFFE0E7FF),
                            Color(0xFFC7D2FE),
                          ],
                        ).createShader(bounds),
                        child: const Text(
                          '文本卡片',
                          style: TextStyle(
                            fontSize: 48,
                            fontWeight: FontWeight.w900,
                            color: Colors.white,
                            letterSpacing: -1.5,
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // 副标题
                      const Text(
                        '现代风格 • 内联编辑 • 高清导出',
                        style: TextStyle(
                          fontSize: 18,
                          color: Color(0xFFB8C5D1),
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.5,
                        ),
                      ),

                      const SizedBox(height: 32),

                      // 创建按钮
                      _buildHeroCreateButton(),

                      const Spacer(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建主要内容区域
  Widget _buildMainContent() {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFFF8FAFC),
        borderRadius: BorderRadius.vertical(top: Radius.circular(32)),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 快速操作区域
            _buildQuickActionsGrid(),
            const SizedBox(height: 40),

            // 功能特色展示
            _buildFeatureShowcase(),
            const SizedBox(height: 40),

            // 底部访问内容库按钮
            _buildContentLibraryAccess(),

            // 添加底部安全间距，确保内容不会被遮挡
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// 构建背景装饰元素
  Widget _buildBackgroundDecorations() {
    return Stack(
      children: [
        // 渐变光晕效果
        Positioned(
          top: -100,
          right: -100,
          child: Container(
            width: 300,
            height: 300,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  const Color(0xFF6366F1).withValues(alpha: 0.3),
                  const Color(0xFF6366F1).withValues(alpha: 0.1),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),

        // 左下角装饰
        Positioned(
          bottom: -50,
          left: -50,
          child: Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  const Color(0xFF8B5CF6).withValues(alpha: 0.2),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),

        // 几何装饰线条
        Positioned(
          top: 100,
          left: 20,
          child: Transform.rotate(
            angle: 0.3,
            child: Container(
              width: 100,
              height: 2,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withValues(alpha: 0.3),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建Hero区域的创建按钮
  Widget _buildHeroCreateButton() {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF6366F1),
            Color(0xFF8B5CF6),
            Color(0xFFEC4899),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6366F1).withValues(alpha: 0.4),
            offset: const Offset(0, 12),
            blurRadius: 32,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _showModernCardCreator,
          borderRadius: BorderRadius.circular(24),
          child: const Padding(
            padding: EdgeInsets.symmetric(horizontal: 32, vertical: 20),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.auto_awesome,
                  size: 24,
                  color: Colors.white,
                ),
                SizedBox(width: 12),
                Text(
                  '开始创作',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建快速操作网格
  Widget _buildQuickActionsGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '快速操作',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: Color(0xFF1E293B),
            letterSpacing: -0.5,
          ),
        ),
        const SizedBox(height: 20),

        Row(
          children: [
            Expanded(
              child: _buildModernActionCard(
                icon: Icons.palette_outlined,
                title: '模板库',
                subtitle: '16+ 精美模板',
                color: const Color(0xFF6366F1),
                onTap: _showTemplateGallery,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildModernActionCard(
                icon: Icons.auto_awesome_outlined,
                title: '智能拆分',
                subtitle: '长文本分段',
                color: const Color(0xFF8B5CF6),
                onTap: _showDocumentSplitter,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        Row(
          children: [
            Expanded(
              child: _buildModernActionCard(
                icon: Icons.library_books_outlined,
                title: '内容库',
                subtitle: '管理所有卡片',
                color: const Color(0xFF06B6D4),
                onTap: _navigateToContentLibrary,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildModernActionCard(
                icon: Icons.share_outlined,
                title: '分享',
                subtitle: '导出高清图片',
                color: const Color(0xFF10B981),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('请先创建卡片')),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建现代化操作卡片
  Widget _buildModernActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      height: 120, // 增加高度从100到120，提供更多空间
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            offset: const Offset(0, 4),
            blurRadius: 16,
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: color.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween, // 确保内容均匀分布
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: 16,
                    color: color,
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1E293B),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2), // 添加固定间距
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 11,
                        color: Color(0xFF64748B),
                        fontWeight: FontWeight.w400,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 显示模板画廊
  void _showTemplateGallery() {
    debugPrint('显示模板画廊');
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => TemplateGallery(
        onTemplateSelected: (template) {
          debugPrint('模板画廊回调被触发: ${template.name}');
          // 使用选中的模板创建新卡片
          _createCardWithTemplate(template);
        },
      ),
    );
  }

  /// 使用指定模板创建卡片
  void _createCardWithTemplate(EnhancedCardTemplate template) {
    debugPrint('创建卡片，使用模板: ${template.name}');
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ModernCardCreator(
        initialTemplate: template,
            onCardCreated:
                (content, selectedTemplate) =>
                    _handleCardCreated(content, selectedTemplate),
      ),
    );
  }

  /// 显示智能文档拆分器
  void _showDocumentSplitter() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SmartTextSplitter(
        onBatchCreate: _handleBatchCreate,
      ),
    );
  }

  /// 构建功能特色展示
  Widget _buildFeatureShowcase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '功能特色',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: Color(0xFF1E293B),
            letterSpacing: -0.5,
          ),
        ),
        const SizedBox(height: 20),

        Container(
          padding: const EdgeInsets.all(28),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF1E293B).withValues(alpha: 0.08),
                offset: const Offset(0, 8),
                blurRadius: 32,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            children: [
              _buildFeatureItem(
                icon: Icons.auto_awesome_outlined,
                title: '现代风格模板',
                description: '精心设计的现代社交和阅读风格模板，让你的内容更有吸引力',
                color: const Color(0xFFFF2442),
              ),
              const SizedBox(height: 24),
              _buildFeatureItem(
                icon: Icons.edit_outlined,
                title: '内联文本编辑',
                description: '选中任意文本片段，实时调整字体、颜色、大小，所见即所得',
                color: const Color(0xFF6366F1),
              ),
              const SizedBox(height: 24),
              _buildFeatureItem(
                icon: Icons.high_quality_outlined,
                title: '高清图片导出',
                description: '支持多种分辨率和宽高比，一键保存到相册，完美适配各平台',
                color: const Color(0xFF10B981),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建功能项
  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(
            icon,
            size: 24,
            color: color,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1E293B),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF64748B),
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建内容库访问按钮
  Widget _buildContentLibraryAccess() {
    return Container(
      width: double.infinity,
      height: 72,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1E293B),
            Color(0xFF334155),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF1E293B).withValues(alpha: 0.3),
            offset: const Offset(0, 8),
            blurRadius: 24,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _navigateToContentLibrary,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.library_books_outlined,
                    size: 20,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '查看我的内容库',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        '管理和浏览所有创建的卡片',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFFB8C5D1),
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Color(0xFFB8C5D1),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }







  /// 构建动画FAB
  Widget _buildAnimatedFAB() {
    return AnimatedBuilder(
      animation: _fabAnimationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _fabAnimationController.value,
          child: FloatingActionButton.extended(
            heroTag: 'text_cards_fab',
            onPressed: _showModernCardCreator,
            backgroundColor: const Color(0xFF6366F1),
            elevation: 12,
            icon: const Icon(
              Icons.auto_awesome,
              color: Colors.white,
              size: 20,
            ),
            label: const Text(
              '创作',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        );
      },
    );
  }
}
