# 文本卡片功能 - 全面重新设计

## 🎨 设计理念

基于现代社交平台和阅读应用的优秀设计理念，全面重新设计文本卡片生成功能，提供世界级的用户体验。

## ✨ 核心特性

### 🎯 现代风格模板
- **经典白底模板**：简约清新的经典风格
- **渐变背景模板**：时尚活力的渐变色彩
- **知识卡片模板**：专业的知识分享风格
- **语录卡片模板**：优雅大气的经典语录风格
- **阅读风格模板**：护眼舒适的阅读风格

### ✏️ 内联文本编辑
- **选中文本编辑**：选中任意文本片段进行样式调整
- **实时预览**：所见即所得的编辑体验
- **字体选择**：支持多种开源中文字体
- **样式控制**：字号、字重、颜色等全面控制
- **手势交互**：流畅的触摸和手势控制

### 🖼️ 高清图片导出
- **多种分辨率**：支持1:1、4:3、16:9、9:16等多种比例
- **高质量渲染**：3倍像素密度，确保图片清晰
- **装饰元素**：根据模板自动添加装饰元素
- **水印支持**：可选的水印和时间戳
- **一键保存**：直接保存到设备相册

### 🎭 丰富的视觉效果
- **渐变背景**：多种精美的渐变色彩
- **装饰元素**：根据模板风格自动添加装饰
- **阴影效果**：立体感的卡片阴影
- **圆角设计**：现代化的圆角边框

## 🏗️ 技术架构

### 核心组件

1. **FontManager** - 字体管理系统
   - 管理所有可用的中文字体
   - 提供字体预设和推荐
   - 支持字重检查和匹配

2. **EnhancedCardTemplate** - 增强模板系统
   - 现代社交风格的模板设计
   - 丰富的样式配置选项
   - 分类管理和快速检索

3. **AdvancedTextEditor** - 高级文本编辑器
   - 选中文本的内联编辑
   - 浮动工具栏界面
   - 实时样式预览

4. **EnhancedCardRenderer** - 增强渲染器
   - 高质量图片渲染
   - 装饰元素自动添加
   - 多种导出配置

5. **ModernCardCreator** - 现代风格创建器
   - 三步式创建流程
   - 流畅的动画效果
   - 直观的用户界面

### 依赖包

```yaml
dependencies:
  screenshot: ^2.1.0              # 截图功能
  image_gallery_saver: ^2.0.3    # 保存到相册
  flutter_colorpicker: ^1.1.0    # 颜色选择器
  permission_handler: ^11.3.0    # 权限管理
```

## 🚀 使用方法

### 1. 安装字体资源

```bash
# 运行字体下载脚本
./scripts/download_fonts.sh

# 手动下载缺失的字体文件到 assets/fonts/ 目录
```

### 2. 创建卡片

```dart
// 显示现代风格的卡片创建器
showModalBottomSheet(
  context: context,
  isScrollControlled: true,
  backgroundColor: Colors.transparent,
  builder: (context) => ModernCardCreator(
    onCardCreated: (content, template) {
      // 处理卡片创建
    },
  ),
);
```

### 3. 自定义模板

```dart
// 创建自定义模板
final customTemplate = EnhancedCardTemplate(
  id: 'custom_template',
  name: '自定义模板',
  category: 'custom',
  description: '我的自定义模板',
  backgroundGradient: LinearGradient(
    colors: [Colors.blue, Colors.purple],
  ),
  textColor: Colors.white,
  titleColor: Colors.white,
  accentColor: Colors.yellow,
  fontFamily: 'SourceHanSansCN',
  // ... 其他配置
);
```

### 4. 导出图片

```dart
// 使用增强渲染器导出图片
final renderer = EnhancedCardRenderer(
  content: cardContent,
  template: selectedTemplate,
  exportConfig: ExportConfig.square(), // 1:1 比例
);

// 调用导出方法
await renderer.exportCard();
```

## 🎨 模板风格

### 经典风格
- 白色背景，简约清新
- 思源黑体字体
- 红色强调色
- 圆角装饰元素

### 渐变风格
- 粉色渐变背景
- 阿里巴巴普惠体字体
- 白色文字
- 光晕装饰效果

### 阅读风格
- 米黄色护眼背景
- HarmonyOS Sans 字体
- 深灰色文字
- 简约无装饰

### 知识卡片
- 蓝紫色渐变背景
- 思源黑体字体
- 白色文字
- 灯泡装饰图标

### 语录卡片
- 深色背景
- 阿里巴巴普惠体字体
- 浅色文字
- 引号装饰元素

## 🔧 配置选项

### 字体配置
- **SourceHanSansCN**: 思源黑体，现代简洁
- **AlibabaPuHuiTi**: 阿里巴巴普惠体，温和友好
- **HarmonyOS_Sans_SC**: 华为鸿蒙字体，科技感
- **MiSans**: 小米兰亭，简约现代

### 导出配置
- **分辨率**: 1:1, 4:3, 16:9, 9:16
- **像素密度**: 1x, 2x, 3x
- **质量**: 50-100
- **水印**: 可选开启/关闭
- **时间戳**: 可选开启/关闭

## 🐛 已知问题

1. **字体文件**: 部分字体需要手动下载
2. **权限**: Android 需要存储权限
3. **性能**: 大尺寸图片渲染可能较慢

## 🔮 未来计划

- [ ] 更多模板风格
- [ ] 动画卡片支持
- [ ] 批量导出功能
- [ ] 云端模板同步
- [ ] AI 智能排版

## 📄 许可证

本项目使用的字体均为开源字体，遵循各自的开源许可证。
