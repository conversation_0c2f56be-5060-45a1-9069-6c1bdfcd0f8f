import 'package:flutter/material.dart';
import '../../config/app_theme.dart';

class FormatToolbar extends StatelessWidget {
  final VoidCallback? onBold;
  final VoidCallback? onItalic;
  final VoidCallback? onUnderline;
  final VoidCallback? onHighlight;
  final VoidCallback? onClose;

  const FormatToolbar({
    super.key,
    this.onBold,
    this.onItalic,
    this.onUnderline,
    this.onHighlight,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          const SizedBox(width: 12),
          const Icon(
            Icons.format_paint,
            size: 16,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(width: 8),
          const Text(
            '格式化',
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Row(
              children: [
                _FormatButton(
                  icon: Icons.format_bold,
                  onPressed: onBold,
                  tooltip: '加粗',
                ),
                _FormatButton(
                  icon: Icons.format_italic,
                  onPressed: onItalic,
                  tooltip: '斜体',
                ),
                _FormatButton(
                  icon: Icons.format_underlined,
                  onPressed: onUnderline,
                  tooltip: '下划线',
                ),
                _FormatButton(
                  icon: Icons.highlight,
                  onPressed: onHighlight,
                  tooltip: '高亮',
                ),
              ],
            ),
          ),
          _FormatButton(icon: Icons.close, onPressed: onClose, tooltip: '关闭'),
          const SizedBox(width: 4),
        ],
      ),
    );
  }
}

class _FormatButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String tooltip;

  const _FormatButton({
    required this.icon,
    this.onPressed,
    required this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          width: 32,
          height: 32,
          margin: const EdgeInsets.symmetric(horizontal: 2),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color:
                onPressed != null
                    ? Colors.transparent
                    : AppTheme.textLightColor.withValues(alpha: 0.1),
          ),
          child: Icon(
            icon,
            size: 16,
            color:
                onPressed != null
                    ? AppTheme.primaryColor
                    : AppTheme.textLightColor,
          ),
        ),
      ),
    );
  }
}
