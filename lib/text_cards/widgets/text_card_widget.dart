import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../models/text_card_model.dart';

class TextCardWidget extends StatelessWidget {
  final TextCardModel card;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;
  final bool isCompact;

  const TextCardWidget({
    super.key,
    required this.card,
    this.onTap,
    this.onDelete,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final template = CardTemplate.getBuiltInTemplates().firstWhere(
      (t) => t.id == card.templateId,
      orElse: () => CardTemplate.getBuiltInTemplates().first,
    );

    return GestureDetector(
      onTap: onTap,
      onLongPress: onDelete != null ? () => _showDeleteDialog(context) : null,
      child: Container(
        height: isCompact ? 120 : null,
        decoration: _buildDecoration(template),
        child: Padding(
          padding: EdgeInsets.all(
            template.styles['padding']?.toDouble() ?? 20.0,
          ),
          child:
              isCompact
                  ? _buildCompactContent(template)
                  : _buildFullContent(template),
        ),
      ),
    );
  }

  BoxDecoration _buildDecoration(CardTemplate template) {
    final styles = template.styles;
    final borderRadius = styles['borderRadius']?.toDouble() ?? 12.0;

    BoxDecoration decoration = BoxDecoration(
      borderRadius: BorderRadius.circular(borderRadius),
    );

    // 处理背景
    final background = styles['background'];
    if (background is String) {
      if (background.startsWith('linear-gradient')) {
        // 解析渐变背景
        decoration = decoration.copyWith(gradient: _parseGradient(background));
      } else if (background.startsWith('#')) {
        // 纯色背景
        decoration = decoration.copyWith(color: _parseColor(background));
      }
    }

    // 添加阴影
    if (styles['shadow'] == true) {
      decoration = decoration.copyWith(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, 4),
            blurRadius: 20,
            spreadRadius: 0,
          ),
        ],
      );
    }

    return decoration;
  }

  Gradient _parseGradient(String gradientString) {
    // 简化的渐变解析器
    if (gradientString.contains('#667eea') &&
        gradientString.contains('#764ba2')) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF667eea), Color(0xFF764ba2)],
      );
    } else if (gradientString.contains('#a8edea') &&
        gradientString.contains('#fed6e3')) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFa8edea), Color(0xFFfed6e3)],
      );
    } else if (gradientString.contains('#d299c2') &&
        gradientString.contains('#fef9d7')) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFd299c2), Color(0xFFfef9d7)],
      );
    } else if (gradientString.contains('#2d3748') &&
        gradientString.contains('#4a5568')) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF2d3748), Color(0xFF4a5568)],
      );
    } else if (gradientString.contains('#fed7aa') &&
        gradientString.contains('#f97316')) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFfed7aa), Color(0xFFf97316)],
      );
    }

    // 默认渐变
    return AppTheme.primaryGradient;
  }

  Color _parseColor(String colorString) {
    final hex = colorString.replaceAll('#', '');
    return Color(int.parse('FF$hex', radix: 16));
  }

  Widget _buildCompactContent(CardTemplate template) {
    final titleColor = _parseColor(template.styles['titleColor'] ?? '#000000');
    final textColor = _parseColor(template.styles['textColor'] ?? '#666666');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题 - 如果不为空才显示
        if (card.title.isNotEmpty) ...[
          MarkdownText(
            text: card.title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: titleColor,
            ),
            maxLines: 1,
          ),
          const SizedBox(height: 8),
        ],
        // 内容
        Expanded(
          child: MarkdownText(
            text: card.content,
            style: TextStyle(fontSize: 14, color: textColor, height: 1.4),
            maxLines: 3,
          ),
        ),
      ],
    );
  }

  Widget _buildFullContent(CardTemplate template) {
    final titleColor = _parseColor(template.styles['titleColor'] ?? '#000000');
    final textColor = _parseColor(template.styles['textColor'] ?? '#666666');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        if (card.title.isNotEmpty) ...[
          MarkdownText(
            text: card.title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: titleColor,
            ),
          ),
          const SizedBox(height: 16),
        ],

        // 内容区域
        Expanded(
          child: SingleChildScrollView(
            child: MarkdownText(
              text: card.content,
              style: TextStyle(fontSize: 14, color: textColor, height: 1.5),
            ),
          ),
        ),
      ],
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('删除卡片'),
            content: const Text('确定要删除这个卡片吗？此操作无法撤销。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  onDelete?.call();
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('删除'),
              ),
            ],
          ),
    );
  }
}

/// 自定义Markdown文本渲染组件
class MarkdownText extends StatelessWidget {
  final String text;
  final TextStyle style;
  final int? maxLines;

  const MarkdownText({
    super.key,
    required this.text,
    required this.style,
    this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    return RichText(
      maxLines: maxLines,
      overflow: maxLines != null ? TextOverflow.ellipsis : TextOverflow.visible,
      text: _buildTextSpan(text, style),
    );
  }

  TextSpan _buildTextSpan(String text, TextStyle baseStyle) {
    final spans = <TextSpan>[];
    final lines = text.split('\n');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];

      if (line.trim().isEmpty) {
        if (i < lines.length - 1) {
          spans.add(const TextSpan(text: '\n'));
        }
        continue;
      }

      // 处理列表项
      if (line.trim().startsWith('• ')) {
        _processListItem(line, baseStyle, spans);
      } else if (RegExp(r'^\d+\.\s').hasMatch(line.trim())) {
        _processNumberedListItem(line, baseStyle, spans);
      } else {
        // 处理普通文本行
        spans.addAll(_parseInlineMarkdown(line, baseStyle));
      }

      // 添加换行符（除了最后一行）
      if (i < lines.length - 1) {
        spans.add(const TextSpan(text: '\n'));
      }
    }

    return TextSpan(children: spans);
  }

  void _processListItem(
    String line,
    TextStyle baseStyle,
    List<TextSpan> spans,
  ) {
    final content = line.trim().substring(2); // 移除 "• "

    spans.add(
      TextSpan(
        text: '• ',
        style: baseStyle.copyWith(
          fontWeight: FontWeight.bold,
          color: baseStyle.color,
        ),
      ),
    );

    spans.addAll(_parseInlineMarkdown(content, baseStyle));
  }

  void _processNumberedListItem(
    String line,
    TextStyle baseStyle,
    List<TextSpan> spans,
  ) {
    final match = RegExp(r'^(\d+\.\s)(.*)').firstMatch(line.trim());
    if (match != null) {
      final number = match.group(1)!;
      final content = match.group(2)!;

      spans.add(
        TextSpan(
          text: number,
          style: baseStyle.copyWith(
            fontWeight: FontWeight.bold,
            color: baseStyle.color,
          ),
        ),
      );

      spans.addAll(_parseInlineMarkdown(content, baseStyle));
    }
  }

  List<TextSpan> _parseInlineMarkdown(String text, TextStyle baseStyle) {
    final spans = <TextSpan>[];
    // 正则表达式匹配各种格式
    final regex = RegExp(
      r'(\*\*[^*]+\*\*|\*[^*]+\*|<u>[^<]+</u>|<mark>[^<]+</mark>|`[^`]+`)',
    );
    final matches = regex.allMatches(text);

    int lastEnd = 0;

    for (final match in matches) {
      // 添加匹配前的普通文本
      if (match.start > lastEnd) {
        final plainText = text.substring(lastEnd, match.start);
        if (plainText.isNotEmpty) {
          spans.add(TextSpan(text: plainText, style: baseStyle));
        }
      }

      final matchedText = match.group(0)!;
      spans.add(_createStyledSpan(matchedText, baseStyle));

      lastEnd = match.end;
    }

    // 添加剩余的普通文本
    if (lastEnd < text.length) {
      final remainingText = text.substring(lastEnd);
      if (remainingText.isNotEmpty) {
        spans.add(TextSpan(text: remainingText, style: baseStyle));
      }
    }

    // 如果没有任何格式化文本，返回原始文本
    if (spans.isEmpty) {
      spans.add(TextSpan(text: text, style: baseStyle));
    }

    return spans;
  }

  TextSpan _createStyledSpan(String text, TextStyle baseStyle) {
    if (text.startsWith('**') && text.endsWith('**')) {
      // 粗体
      return TextSpan(
        text: text.substring(2, text.length - 2),
        style: baseStyle.copyWith(fontWeight: FontWeight.bold),
      );
    } else if (text.startsWith('*') && text.endsWith('*')) {
      // 斜体
      return TextSpan(
        text: text.substring(1, text.length - 1),
        style: baseStyle.copyWith(fontStyle: FontStyle.italic),
      );
    } else if (text.startsWith('<u>') && text.endsWith('</u>')) {
      // 下划线
      return TextSpan(
        text: text.substring(3, text.length - 4),
        style: baseStyle.copyWith(decoration: TextDecoration.underline),
      );
    } else if (text.startsWith('<mark>') && text.endsWith('</mark>')) {
      // 高亮
      return TextSpan(
        text: text.substring(6, text.length - 7),
        style: baseStyle.copyWith(
          backgroundColor: Colors.yellow.withValues(alpha: 0.3),
        ),
      );
    } else if (text.startsWith('`') && text.endsWith('`')) {
      // 代码
      return TextSpan(
        text: text.substring(1, text.length - 1),
        style: baseStyle.copyWith(
          fontFamily: 'Courier',
          backgroundColor: Colors.grey.withValues(alpha: 0.1),
        ),
      );
    }

    return TextSpan(text: text, style: baseStyle);
  }
}
