import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../models/enhanced_card_template.dart';

/// 模板画廊
/// 展示所有可用的卡片模板，支持分类浏览和预览
class TemplateGallery extends StatefulWidget {
  final Function(EnhancedCardTemplate)? onTemplateSelected;

  const TemplateGallery({
    super.key,
    this.onTemplateSelected,
  });

  @override
  State<TemplateGallery> createState() => _TemplateGalleryState();
}

class _TemplateGalleryState extends State<TemplateGallery>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  String _selectedCategory = 'all';
  List<EnhancedCardTemplate> _filteredTemplates = [];

  @override
  void initState() {
    super.initState();
    
    final categories = ['all', ...EnhancedCardTemplate.getAllCategories()];
    _tabController = TabController(length: categories.length, vsync: this);
    _tabController.addListener(_onTabChanged);
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _slideController.forward();
    _filterTemplates();
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    final categories = ['all', ...EnhancedCardTemplate.getAllCategories()];
    _selectedCategory = categories[_tabController.index];
    _filterTemplates();
  }

  void _filterTemplates() {
    setState(() {
      if (_selectedCategory == 'all') {
        _filteredTemplates = EnhancedCardTemplate.getModernTemplates();
      } else {
        _filteredTemplates = EnhancedCardTemplate.getTemplatesByCategory(_selectedCategory);
      }
    });
  }

  void _filterByCategory(String category) {
    setState(() {
      _selectedCategory = category;
    });
    _filterTemplates();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          color: AppTheme.bgWhiteColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildCategoryTabs(),
            Expanded(child: _buildTemplateGrid()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.textLightColor.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          
          // 标题和关闭按钮
          Row(
            children: [
              const Expanded(
                child: Text(
                  '模板画廊',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textDarkColor,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close, color: AppTheme.textLightColor),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          Text(
            '浏览 ${_filteredTemplates.length} 个精美模板，找到最适合的设计',
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textLightColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryTabs() {
    final categories = ['all', ...EnhancedCardTemplate.getAllCategories()];
    final categoryNames = {
      'all': '全部',
      'business': '商务',
      'academic': '学术',
      'creative': '创意',
      'minimal': '简约',
      'modern': '现代',
      'dark': '深色',
      'nature': '自然',
      'warm': '温暖',
      'tech': '科技',
      'elegant': '优雅',
      'vintage': '复古',
    };

    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = category == _selectedCategory;

          return GestureDetector(
            onTap: () => _filterByCategory(category),
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.primaryColor
                    : AppTheme.bgLightColor,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: isSelected
                      ? AppTheme.primaryColor
                      : AppTheme.textLightColor.withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: isSelected ? [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.3),
                    offset: const Offset(0, 2),
                    blurRadius: 8,
                  ),
                ] : null,
              ),
              child: Center(
                child: Text(
                  categoryNames[category] ?? category,
                  style: TextStyle(
                    color: isSelected
                        ? Colors.white
                        : AppTheme.textDarkColor,
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTemplateGrid() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: _filteredTemplates.length,
        itemBuilder: (context, index) {
          final template = _filteredTemplates[index];
          return _buildTemplateCard(template);
        },
      ),
    );
  }

  Widget _buildTemplateCard(EnhancedCardTemplate template) {
    debugPrint('构建模板卡片: ${template.name}');
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          debugPrint('InkWell onTap 被触发: ${template.name}');
          _selectTemplate(template);
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: template.backgroundGradient.colors.first.withValues(alpha: 0.2),
                offset: const Offset(0, 8),
                blurRadius: 20,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // 模板预览
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: template.backgroundGradient,
                    ),
                    padding: EdgeInsets.all(template.padding * 0.8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 示例标题
                        Text(
                          template.name,
                          style: TextStyle(
                            color: template.titleColor,
                            fontSize: template.titleFontSize * 0.8,
                            fontWeight: template.titleFontWeight,
                          ),
                        ),
                        SizedBox(height: template.padding * 0.3),

                        // 示例内容
                        Expanded(
                          child: Text(
                            template.description,
                            style: TextStyle(
                              color: template.textColor,
                              fontSize: template.contentFontSize * 0.7,
                              fontWeight: template.contentFontWeight,
                              height: template.lineHeight,
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        // 底部装饰
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: template.accentColor.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                template.category,
                                style: TextStyle(
                                  color: template.accentColor,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const Spacer(),
                            if (template.hasDecorationElements)
                              Icon(
                                Icons.auto_awesome,
                                size: 16,
                                color: template.accentColor,
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // 悬浮操作按钮
                Positioned(
                  top: 12,
                  right: 12,
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        debugPrint('加号按钮被点击: ${template.name}');
                        _selectTemplate(template);
                      },
                      borderRadius: BorderRadius.circular(18),
                      child: Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.9),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              offset: const Offset(0, 2),
                              blurRadius: 8,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.add,
                          size: 20,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _selectTemplate(EnhancedCardTemplate template) {
    debugPrint('模板被选中: ${template.name}');
    debugPrint('onTemplateSelected 是否为空: ${widget.onTemplateSelected == null}');

    if (widget.onTemplateSelected != null) {
      debugPrint('调用 onTemplateSelected 回调');
      widget.onTemplateSelected!(template);
      Navigator.pop(context);
    } else {
      debugPrint('显示模板详情对话框');
      // 显示模板详情或其他操作
      _showTemplateDetails(template);
    }
  }

  void _showTemplateDetails(EnhancedCardTemplate template) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(template.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('分类：${template.category}'),
            const SizedBox(height: 8),
            Text('描述：${template.description}'),
            const SizedBox(height: 16),
            Container(
              height: 100,
              decoration: BoxDecoration(
                gradient: template.backgroundGradient,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  '预览效果',
                  style: TextStyle(
                    color: template.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _selectTemplate(template);
            },
            child: const Text('使用模板'),
          ),
        ],
      ),
    );
  }
}
