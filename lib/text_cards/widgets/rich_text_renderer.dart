import 'package:flutter/material.dart';

class RichTextRenderer extends StatelessWidget {
  final String content;
  final TextStyle baseStyle;
  final TextStyle boldStyle;
  final TextStyle italicStyle;
  final TextStyle underlineStyle;
  final TextStyle highlightStyle;

  const RichTextRenderer({
    super.key,
    required this.content,
    required this.baseStyle,
    required this.boldStyle,
    required this.italicStyle,
    required this.underlineStyle,
    required this.highlightStyle,
  });

  @override
  Widget build(BuildContext context) {
    return _buildRichText();
  }

  Widget _buildRichText() {
    final spans = <TextSpan>[];
    final lines = content.split('\n');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();

      if (line.isEmpty) {
        spans.add(const TextSpan(text: '\n'));
        continue;
      }

      // 处理列表
      if (line.startsWith('• ') || line.startsWith('- ')) {
        spans.add(TextSpan(text: '• ${line.substring(2)}\n', style: baseStyle));
        continue;
      }

      // 处理有序列表
      final orderedListMatch = RegExp(r'^\d+\.\s').firstMatch(line);
      if (orderedListMatch != null) {
        spans.add(TextSpan(text: '$line\n', style: baseStyle));
        continue;
      }

      // 处理普通文本行
      spans.addAll(_parseLineSpans(line));

      if (i < lines.length - 1) {
        spans.add(const TextSpan(text: '\n'));
      }
    }

    return RichText(text: TextSpan(children: spans));
  }

  List<TextSpan> _parseLineSpans(String line) {
    final spans = <TextSpan>[];
    final buffer = StringBuffer();
    var currentStyle = baseStyle;

    var isBold = false;
    var isItalic = false;
    var isUnderline = false;
    var isHighlight = false;

    for (int i = 0; i < line.length; i++) {
      final char = line[i];

      // 处理粗体 **text**
      if (char == '*' && i + 1 < line.length && line[i + 1] == '*') {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: currentStyle));
          buffer.clear();
        }
        isBold = !isBold;
        currentStyle = _updateStyle(isBold, isItalic, isUnderline, isHighlight);
        i++; // Skip the next '*'
        continue;
      }

      // 处理斜体 *text*
      if (char == '*' &&
          (i == 0 || line[i - 1] != '*') &&
          (i + 1 >= line.length || line[i + 1] != '*')) {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: currentStyle));
          buffer.clear();
        }
        isItalic = !isItalic;
        currentStyle = _updateStyle(isBold, isItalic, isUnderline, isHighlight);
        continue;
      }

      // 处理下划线 <u>text</u>
      if (char == '<' &&
          i + 2 < line.length &&
          line.substring(i, i + 3) == '<u>') {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: currentStyle));
          buffer.clear();
        }
        isUnderline = true;
        currentStyle = _updateStyle(isBold, isItalic, isUnderline, isHighlight);
        i += 2; // Skip 'u>'
        continue;
      }

      if (char == '<' &&
          i + 3 < line.length &&
          line.substring(i, i + 4) == '</u>') {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: currentStyle));
          buffer.clear();
        }
        isUnderline = false;
        currentStyle = _updateStyle(isBold, isItalic, isUnderline, isHighlight);
        i += 3; // Skip '/u>'
        continue;
      }

      // 处理高亮 <mark>text</mark>
      if (char == '<' &&
          i + 5 < line.length &&
          line.substring(i, i + 6) == '<mark>') {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: currentStyle));
          buffer.clear();
        }
        isHighlight = true;
        currentStyle = _updateStyle(isBold, isItalic, isUnderline, isHighlight);
        i += 5; // Skip 'mark>'
        continue;
      }

      if (char == '<' &&
          i + 6 < line.length &&
          line.substring(i, i + 7) == '</mark>') {
        if (buffer.isNotEmpty) {
          spans.add(TextSpan(text: buffer.toString(), style: currentStyle));
          buffer.clear();
        }
        isHighlight = false;
        currentStyle = _updateStyle(isBold, isItalic, isUnderline, isHighlight);
        i += 6; // Skip '/mark>'
        continue;
      }

      buffer.write(char);
    }

    if (buffer.isNotEmpty) {
      spans.add(TextSpan(text: buffer.toString(), style: currentStyle));
    }

    return spans;
  }

  TextStyle _updateStyle(
    bool isBold,
    bool isItalic,
    bool isUnderline,
    bool isHighlight,
  ) {
    var style = baseStyle;

    if (isBold) {
      style = style.merge(boldStyle);
    }

    if (isItalic) {
      style = style.merge(italicStyle);
    }

    if (isUnderline) {
      style = style.merge(underlineStyle);
    }

    if (isHighlight) {
      style = style.merge(highlightStyle);
    }

    return style;
  }
}
