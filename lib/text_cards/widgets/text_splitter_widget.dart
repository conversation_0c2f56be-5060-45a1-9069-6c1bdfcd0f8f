import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../models/text_card_model.dart';

class TextSplitterWidget extends StatefulWidget {
  final List<TextCardModel> cards;
  final Function(List<TextCardModel>) onCardsChanged;
  final CardTemplate globalTemplate;

  const TextSplitterWidget({
    super.key,
    required this.cards,
    required this.onCardsChanged,
    required this.globalTemplate,
  });

  @override
  State<TextSplitterWidget> createState() => _TextSplitterWidgetState();
}

class _TextSplitterWidgetState extends State<TextSplitterWidget> {
  late List<TextCardModel> _cards;

  @override
  void initState() {
    super.initState();
    _cards = List.from(widget.cards);
  }

  @override
  void didUpdateWidget(TextSplitterWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.cards != oldWidget.cards) {
      _cards = List.from(widget.cards);
    }
  }

  void _editCard(int index) {
    final card = _cards[index];
    showDialog(
      context: context,
      builder:
          (context) => _CardEditDialog(
            card: card,
            template: widget.globalTemplate,
            onSave: (updatedCard) {
              setState(() {
                _cards[index] = updatedCard;
              });
              widget.onCardsChanged(_cards);
            },
          ),
    );
  }

  void _deleteCard(int index) {
    setState(() {
      _cards.removeAt(index);
    });
    widget.onCardsChanged(_cards);
  }

  void _addCard() {
    final newCard = TextCardModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: '新卡片',
      content: '点击编辑内容...',
      templateId: widget.globalTemplate.id,
      createdAt: DateTime.now(),
    );

    setState(() {
      _cards.add(newCard);
    });
    widget.onCardsChanged(_cards);
  }

  void _reorderCards(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final card = _cards.removeAt(oldIndex);
      _cards.insert(newIndex, card);
    });
    widget.onCardsChanged(_cards);
  }

  @override
  Widget build(BuildContext context) {
    if (_cards.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.text_snippet_outlined,
              size: 48,
              color: AppTheme.textLightColor,
            ),
            const SizedBox(height: 16),
            const Text(
              '还没有卡片',
              style: TextStyle(fontSize: 16, color: AppTheme.textLightColor),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _addCard,
              icon: const Icon(Icons.add),
              label: const Text('添加卡片'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Expanded(
          child: ReorderableListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _cards.length,
            onReorder: _reorderCards,
            itemBuilder: (context, index) {
              final card = _cards[index];
              return _CardPreviewItem(
                key: ValueKey(card.id),
                card: card,
                template: widget.globalTemplate,
                index: index + 1,
                onEdit: () => _editCard(index),
                onDelete: () => _deleteCard(index),
              );
            },
          ),
        ),

        // 添加卡片按钮
        Container(
          color: AppTheme.bgWhiteColor,
          padding: const EdgeInsets.all(16),
          child: SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _addCard,
              icon: const Icon(Icons.add),
              label: const Text('添加新卡片'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
                side: const BorderSide(color: AppTheme.primaryColor),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _CardPreviewItem extends StatelessWidget {
  final TextCardModel card;
  final CardTemplate template;
  final int index;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const _CardPreviewItem({
    super.key,
    required this.card,
    required this.template,
    required this.index,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 序号和拖拽手柄
          Container(
            width: 40,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Column(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      index.toString(),
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Icon(
                  Icons.drag_handle,
                  size: 16,
                  color: AppTheme.textLightColor.withValues(alpha: 0.6),
                ),
              ],
            ),
          ),

          const SizedBox(width: 12),

          // 卡片预览
          Expanded(
            child: GestureDetector(
              onTap: onEdit,
              child: Container(
                constraints: const BoxConstraints(minHeight: 80),
                decoration: _buildCardDecoration(),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              card.title,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: _getTextColor(
                                  template.styles['titleColor'],
                                ),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          PopupMenuButton<String>(
                            icon: Icon(
                              Icons.more_vert,
                              size: 16,
                              color: _getTextColor(
                                template.styles['titleColor'],
                              ).withValues(alpha: 0.6),
                            ),
                            onSelected: (value) {
                              if (value == 'edit') {
                                onEdit();
                              } else if (value == 'delete') {
                                onDelete();
                              }
                            },
                            itemBuilder:
                                (context) => [
                                  const PopupMenuItem(
                                    value: 'edit',
                                    child: Row(
                                      children: [
                                        Icon(Icons.edit, size: 16),
                                        SizedBox(width: 8),
                                        Text('编辑'),
                                      ],
                                    ),
                                  ),
                                  const PopupMenuItem(
                                    value: 'delete',
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.delete,
                                          size: 16,
                                          color: Colors.red,
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          '删除',
                                          style: TextStyle(color: Colors.red),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        card.content,
                        style: TextStyle(
                          fontSize: 12,
                          color: _getTextColor(template.styles['textColor']),
                          height: 1.4,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  BoxDecoration _buildCardDecoration() {
    final styles = template.styles;
    final background = styles['background'];

    BoxDecoration decoration = BoxDecoration(
      borderRadius: BorderRadius.circular(8),
      border: Border.all(
        color: AppTheme.textLightColor.withValues(alpha: 0.2),
        width: 1,
      ),
    );

    if (background is String) {
      if (background.startsWith('linear-gradient')) {
        decoration = decoration.copyWith(gradient: _parseGradient(background));
      } else if (background.startsWith('#')) {
        decoration = decoration.copyWith(color: _parseColor(background));
      }
    }

    if (styles['shadow'] == true) {
      decoration = decoration.copyWith(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      );
    }

    return decoration;
  }

  Gradient _parseGradient(String gradientString) {
    if (gradientString.contains('#667eea') &&
        gradientString.contains('#764ba2')) {
      return const LinearGradient(
        colors: [Color(0xFF667eea), Color(0xFF764ba2)],
      );
    } else if (gradientString.contains('#a8edea') &&
        gradientString.contains('#fed6e3')) {
      return const LinearGradient(
        colors: [Color(0xFFa8edea), Color(0xFFfed6e3)],
      );
    } else if (gradientString.contains('#d299c2') &&
        gradientString.contains('#fef9d7')) {
      return const LinearGradient(
        colors: [Color(0xFFd299c2), Color(0xFFfef9d7)],
      );
    } else if (gradientString.contains('#2d3748') &&
        gradientString.contains('#4a5568')) {
      return const LinearGradient(
        colors: [Color(0xFF2d3748), Color(0xFF4a5568)],
      );
    } else if (gradientString.contains('#fed7aa') &&
        gradientString.contains('#f97316')) {
      return const LinearGradient(
        colors: [Color(0xFFfed7aa), Color(0xFFf97316)],
      );
    }

    return AppTheme.primaryGradient;
  }

  Color _parseColor(String colorString) {
    final hex = colorString.replaceAll('#', '');
    return Color(int.parse('FF$hex', radix: 16));
  }

  Color _getTextColor(dynamic colorString) {
    if (colorString is String) {
      return _parseColor(colorString);
    }
    return AppTheme.textDarkColor;
  }
}

class _CardEditDialog extends StatefulWidget {
  final TextCardModel card;
  final CardTemplate template;
  final Function(TextCardModel) onSave;

  const _CardEditDialog({
    required this.card,
    required this.template,
    required this.onSave,
  });

  @override
  State<_CardEditDialog> createState() => _CardEditDialogState();
}

class _CardEditDialogState extends State<_CardEditDialog> {
  late TextEditingController _titleController;
  late TextEditingController _contentController;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.card.title);
    _contentController = TextEditingController(text: widget.card.content);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _save() {
    final updatedCard = widget.card.copyWith(
      title: _titleController.text.trim(),
      content: _contentController.text.trim(),
      updatedAt: DateTime.now(),
    );
    widget.onSave(updatedCard);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                const Expanded(
                  child: Text(
                    '编辑卡片',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textDarkColor,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                  color: AppTheme.textLightColor,
                ),
              ],
            ),

            const SizedBox(height: 16),

            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: '标题',
                border: OutlineInputBorder(),
              ),
            ),

            const SizedBox(height: 16),

            Expanded(
              child: TextField(
                controller: _contentController,
                decoration: const InputDecoration(
                  labelText: '内容',
                  border: OutlineInputBorder(),
                  alignLabelWithHint: true,
                ),
                maxLines: null,
                expands: true,
                textAlignVertical: TextAlignVertical.top,
              ),
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _save,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('保存'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
