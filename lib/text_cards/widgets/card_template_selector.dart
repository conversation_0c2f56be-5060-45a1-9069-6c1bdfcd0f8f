import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../models/text_card_model.dart';

class CardTemplateSelector extends StatelessWidget {
  final Function(String) onTemplateSelected;

  const CardTemplateSelector({super.key, required this.onTemplateSelected});

  @override
  Widget build(BuildContext context) {
    final templates = CardTemplate.getBuiltInTemplates();

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 顶部指示器
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 16),
            decoration: BoxDecoration(
              color: AppTheme.textLightColor.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // 标题
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                const Expanded(
                  child: Text(
                    '选择卡片模板',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textDarkColor,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                  color: AppTheme.textLightColor,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 模板网格
          Expanded(
            child: GridView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.8,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: templates.length,
              itemBuilder: (context, index) {
                final template = templates[index];
                return _TemplateCard(
                  template: template,
                  onTap: () => onTemplateSelected(template.id),
                );
              },
            ),
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

class _TemplateCard extends StatelessWidget {
  final CardTemplate template;
  final VoidCallback onTap;

  const _TemplateCard({required this.template, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.textLightColor.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            // 模板预览
            Expanded(
              flex: 3,
              child: Container(
                margin: const EdgeInsets.all(8),
                decoration: _buildTemplateDecoration(),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '示例标题',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: _parseColor(
                            template.styles['titleColor'] ?? '#000000',
                          ),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6),
                      Expanded(
                        child: Text(
                          '这是一个示例内容，展示了模板的样式效果。',
                          style: TextStyle(
                            fontSize: 10,
                            color: _parseColor(
                              template.styles['textColor'] ?? '#666666',
                            ),
                            height: 1.3,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        width: 30,
                        height: 8,
                        decoration: BoxDecoration(
                          color: _parseColor(
                            template.styles['textColor'] ?? '#666666',
                          ).withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // 模板信息
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                child: Column(
                  children: [
                    Text(
                      template.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textDarkColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      template.preview,
                      style: TextStyle(
                        fontSize: 11,
                        color: AppTheme.textLightColor,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  BoxDecoration _buildTemplateDecoration() {
    final styles = template.styles;
    final borderRadius = (styles['borderRadius']?.toDouble() ?? 12.0) * 0.8;

    BoxDecoration decoration = BoxDecoration(
      borderRadius: BorderRadius.circular(borderRadius),
    );

    // 处理背景
    final background = styles['background'];
    if (background is String) {
      if (background.startsWith('linear-gradient')) {
        decoration = decoration.copyWith(gradient: _parseGradient(background));
      } else if (background.startsWith('#')) {
        decoration = decoration.copyWith(color: _parseColor(background));
      }
    }

    // 添加阴影
    if (styles['shadow'] == true) {
      decoration = decoration.copyWith(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      );
    }

    return decoration;
  }

  Gradient _parseGradient(String gradientString) {
    if (gradientString.contains('#667eea') &&
        gradientString.contains('#764ba2')) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF667eea), Color(0xFF764ba2)],
      );
    } else if (gradientString.contains('#a8edea') &&
        gradientString.contains('#fed6e3')) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFa8edea), Color(0xFFfed6e3)],
      );
    } else if (gradientString.contains('#d299c2') &&
        gradientString.contains('#fef9d7')) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFd299c2), Color(0xFFfef9d7)],
      );
    } else if (gradientString.contains('#2d3748') &&
        gradientString.contains('#4a5568')) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF2d3748), Color(0xFF4a5568)],
      );
    } else if (gradientString.contains('#fed7aa') &&
        gradientString.contains('#f97316')) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFfed7aa), Color(0xFFf97316)],
      );
    }

    // 默认渐变
    return AppTheme.primaryGradient;
  }

  Color _parseColor(String colorString) {
    final hex = colorString.replaceAll('#', '');
    return Color(int.parse('FF$hex', radix: 16));
  }
}
