import 'package:flutter/material.dart';
import '../models/enhanced_card_template.dart';
import '../models/font_manager.dart';

/// 卡片预览组件
/// 用于在创建器中显示卡片预览，不包含导出功能
class CardPreviewWidget extends StatelessWidget {
  final String content;
  final EnhancedCardTemplate template;
  final Map<String, dynamic>? customStyles;

  const CardPreviewWidget({
    super.key,
    required this.content,
    required this.template,
    this.customStyles,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: template.backgroundGradient,
        borderRadius: BorderRadius.circular(template.borderRadius),
        boxShadow: template.hasShadow
            ? [
                BoxShadow(
                  color: template.backgroundGradient.colors.first
                      .withValues(alpha: 0.3),
                  offset: const Offset(0, 8),
                  blurRadius: 24,
                ),
              ]
            : null,
      ),
      child: Stack(
        children: [
          // 背景装饰元素
          if (template.hasDecorationElements) _buildDecorationElements(),

          // 主要内容
          Positioned.fill(
            child: Padding(
              padding: EdgeInsets.all(template.padding),
              child: _buildContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDecorationElements() {
    return Stack(
      children: [
        // 根据模板风格添加不同的装饰元素
        if (template.layoutStyle == 'classic_style') ...[
          _buildClassicDecorations(),
        ] else if (template.layoutStyle == 'gradient_style') ...[
          _buildGradientDecorations(),
        ] else if (template.layoutStyle == 'knowledge') ...[
          _buildKnowledgeDecorations(),
        ] else if (template.layoutStyle == 'quote') ...[
          _buildQuoteDecorations(),
        ],
      ],
    );
  }

  Widget _buildClassicDecorations() {
    return Stack(
      children: [
        // 右上角现代风格装饰
        Positioned(
          top: 20,
          right: 20,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: template.accentColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
          ),
        ),
        // 左下角装饰线条
        Positioned(
          bottom: 30,
          left: 20,
          child: Container(
            width: 40,
            height: 3,
            decoration: BoxDecoration(
              color: template.accentColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGradientDecorations() {
    return Stack(
      children: [
        // 渐变光晕效果
        Positioned(
          top: -30,
          right: -30,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  Colors.white.withValues(alpha: 0.2),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildKnowledgeDecorations() {
    return Stack(
      children: [
        // 知识卡片装饰
        Positioned(
          top: 20,
          left: 20,
          child: Icon(
            Icons.lightbulb_outline,
            size: 24,
            color: template.accentColor.withValues(alpha: 0.3),
          ),
        ),
      ],
    );
  }

  Widget _buildQuoteDecorations() {
    return Stack(
      children: [
        // 引号装饰
        Positioned(
          top: 20,
          left: 20,
          child: Text(
            '"',
            style: TextStyle(
              fontSize: 48,
              color: template.accentColor.withValues(alpha: 0.3),
              fontWeight: FontWeight.w300,
            ),
          ),
        ),
        Positioned(
          bottom: 20,
          right: 20,
          child: Text(
            '"',
            style: TextStyle(
              fontSize: 48,
              color: template.accentColor.withValues(alpha: 0.3),
              fontWeight: FontWeight.w300,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    final lines = content.split('\n');
    final title = lines.isNotEmpty ? lines.first : '';
    final body = lines.length > 1 ? lines.skip(1).join('\n').trim() : '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        if (title.isNotEmpty) ...[
          Text(
            title,
            style: FontManager.createTextStyle(
              fontFamily: FontManager.getFontByName(template.fontFamily) ??
                  FontManager.getDefaultFont(),
              fontSize: template.titleFontSize,
              fontWeight: template.titleFontWeight,
              color: template.titleColor,
              height: 1.2,
            ),
          ),
          SizedBox(height: template.padding * 0.3),
        ],

        // 内容
        if (body.isNotEmpty) ...[
          Expanded(
            child: Text(
              body,
              style: FontManager.createTextStyle(
                fontFamily: FontManager.getFontByName(template.fontFamily) ??
                    FontManager.getDefaultFont(),
                fontSize: template.contentFontSize,
                fontWeight: template.contentFontWeight,
                color: template.textColor,
                height: template.lineHeight,
              ),
              textAlign: template.textAlign,
            ),
          ),
        ],
      ],
    );
  }
}
