import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:screenshot/screenshot.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/enhanced_card_template.dart';
import '../models/font_manager.dart';

/// 增强的卡片渲染器
/// 支持高质量图片导出、多种分辨率、装饰元素等
class EnhancedCardRenderer extends StatefulWidget {
  final String content;
  final EnhancedCardTemplate template;
  final Map<String, dynamic>? customStyles;
  final ExportConfig? exportConfig;

  const EnhancedCardRenderer({
    super.key,
    required this.content,
    required this.template,
    this.customStyles,
    this.exportConfig,
  });

  @override
  State<EnhancedCardRenderer> createState() => _EnhancedCardRendererState();
}

class _EnhancedCardRendererState extends State<EnhancedCardRenderer> {
  final ScreenshotController _screenshotController = ScreenshotController();

  /// 导出卡片为图片
  Future<void> exportCard() async {
    try {
      // 请求存储权限（iOS和Android不同的权限处理）
      bool hasPermission = false;

      if (Theme.of(context).platform == TargetPlatform.iOS) {
        // iOS 使用 photos 权限
        final permission = await Permission.photos.request();
        hasPermission = permission.isGranted;
      } else {
        // Android 使用 storage 权限
        final permission = await Permission.storage.request();
        hasPermission = permission.isGranted;
      }

      if (!hasPermission) {
        throw Exception('需要相册权限才能保存图片，请在设置中开启权限');
      }

      // 显示导出状态（但不在截图区域内）
      _showExportingMessage();

      // 等待一帧确保UI更新完成，但不显示loading在卡片上
      await Future.delayed(const Duration(milliseconds: 100));

      // 截取卡片（此时不显示loading指示器）
      final Uint8List? imageBytes = await _screenshotController.capture(
        pixelRatio: widget.exportConfig?.pixelRatio ?? 3.0,
      );

      if (imageBytes == null) {
        throw Exception('截图失败，请重试');
      }

      // 保存到相册
      final result = await ImageGallerySaver.saveImage(
        imageBytes,
        name: 'ContentPal_TextCard_${DateTime.now().millisecondsSinceEpoch}',
        quality: widget.exportConfig?.quality ?? 100,
      );

      // 检查保存结果
      if (result != null && result['isSuccess'] == true) {
        _showSuccessMessage('✅ 图片已成功保存到相册');
      } else {
        // 提供更详细的错误信息
        final errorMsg = result?['errorMessage'] ?? '未知错误';
        throw Exception('保存失败：$errorMsg');
      }
    } catch (e) {
      String errorMessage = '导出失败：';
      if (e.toString().contains('permission')) {
        errorMessage += '权限不足，请在设置中开启相册权限';
      } else if (e.toString().contains('截图失败')) {
        errorMessage += '截图失败，请重试';
      } else {
        errorMessage += e.toString().replaceAll('Exception: ', '');
      }
      _showErrorMessage(errorMessage);
    }
  }

  /// 显示导出中的消息（不在截图区域内）
  void _showExportingMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 12),
            const Text('正在导出图片...'),
          ],
        ),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Screenshot(
      controller: _screenshotController,
      child: _buildExportCard(),
    );
  }

  /// 构建用于导出的卡片（包含背景和边距）
  Widget _buildExportCard() {
    final config = widget.exportConfig ?? ExportConfig.defaultConfig();

    return Container(
      width: config.size.width.toDouble(),
      height: config.size.height.toDouble(),
      // 添加纯色背景，避免透明区域问题
      color: Colors.white,
      child: Center(
        child: Container(
          // 卡片尺寸，留出边距
          width: config.size.width.toDouble() - 80, // 左右各40px边距
          height: config.size.height.toDouble() - 120, // 上下各60px边距
          decoration: BoxDecoration(
            gradient: widget.template.backgroundGradient,
            borderRadius: BorderRadius.circular(widget.template.borderRadius),
            boxShadow:
                widget.template.hasShadow
                ? [
                      BoxShadow(
                        color: widget.template.backgroundGradient.colors.first
                            .withValues(alpha: 0.3),
                        offset: const Offset(0, 8),
                        blurRadius: 24,
                      ),
                    ]
                : null,
          ),
          child: Stack(
            children: [
              // 背景装饰元素
              if (widget.template.hasDecorationElements)
                _buildDecorationElements(),

              // 主要内容
              Positioned.fill(
                child: Padding(
                  padding: EdgeInsets.all(widget.template.padding),
                  child: _buildContent(),
                ),
              ),

              // 水印
              if (config.includeWatermark) _buildWatermark(config),

              // 时间戳
              if (config.includeTimestamp) _buildTimestamp(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDecorationElements() {
    return Stack(
      children: [
        // 根据模板风格添加不同的装饰元素
        if (widget.template.layoutStyle == 'classic_style') ...[
          _buildClassicDecorations(),
        ] else if (widget.template.layoutStyle == 'gradient_style') ...[
          _buildGradientDecorations(),
        ] else if (widget.template.layoutStyle == 'knowledge') ...[
          _buildKnowledgeDecorations(),
        ] else if (widget.template.layoutStyle == 'quote') ...[
          _buildQuoteDecorations(),
        ],
      ],
    );
  }

  Widget _buildClassicDecorations() {
    return Stack(
      children: [
        // 右上角现代风格装饰
        Positioned(
          top: 20,
          right: 20,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: widget.template.accentColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
          ),
        ),
        // 左下角装饰线条
        Positioned(
          bottom: 30,
          left: 20,
          child: Container(
            width: 40,
            height: 3,
            decoration: BoxDecoration(
              color: widget.template.accentColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGradientDecorations() {
    return Stack(
      children: [
        // 渐变光晕效果
        Positioned(
          top: -30,
          right: -30,
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  Colors.white.withValues(alpha: 0.2),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildKnowledgeDecorations() {
    return Stack(
      children: [
        // 知识卡片装饰
        Positioned(
          top: 20,
          left: 20,
          child: Icon(
            Icons.lightbulb_outline,
            size: 24,
            color: widget.template.accentColor.withValues(alpha: 0.3),
          ),
        ),
      ],
    );
  }

  Widget _buildQuoteDecorations() {
    return Stack(
      children: [
        // 引号装饰
        Positioned(
          top: 20,
          left: 20,
          child: Text(
            '"',
            style: TextStyle(
              fontSize: 48,
              color: widget.template.accentColor.withValues(alpha: 0.3),
              fontWeight: FontWeight.w300,
            ),
          ),
        ),
        Positioned(
          bottom: 20,
          right: 20,
          child: Text(
            '"',
            style: TextStyle(
              fontSize: 48,
              color: widget.template.accentColor.withValues(alpha: 0.3),
              fontWeight: FontWeight.w300,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    final lines = widget.content.split('\n');
    final title = lines.isNotEmpty ? lines.first : '';
    final body = lines.length > 1 ? lines.skip(1).join('\n').trim() : '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        if (title.isNotEmpty) ...[
          Text(
            title,
            style: FontManager.createTextStyle(
              fontFamily:
                  FontManager.getFontByName(widget.template.fontFamily) ??
                  FontManager.getDefaultFont(),
              fontSize: widget.template.titleFontSize,
              fontWeight: widget.template.titleFontWeight,
              color: widget.template.titleColor,
              height: 1.2,
            ),
          ),
          SizedBox(height: widget.template.padding * 0.3),
        ],

        // 内容
        if (body.isNotEmpty) ...[
          Expanded(
            child: Text(
              body,
              style: FontManager.createTextStyle(
                fontFamily:
                    FontManager.getFontByName(widget.template.fontFamily) ??
                    FontManager.getDefaultFont(),
                fontSize: widget.template.contentFontSize,
                fontWeight: widget.template.contentFontWeight,
                color: widget.template.textColor,
                height: widget.template.lineHeight,
              ),
              textAlign: widget.template.textAlign,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildWatermark(ExportConfig config) {
    return Positioned(
      bottom: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          config.customWatermark ?? 'ContentPal',
          style: TextStyle(
            fontSize: 10,
            color: Colors.black.withValues(alpha: 0.5),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildTimestamp() {
    return Positioned(
      bottom: 16,
      left: 16,
      child: Text(
        _formatDateTime(DateTime.now()),
        style: TextStyle(
          fontSize: 10,
          color: Colors.black.withValues(alpha: 0.4),
        ),
      ),
    );
  }



  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}.${dateTime.month.toString().padLeft(2, '0')}.${dateTime.day.toString().padLeft(2, '0')}';
  }
}

/// 导出配置
class ExportConfig {
  final Size size;
  final double pixelRatio;
  final int quality;
  final bool includeWatermark;
  final bool includeTimestamp;
  final String? customWatermark;

  const ExportConfig({
    required this.size,
    this.pixelRatio = 3.0,
    this.quality = 100,
    this.includeWatermark = true,
    this.includeTimestamp = false,
    this.customWatermark,
  });

  factory ExportConfig.defaultConfig() {
    return const ExportConfig(
      size: Size(800, 1200), // 2:3 比例
    );
  }

  factory ExportConfig.square() {
    return const ExportConfig(
      size: Size(1080, 1080), // 1:1 比例
    );
  }

  factory ExportConfig.story() {
    return const ExportConfig(
      size: Size(1080, 1920), // 9:16 比例
    );
  }

  factory ExportConfig.landscape() {
    return const ExportConfig(
      size: Size(1920, 1080), // 16:9 比例
    );
  }
}
