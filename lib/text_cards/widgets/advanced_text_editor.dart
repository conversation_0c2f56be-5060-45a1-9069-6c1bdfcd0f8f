import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/font_manager.dart';
import '../models/enhanced_card_template.dart';

/// 高级文本编辑器
/// 支持选中文本的内联编辑、实时预览、手势控制等功能
class AdvancedTextEditor extends StatefulWidget {
  final String initialText;
  final EnhancedCardTemplate template;
  final Function(String) onTextChanged;
  final Function(Map<String, dynamic>)? onStyleChanged;
  final VoidCallback? onTemplateEdit;

  const AdvancedTextEditor({
    super.key,
    required this.initialText,
    required this.template,
    required this.onTextChanged,
    this.onStyleChanged,
    this.onTemplateEdit,
  });

  @override
  State<AdvancedTextEditor> createState() => _AdvancedTextEditorState();
}

class _AdvancedTextEditorState extends State<AdvancedTextEditor>
    with TickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _toolbarAnimationController;
  late Animation<double> _toolbarAnimation;

  // 选中文本相关
  TextSelection? _currentSelection;
  OverlayEntry? _toolbarOverlay;
  bool _isToolbarVisible = false;

  // 当前样式状态
  Map<String, dynamic> _currentStyles = {};
  FontFamily _selectedFont = FontManager.getDefaultFont();
  double _fontSize = 16.0;
  FontWeight _fontWeight = FontWeight.w400;
  Color _textColor = Colors.black;
  TextAlign _textAlign = TextAlign.left;

  @override
  void initState() {
    super.initState();
    _initializeEditor();
    _initializeAnimations();
    _loadTemplateStyles();
  }

  void _initializeEditor() {
    _controller = TextEditingController(text: widget.initialText);
    _focusNode = FocusNode();

    _controller.addListener(_onTextChanged);
    _controller.addListener(_onSelectionChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  void _initializeAnimations() {
    _toolbarAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _toolbarAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _toolbarAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _loadTemplateStyles() {
    _selectedFont = FontManager.getFontByName(widget.template.fontFamily) ??
        FontManager.getDefaultFont();
    _fontSize = widget.template.contentFontSize;
    _fontWeight = widget.template.contentFontWeight;
    _textColor = widget.template.textColor;
    _textAlign = widget.template.textAlign;

    _currentStyles = {
      'fontFamily': _selectedFont.name,
      'fontSize': _fontSize,
      'fontWeight': _fontWeight,
      'color': _textColor,
      'textAlign': _textAlign,
    };
  }

  @override
  void dispose() {
    _removeToolbarOverlay();
    _toolbarAnimationController.dispose();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    widget.onTextChanged(_controller.text);
  }

  void _onSelectionChanged() {
    final selection = _controller.selection;
    if (selection != _currentSelection) {
      _currentSelection = selection;
      _handleSelectionChange();
    }
  }

  void _onFocusChanged() {
    // 如果工具栏正在显示，不要因为焦点变化而隐藏工具栏
    // 用户可能正在与工具栏交互
    if (!_focusNode.hasFocus && !_isToolbarVisible) {
      _hideToolbar();
    }
  }

  void _handleSelectionChange() {
    final selection = _controller.selection;
    // 如果工具栏正在显示，不要因为选择变化而隐藏工具栏
    // 用户可能正在调整样式，这会导致选择状态变化
    if ((!selection.isValid || selection.isCollapsed) && !_isToolbarVisible) {
      _hideToolbar();
    }
    // 工具栏现在只通过上下文菜单的"修改样式"选项显示
  }

  void _showToolbar() {
    if (_isToolbarVisible) return;

    setState(() {
      _isToolbarVisible = true;
    });

    _removeToolbarOverlay();
    _toolbarOverlay = _createToolbarOverlay();
    Overlay.of(context).insert(_toolbarOverlay!);
    _toolbarAnimationController.forward();

    // 触觉反馈
    HapticFeedback.selectionClick();
  }

  void _hideToolbar() {
    if (!_isToolbarVisible) return;

    setState(() {
      _isToolbarVisible = false;
    });

    _toolbarAnimationController.reverse().then((_) {
      _removeToolbarOverlay();
    });
  }

  void _removeToolbarOverlay() {
    _toolbarOverlay?.remove();
    _toolbarOverlay = null;
  }

  OverlayEntry _createToolbarOverlay() {
    return OverlayEntry(
      builder:
          (context) => Stack(
            children: [
              // 半透明背景遮罩，点击可关闭工具栏
              Positioned.fill(
                child: GestureDetector(
                  onTap: _hideToolbar,
                  child: Container(color: Colors.black.withValues(alpha: 0.3)),
                ),
              ),
              // 样式工具栏
              _buildPositionedToolbar(),
            ],
          ),
    );
  }

  Widget _buildPositionedToolbar() {
    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;
    final toolbarWidth = screenSize.width - 40.0; // 左右各20的边距
    final toolbarHeight = 200.0;

    // 计算工具栏位置，确保不超出屏幕边界
    double left = 20.0;
    double top = screenSize.height * 0.3;

    // 确保不超出屏幕边界
    top = top.clamp(100.0, screenSize.height - toolbarHeight - 100.0);

    return Positioned(
      left: left,
      top: top,
      width: toolbarWidth,
      child: AnimatedBuilder(
        animation: _toolbarAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _toolbarAnimation.value,
            child: Opacity(
              opacity: _toolbarAnimation.value,
              child: _buildFloatingToolbar(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFloatingToolbar() {
    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      elevation: 8,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 工具栏标题
          Row(
            children: [
              const Icon(Icons.edit, size: 20, color: Color(0xFF6366F1)),
              const SizedBox(width: 8),
              const Text(
                '文本样式',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1E293B),
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: _hideToolbar,
                icon: const Icon(Icons.close, size: 20),
                style: IconButton.styleFrom(
                  backgroundColor: const Color(0xFFF1F5F9),
                  minimumSize: const Size(32, 32),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 字体选择
          _buildFontSelector(),

          const SizedBox(height: 12),

          // 样式控制
          _buildStyleControls(),

          const SizedBox(height: 12),

          // 应用按钮
          _buildApplyButton(),
        ],
        ),
      ),
    );
  }

  Widget _buildFontSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<FontFamily>(
          value: _selectedFont,
          isExpanded: true,
          items:
              FontManager.availableFonts.map((font) {
                return DropdownMenuItem<FontFamily>(
                  value: font,
                  child: Text(
                    font.displayName,
                    style: TextStyle(fontFamily: font.fontFamily, fontSize: 14),
                  ),
                );
              }).toList(),
          onChanged: (font) {
            if (font != null) {
              setState(() {
                _selectedFont = font;
              });
              // 应用字体变更，但不关闭工具栏
              _applyStylesToSelection();
            }
          },
        ),
      ),
    );
  }

  Widget _buildStyleControls() {
    return Row(
      children: [
        // 字号控制
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '字号',
                style: TextStyle(fontSize: 12, color: Color(0xFF64748B)),
              ),
              const SizedBox(height: 4),
              Slider(
                value: _fontSize,
                min: 12,
                max: 32,
                divisions: 20,
                label: _fontSize.round().toString(),
                activeColor: const Color(0xFF6366F1),
                onChanged: (value) {
                  setState(() {
                    _fontSize = value;
                  });
                  // 实时应用样式变更
                  _applyStylesToSelection();
                },
              ),
            ],
          ),
        ),

        const SizedBox(width: 16),

        // 字重选择
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '字重',
                style: TextStyle(fontSize: 12, color: Color(0xFF64748B)),
              ),
              const SizedBox(height: 4),
              _buildWeightSelector(),
            ],
          ),
        ),

        const SizedBox(width: 16),

        // 颜色选择
        Column(
          children: [
            const Text(
              '颜色',
              style: TextStyle(fontSize: 12, color: Color(0xFF64748B)),
            ),
            const SizedBox(height: 4),
            _buildColorPicker(),
          ],
        ),
      ],
    );
  }

  Widget _buildWeightSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(6),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<FontWeight>(
          value: _fontWeight,
          isExpanded: true,
          items:
              _selectedFont.getSupportedWeights().map((weight) {
                return DropdownMenuItem<FontWeight>(
                  value: weight,
                  child: Text(
                    _getWeightName(weight),
                    style: const TextStyle(fontSize: 12),
                  ),
                );
              }).toList(),
          onChanged: (weight) {
            if (weight != null) {
              setState(() {
                _fontWeight = weight;
              });
              // 应用字重变更，但不关闭工具栏
              _applyStylesToSelection();
            }
          },
        ),
      ),
    );
  }

  Widget _buildColorPicker() {
    return GestureDetector(
      onTap: _showColorPicker,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: _textColor,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: const Color(0xFFE2E8F0)),
        ),
      ),
    );
  }

  Widget _buildApplyButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _applyStylesAndClose,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF6366F1),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          '应用样式',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  void _showColorPicker() {
    // 这里可以集成 flutter_colorpicker 包
    // 暂时使用简单的颜色选择
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择颜色'),
        content: Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            Colors.black,
            Colors.red,
            Colors.blue,
            Colors.green,
            Colors.orange,
            Colors.purple,
            Colors.teal,
            Colors.pink,
          ].map((color) {
            return GestureDetector(
              onTap: () {
                setState(() {
                  _textColor = color;
                });
                Navigator.pop(context);
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.grey.shade300),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 实时应用样式变更（不关闭工具栏）
  void _applyStylesToSelection() {
    if (_currentSelection == null) return;

    _currentStyles = {
      'fontFamily': _selectedFont.name,
      'fontSize': _fontSize,
      'fontWeight': _fontWeight,
      'color': _textColor,
      'textAlign': _textAlign,
    };

    widget.onStyleChanged?.call(_currentStyles);
    // 不关闭工具栏，让用户可以继续调整样式
  }

  /// 最终应用样式并关闭工具栏
  void _applyStylesAndClose() {
    if (_currentSelection == null) return;

    _currentStyles = {
      'fontFamily': _selectedFont.name,
      'fontSize': _fontSize,
      'fontWeight': _fontWeight,
      'color': _textColor,
      'textAlign': _textAlign,
    };

    widget.onStyleChanged?.call(_currentStyles);
    _hideToolbar();

    // 触觉反馈
    HapticFeedback.lightImpact();

    // 显示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('样式已应用'),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  String _getWeightName(FontWeight weight) {
    switch (weight) {
      case FontWeight.w300:
        return '细体';
      case FontWeight.w400:
        return '常规';
      case FontWeight.w500:
        return '中等';
      case FontWeight.w600:
        return '半粗';
      case FontWeight.w700:
        return '粗体';
      case FontWeight.w800:
        return '特粗';
      case FontWeight.w900:
        return '黑体';
      default:
        return '常规';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: widget.template.backgroundGradient,
        borderRadius: BorderRadius.circular(widget.template.borderRadius),
        boxShadow: widget.template.hasShadow
            ? [
                BoxShadow(
                  color: widget.template.backgroundGradient.colors.first
                      .withValues(alpha: 0.3),
                  offset: const Offset(0, 8),
                  blurRadius: 24,
                ),
              ]
            : null,
      ),
      child: Padding(
        padding: EdgeInsets.all(widget.template.padding),
        child: TextField(
          controller: _controller,
          focusNode: _focusNode,
          maxLines: null,
          style: FontManager.createTextStyle(
            fontFamily: _selectedFont,
            fontSize: _fontSize,
            fontWeight: _fontWeight,
            color: _textColor,
            height: widget.template.lineHeight,
          ),
          decoration: InputDecoration(
            hintText: '在这里输入文本内容...\n\n💡 选中文本可以修改样式',
            hintStyle: TextStyle(
              color: widget.template.textColor.withValues(alpha: 0.5),
              fontSize: _fontSize,
            ),
            border: InputBorder.none,
            contentPadding: EdgeInsets.zero,
          ),
          textAlign: _textAlign,
          contextMenuBuilder: _buildCustomContextMenu,
        ),
      ),
    );
  }

  /// 构建自定义上下文菜单
  Widget _buildCustomContextMenu(
    BuildContext context,
    EditableTextState editableTextState,
  ) {
    final List<ContextMenuButtonItem> buttonItems = [];

    // 如果有选中文本，首先添加"修改样式"选项
    if (_controller.selection.isValid && !_controller.selection.isCollapsed) {
      buttonItems.add(
        ContextMenuButtonItem(
          label: '修改样式',
          onPressed: () {
            // 保存当前选择，因为关闭菜单可能会改变选择
            final currentSelection = _controller.selection;

            // 先关闭上下文菜单
            ContextMenuController.removeAny();

            // 延迟显示样式工具栏，确保上下文菜单完全关闭
            Future.delayed(const Duration(milliseconds: 200), () {
              // 恢复选择状态
              if (currentSelection.isValid && !currentSelection.isCollapsed) {
                _currentSelection = currentSelection;
                _controller.selection = currentSelection;
                _showToolbar();
              }
            });
          },
        ),
      );
    }

    // 然后添加默认的上下文菜单选项
    buttonItems.addAll(editableTextState.contextMenuButtonItems);

    return AdaptiveTextSelectionToolbar.buttonItems(
      anchors: editableTextState.contextMenuAnchors,
      buttonItems: buttonItems,
    );
  }
}
