import 'package:flutter/material.dart';
import 'package:screenshot/screenshot.dart';
import 'package:saver_gallery/saver_gallery.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/text_card_model.dart';
import '../../config/app_theme.dart';
import 'rich_text_renderer.dart';

class TextCardPreview extends StatefulWidget {
  final TextCardModel card;
  final CardTemplate template;
  final Function(CardTemplate) onTemplateChanged;

  const TextCardPreview({
    super.key,
    required this.card,
    required this.template,
    required this.onTemplateChanged,
  });

  @override
  State<TextCardPreview> createState() => _TextCardPreviewState();
}

class _TextCardPreviewState extends State<TextCardPreview> {
  final ScreenshotController _screenshotController = ScreenshotController();
  bool _isExporting = false;
  bool _showStylePanel = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      body: Column(
        children: [
          // 顶部操作栏
          Container(
            color: AppTheme.bgWhiteColor,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              children: [
                // 样式按钮
                IconButton(
                  onPressed:
                      () => setState(() => _showStylePanel = !_showStylePanel),
                  icon: Icon(
                    Icons.palette,
                    color:
                        _showStylePanel
                            ? AppTheme.primaryColor
                            : AppTheme.textLightColor,
                  ),
                  tooltip: '样式设置',
                ),
                const Spacer(),
                // 导出按钮
                ElevatedButton.icon(
                  onPressed: _isExporting ? null : _exportAsImage,
                  icon:
                      _isExporting
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Icon(Icons.file_download, size: 18),
                  label: Text(_isExporting ? '导出中...' : '导出'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    minimumSize: const Size(0, 36),
                  ),
                ),
              ],
            ),
          ),

          // 样式面板
          if (_showStylePanel)
            Container(color: AppTheme.bgWhiteColor, child: _buildStylePanel()),

          const Divider(height: 1),

          // 预览区域
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              child: Center(
                child: SingleChildScrollView(
                  child: Screenshot(
                    controller: _screenshotController,
                    child: _buildCardPreview(),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStylePanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '模板样式',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: CardTemplate.getBuiltInTemplates().length,
              itemBuilder: (context, index) {
                final template = CardTemplate.getBuiltInTemplates()[index];
                final isSelected = template.id == widget.template.id;

                return GestureDetector(
                  onTap: () => widget.onTemplateChanged(template),
                  child: Container(
                    width: 60,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color:
                            isSelected
                                ? AppTheme.primaryColor
                                : AppTheme.textLightColor.withValues(
                                  alpha: 0.3,
                                ),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.all(4),
                            decoration: _buildTemplateDecoration(template),
                            child: const Center(
                              child: Icon(
                                Icons.text_snippet,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 4,
                            vertical: 4,
                          ),
                          child: Text(
                            template.name,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                              color:
                                  isSelected
                                      ? AppTheme.primaryColor
                                      : AppTheme.textDarkColor,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardPreview() {
    return Container(
      width: 320,
      constraints: const BoxConstraints(minHeight: 180, maxWidth: 360),
      decoration: _buildCardDecoration(),
      padding: EdgeInsets.all(
        widget.template.styles['padding']?.toDouble() ?? 16.0,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          if (widget.card.title.isNotEmpty) ...[
            Text(
              widget.card.title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _getTitleColor(),
              ),
            ),
            const SizedBox(height: 12),
          ],

          // 内容
          _buildContent(),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return RichTextRenderer(
      content: widget.card.content,
      baseStyle: TextStyle(fontSize: 14, color: _getTextColor(), height: 1.5),
      boldStyle: TextStyle(fontWeight: FontWeight.bold, color: _getTextColor()),
      italicStyle: TextStyle(
        fontStyle: FontStyle.italic,
        color: _getTextColor(),
      ),
      underlineStyle: TextStyle(
        decoration: TextDecoration.underline,
        color: _getTextColor(),
      ),
      highlightStyle: TextStyle(
        backgroundColor: _getTextColor().withValues(alpha: 0.2),
        color: _getTextColor(),
      ),
    );
  }

  BoxDecoration _buildTemplateDecoration(CardTemplate template) {
    final styles = template.styles;
    final background = styles['background'];
    final borderRadius = styles['borderRadius']?.toDouble() ?? 8.0;

    if (background is String && background.startsWith('linear-gradient')) {
      return BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: _parseGradient(background),
      );
    } else if (background is String && background.startsWith('#')) {
      return BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        color: _parseColor(background),
      );
    }

    return BoxDecoration(
      borderRadius: BorderRadius.circular(borderRadius),
      gradient: AppTheme.primaryGradient,
    );
  }

  BoxDecoration _buildCardDecoration() {
    final styles = widget.template.styles;
    final background = styles['background'];
    final borderRadius = styles['borderRadius']?.toDouble() ?? 12.0;
    final hasShadow = styles['shadow'] == true;

    BoxDecoration decoration;

    if (background is String && background.startsWith('linear-gradient')) {
      decoration = BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: _parseGradient(background),
      );
    } else if (background is String && background.startsWith('#')) {
      decoration = BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        color: _parseColor(background),
      );
    } else {
      decoration = BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: AppTheme.primaryGradient,
      );
    }

    if (hasShadow) {
      decoration = decoration.copyWith(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 12,
            offset: const Offset(0, 6),
            spreadRadius: 0,
          ),
        ],
      );
    }

    return decoration;
  }

  Gradient _parseGradient(String gradientString) {
    if (gradientString.contains('#667eea') &&
        gradientString.contains('#764ba2')) {
      return const LinearGradient(
        colors: [Color(0xFF667eea), Color(0xFF764ba2)],
      );
    } else if (gradientString.contains('#a8edea') &&
        gradientString.contains('#fed6e3')) {
      return const LinearGradient(
        colors: [Color(0xFFa8edea), Color(0xFFfed6e3)],
      );
    } else if (gradientString.contains('#d299c2') &&
        gradientString.contains('#fef9d7')) {
      return const LinearGradient(
        colors: [Color(0xFFd299c2), Color(0xFFfef9d7)],
      );
    } else if (gradientString.contains('#2d3748') &&
        gradientString.contains('#4a5568')) {
      return const LinearGradient(
        colors: [Color(0xFF2d3748), Color(0xFF4a5568)],
      );
    } else if (gradientString.contains('#fed7aa') &&
        gradientString.contains('#f97316')) {
      return const LinearGradient(
        colors: [Color(0xFFfed7aa), Color(0xFFf97316)],
      );
    }
    return AppTheme.primaryGradient;
  }

  Color _parseColor(String colorString) {
    final hex = colorString.replaceAll('#', '');
    return Color(int.parse('FF$hex', radix: 16));
  }

  Color _getTextColor() {
    final textColorString = widget.template.styles['textColor'] as String?;
    if (textColorString != null) {
      return _parseColor(textColorString);
    }
    return AppTheme.textDarkColor;
  }

  Color _getTitleColor() {
    final titleColorString = widget.template.styles['titleColor'] as String?;
    if (titleColorString != null) {
      return _parseColor(titleColorString);
    }
    return _getTextColor();
  }

  Future<void> _exportAsImage() async {
    setState(() => _isExporting = true);

    try {
      // 请求存储权限
      final permission = await Permission.storage.request();
      if (!permission.isGranted) {
        _showMessage('需要存储权限才能保存图片');
        return;
      }

      // 截图
      final imageData = await _screenshotController.capture(
        pixelRatio: 3.0, // 高分辨率
      );

      if (imageData != null) {
        // 保存到相册
        final result = await SaverGallery.saveImage(
          imageData,
          fileName: 'textcard_${DateTime.now().millisecondsSinceEpoch}.png',
          skipIfExists: false,
          androidRelativePath: "Pictures/ContentPal",
        );

        if (result.isSuccess) {
          _showMessage('图片已保存到相册');
        } else {
          _showMessage('保存失败: ${result.errorMessage ?? "未知错误"}');
        }
      }
    } catch (e) {
      _showMessage('导出失败: $e');
    } finally {
      setState(() => _isExporting = false);
    }
  }

  void _showMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }
  }
}
