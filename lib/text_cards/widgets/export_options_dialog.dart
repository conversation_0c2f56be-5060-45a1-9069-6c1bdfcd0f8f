import 'package:flutter/material.dart';
import '../models/export_config.dart';

/// 导出选项对话框
class ExportOptionsDialog extends StatefulWidget {
  final ExportConfig initialConfig;

  const ExportOptionsDialog({super.key, required this.initialConfig});

  @override
  State<ExportOptionsDialog> createState() => _ExportOptionsDialogState();
}

class _ExportOptionsDialogState extends State<ExportOptionsDialog> {
  late ExportConfig _config;

  @override
  void initState() {
    super.initState();
    _config = widget.initialConfig;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: BoxConstraints(
          maxWidth: 400,
          maxHeight: MediaQuery.of(context).size.height * 0.8, // 限制最大高度
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                Icon(Icons.tune, color: Theme.of(context).primaryColor),
                const SizedBox(width: 12),
                const Text(
                  '导出设置',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 可滚动的内容区域
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 尺寸选择
                    _buildSectionTitle('图片尺寸'),
                    const SizedBox(height: 8),
                    _buildSizeSelector(),
                    const SizedBox(height: 16),

                    // 比例选择
                    _buildSectionTitle('图片比例'),
                    const SizedBox(height: 8),
                    _buildRatioSelector(),
                    const SizedBox(height: 16),

                    // 质量选择
                    _buildSectionTitle('图片质量'),
                    const SizedBox(height: 8),
                    _buildQualitySlider(),
                    const SizedBox(height: 16),

                    // 水印选择
                    _buildWatermarkToggle(),
                    const SizedBox(height: 16),

                    // 预览信息
                    _buildPreviewInfo(),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 按钮区域（固定在底部）
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: () {
                    debugPrint('🔥 导出选项对话框：用户点击确定导出');
                    Navigator.of(context).pop(_config);
                  },
                  child: const Text('确定导出'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
    );
  }

  Widget _buildSizeSelector() {
    return Wrap(
      spacing: 8,
      children:
          ExportSize.values.map((size) {
            final isSelected = _config.size == size;
            return ChoiceChip(
              label: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(size.label),
                  Text(
                    size.description,
                    style: TextStyle(
                      fontSize: 10,
                      color: isSelected ? Colors.white : Colors.grey[600],
                    ),
                  ),
                ],
              ),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _config = _config.copyWith(size: size);
                  });
                }
              },
            );
          }).toList(),
    );
  }

  Widget _buildRatioSelector() {
    return Wrap(
      spacing: 8,
      children:
          ExportRatio.values.map((ratio) {
            final isSelected = _config.ratio == ratio;
            return ChoiceChip(
              label: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(ratio.label),
                  Text(
                    ratio.description,
                    style: TextStyle(
                      fontSize: 10,
                      color: isSelected ? Colors.white : Colors.grey[600],
                    ),
                  ),
                ],
              ),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _config = _config.copyWith(ratio: ratio);
                  });
                }
              },
            );
          }).toList(),
    );
  }

  Widget _buildQualitySlider() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [const Text('质量'), Text('${_config.quality}%')],
        ),
        Slider(
          value: _config.quality.toDouble(),
          min: 50,
          max: 100,
          divisions: 10,
          onChanged: (value) {
            setState(() {
              _config = _config.copyWith(quality: value.round());
            });
          },
        ),
      ],
    );
  }

  Widget _buildWatermarkToggle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text('包含水印'),
        Switch(
          value: _config.includeWatermark,
          onChanged: (value) {
            setState(() {
              _config = _config.copyWith(includeWatermark: value);
            });
          },
        ),
      ],
    );
  }

  Widget _buildPreviewInfo() {
    final dimensions = _config.getDimensions();
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('预览信息', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Text(
            '尺寸: ${dimensions.width.toInt()} × ${dimensions.height.toInt()} 像素',
          ),
          Text('比例: ${_config.ratio.label}'),
          Text('质量: ${_config.quality}%'),
          Text('水印: ${_config.includeWatermark ? "包含" : "不包含"}'),
        ],
      ),
    );
  }
}

/// 显示导出选项对话框
Future<ExportConfig?> showExportOptionsDialog(
  BuildContext context, {
  ExportConfig? initialConfig,
}) {
  return showDialog<ExportConfig>(
    context: context,
    builder:
        (context) => ExportOptionsDialog(
          initialConfig: initialConfig ?? const ExportConfig(),
        ),
  );
}
