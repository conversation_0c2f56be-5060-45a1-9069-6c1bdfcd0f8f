import 'package:flutter/material.dart';
import '../models/split_config.dart';

/// 拆分预览卡片组件
class SplitPreviewCard extends StatelessWidget {
  final SplitResultItem item;
  final int index;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onMerge;

  const SplitPreviewCard({
    super.key,
    required this.item,
    required this.index,
    this.isSelected = false,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onMerge,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected 
            ? const Color(0xFF6366F1) 
            : Colors.grey.withValues(alpha: 0.2),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 头部信息
                Row(
                  children: [
                    // 类型指示器
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getTypeColor().withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getTypeIcon(),
                            size: 12,
                            color: _getTypeColor(),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            item.type.displayName,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              color: _getTypeColor(),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(width: 8),
                    
                    // 索引
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        '#${index + 1}',
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF64748B),
                        ),
                      ),
                    ),
                    
                    const Spacer(),
                    
                    // 操作按钮
                    _buildActionButtons(),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // 标题（如果有）
                if (item.title != null && item.title!.isNotEmpty) ...[
                  Text(
                    item.title!,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1E293B),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                ],
                
                // 内容预览
                Text(
                  _getPreviewContent(),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF64748B),
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: 8),
                
                // 统计信息
                Row(
                  children: [
                    Icon(
                      Icons.text_fields,
                      size: 12,
                      color: Colors.grey.withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${item.content.length} 字符',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey.withValues(alpha: 0.8),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Icon(
                      Icons.format_list_numbered,
                      size: 12,
                      color: Colors.grey.withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${item.content.split('\n').length} 行',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (onEdit != null)
          _buildActionButton(
            icon: Icons.edit_outlined,
            onTap: onEdit!,
            color: const Color(0xFF6366F1),
          ),
        if (onMerge != null)
          _buildActionButton(
            icon: Icons.merge_outlined,
            onTap: onMerge!,
            color: const Color(0xFF8B5CF6),
          ),
        if (onDelete != null)
          _buildActionButton(
            icon: Icons.delete_outline,
            onTap: onDelete!,
            color: const Color(0xFFEF4444),
          ),
      ],
    );
  }

  /// 构建单个操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.only(left: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(6),
          child: Container(
            padding: const EdgeInsets.all(4),
            child: Icon(
              icon,
              size: 14,
              color: color,
            ),
          ),
        ),
      ),
    );
  }

  /// 获取类型颜色
  Color _getTypeColor() {
    switch (item.type) {
      case SplitItemType.title:
        return const Color(0xFF6366F1);
      case SplitItemType.list:
        return const Color(0xFF10B981);
      case SplitItemType.quote:
        return const Color(0xFF8B5CF6);
      case SplitItemType.code:
        return const Color(0xFFEF4444);
      case SplitItemType.content:
        return const Color(0xFF64748B);
    }
  }

  /// 获取类型图标
  IconData _getTypeIcon() {
    switch (item.type) {
      case SplitItemType.title:
        return Icons.title;
      case SplitItemType.list:
        return Icons.list;
      case SplitItemType.quote:
        return Icons.format_quote;
      case SplitItemType.code:
        return Icons.code;
      case SplitItemType.content:
        return Icons.text_snippet;
    }
  }

  /// 获取预览内容
  String _getPreviewContent() {
    String content = item.content;
    
    // 如果有标题，显示标题后的内容
    if (item.title != null && item.title!.isNotEmpty) {
      final lines = content.split('\n');
      if (lines.length > 1) {
        content = lines.skip(1).join('\n').trim();
      }
    }
    
    return content.isEmpty ? '(空内容)' : content;
  }
}
