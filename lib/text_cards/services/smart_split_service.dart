import '../models/split_config.dart';

/// 智能文本拆分服务
class SmartSplitService {
  static const String _defaultSeparator = '---CARD---';

  /// 根据配置拆分文本
  static List<SplitResultItem> splitText(String text, SplitConfig config) {
    if (text.trim().isEmpty) return [];

    switch (config.mode) {
      case SplitMode.paragraph:
        return _splitByParagraph(text, config);
      case SplitMode.sentence:
        return _splitBySentence(text, config);
      case SplitMode.length:
        return _splitByLength(text, config);
      case SplitMode.custom:
        return _splitByCustomSeparator(text, config);
      case SplitMode.smart:
        return _smartSplit(text, config);
    }
  }

  /// 按段落拆分
  static List<SplitResultItem> _splitByParagraph(String text, SplitConfig config) {
    final paragraphs = text.split(RegExp(r'\n\s*\n')).where((p) => p.trim().isNotEmpty).toList();
    final results = <SplitResultItem>[];

    for (int i = 0; i < paragraphs.length; i++) {
      final paragraph = paragraphs[i].trim();
      if (paragraph.isEmpty) continue;

      final item = _createSplitItem(paragraph, i, config);
      results.add(item);
    }

    return results;
  }

  /// 按句子拆分
  static List<SplitResultItem> _splitBySentence(String text, SplitConfig config) {
    final sentences = text.split(RegExp(r'[.!?。！？]\s*')).where((s) => s.trim().isNotEmpty).toList();
    final results = <SplitResultItem>[];

    for (int i = 0; i < sentences.length; i++) {
      final sentence = sentences[i].trim();
      if (sentence.isEmpty) continue;

      final item = _createSplitItem(sentence, i, config);
      results.add(item);
    }

    return results;
  }

  /// 按长度拆分
  static List<SplitResultItem> _splitByLength(String text, SplitConfig config) {
    final results = <SplitResultItem>[];
    final maxLength = config.maxLength;
    int index = 0;

    for (int i = 0; i < text.length; i += maxLength) {
      final end = (i + maxLength < text.length) ? i + maxLength : text.length;
      final chunk = text.substring(i, end).trim();
      
      if (chunk.isNotEmpty) {
        final item = _createSplitItem(chunk, index++, config);
        results.add(item);
      }
    }

    return results;
  }

  /// 按自定义分隔符拆分
  static List<SplitResultItem> _splitByCustomSeparator(String text, SplitConfig config) {
    final separator = config.customSeparator.isNotEmpty ? config.customSeparator : _defaultSeparator;
    final parts = text.split(separator).where((p) => p.trim().isNotEmpty).toList();
    final results = <SplitResultItem>[];

    for (int i = 0; i < parts.length; i++) {
      final part = parts[i].trim();
      if (part.isEmpty) continue;

      final item = _createSplitItem(part, i, config);
      results.add(item);
    }

    return results;
  }

  /// 智能拆分
  static List<SplitResultItem> _smartSplit(String text, SplitConfig config) {
    final results = <SplitResultItem>[];
    final lines = text.split('\n');
    String currentContent = '';
    int index = 0;

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      // 空行处理
      if (line.isEmpty) {
        if (currentContent.isNotEmpty) {
          final item = _createSplitItem(currentContent.trim(), index++, config);
          results.add(item);
          currentContent = '';
        }
        continue;
      }

      // 检查是否是标题
      if (_isLikelyTitle(line, config)) {
        // 保存之前的内容
        if (currentContent.isNotEmpty) {
          final item = _createSplitItem(currentContent.trim(), index++, config);
          results.add(item);
          currentContent = '';
        }
        
        // 创建标题项
        final titleItem = SplitResultItem(
          id: 'split_${DateTime.now().millisecondsSinceEpoch}_$index',
          content: line,
          title: line,
          originalIndex: index++,
          isTitle: true,
          type: SplitItemType.title,
        );
        results.add(titleItem);
      } else {
        // 累积内容
        currentContent += (currentContent.isEmpty ? '' : '\n') + line;
        
        // 检查是否达到长度限制
        if (currentContent.length > config.maxLength) {
          final item = _createSplitItem(currentContent.trim(), index++, config);
          results.add(item);
          currentContent = '';
        }
      }
    }

    // 处理剩余内容
    if (currentContent.isNotEmpty) {
      final item = _createSplitItem(currentContent.trim(), index, config);
      results.add(item);
    }

    return results;
  }

  /// 创建拆分项
  static SplitResultItem _createSplitItem(String content, int index, SplitConfig config) {
    String? title;
    SplitItemType type = SplitItemType.content;

    if (config.autoDetectTitles) {
      final lines = content.split('\n').where((l) => l.trim().isNotEmpty).toList();
      if (lines.isNotEmpty) {
        final firstLine = lines.first.trim();
        if (_isLikelyTitle(firstLine, config)) {
          title = firstLine;
          type = SplitItemType.title;
        }
      }
    }

    // 检测其他类型
    if (content.startsWith('- ') || content.startsWith('* ') || content.contains('\n- ')) {
      type = SplitItemType.list;
    } else if (content.startsWith('> ') || content.contains('\n> ')) {
      type = SplitItemType.quote;
    } else if (content.startsWith('```') || content.contains('```')) {
      type = SplitItemType.code;
    }

    return SplitResultItem(
      id: 'split_${DateTime.now().millisecondsSinceEpoch}_$index',
      content: content,
      title: title,
      originalIndex: index,
      isTitle: title != null,
      type: type,
    );
  }

  /// 判断是否像标题
  static bool _isLikelyTitle(String line, SplitConfig config) {
    if (line.isEmpty) return false;

    // 检查长度
    if (line.length > 50) return false;

    // 检查标点符号
    if (line.endsWith('？') || line.endsWith('?') || 
        line.endsWith('：') || line.endsWith(':')) {
      return true;
    }

    // 检查Markdown标题
    if (line.startsWith('#')) return true;

    // 检查是否全大写或包含特殊格式
    if (line == line.toUpperCase() && line.length < 20) return true;

    // 检查是否包含关键词
    final titleKeywords = ['什么是', '如何', '为什么', '怎么', '介绍', '概述', '总结'];
    for (final keyword in titleKeywords) {
      if (line.contains(keyword)) return true;
    }

    return false;
  }

  /// 合并拆分项
  static List<SplitResultItem> mergeItems(List<SplitResultItem> items, int index1, int index2) {
    if (index1 >= items.length || index2 >= items.length || index1 == index2) {
      return items;
    }

    final newItems = List<SplitResultItem>.from(items);
    final item1 = newItems[index1];
    final item2 = newItems[index2];

    // 合并内容
    final mergedContent = '${item1.content}\n\n${item2.content}';
    final mergedItem = item1.copyWith(
      content: mergedContent,
      title: item1.title ?? item2.title,
    );

    // 移除较大索引的项，更新较小索引的项
    final minIndex = index1 < index2 ? index1 : index2;
    final maxIndex = index1 > index2 ? index1 : index2;

    newItems[minIndex] = mergedItem;
    newItems.removeAt(maxIndex);

    return newItems;
  }

  /// 删除拆分项
  static List<SplitResultItem> removeItem(List<SplitResultItem> items, int index) {
    if (index >= items.length) return items;
    
    final newItems = List<SplitResultItem>.from(items);
    newItems.removeAt(index);
    return newItems;
  }
}
