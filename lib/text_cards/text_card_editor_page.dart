import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import 'models/text_card_model.dart';
import 'widgets/text_editor_toolbar.dart';
import 'widgets/format_toolbar.dart';
import 'widgets/text_card_preview.dart';

class TextCardEditorPage extends StatefulWidget {
  final String? cardId;
  final String? templateId;
  final TextCardModel? existingCard;

  const TextCardEditorPage({
    super.key,
    this.cardId,
    this.templateId,
    this.existingCard,
  });

  @override
  State<TextCardEditorPage> createState() => _TextCardEditorPageState();
}

class _TextCardEditorPageState extends State<TextCardEditorPage>
    with SingleTickerProviderStateMixin {
  late TextEditingController _titleController;
  late TextEditingController _contentController;
  late CardTemplate _selectedTemplate;
  late TabController _tabController;

  bool _isLoading = false;
  bool _showFormatToolbar = false;
  bool _showTitle = true;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController();
    _contentController = TextEditingController();
    _tabController = TabController(length: 2, vsync: this);

    // 添加监听器以便实时更新预览
    _titleController.addListener(() => setState(() {}));
    _contentController.addListener(() => setState(() {}));

    _loadCardData();
  }

  void _loadCardData() {
    if (widget.existingCard != null) {
      final card = widget.existingCard!;
      _titleController.text = card.title;
      _contentController.text = card.content;
      _showTitle = card.title.isNotEmpty;

      _selectedTemplate = CardTemplate.getBuiltInTemplates().firstWhere(
        (t) => t.id == card.templateId,
        orElse: () => CardTemplate.getBuiltInTemplates().first,
      );
    } else {
      _selectedTemplate = CardTemplate.getBuiltInTemplates().firstWhere(
        (t) => t.id == widget.templateId,
        orElse: () => CardTemplate.getBuiltInTemplates().first,
      );
      _showTitle = true;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _onContentSelectionChanged() {
    final selection = _contentController.selection;
    setState(() {
      _showFormatToolbar = selection.start != selection.end;
    });
  }

  void _insertFormatText(String prefix, String suffix, {String? placeholder}) {
    final text = _contentController.text;
    final selection = _contentController.selection;

    String selectedText = '';
    if (selection.start != selection.end) {
      selectedText = text.substring(selection.start, selection.end);
    } else if (placeholder != null) {
      selectedText = placeholder;
    }

    final formattedText = '$prefix$selectedText$suffix';
    final newText = text.replaceRange(
      selection.start,
      selection.end,
      formattedText,
    );

    _contentController.text = newText;
    _contentController.selection = TextSelection.collapsed(
      offset: selection.start + prefix.length + selectedText.length,
    );
  }

  void _insertList(bool isOrdered) {
    final text = _contentController.text;
    final selection = _contentController.selection;

    // 获取当前行
    int lineStart = selection.start;
    while (lineStart > 0 && text[lineStart - 1] != '\n') {
      lineStart--;
    }

    final prefix = isOrdered ? '1. ' : '• ';
    final newText =
        text.substring(0, lineStart) + prefix + text.substring(lineStart);

    _contentController.text = newText;
    _contentController.selection = TextSelection.collapsed(
      offset: lineStart + prefix.length,
    );
  }

  void _insertTable() {
    const tableTemplate = '''
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 行1 | 数据 | 数据 |
| 行2 | 数据 | 数据 |
''';

    final text = _contentController.text;
    final selection = _contentController.selection;

    final newText = text.replaceRange(
      selection.start,
      selection.end,
      '\n$tableTemplate\n',
    );

    _contentController.text = newText;
  }

  void _changeTemplate() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.6,
            decoration: const BoxDecoration(
              color: AppTheme.bgWhiteColor,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 16),
                  decoration: BoxDecoration(
                    color: AppTheme.textLightColor.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    '更换模板',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textDarkColor,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Expanded(
                  child: GridView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 1.2,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                        ),
                    itemCount: CardTemplate.getBuiltInTemplates().length,
                    itemBuilder: (context, index) {
                      final template =
                          CardTemplate.getBuiltInTemplates()[index];
                      final isSelected = template.id == _selectedTemplate.id;

                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedTemplate = template;
                          });
                          Navigator.pop(context);
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color:
                                  isSelected
                                      ? AppTheme.primaryColor
                                      : AppTheme.textLightColor.withValues(
                                        alpha: 0.2,
                                      ),
                              width: isSelected ? 2 : 1,
                            ),
                          ),
                          child: Column(
                            children: [
                              Expanded(
                                child: Container(
                                  margin: const EdgeInsets.all(8),
                                  decoration: _buildTemplateDecoration(
                                    template,
                                  ),
                                  child: const Center(
                                    child: Icon(
                                      Icons.text_snippet,
                                      color: Colors.white,
                                      size: 24,
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(8),
                                child: Text(
                                  template.name,
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight:
                                        isSelected
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                    color:
                                        isSelected
                                            ? AppTheme.primaryColor
                                            : AppTheme.textDarkColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
    );
  }

  BoxDecoration _buildTemplateDecoration(CardTemplate template) {
    final styles = template.styles;
    final background = styles['background'];

    if (background is String && background.startsWith('linear-gradient')) {
      return BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: _parseGradient(background),
      );
    } else if (background is String && background.startsWith('#')) {
      return BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: _parseColor(background),
      );
    }

    return BoxDecoration(
      borderRadius: BorderRadius.circular(8),
      gradient: AppTheme.primaryGradient,
    );
  }

  Gradient _parseGradient(String gradientString) {
    if (gradientString.contains('#667eea') &&
        gradientString.contains('#764ba2')) {
      return const LinearGradient(
        colors: [Color(0xFF667eea), Color(0xFF764ba2)],
      );
    } else if (gradientString.contains('#a8edea') &&
        gradientString.contains('#fed6e3')) {
      return const LinearGradient(
        colors: [Color(0xFFa8edea), Color(0xFFfed6e3)],
      );
    } else if (gradientString.contains('#d299c2') &&
        gradientString.contains('#fef9d7')) {
      return const LinearGradient(
        colors: [Color(0xFFd299c2), Color(0xFFfef9d7)],
      );
    } else if (gradientString.contains('#2d3748') &&
        gradientString.contains('#4a5568')) {
      return const LinearGradient(
        colors: [Color(0xFF2d3748), Color(0xFF4a5568)],
      );
    } else if (gradientString.contains('#fed7aa') &&
        gradientString.contains('#f97316')) {
      return const LinearGradient(
        colors: [Color(0xFFfed7aa), Color(0xFFf97316)],
      );
    }

    return AppTheme.primaryGradient;
  }

  Color _parseColor(String colorString) {
    final hex = colorString.replaceAll('#', '');
    return Color(int.parse('FF$hex', radix: 16));
  }

  void _saveCard() async {
    // 内容不能为空
    if (_contentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入内容')));
      return;
    }

    setState(() => _isLoading = true);

    try {
      final card = TextCardModel(
        id:
            widget.existingCard?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        title: _showTitle ? _titleController.text.trim() : '', // 标题可选
        content: _contentController.text.trim(),
        templateId: _selectedTemplate.id,
        createdAt: widget.existingCard?.createdAt ?? DateTime.now(),
        updatedAt: widget.existingCard != null ? DateTime.now() : null,
      );

      Navigator.pop(context, card);
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('保存失败: $e')));
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: Text(
          widget.cardId != null ? '编辑卡片' : '创建卡片',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textDarkColor,
          ),
        ),
        backgroundColor: AppTheme.bgWhiteColor,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveCard,
            child:
                _isLoading
                    ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : const Text(
                      '保存',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryColor,
                      ),
                    ),
          ),
          const SizedBox(width: 8),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textLightColor,
          indicatorColor: AppTheme.primaryColor,
          tabs: const [
            Tab(icon: Icon(Icons.edit), text: '编辑'),
            Tab(icon: Icon(Icons.preview), text: '预览'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // 编辑Tab
          _buildEditorTab(),
          // 预览Tab
          _buildPreviewTab(),
        ],
      ),
    );
  }

  Widget _buildEditorTab() {
    return Column(
      children: [
        // 工具栏
        Container(
          color: AppTheme.bgWhiteColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: TextEditorToolbar(
                    onBold:
                        () =>
                            _insertFormatText('**', '**', placeholder: '加粗文本'),
                    onItalic:
                        () => _insertFormatText('*', '*', placeholder: '斜体文本'),
                    onUnderline:
                        () => _insertFormatText(
                          '<u>',
                          '</u>',
                          placeholder: '下划线文本',
                        ),
                    onBulletList: () => _insertList(false),
                    onNumberList: () => _insertList(true),
                    onTable: _insertTable,
                    onTemplate: _changeTemplate,
                  ),
                ),
              ),
              // 标题显示切换按钮
              IconButton(
                icon: Icon(
                  _showTitle ? Icons.title : Icons.short_text,
                  color:
                      _showTitle
                          ? AppTheme.primaryColor
                          : AppTheme.textLightColor,
                ),
                onPressed: () {
                  setState(() {
                    _showTitle = !_showTitle;
                    if (!_showTitle) {
                      _titleController.clear(); // 隐藏标题时清空内容
                    }
                  });
                },
                tooltip: _showTitle ? '隐藏标题' : '显示标题',
              ),
            ],
          ),
        ),

        const Divider(height: 1),

        // 编辑区域
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // 标题输入
                if (_showTitle)
                  TextField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      hintText: '输入卡片标题...',
                      border: OutlineInputBorder(),
                      filled: true,
                      fillColor: AppTheme.bgWhiteColor,
                    ),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                const SizedBox(height: 16),

                // 内容输入
                Expanded(
                  child: TextField(
                    controller: _contentController,
                    decoration: const InputDecoration(
                      hintText:
                          '输入内容...\n\n支持 Markdown 格式：\n• **粗体**\n• *斜体*\n• • 无序列表\n• 1. 有序列表',
                      border: OutlineInputBorder(),
                      filled: true,
                      fillColor: AppTheme.bgWhiteColor,
                      alignLabelWithHint: true,
                    ),
                    maxLines: null,
                    expands: true,
                    textAlignVertical: TextAlignVertical.top,
                    onTap: _onContentSelectionChanged,
                    onChanged: (text) => _onContentSelectionChanged(),
                  ),
                ),
              ],
            ),
          ),
        ),

        // 格式工具栏（选中文本时显示）
        if (_showFormatToolbar)
          Container(
            color: AppTheme.bgWhiteColor,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: FormatToolbar(
              onBold: () => _insertFormatText('**', '**'),
              onItalic: () => _insertFormatText('*', '*'),
              onUnderline: () => _insertFormatText('<u>', '</u>'),
              onHighlight: () => _insertFormatText('<mark>', '</mark>'),
              onClose: () => setState(() => _showFormatToolbar = false),
            ),
          ),
      ],
    );
  }

  Widget _buildPreviewTab() {
    // 创建一个临时的TextCardModel用于预览
    final previewCard = TextCardModel(
      id: widget.existingCard?.id ?? 'preview',
      title: _showTitle ? _titleController.text.trim() : '',
      content:
          _contentController.text.trim().isEmpty
              ? '在编辑标签页输入内容以查看预览效果...'
              : _contentController.text.trim(),
      templateId: _selectedTemplate.id,
      createdAt: DateTime.now(),
    );

    return TextCardPreview(
      card: previewCard,
      template: _selectedTemplate,
      onTemplateChanged: (template) {
        setState(() {
          _selectedTemplate = template;
        });
      },
    );
  }
}
