import 'package:flutter/material.dart';
import '../../services/service_locator.dart';
import 'subscription_model.dart';
import 'subscription_service.dart';

/// 订阅调试页面 - 仅用于开发阶段
class SubscriptionDebugScreen extends StatefulWidget {
  /// 构造函数
  const SubscriptionDebugScreen({super.key});

  @override
  State<SubscriptionDebugScreen> createState() =>
      _SubscriptionDebugScreenState();
}

class _SubscriptionDebugScreenState extends State<SubscriptionDebugScreen> {
  final SubscriptionService _subscriptionService =
      ServiceLocator().subscriptionService;
  UserSubscription? _currentSubscription;
  bool _isLoading = true;

  // 选择的订阅类型和状态
  SubscriptionType _selectedType = SubscriptionType.free;
  SubscriptionStatus _selectedStatus = SubscriptionStatus.none;

  // 到期日期
  DateTime? _expiryDate;

  @override
  void initState() {
    super.initState();
    _loadCurrentSubscription();
  }

  /// 加载当前订阅信息
  Future<void> _loadCurrentSubscription() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final subscription = _subscriptionService.subscription;
      setState(() {
        _currentSubscription = subscription;
        _selectedType = subscription.type;
        _selectedStatus = subscription.status;
        _expiryDate = subscription.endDate;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('加载订阅信息失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 应用订阅更改
  Future<void> _applySubscriptionChanges() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 创建新的订阅对象
      final newSubscription = UserSubscription(
        type: _selectedType,
        status: _selectedStatus,
        startDate: DateTime.now(),
        endDate: _expiryDate,
        transactionId: 'debug_transaction',
        receiptData: 'debug_receipt',
      );

      // 保存订阅信息
      await _subscriptionService.updateSubscription(newSubscription);

      // 重新加载订阅信息
      await _loadCurrentSubscription();

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('订阅状态已更新')));
      }
    } catch (e) {
      debugPrint('更新订阅信息失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('更新订阅信息失败: $e')));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('订阅状态调试'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCurrentSubscription,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 当前订阅状态卡片
                    _buildCurrentSubscriptionCard(),

                    const SizedBox(height: 24),
                    const Divider(),
                    const SizedBox(height: 24),

                    // 修改订阅状态表单
                    _buildSubscriptionForm(),

                    const SizedBox(height: 32),

                    // 应用按钮
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _applySubscriptionChanges,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          '应用更改',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // 重置按钮
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () {
                          setState(() {
                            _selectedType = SubscriptionType.free;
                            _selectedStatus = SubscriptionStatus.none;
                            _expiryDate = null;
                          });
                        },
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          '重置为免费版',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  /// 构建当前订阅状态卡片
  Widget _buildCurrentSubscriptionCard() {
    if (_currentSubscription == null) {
      return const Card(
        child: Padding(padding: EdgeInsets.all(16), child: Text('无订阅信息')),
      );
    }

    final subscription = _currentSubscription!;

    // 确定状态颜色
    Color statusColor;
    if (subscription.isActive && subscription.isPaid) {
      statusColor = subscription.isLifetime ? Colors.amber : Colors.green;
    } else if (subscription.isExpired) {
      statusColor = Colors.orange;
    } else {
      statusColor = Colors.blue;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '当前订阅状态',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const Divider(),
            _buildInfoRow('订阅类型', _getSubscriptionTypeText(subscription.type)),
            _buildInfoRow(
              '订阅状态',
              _getSubscriptionStatusText(subscription.status),
            ),
            if (subscription.startDate != null)
              _buildInfoRow('开始日期', _formatDate(subscription.startDate!)),
            if (subscription.endDate != null)
              _buildInfoRow('到期日期', _formatDate(subscription.endDate!)),
            if (subscription.remainingDays != null)
              _buildInfoRow('剩余天数', '${subscription.remainingDays} 天'),

            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: statusColor.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    subscription.isActive
                        ? Icons.check_circle
                        : Icons.info_outline,
                    color: statusColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getStatusMessage(subscription),
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  /// 获取状态消息
  String _getStatusMessage(UserSubscription subscription) {
    if (subscription.isActive) {
      if (subscription.isLifetime) {
        return '终身订阅，永不过期';
      } else if (subscription.isPaid) {
        return '订阅有效，将于${_formatDate(subscription.endDate!)}到期';
      } else {
        return '免费版';
      }
    } else if (subscription.isExpired) {
      return '订阅已过期，请续订';
    } else {
      return '未订阅';
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 获取订阅类型文本
  String _getSubscriptionTypeText(SubscriptionType type) {
    switch (type) {
      case SubscriptionType.free:
        return '免费版';
      case SubscriptionType.monthly:
        return '月度订阅';
      case SubscriptionType.yearly:
        return '年度订阅';
      case SubscriptionType.lifetime:
        return '终身订阅';
    }
  }

  /// 获取订阅状态文本
  String _getSubscriptionStatusText(SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        return '有效';
      case SubscriptionStatus.expired:
        return '已过期';
      case SubscriptionStatus.none:
        return '未订阅';
      case SubscriptionStatus.pendingRestore:
        return '等待恢复';
      case SubscriptionStatus.pendingPurchase:
        return '等待购买';
    }
  }

  /// 构建订阅表单
  Widget _buildSubscriptionForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '修改订阅状态',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        // 订阅类型选择
        const Text('订阅类型', style: TextStyle(fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        _buildSubscriptionTypeSelector(),

        const SizedBox(height: 16),

        // 订阅状态选择
        const Text('订阅状态', style: TextStyle(fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        _buildSubscriptionStatusSelector(),

        const SizedBox(height: 16),

        // 到期日期选择
        const Text('到期日期', style: TextStyle(fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        _buildExpiryDateSelector(),
      ],
    );
  }

  /// 构建订阅类型选择器
  Widget _buildSubscriptionTypeSelector() {
    return Card(
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Column(
        children: [
          _buildRadioTile<SubscriptionType>(
            title: '免费版',
            value: SubscriptionType.free,
            groupValue: _selectedType,
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
                if (value == SubscriptionType.free) {
                  _selectedStatus = SubscriptionStatus.active;
                }
                if (value == SubscriptionType.lifetime) {
                  _expiryDate = null;
                }
              });
            },
          ),
          const Divider(height: 1),
          _buildRadioTile<SubscriptionType>(
            title: '月度订阅',
            value: SubscriptionType.monthly,
            groupValue: _selectedType,
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
                _expiryDate ??= DateTime.now().add(const Duration(days: 30));
              });
            },
          ),
          const Divider(height: 1),
          _buildRadioTile<SubscriptionType>(
            title: '年度订阅',
            value: SubscriptionType.yearly,
            groupValue: _selectedType,
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
                _expiryDate ??= DateTime.now().add(const Duration(days: 365));
              });
            },
          ),
          const Divider(height: 1),
          _buildRadioTile<SubscriptionType>(
            title: '终身订阅',
            value: SubscriptionType.lifetime,
            groupValue: _selectedType,
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
                _expiryDate = null; // 终身订阅没有到期日期
              });
            },
          ),
        ],
      ),
    );
  }

  /// 构建订阅状态选择器
  Widget _buildSubscriptionStatusSelector() {
    return Card(
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Column(
        children: [
          _buildRadioTile<SubscriptionStatus>(
            title: '有效',
            value: SubscriptionStatus.active,
            groupValue: _selectedStatus,
            onChanged: (value) {
              setState(() {
                _selectedStatus = value!;
              });
            },
          ),
          const Divider(height: 1),
          _buildRadioTile<SubscriptionStatus>(
            title: '已过期',
            value: SubscriptionStatus.expired,
            groupValue: _selectedStatus,
            onChanged:
                _selectedType == SubscriptionType.lifetime
                    ? null
                    : (value) {
                      setState(() {
                        _selectedStatus = value!;
                      });
                    },
          ),
          const Divider(height: 1),
          _buildRadioTile<SubscriptionStatus>(
            title: '未订阅',
            value: SubscriptionStatus.none,
            groupValue: _selectedStatus,
            onChanged: (value) {
              setState(() {
                _selectedStatus = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  /// 构建到期日期选择器
  Widget _buildExpiryDateSelector() {
    return Card(
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        onTap:
            _selectedType == SubscriptionType.lifetime
                ? null
                : () => _selectExpiryDate(),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _selectedType == SubscriptionType.lifetime
                    ? '终身订阅无到期日期'
                    : _expiryDate == null
                    ? '选择到期日期'
                    : _formatDate(_expiryDate!),
                style: TextStyle(
                  color:
                      _selectedType == SubscriptionType.lifetime
                          ? Colors.grey
                          : null,
                ),
              ),
              Icon(
                _selectedType == SubscriptionType.lifetime
                    ? Icons.all_inclusive
                    : Icons.calendar_today,
                color:
                    _selectedType == SubscriptionType.lifetime
                        ? Colors.grey
                        : null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 选择到期日期
  Future<void> _selectExpiryDate() async {
    if (_selectedType == SubscriptionType.lifetime) return;

    final DateTime now = DateTime.now();
    final DateTime initialDate =
        _expiryDate ?? now.add(const Duration(days: 30));

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: now,
      lastDate: now.add(const Duration(days: 365 * 10)), // 10年后
    );

    if (picked != null) {
      setState(() {
        _expiryDate = picked;
      });
    }
  }

  /// 构建单选按钮
  Widget _buildRadioTile<T>({
    required String title,
    required T value,
    required T groupValue,
    required ValueChanged<T?>? onChanged,
  }) {
    return InkWell(
      onTap: onChanged == null ? null : () => onChanged(value),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Radio<T>(
              value: value,
              groupValue: groupValue,
              onChanged: onChanged,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontWeight:
                    value == groupValue ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
