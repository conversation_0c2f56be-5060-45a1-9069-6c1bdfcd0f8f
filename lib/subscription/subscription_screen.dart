import 'dart:async';
import 'dart:math';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../services/service_locator.dart';
import 'subscription_model.dart';
import 'subscription_service.dart';

/// 订阅页面 - 展示订阅计划并处理购买
class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen>
    with SingleTickerProviderStateMixin {
  final SubscriptionService _subscriptionService =
      ServiceLocator().subscriptionService;
  late List<SubscriptionPlan> _plans;
  late SubscriptionPlan _selectedPlan;
  bool _isLoading = false;
  bool _isPurchasing = false;
  String? _errorMessage;

  // 动画控制器
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  // 滚动控制器
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    // 打印产品ID供调试
    _subscriptionService.printProductIdsForDebug();

    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOutCubic),
      ),
    );

    // 加载订阅计划
    _loadSubscriptionPlans();

    // 启动动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 加载订阅计划
  Future<void> _loadSubscriptionPlans() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 获取订阅计划
      _plans =
          _subscriptionService.plans
              .where((plan) => plan.type != SubscriptionType.free)
              .toList();

      // 默认选择年度计划
      _selectedPlan = _plans.firstWhere(
        (plan) => plan.type == SubscriptionType.yearly,
        orElse: () => _plans.first,
      );
    } catch (e) {
      debugPrint('加载订阅计划失败: $e');
      _errorMessage = '加载订阅计划失败，请稍后再试';
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 处理订阅购买
  Future<void> _handlePurchase() async {
    setState(() {
      _isPurchasing = true;
      _errorMessage = null;
    });

    try {
      // 如果之前有错误，先尝试排查
      if (_errorMessage != null) {
        debugPrint('之前有购买错误，尝试排查问题...');
        await _subscriptionService.troubleshootPurchaseIssues();
      }

      // 检查IAP状态并打印
      final status = await _subscriptionService.checkIAPServiceStatus();
      debugPrint(status);

      // 发起购买
      debugPrint('开始购买: ${_selectedPlan.id}');
      final success = await _subscriptionService.purchaseSubscription(
        _selectedPlan,
      );

      if (success) {
        // 购买成功，关闭页面并返回结果
        if (mounted) {
          Navigator.of(context).pop(true);
        }
      } else {
        setState(() {
          _errorMessage = '购买失败，请稍后再试。';
        });

        // 显示更详细的错误
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('提示：应用内购买目前处于开发阶段，实际购买功能将在上线后可用'),
              duration: Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('购买订阅失败: $e');
      setState(() {
        _errorMessage = '购买过程中出错: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isPurchasing = false;
        });
      }
    }
  }

  /// 处理恢复购买
  Future<void> _handleRestore() async {
    setState(() {
      _isPurchasing = true;
      _errorMessage = null;
    });

    try {
      final success = await _subscriptionService.restorePurchases();

      if (success) {
        // 恢复成功，关闭页面并返回结果
        if (mounted) {
          Navigator.of(context).pop(true);
        }
      } else {
        setState(() {
          _errorMessage = '未找到之前的购买记录';
        });
      }
    } catch (e) {
      debugPrint('恢复购买失败: $e');
      setState(() {
        _errorMessage = '恢复购买过程中出错: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isPurchasing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 设置状态栏为透明
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );

    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          TextButton(
            onPressed: _isPurchasing ? null : _handleRestore,
            child: const Text('恢复购买', style: TextStyle(color: Colors.white70)),
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: Colors.white),
              )
              : Stack(
                children: [
                  // 背景渐变
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          const Color(0xFF1A1A2E),
                          const Color(0xFF16213E),
                          const Color(0xFF0F3460),
                        ],
                      ),
                    ),
                  ),

                  // 背景装饰元素
                  Positioned.fill(child: _buildBackgroundDecoration()),

                  // 主内容
                  SafeArea(
                    child: AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return FadeTransition(
                          opacity: _fadeAnimation,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: ScaleTransition(
                              scale: _scaleAnimation,
                              child: child,
                            ),
                          ),
                        );
                      },
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        physics: const BouncingScrollPhysics(),
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 20),

                            // 开发模式提示
                            if (kDebugMode)
                              Container(
                                padding: const EdgeInsets.all(8),
                                margin: const EdgeInsets.only(bottom: 16),
                                decoration: BoxDecoration(
                                  color: Colors.orange.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.orange),
                                ),
                                child: const Row(
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      color: Colors.orange,
                                      size: 16,
                                    ),
                                    SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        '开发模式：订阅功能当前使用模拟数据，App Store Connect批准后将使用真实数据',
                                        style: TextStyle(
                                          color: Colors.orange,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                            // 标题
                            _buildHeader(),

                            const SizedBox(height: 40),

                            // 订阅计划选择
                            _buildSubscriptionPlans(),

                            const SizedBox(height: 30),

                            // 特性列表
                            _buildFeaturesList(),

                            const SizedBox(height: 30),

                            // 购买按钮
                            _buildPurchaseButton(),

                            const SizedBox(height: 16),

                            // 错误消息
                            if (_errorMessage != null)
                              Center(
                                child: Padding(
                                  padding: const EdgeInsets.only(bottom: 16),
                                  child: Text(
                                    _errorMessage!,
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: 14,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),

                            // 错误时显示诊断工具按钮
                            if (_errorMessage != null)
                              Center(
                                child: Padding(
                                  padding: const EdgeInsets.only(bottom: 16),
                                  child: TextButton.icon(
                                    onPressed: () async {
                                      final status =
                                          await _subscriptionService
                                              .checkIAPServiceStatus();
                                      if (!context.mounted) return;
                                      _showDiagnosticDialog(context, status);
                                    },
                                    icon: const Icon(Icons.search),
                                    label: const Text('诊断购买服务'),
                                    style: TextButton.styleFrom(
                                      foregroundColor: Colors.blue,
                                    ),
                                  ),
                                ),
                              ),

                            // 隐私政策和服务条款
                            _buildTermsAndPrivacy(),

                            const SizedBox(height: 30),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
    );
  }

  /// 构建背景装饰
  Widget _buildBackgroundDecoration() {
    return Stack(
      children: [
        // 顶部光晕
        Positioned(
          top: -100,
          right: -100,
          child: Container(
            width: 300,
            height: 300,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [Colors.purple.withAlpha(76), Colors.transparent],
              ),
            ),
          ),
        ),

        // 底部光晕
        Positioned(
          bottom: -150,
          left: -100,
          child: Container(
            width: 350,
            height: 350,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [Colors.blue.withAlpha(51), Colors.transparent],
              ),
            ),
          ),
        ),

        // 装饰性粒子
        ...List.generate(20, (index) {
          final random = index * 0.05;
          return Positioned(
            top: 100 + (index * 30) % 600,
            left: (index * 40) % 400,
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(
                    0,
                    5 *
                        (0.5 -
                            0.5 *
                                (0.5 +
                                    0.5 *
                                        sin(
                                          random +
                                              _animationController.value * 3,
                                        ))),
                  ),
                  child: Opacity(
                    opacity:
                        0.1 +
                        0.1 * sin(random + _animationController.value * 2),
                    child: Container(
                      width: 4 + (index % 4),
                      height: 4 + (index % 4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        }),

        // 模糊效果
        BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 0, sigmaY: 0),
          child: Container(color: Colors.transparent),
        ),
      ],
    );
  }

  /// 构建页面标题
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '升级到高级版',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            letterSpacing: 0.5,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          '解锁所有高级功能，提升您的AI体验',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white.withAlpha(179),
            letterSpacing: 0.3,
          ),
        ),
      ],
    );
  }

  /// 构建订阅计划选择
  Widget _buildSubscriptionPlans() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选择您的订阅计划',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white.withAlpha(230),
          ),
        ),
        const SizedBox(height: 16),
        ...List.generate(_plans.length, (index) {
          final plan = _plans[index];
          final isSelected = plan.id == _selectedPlan.id;

          // 计算折扣文本（仅对年度计划）
          String? discountText;
          if (plan.type == SubscriptionType.yearly) {
            final monthlyPlan = _plans.firstWhere(
              (p) => p.type == SubscriptionType.monthly,
              orElse: () => plan,
            );

            if (plan != monthlyPlan &&
                plan.monthlyPrice != null &&
                monthlyPlan.price > 0) {
              final discount =
                  (1 - (plan.monthlyPrice! / monthlyPlan.price)) * 100;
              if (discount > 0) {
                discountText = '省${discount.round()}%';
              }
            }
          }

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedPlan = plan;
              });
            },
            child: Container(
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color:
                      isSelected
                          ? plan.accentColor ?? Colors.blue
                          : Colors.white24,
                  width: isSelected ? 2 : 1,
                ),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors:
                      isSelected
                          ? [
                            (plan.accentColor ?? Colors.blue).withAlpha(51),
                            (plan.accentColor ?? Colors.blue).withAlpha(13),
                          ]
                          : [
                            Colors.white.withAlpha(13),
                            Colors.white.withAlpha(5),
                          ],
                ),
                boxShadow:
                    isSelected
                        ? [
                          BoxShadow(
                            color: (plan.accentColor ?? Colors.blue).withAlpha(
                              76,
                            ),
                            blurRadius: 20,
                            spreadRadius: -5,
                          ),
                        ]
                        : null,
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    // 选择指示器
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color:
                              isSelected
                                  ? plan.accentColor ?? Colors.blue
                                  : Colors.white38,
                          width: 2,
                        ),
                      ),
                      child:
                          isSelected
                              ? Center(
                                child: Container(
                                  width: 12,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: plan.accentColor ?? Colors.blue,
                                  ),
                                ),
                              )
                              : null,
                    ),
                    const SizedBox(width: 16),

                    // 计划信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                plan.name,
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      isSelected
                                          ? Colors.white
                                          : Colors.white70,
                                ),
                              ),
                              if (discountText != null) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.green,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    discountText,
                                    style: const TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            plan.description,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white.withAlpha(153),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                plan.formattedPrice,
                                style: TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      isSelected
                                          ? plan.accentColor ?? Colors.blue
                                          : Colors.white70,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                plan.periodText,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white.withAlpha(153),
                                ),
                              ),
                            ],
                          ),
                          if (plan.type != SubscriptionType.lifetime &&
                              plan.formattedMonthlyPrice != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              '相当于 ${plan.formattedMonthlyPrice}',
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.white.withAlpha(128),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  /// 构建特性列表
  Widget _buildFeaturesList() {
    // 获取所选计划的特性
    final features = _selectedPlan.features;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '包含的功能',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white.withAlpha(230),
          ),
        ),
        const SizedBox(height: 16),
        ...features.map((feature) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 2),
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: (_selectedPlan.accentColor ?? Colors.blue).withAlpha(
                      51,
                    ),
                  ),
                  child: Icon(
                    Icons.check,
                    size: 14,
                    color: _selectedPlan.accentColor ?? Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        feature.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        feature.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withAlpha(153),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  /// 构建购买按钮
  Widget _buildPurchaseButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isPurchasing ? null : _handlePurchase,
        style: ElevatedButton.styleFrom(
          backgroundColor: _selectedPlan.accentColor ?? Colors.blue,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child:
            _isPurchasing
                ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
                : Text(
                  '立即订阅 ${_selectedPlan.formattedPrice}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
      ),
    );
  }

  /// 构建隐私政策和服务条款
  Widget _buildTermsAndPrivacy() {
    return Center(
      child: Wrap(
        alignment: WrapAlignment.center,
        crossAxisAlignment: WrapCrossAlignment.center,
        children: [
          Text(
            '订阅即表示您同意我们的',
            style: TextStyle(fontSize: 12, color: Colors.white.withAlpha(102)),
          ),
          TextButton(
            onPressed: () {
              // 打开服务条款
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: const Text('服务条款', style: TextStyle(fontSize: 12)),
          ),
          Text(
            '和',
            style: TextStyle(fontSize: 12, color: Colors.white.withAlpha(102)),
          ),
          TextButton(
            onPressed: () {
              // 打开隐私政策
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: const Text('隐私政策', style: TextStyle(fontSize: 12)),
          ),
          Text(
            '。订阅会自动续费，可随时取消。',
            style: TextStyle(fontSize: 12, color: Colors.white.withAlpha(102)),
          ),
        ],
      ),
    );
  }

  /// 显示诊断对话框
  void _showDiagnosticDialog(BuildContext context, String status) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('诊断结果'),
          content: Text(status),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
        );
      },
    );
  }
}
