import 'package:flutter/material.dart' show Color;

/// 订阅类型枚举
enum SubscriptionType {
  /// 月度订阅
  monthly,

  /// 年度订阅
  yearly,

  /// 一次性买断
  lifetime,

  /// 免费
  free,
}

/// 订阅状态枚举
enum SubscriptionStatus {
  /// 有效
  active,

  /// 已过期
  expired,

  /// 未订阅
  none,

  /// 等待恢复
  pendingRestore,

  /// 等待购买
  pendingPurchase,
}

/// 订阅功能特权
class SubscriptionFeature {
  final String id;
  final String name;
  final String description;
  final bool isAvailableInFree;

  const SubscriptionFeature({
    required this.id,
    required this.name,
    required this.description,
    this.isAvailableInFree = false,
  });

  /// 从JSON创建
  factory SubscriptionFeature.fromJson(Map<String, dynamic> json) {
    return SubscriptionFeature(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      isAvailableInFree: json['isAvailableInFree'] as bool? ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isAvailableInFree': isAvailableInFree,
    };
  }
}

/// 订阅计划
class SubscriptionPlan {
  final String id;
  final String name;
  final String description;
  final SubscriptionType type;
  final double price;
  final String currencyCode;
  final String currencySymbol;
  final List<SubscriptionFeature> features;
  final Color? accentColor;
  final String? productId; // 应用商店产品ID

  const SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.price,
    required this.currencyCode,
    required this.currencySymbol,
    required this.features,
    this.accentColor,
    this.productId,
  });

  /// 从JSON创建
  factory SubscriptionPlan.fromJson(Map<String, dynamic> json) {
    // 解析特性列表
    final List<dynamic> featuresJson = json['features'] as List<dynamic>;
    final List<SubscriptionFeature> features =
        featuresJson
            .map(
              (featureJson) => SubscriptionFeature.fromJson(
                featureJson as Map<String, dynamic>,
              ),
            )
            .toList();

    // 解析订阅类型
    final typeStr = json['type'] as String;
    SubscriptionType type;
    switch (typeStr) {
      case 'monthly':
        type = SubscriptionType.monthly;
        break;
      case 'yearly':
        type = SubscriptionType.yearly;
        break;
      case 'lifetime':
        type = SubscriptionType.lifetime;
        break;
      case 'free':
        type = SubscriptionType.free;
        break;
      default:
        type = SubscriptionType.free;
    }

    // 解析颜色
    Color? accentColor;
    if (json['accentColor'] != null) {
      final colorValue = int.tryParse(json['accentColor'] as String);
      if (colorValue != null) {
        accentColor = Color(colorValue);
      }
    }

    return SubscriptionPlan(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: type,
      price: (json['price'] as num).toDouble(),
      currencyCode: json['currencyCode'] as String,
      currencySymbol: json['currencySymbol'] as String,
      features: features,
      accentColor: accentColor,
      productId: json['productId'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    String typeStr;
    switch (type) {
      case SubscriptionType.monthly:
        typeStr = 'monthly';
        break;
      case SubscriptionType.yearly:
        typeStr = 'yearly';
        break;
      case SubscriptionType.lifetime:
        typeStr = 'lifetime';
        break;
      case SubscriptionType.free:
        typeStr = 'free';
        break;
    }

    return {
      'id': id,
      'name': name,
      'description': description,
      'type': typeStr,
      'price': price,
      'currencyCode': currencyCode,
      'currencySymbol': currencySymbol,
      'features': features.map((feature) => feature.toJson()).toList(),
      'accentColor': accentColor?.toARGB32().toRadixString(16),
      'productId': productId,
    };
  }

  /// 格式化价格显示
  String get formattedPrice => '$currencySymbol$price';

  /// 获取周期文本
  String get periodText {
    switch (type) {
      case SubscriptionType.monthly:
        return '每月';
      case SubscriptionType.yearly:
        return '每年';
      case SubscriptionType.lifetime:
        return '终身';
      case SubscriptionType.free:
        return '免费';
    }
  }

  /// 获取每月价格（仅对月度和年度有效）
  double? get monthlyPrice {
    switch (type) {
      case SubscriptionType.monthly:
        return price;
      case SubscriptionType.yearly:
        return price / 12;
      default:
        return null;
    }
  }

  /// 格式化每月价格
  String? get formattedMonthlyPrice {
    final monthly = monthlyPrice;
    if (monthly == null) return null;
    return '$currencySymbol${monthly.toStringAsFixed(2)}/月';
  }
}

/// 用户订阅信息
class UserSubscription {
  final SubscriptionStatus status;
  final SubscriptionType type;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? transactionId;
  final String? receiptData;

  const UserSubscription({
    required this.status,
    required this.type,
    this.startDate,
    this.endDate,
    this.transactionId,
    this.receiptData,
  });

  /// 创建默认的免费订阅
  factory UserSubscription.free() {
    return const UserSubscription(
      status: SubscriptionStatus.active,
      type: SubscriptionType.free,
    );
  }

  /// 创建默认的未订阅状态
  factory UserSubscription.none() {
    return const UserSubscription(
      status: SubscriptionStatus.none,
      type: SubscriptionType.free,
    );
  }

  /// 检查订阅是否有效
  bool get isActive => status == SubscriptionStatus.active;

  /// 检查订阅是否已过期
  bool get isExpired => status == SubscriptionStatus.expired;

  /// 检查是否是付费订阅
  bool get isPaid => type != SubscriptionType.free;

  /// 检查是否是终身订阅
  bool get isLifetime => type == SubscriptionType.lifetime;

  /// 检查订阅是否有效（兼容旧代码）
  bool get isValid {
    if (status != SubscriptionStatus.active) {
      return false;
    }

    if (type == SubscriptionType.lifetime || type == SubscriptionType.free) {
      return true;
    }

    if (endDate == null) {
      return false;
    }

    return endDate!.isAfter(DateTime.now());
  }

  /// 获取剩余天数（如果适用）
  int? get remainingDays {
    if (endDate == null || !isActive) return null;
    final now = DateTime.now();
    return endDate!.difference(now).inDays;
  }

  /// 复制并更新属性
  UserSubscription copyWith({
    SubscriptionStatus? status,
    SubscriptionType? type,
    DateTime? startDate,
    DateTime? endDate,
    String? transactionId,
    String? receiptData,
  }) {
    return UserSubscription(
      status: status ?? this.status,
      type: type ?? this.type,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      transactionId: transactionId ?? this.transactionId,
      receiptData: receiptData ?? this.receiptData,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'status': status.index,
      'type': type.index,
      'startDate': startDate?.millisecondsSinceEpoch,
      'endDate': endDate?.millisecondsSinceEpoch,
      'transactionId': transactionId,
      'receiptData': receiptData,
    };
  }

  /// 从JSON创建
  factory UserSubscription.fromJson(Map<String, dynamic> json) {
    return UserSubscription(
      status: SubscriptionStatus.values[json['status'] ?? 0],
      type: SubscriptionType.values[json['type'] ?? 0],
      startDate:
          json['startDate'] != null
              ? DateTime.fromMillisecondsSinceEpoch(json['startDate'])
              : null,
      endDate:
          json['endDate'] != null
              ? DateTime.fromMillisecondsSinceEpoch(json['endDate'])
              : null,
      transactionId: json['transactionId'],
      receiptData: json['receiptData'],
    );
  }
}
