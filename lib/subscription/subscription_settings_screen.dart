import 'dart:math' as math;

import 'package:flutter/material.dart';

import '../config/app_theme.dart';
import 'subscription_screen.dart';

class SubscriptionSettingsScreen extends StatefulWidget {
  const SubscriptionSettingsScreen({super.key});

  @override
  State<SubscriptionSettingsScreen> createState() =>
      _SubscriptionSettingsScreenState();
}

class _SubscriptionSettingsScreenState extends State<SubscriptionSettingsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat();

    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.offset > 0 && !_isScrolled) {
      setState(() => _isScrolled = true);
    } else if (_scrollController.offset <= 0 && _isScrolled) {
      setState(() => _isScrolled = false);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      body: Stack(
        children: [
          // 背景渐变动画
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return CustomPaint(
                  painter: SubscriptionBackgroundPainter(
                    _animationController.value,
                  ),
                );
              },
            ),
          ),
          // 主内容
          SafeArea(
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                // 应用栏
                SliverAppBar(
                  expandedHeight: 160,
                  floating: false,
                  pinned: true,
                  elevation: _isScrolled ? 4 : 0,
                  backgroundColor:
                      _isScrolled
                          ? AppTheme.bgWhiteColor.withValues(alpha: 0.95)
                          : Colors.transparent,
                  leading: IconButton(
                    icon: Icon(
                      Icons.arrow_back,
                      color:
                          _isScrolled
                              ? AppTheme.textDarkColor
                              : AppTheme.bgWhiteColor,
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    title: Text(
                      '订阅管理',
                      style: TextStyle(
                        color:
                            _isScrolled
                                ? AppTheme.textDarkColor
                                : AppTheme.bgWhiteColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        // 顶部装饰图案
                        Positioned(
                          top: -30,
                          right: -30,
                          child: Transform.rotate(
                            angle: math.pi / 3,
                            child: Container(
                              width: 180,
                              height: 180,
                              decoration: BoxDecoration(
                                gradient: AppTheme.orangeGradient,
                                borderRadius: BorderRadius.circular(40),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          top: 50,
                          left: -50,
                          child: Transform.rotate(
                            angle: -math.pi / 5,
                            child: Opacity(
                              opacity: 0.7,
                              child: Container(
                                width: 120,
                                height: 120,
                                decoration: BoxDecoration(
                                  gradient: AppTheme.yellowGradient,
                                  borderRadius: BorderRadius.circular(30),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // 主要内容区域
                SliverPadding(
                  padding: const EdgeInsets.all(16.0),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      const SizedBox(height: 8),

                      // 当前订阅信息
                      _buildSubscriptionInfoCard(),

                      const SizedBox(height: 16),

                      // 订阅管理选项
                      _buildSectionHeader('订阅管理', Icons.subscriptions),

                      _buildActionCard(
                        '升级订阅',
                        '查看和购买更高级的订阅计划',
                        Icons.upgrade,
                        AppTheme.primaryGradient,
                        () {
                          // 导航到订阅购买页面
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SubscriptionScreen(),
                            ),
                          );
                        },
                      ),

                      _buildActionCard(
                        '恢复购买',
                        '恢复您之前的订阅购买',
                        Icons.restore,
                        AppTheme.blueGradient,
                        () {
                          // 恢复购买逻辑
                          _showRestoringDialog();
                        },
                      ),

                      const SizedBox(height: 16),

                      // 帮助和支持
                      _buildSectionHeader('帮助和支持', Icons.help),

                      _buildSimpleActionTile(
                        '常见问题',
                        '查看关于订阅的常见问题解答',
                        Icons.help_outline,
                        onTap: () {
                          // 打开FAQ页面
                        },
                      ),

                      _buildSimpleActionTile(
                        '联系客服',
                        '获取关于订阅的帮助',
                        Icons.contact_support,
                        onTap: () {
                          // 打开联系页面
                        },
                      ),

                      _buildSimpleActionTile(
                        '退款政策',
                        '了解我们的退款和取消政策',
                        Icons.policy,
                        onTap: () {
                          // 打开退款政策页面
                        },
                      ),

                      const SizedBox(height: 100), // 底部留白
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 显示恢复购买对话框
  void _showRestoringDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('正在恢复购买'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                const Text('正在与应用商店通信，请稍候...'),
              ],
            ),
          ),
    );

    // 模拟恢复过程
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('未找到可恢复的购买记录'),
          backgroundColor: AppTheme.redDark,
        ),
      );
    });
  }

  // 构建订阅信息卡片
  Widget _buildSubscriptionInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppTheme.orangeLight.withValues(alpha: 0.3),
              AppTheme.orangeLight.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: AppTheme.orangeGradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.workspace_premium,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '当前订阅',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.textMediumColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '免费版',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textDarkColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.greenLight,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Text(
                    '有效',
                    style: TextStyle(
                      color: AppTheme.greenDark,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            const Text(
              '可用功能',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.textDarkColor,
              ),
            ),
            const SizedBox(height: 12),
            _buildFeatureItem('基础处理', true),
            _buildFeatureItem('带水印导出', true),
            _buildFeatureItem('无限导出', false),
            _buildFeatureItem('批量处理', false),
            _buildFeatureItem('高级工具', false),
          ],
        ),
      ),
    );
  }

  // 构建功能项
  Widget _buildFeatureItem(String feature, bool isAvailable) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            isAvailable ? Icons.check_circle : Icons.cancel,
            color: isAvailable ? AppTheme.greenDark : AppTheme.textLightColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            feature,
            style: TextStyle(
              fontSize: 14,
              color:
                  isAvailable
                      ? AppTheme.textDarkColor
                      : AppTheme.textLightColor,
            ),
          ),
        ],
      ),
    );
  }

  // 构建区块标题
  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(left: 8, top: 8, bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppTheme.primaryColor),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
          ),
        ],
      ),
    );
  }

  // 构建操作卡片
  Widget _buildActionCard(
    String title,
    String description,
    IconData icon,
    LinearGradient gradient,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                gradient.colors.first.withValues(alpha: 0.1),
                gradient.colors.last.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  gradient: gradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: Colors.white, size: 22),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textDarkColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textMediumColor,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textLightColor,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建简单操作项
  Widget _buildSimpleActionTile(
    String title,
    String description,
    IconData icon, {
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 0,
      color: AppTheme.bgWhiteColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: const BorderSide(color: AppTheme.borderColor),
      ),
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(icon, color: AppTheme.primaryColor, size: 22),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppTheme.textDarkColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textMediumColor,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textLightColor,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 背景动画绘制器
class SubscriptionBackgroundPainter extends CustomPainter {
  final double animationValue;

  SubscriptionBackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..shader = LinearGradient(
            colors: [
              AppTheme.orangeDark.withValues(alpha: 0.2),
              AppTheme.yellowDark.withValues(alpha: 0.1),
            ],
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
          ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    final path = Path();

    // 绘制波浪背景
    for (int i = 0; i < 5; i++) {
      final wavePhase = i * 0.5 + animationValue * math.pi * 2;
      final waveAmplitude = 20.0 - i * 3.0;
      final waveFrequency = 0.02 + i * 0.005;

      path.reset();
      path.moveTo(0, size.height * 0.25 + math.sin(wavePhase) * waveAmplitude);

      for (double x = 0; x <= size.width; x += 5) {
        double y =
            size.height * 0.25 +
            math.sin(wavePhase + x * waveFrequency) * waveAmplitude;
        path.lineTo(x, y);
      }

      path.lineTo(size.width, size.height);
      path.lineTo(0, size.height);
      path.close();

      canvas.drawPath(
        path,
        paint..color = AppTheme.orangeDark.withValues(alpha: 0.05 - i * 0.01),
      );
    }

    // 添加浮动的圆点
    for (int i = 0; i < 12; i++) {
      final x =
          (size.width * 0.2) +
          math.sin(animationValue * 2 + i) * (size.width * 0.3);
      final y =
          (size.height * 0.1) +
          math.cos(animationValue * 2 + i * 0.8) * (size.height * 0.2);
      final radius = 2.0 + (math.sin(animationValue * 3 + i * 0.5) + 1) * 4;

      canvas.drawCircle(
        Offset(x, y),
        radius,
        Paint()
          ..color = AppTheme.orangeDark.withValues(alpha: 0.15)
          ..style = PaintingStyle.fill,
      );
    }
  }

  @override
  bool shouldRepaint(SubscriptionBackgroundPainter oldDelegate) =>
      oldDelegate.animationValue != animationValue;
}
