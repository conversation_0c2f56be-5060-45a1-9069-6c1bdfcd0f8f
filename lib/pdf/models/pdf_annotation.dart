import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

/// PDF注释类型
enum PdfAnnotationType {
  /// 文本批注
  text,

  /// 高亮
  highlight,

  /// 下划线
  underline,

  /// 删除线
  strikethrough,

  /// 手写笔记
  ink,

  /// 矩形
  rectangle,

  /// 椭圆
  oval,

  /// 箭头
  arrow,

  /// 图片
  image,
}

/// PDF注释基类
abstract class PdfAnnotation {
  /// 唯一标识
  final String id;

  /// 所属页码
  final int pageNumber;

  /// 注释类型
  final PdfAnnotationType type;

  /// 注释创建时间
  final DateTime createdAt;

  /// 注释修改时间
  DateTime modifiedAt;

  /// 注释颜色
  Color color;

  /// 注释在PDF中的位置
  Rect bounds;

  /// 注释透明度 (0.0-1.0)
  double opacity;

  /// 注释作者
  String author;

  PdfAnnotation({
    String? id,
    required this.pageNumber,
    required this.type,
    required this.bounds,
    Color? color,
    double? opacity,
    String? author,
    DateTime? createdAt,
    DateTime? modifiedAt,
  }) : id = id ?? const Uuid().v4(),
       color = color ?? Colors.yellow,
       opacity = opacity ?? 1.0,
       author = author ?? 'User',
       createdAt = createdAt ?? DateTime.now(),
       modifiedAt = modifiedAt ?? DateTime.now();

  /// 转换为Map对象，用于存储
  Map<String, dynamic> toMap();

  /// 从具体类实现的工厂方法
  factory PdfAnnotation.fromMap(Map<String, dynamic> map) {
    final type = PdfAnnotationType.values.firstWhere(
      (e) => e.toString() == map['type'],
      orElse: () => PdfAnnotationType.text,
    );

    // 注意：目前只实现了text和highlight两种注释类型
    // 其他类型将在后续开发中实现
    switch (type) {
      case PdfAnnotationType.text:
        return TextAnnotation.fromMap(map);
      case PdfAnnotationType.highlight:
        return HighlightAnnotation.fromMap(map);
      default:
        // 对于未实现的类型，暂时返回文本注释
        return TextAnnotation.fromMap(map);
    }
  }
}

/// 文本注释
class TextAnnotation extends PdfAnnotation {
  /// 注释内容
  String content;

  /// 文本大小
  double fontSize;

  /// 是否加粗
  bool isBold;

  /// 是否斜体
  bool isItalic;

  TextAnnotation({
    super.id,
    required super.pageNumber,
    required super.bounds,
    required this.content,
    this.fontSize = 14.0,
    this.isBold = false,
    this.isItalic = false,
    super.color,
    super.opacity,
    super.author,
    super.createdAt,
    super.modifiedAt,
  }) : super(
         type: PdfAnnotationType.text,
       );

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'pageNumber': pageNumber,
      'type': type.toString(),
      'bounds': {
        'left': bounds.left,
        'top': bounds.top,
        'right': bounds.right,
        'bottom': bounds.bottom,
      },
      'color': {
        'r': color.r,
        'g': color.g,
        'b': color.b,
        'a': color.a,
      },
      'opacity': opacity,
      'author': author,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'modifiedAt': modifiedAt.millisecondsSinceEpoch,
      'content': content,
      'fontSize': fontSize,
      'isBold': isBold,
      'isItalic': isItalic,
    };
  }

  factory TextAnnotation.fromMap(Map<String, dynamic> map) {
    final boundsMap = map['bounds'] as Map<String, dynamic>;
    final colorMap = map['color'] as Map<String, dynamic>;

    return TextAnnotation(
      id: map['id'],
      pageNumber: map['pageNumber'],
      bounds: Rect.fromLTRB(
        boundsMap['left'],
        boundsMap['top'],
        boundsMap['right'],
        boundsMap['bottom'],
      ),
      color: Color.fromARGB(
        colorMap['a'],
        colorMap['r'],
        colorMap['g'],
        colorMap['b'],
      ),
      opacity: map['opacity'],
      author: map['author'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      modifiedAt: DateTime.fromMillisecondsSinceEpoch(map['modifiedAt']),
      content: map['content'],
      fontSize: map['fontSize'],
      isBold: map['isBold'],
      isItalic: map['isItalic'],
    );
  }
}

/// 高亮注释
class HighlightAnnotation extends PdfAnnotation {
  /// 高亮文本内容
  String text;

  HighlightAnnotation({
    super.id,
    required super.pageNumber,
    required super.bounds,
    required this.text,
    Color? color,
    super.opacity,
    super.author,
    super.createdAt,
    super.modifiedAt,
  }) : super(
         type: PdfAnnotationType.highlight,
         color: color ?? Colors.yellow.withValues(alpha: 0.5),
       );

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'pageNumber': pageNumber,
      'type': type.toString(),
      'bounds': {
        'left': bounds.left,
        'top': bounds.top,
        'right': bounds.right,
        'bottom': bounds.bottom,
      },
      'color': {
        'r': color.r,
        'g': color.g,
        'b': color.b,
        'a': color.a,
      },
      'opacity': opacity,
      'author': author,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'modifiedAt': modifiedAt.millisecondsSinceEpoch,
      'text': text,
    };
  }

  factory HighlightAnnotation.fromMap(Map<String, dynamic> map) {
    final boundsMap = map['bounds'] as Map<String, dynamic>;
    final colorMap = map['color'] as Map<String, dynamic>;

    return HighlightAnnotation(
      id: map['id'],
      pageNumber: map['pageNumber'],
      bounds: Rect.fromLTRB(
        boundsMap['left'],
        boundsMap['top'],
        boundsMap['right'],
        boundsMap['bottom'],
      ),
      color: Color.fromARGB(
        colorMap['a'],
        colorMap['r'],
        colorMap['g'],
        colorMap['b'],
      ),
      opacity: map['opacity'],
      author: map['author'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      modifiedAt: DateTime.fromMillisecondsSinceEpoch(map['modifiedAt']),
      text: map['text'],
    );
  }
}

/// 其他注释类实现省略，根据实际需要再补充
/// 包括下划线、删除线、墨迹、矩形、椭圆、箭头和图片注释等
