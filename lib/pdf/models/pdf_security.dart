/// PDF安全设置模型
class PdfSecurity {
  /// 是否加密
  bool isEncrypted;

  /// 用户密码（用于打开PDF）
  String? userPassword;

  /// 所有者密码（用于修改权限）
  String? ownerPassword;

  /// 权限设置
  PdfPermissions permissions;

  PdfSecurity({
    this.isEncrypted = false,
    this.userPassword,
    this.ownerPassword,
    PdfPermissions? permissions,
  }) : permissions = permissions ?? PdfPermissions();

  /// 转换为Map对象，用于存储
  Map<String, dynamic> toMap() {
    return {
      'isEncrypted': isEncrypted,
      'userPassword': userPassword,
      'ownerPassword': ownerPassword,
      'permissions': permissions.toMap(),
    };
  }

  /// 从Map对象创建
  factory PdfSecurity.fromMap(Map<String, dynamic> map) {
    return PdfSecurity(
      isEncrypted: map['isEncrypted'] ?? false,
      userPassword: map['userPassword'],
      ownerPassword: map['ownerPassword'],
      permissions: PdfPermissions.fromMap(map['permissions'] ?? {}),
    );
  }

  /// 复制实例
  PdfSecurity copyWith({
    bool? isEncrypted,
    String? userPassword,
    String? ownerPassword,
    PdfPermissions? permissions,
  }) {
    return PdfSecurity(
      isEncrypted: isEncrypted ?? this.isEncrypted,
      userPassword: userPassword ?? this.userPassword,
      ownerPassword: ownerPassword ?? this.ownerPassword,
      permissions: permissions ?? this.permissions,
    );
  }
}

/// PDF权限设置
class PdfPermissions {
  /// 是否允许打印
  bool allowPrint;

  /// 是否允许复制内容
  bool allowCopy;

  /// 是否允许编辑文档
  bool allowEdit;

  /// 是否允许编辑注释
  bool allowEditAnnotations;

  /// 是否允许填写表单
  bool allowFillForms;

  /// 是否允许提取页面
  bool allowExtractPages;

  /// 是否允许辅助功能访问
  bool allowAccessibilityExtract;

  /// 是否允许装配文档（插入、删除、旋转页面）
  bool allowAssembleDocument;

  /// 是否允许高质量打印
  bool allowHighQualityPrint;

  PdfPermissions({
    this.allowPrint = true,
    this.allowCopy = true,
    this.allowEdit = true,
    this.allowEditAnnotations = true,
    this.allowFillForms = true,
    this.allowExtractPages = true,
    this.allowAccessibilityExtract = true,
    this.allowAssembleDocument = true,
    this.allowHighQualityPrint = true,
  });

  /// 转换为Map对象
  Map<String, dynamic> toMap() {
    return {
      'allowPrint': allowPrint,
      'allowCopy': allowCopy,
      'allowEdit': allowEdit,
      'allowEditAnnotations': allowEditAnnotations,
      'allowFillForms': allowFillForms,
      'allowExtractPages': allowExtractPages,
      'allowAccessibilityExtract': allowAccessibilityExtract,
      'allowAssembleDocument': allowAssembleDocument,
      'allowHighQualityPrint': allowHighQualityPrint,
    };
  }

  /// 从Map对象创建
  factory PdfPermissions.fromMap(Map<String, dynamic> map) {
    return PdfPermissions(
      allowPrint: map['allowPrint'] ?? true,
      allowCopy: map['allowCopy'] ?? true,
      allowEdit: map['allowEdit'] ?? true,
      allowEditAnnotations: map['allowEditAnnotations'] ?? true,
      allowFillForms: map['allowFillForms'] ?? true,
      allowExtractPages: map['allowExtractPages'] ?? true,
      allowAccessibilityExtract: map['allowAccessibilityExtract'] ?? true,
      allowAssembleDocument: map['allowAssembleDocument'] ?? true,
      allowHighQualityPrint: map['allowHighQualityPrint'] ?? true,
    );
  }

  /// 复制实例
  PdfPermissions copyWith({
    bool? allowPrint,
    bool? allowCopy,
    bool? allowEdit,
    bool? allowEditAnnotations,
    bool? allowFillForms,
    bool? allowExtractPages,
    bool? allowAccessibilityExtract,
    bool? allowAssembleDocument,
    bool? allowHighQualityPrint,
  }) {
    return PdfPermissions(
      allowPrint: allowPrint ?? this.allowPrint,
      allowCopy: allowCopy ?? this.allowCopy,
      allowEdit: allowEdit ?? this.allowEdit,
      allowEditAnnotations: allowEditAnnotations ?? this.allowEditAnnotations,
      allowFillForms: allowFillForms ?? this.allowFillForms,
      allowExtractPages: allowExtractPages ?? this.allowExtractPages,
      allowAccessibilityExtract:
          allowAccessibilityExtract ?? this.allowAccessibilityExtract,
      allowAssembleDocument:
          allowAssembleDocument ?? this.allowAssembleDocument,
      allowHighQualityPrint:
          allowHighQualityPrint ?? this.allowHighQualityPrint,
    );
  }

  /// 获取全部权限
  static PdfPermissions get all => PdfPermissions(
    allowPrint: true,
    allowCopy: true,
    allowEdit: true,
    allowEditAnnotations: true,
    allowFillForms: true,
    allowExtractPages: true,
    allowAccessibilityExtract: true,
    allowAssembleDocument: true,
    allowHighQualityPrint: true,
  );

  /// 获取只读权限
  static PdfPermissions get readOnly => PdfPermissions(
    allowPrint: false,
    allowCopy: false,
    allowEdit: false,
    allowEditAnnotations: false,
    allowFillForms: false,
    allowExtractPages: false,
    allowAccessibilityExtract: true,
    allowAssembleDocument: false,
    allowHighQualityPrint: false,
  );

  /// 获取基本权限（允许打印和复制）
  static PdfPermissions get basic => PdfPermissions(
    allowPrint: true,
    allowCopy: true,
    allowEdit: false,
    allowEditAnnotations: false,
    allowFillForms: false,
    allowExtractPages: false,
    allowAccessibilityExtract: true,
    allowAssembleDocument: false,
    allowHighQualityPrint: false,
  );
}
