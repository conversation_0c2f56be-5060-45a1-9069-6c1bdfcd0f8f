import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'pdf_security.dart';

/// PDF文档模型类
class PdfDocument {
  /// 唯一标识
  final String id;

  /// 文件名
  String fileName;

  /// 文件路径
  String filePath;

  /// 文件大小（字节）
  int fileSize;

  /// 文件创建时间
  final DateTime createdAt;

  /// 文件最后修改时间
  DateTime modifiedAt;

  /// 页数
  int? pageCount;

  /// 标签
  List<String> tags;

  /// 是否收藏
  bool isFavorite;

  /// 注释数量
  int annotationCount;

  /// 安全设置
  PdfSecurity security;

  PdfDocument({
    String? id,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    DateTime? createdAt,
    DateTime? modifiedAt,
    this.pageCount,
    List<String>? tags,
    this.isFavorite = false,
    this.annotationCount = 0,
    PdfSecurity? security,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       modifiedAt = modifiedAt ?? DateTime.now(),
       tags = tags ?? [],
       security = security ?? PdfSecurity();

  /// 从文件创建PDF文档模型
  static Future<PdfDocument> fromFile(File file) async {
    final stat = await file.stat();
    return PdfDocument(
      fileName: path.basename(file.path),
      filePath: file.path,
      fileSize: stat.size,
      createdAt: stat.changed,
      modifiedAt: stat.modified,
    );
  }

  /// 转换为Map对象，用于存储
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'fileName': fileName,
      'filePath': filePath,
      'fileSize': fileSize,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'modifiedAt': modifiedAt.millisecondsSinceEpoch,
      'pageCount': pageCount,
      'tags': tags,
      'isFavorite': isFavorite,
      'annotationCount': annotationCount,
      'security': security.toMap(),
    };
  }

  /// 从Map对象创建PDF文档模型，用于读取存储的数据
  factory PdfDocument.fromMap(Map<String, dynamic> map) {
    return PdfDocument(
      id: map['id'],
      fileName: map['fileName'],
      filePath: map['filePath'],
      fileSize: map['fileSize'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      modifiedAt: DateTime.fromMillisecondsSinceEpoch(map['modifiedAt']),
      pageCount: map['pageCount'],
      tags: List<String>.from(map['tags'] ?? []),
      isFavorite: map['isFavorite'] ?? false,
      annotationCount: map['annotationCount'] ?? 0,
      security: PdfSecurity.fromMap(map['security'] ?? {}),
    );
  }

  /// 更新文件信息
  Future<void> updateFileInfo() async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        final stat = await file.stat();
        fileSize = stat.size;
        modifiedAt = stat.modified;
      }
    } catch (e) {
      debugPrint('更新文件信息失败: $e');
    }
  }

  /// 检查文件是否存在
  Future<bool> exists() async {
    try {
      return await File(filePath).exists();
    } catch (e) {
      return false;
    }
  }

  /// 格式化文件大小显示
  String get formattedSize {
    const units = ['B', 'KB', 'MB', 'GB'];
    var size = fileSize.toDouble();
    var unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(2)} ${units[unitIndex]}';
  }

  /// 是否加密
  bool get isEncrypted => security.isEncrypted;

  /// 是否允许打印
  bool get allowPrint => security.permissions.allowPrint;

  /// 是否允许复制
  bool get allowCopy => security.permissions.allowCopy;

  /// 是否允许编辑
  bool get allowEdit => security.permissions.allowEdit;

  /// 设置加密
  void setEncryption({
    required bool isEncrypted,
    String? userPassword,
    String? ownerPassword,
  }) {
    security.isEncrypted = isEncrypted;
    security.userPassword = userPassword;
    security.ownerPassword = ownerPassword;
  }

  /// 设置权限
  void setPermissions(PdfPermissions permissions) {
    security.permissions = permissions;
  }

  /// 获取安全状态描述
  String get securityStatus {
    if (isEncrypted) {
      return '已加密';
    }
    if (!allowPrint && !allowCopy && !allowEdit) {
      return '只读';
    }
    if (!allowPrint || !allowCopy || !allowEdit) {
      return '受限';
    }
    return '开放';
  }
}
