import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import 'models/pdf_document.dart';
import 'models/pdf_security.dart';
import 'pdf_service.dart';

/// PDF安全设置界面
class PdfSecurityScreen extends StatefulWidget {
  final PdfDocument document;

  const PdfSecurityScreen({super.key, required this.document});

  @override
  State<PdfSecurityScreen> createState() => _PdfSecurityScreenState();
}

class _PdfSecurityScreenState extends State<PdfSecurityScreen> {
  /// PDF服务
  final _pdfService = PdfService();

  /// 用户密码控制器
  final _userPasswordController = TextEditingController();

  /// 所有者密码控制器
  final _ownerPasswordController = TextEditingController();

  /// 当前密码控制器（用于解密）
  final _currentPasswordController = TextEditingController();

  /// 是否正在加载
  bool _isLoading = false;

  /// 是否显示用户密码
  bool _showUserPassword = false;

  /// 是否显示所有者密码
  bool _showOwnerPassword = false;

  /// 是否显示当前密码
  bool _showCurrentPassword = false;

  /// 权限设置
  late PdfPermissions _permissions;

  /// 是否加密
  bool _isEncrypted = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  /// 初始化数据
  void _initializeData() {
    _permissions = widget.document.security.permissions.copyWith();
    _isEncrypted = widget.document.security.isEncrypted;
  }

  /// 执行加密
  Future<void> _encryptPdf() async {
    if (_userPasswordController.text.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入用户密码')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _pdfService.encryptPdf(
        widget.document.id,
        _userPasswordController.text,
        _ownerPasswordController.text.isNotEmpty
            ? _ownerPasswordController.text
            : null,
        _permissions,
      );

      if (success) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('PDF加密成功')));
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('PDF加密失败')));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('加密失败: $e')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 执行解密
  Future<void> _decryptPdf() async {
    if (_currentPasswordController.text.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入当前密码')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _pdfService.decryptPdf(
        widget.document.id,
        _currentPasswordController.text,
      );

      if (success) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('PDF解密成功')));
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('PDF解密失败，请检查密码')));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('解密失败: $e')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 设置权限
  Future<void> _setPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _pdfService.setPdfPermissions(
        widget.document.id,
        _permissions,
        _ownerPasswordController.text.isNotEmpty
            ? _ownerPasswordController.text
            : null,
      );

      if (success) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('权限设置成功')));
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('权限设置失败')));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('设置失败: $e')));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _userPasswordController.dispose();
    _ownerPasswordController.dispose();
    _currentPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF安全设置'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildBody(),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDocumentInfo(),
          const SizedBox(height: 24),
          if (_isEncrypted) ...[
            _buildDecryptSection(),
            const SizedBox(height: 24),
          ] else ...[
            _buildEncryptSection(),
            const SizedBox(height: 24),
            _buildPermissionsSection(),
            const SizedBox(height: 24),
            _buildPresetPermissions(),
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  /// 构建文档信息
  Widget _buildDocumentInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('文档信息', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.picture_as_pdf, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.document.fileName,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.security, size: 20),
                const SizedBox(width: 8),
                Text(
                  '状态: ${widget.document.securityStatus}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建解密部分
  Widget _buildDecryptSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('解密PDF', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            Text(
              '该PDF已加密，请输入密码进行解密',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _currentPasswordController,
              obscureText: !_showCurrentPassword,
              decoration: InputDecoration(
                labelText: '当前密码',
                hintText: '请输入当前密码',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showCurrentPassword
                        ? Icons.visibility_off
                        : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showCurrentPassword = !_showCurrentPassword;
                    });
                  },
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _decryptPdf,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('解密'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建加密部分
  Widget _buildEncryptSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('加密设置', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            TextFormField(
              controller: _userPasswordController,
              obscureText: !_showUserPassword,
              decoration: InputDecoration(
                labelText: '用户密码 *',
                hintText: '用于打开PDF的密码',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showUserPassword ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showUserPassword = !_showUserPassword;
                    });
                  },
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _ownerPasswordController,
              obscureText: !_showOwnerPassword,
              decoration: InputDecoration(
                labelText: '所有者密码（可选）',
                hintText: '用于修改权限的密码',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showOwnerPassword
                        ? Icons.visibility_off
                        : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showOwnerPassword = !_showOwnerPassword;
                    });
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建权限设置部分
  Widget _buildPermissionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('权限设置', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            _buildPermissionSwitch(
              '允许打印',
              '允许用户打印PDF文档',
              _permissions.allowPrint,
              (value) {
                setState(() {
                  _permissions.allowPrint = value;
                });
              },
            ),
            _buildPermissionSwitch(
              '允许复制',
              '允许用户复制PDF内容',
              _permissions.allowCopy,
              (value) {
                setState(() {
                  _permissions.allowCopy = value;
                });
              },
            ),
            _buildPermissionSwitch(
              '允许编辑',
              '允许用户编辑PDF文档',
              _permissions.allowEdit,
              (value) {
                setState(() {
                  _permissions.allowEdit = value;
                });
              },
            ),
            _buildPermissionSwitch(
              '允许编辑注释',
              '允许用户添加或编辑注释',
              _permissions.allowEditAnnotations,
              (value) {
                setState(() {
                  _permissions.allowEditAnnotations = value;
                });
              },
            ),
            _buildPermissionSwitch(
              '允许填写表单',
              '允许用户填写表单字段',
              _permissions.allowFillForms,
              (value) {
                setState(() {
                  _permissions.allowFillForms = value;
                });
              },
            ),
            _buildPermissionSwitch(
              '允许提取页面',
              '允许用户提取页面内容',
              _permissions.allowExtractPages,
              (value) {
                setState(() {
                  _permissions.allowExtractPages = value;
                });
              },
            ),
            _buildPermissionSwitch(
              '允许装配文档',
              '允许用户插入、删除、旋转页面',
              _permissions.allowAssembleDocument,
              (value) {
                setState(() {
                  _permissions.allowAssembleDocument = value;
                });
              },
            ),
            _buildPermissionSwitch(
              '允许高质量打印',
              '允许用户高质量打印',
              _permissions.allowHighQualityPrint,
              (value) {
                setState(() {
                  _permissions.allowHighQualityPrint = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建权限开关
  Widget _buildPermissionSwitch(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: AppTheme.primaryColor,
    );
  }

  /// 构建预设权限
  Widget _buildPresetPermissions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('预设权限', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildPresetButton('全部权限', Icons.lock_open, () {
                  setState(() {
                    _permissions = PdfPermissions.all;
                  });
                }),
                _buildPresetButton('基本权限', Icons.lock_outline, () {
                  setState(() {
                    _permissions = PdfPermissions.basic;
                  });
                }),
                _buildPresetButton('只读', Icons.lock, () {
                  setState(() {
                    _permissions = PdfPermissions.readOnly;
                  });
                }),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建预设按钮
  Widget _buildPresetButton(
    String label,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
        foregroundColor: AppTheme.primaryColor,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [Icon(icon), const SizedBox(height: 4), Text(label)],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _setPermissions,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
              foregroundColor: AppTheme.primaryColor,
            ),
            child: const Text('仅设置权限'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _encryptPdf,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('加密并设置权限'),
          ),
        ),
      ],
    );
  }
}
