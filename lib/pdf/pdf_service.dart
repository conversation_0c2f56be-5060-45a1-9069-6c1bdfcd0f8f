import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart' as syncfusion;

import '../config/constants.dart';
import 'models/pdf_annotation.dart';
import 'models/pdf_document.dart';
import 'models/pdf_security.dart';

/// PDF处理服务类
class PdfService {
  /// 单例实例
  static final PdfService _instance = PdfService._internal();

  /// 获取单例
  factory PdfService() => _instance;

  /// 内部构造函数
  PdfService._internal();

  /// 用于存储PDF文档信息的Hive盒子
  late Box _pdfBox;

  /// 用于存储注释的Hive盒子
  late Box _annotationsBox;

  /// 初始化服务
  Future<void> initialize() async {
    // 打开Hive盒子
    _pdfBox = await Hive.openBox('${AppConstants.mainBoxName}_pdf');
    _annotationsBox = await Hive.openBox(
      '${AppConstants.mainBoxName}_pdf_annotations',
    );

    // 检查并请求必要的权限
    await _checkPermissions();

    // 创建必要的目录
    await _createDirectories();
  }

  /// 检查并请求必要的权限
  Future<void> _checkPermissions() async {
    if (Platform.isAndroid || Platform.isIOS) {
      final status = await Permission.storage.status;
      if (!status.isGranted) {
        await Permission.storage.request();
      }
    }
  }

  /// 创建必要的目录
  Future<void> _createDirectories() async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final pdfDir = Directory('${dir.path}/pdfs');
      if (!await pdfDir.exists()) {
        await pdfDir.create(recursive: true);
      }
    } catch (e) {
      debugPrint('创建PDF目录失败: $e');
    }
  }

  /// 获取PDF保存目录
  Future<String> get _pdfDirectory async {
    final dir = await getApplicationDocumentsDirectory();
    return '${dir.path}/pdfs';
  }

  /// 获取所有保存的PDF文档
  Future<List<PdfDocument>> getAllDocuments() async {
    final List<PdfDocument> documents = [];

    try {
      final keys = _pdfBox.keys;
      for (final key in keys) {
        final map = _pdfBox.get(key);
        if (map != null) {
          final doc = PdfDocument.fromMap(Map<String, dynamic>.from(map));
          // 检查文件是否存在
          if (await doc.exists()) {
            documents.add(doc);
          }
        }
      }
    } catch (e) {
      debugPrint('获取PDF文档列表失败: $e');
    }

    // 按修改时间排序，最新的排在前面
    documents.sort((a, b) => b.modifiedAt.compareTo(a.modifiedAt));

    return documents;
  }

  /// 保存PDF文档信息
  Future<void> saveDocument(PdfDocument document) async {
    try {
      await _pdfBox.put(document.id, document.toMap());
    } catch (e) {
      debugPrint('保存PDF文档信息失败: $e');
    }
  }

  /// 删除PDF文档
  Future<bool> deleteDocument(String documentId) async {
    try {
      final map = _pdfBox.get(documentId);
      if (map != null) {
        final doc = PdfDocument.fromMap(Map<String, dynamic>.from(map));
        final file = File(doc.filePath);
        if (await file.exists()) {
          await file.delete();
        }

        // 删除文档相关的所有注释
        final annotationKeys = _annotationsBox.keys.where(
          (key) => key.toString().startsWith('$documentId:'),
        );
        for (final key in annotationKeys) {
          await _annotationsBox.delete(key);
        }

        // 删除文档记录
        await _pdfBox.delete(documentId);

        return true;
      }
    } catch (e) {
      debugPrint('删除PDF文档失败: $e');
    }

    return false;
  }

  /// 导入PDF文件
  Future<PdfDocument?> importPdf() async {
    try {
      // 使用文件选择器选择PDF文件
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final filePath = file.path;

        if (filePath != null) {
          // 复制文件到应用目录
          final pdfDir = await _pdfDirectory;
          final fileName = path.basename(filePath);
          final targetPath = '$pdfDir/$fileName';

          // 检查是否已存在同名文件
          if (await File(targetPath).exists()) {
            // 生成唯一文件名
            final nameWithoutExt = path.basenameWithoutExtension(fileName);
            final extension = path.extension(fileName);
            final newFileName =
                '${nameWithoutExt}_${DateTime.now().millisecondsSinceEpoch}$extension';
            final newTargetPath = '$pdfDir/$newFileName';

            await File(filePath).copy(newTargetPath);

            // 创建PDF文档模型
            final importedFile = File(newTargetPath);
            final document = await PdfDocument.fromFile(importedFile);

            // 获取页数
            document.pageCount = await _getPageCount(importedFile);

            // 保存文档信息
            await saveDocument(document);

            return document;
          } else {
            await File(filePath).copy(targetPath);

            // 创建PDF文档模型
            final importedFile = File(targetPath);
            final document = await PdfDocument.fromFile(importedFile);

            // 获取页数
            document.pageCount = await _getPageCount(importedFile);

            // 保存文档信息
            await saveDocument(document);

            return document;
          }
        }
      }
    } catch (e) {
      debugPrint('导入PDF文件失败: $e');
    }

    return null;
  }

  /// 获取PDF文件页数
  Future<int> _getPageCount(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final document = syncfusion.PdfDocument(inputBytes: bytes);
      final pageCount = document.pages.count;
      document.dispose();
      return pageCount;
    } catch (e) {
      debugPrint('获取PDF页数失败: $e');
      return 0;
    }
  }

  /// 保存注释
  Future<void> saveAnnotation(
    String documentId,
    PdfAnnotation annotation,
  ) async {
    try {
      final key = '$documentId:${annotation.id}';
      await _annotationsBox.put(key, annotation.toMap());

      // 更新文档的注释计数
      final docMap = _pdfBox.get(documentId);
      if (docMap != null) {
        final doc = PdfDocument.fromMap(Map<String, dynamic>.from(docMap));
        doc.annotationCount += 1;
        await saveDocument(doc);
      }
    } catch (e) {
      debugPrint('保存PDF注释失败: $e');
    }
  }

  /// 获取文档的所有注释
  Future<List<PdfAnnotation>> getAnnotations(String documentId) async {
    final List<PdfAnnotation> annotations = [];

    try {
      final annotationKeys = _annotationsBox.keys.where(
        (key) => key.toString().startsWith('$documentId:'),
      );

      for (final key in annotationKeys) {
        final map = _annotationsBox.get(key);
        if (map != null) {
          final annotation = PdfAnnotation.fromMap(
            Map<String, dynamic>.from(map),
          );
          annotations.add(annotation);
        }
      }
    } catch (e) {
      debugPrint('获取PDF注释失败: $e');
    }

    return annotations;
  }

  /// 删除注释
  Future<void> deleteAnnotation(String documentId, String annotationId) async {
    try {
      final key = '$documentId:$annotationId';
      await _annotationsBox.delete(key);

      // 更新文档的注释计数
      final docMap = _pdfBox.get(documentId);
      if (docMap != null) {
        final doc = PdfDocument.fromMap(Map<String, dynamic>.from(docMap));
        if (doc.annotationCount > 0) {
          doc.annotationCount -= 1;
          await saveDocument(doc);
        }
      }
    } catch (e) {
      debugPrint('删除PDF注释失败: $e');
    }
  }

  /// 合并PDF文件
  Future<PdfDocument?> mergePdfs(List<PdfDocument> documents) async {
    if (documents.isEmpty) return null;

    try {
      // 简单实现：直接复制第一个文档
      // 注：真实的PDF合并应该使用库的更高级功能
      final pdfDir = await _pdfDirectory;
      final fileName = 'merged_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final filePath = '$pdfDir/$fileName';

      // 使用第一个文档作为基础
      final firstDoc = documents.first;
      if (await File(firstDoc.filePath).exists()) {
        await File(firstDoc.filePath).copy(filePath);

        // 创建PDF文档模型
        final mergedFile = File(filePath);
        final document = await PdfDocument.fromFile(mergedFile);

        // 设置页数（这里仅使用第一个文档的页数，实际合并功能应累加所有文档页数）
        document.pageCount = await _getPageCount(mergedFile);

        // 保存文档信息
        await saveDocument(document);

        return document;
      }
    } catch (e) {
      debugPrint('合并PDF文件失败: $e');
    }

    return null;
  }

  /// 加密PDF文件
  Future<bool> encryptPdf(
    String documentId,
    String? userPassword,
    String? ownerPassword,
    PdfPermissions permissions,
  ) async {
    try {
      final docMap = _pdfBox.get(documentId);
      if (docMap == null) {
        debugPrint('文档未找到: $documentId');
        return false;
      }

      final doc = PdfDocument.fromMap(Map<String, dynamic>.from(docMap));
      final file = File(doc.filePath);
      
      if (!await file.exists()) {
        debugPrint('文件不存在: ${doc.filePath}');
        return false;
      }

      // 读取PDF文件
      final bytes = await file.readAsBytes();
      final pdfDocument = syncfusion.PdfDocument(inputBytes: bytes);

      // 设置安全选项
      final security = pdfDocument.security;
      
      // 设置用户密码
      if (userPassword != null && userPassword.isNotEmpty) {
        security.userPassword = userPassword;
      }
      
      // 设置所有者密码
      if (ownerPassword != null && ownerPassword.isNotEmpty) {
        security.ownerPassword = ownerPassword;
      }

      // 设置权限
      security.permissions.clear();
      security.permissions.addAll(_convertPermissions(permissions));

      // 保存加密后的文档
      final encryptedBytes = await pdfDocument.save();
      pdfDocument.dispose();

      // 创建加密文件
      final pdfDir = await _pdfDirectory;
      final encryptedFileName = 'encrypted_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final encryptedFilePath = '$pdfDir/$encryptedFileName';
      
      final encryptedFile = File(encryptedFilePath);
      await encryptedFile.writeAsBytes(encryptedBytes);

      // 更新文档信息
      doc.filePath = encryptedFilePath;
      doc.fileName = encryptedFileName;
      doc.setEncryption(
        isEncrypted: true,
        userPassword: userPassword,
        ownerPassword: ownerPassword,
      );
      doc.setPermissions(permissions);
      
      await doc.updateFileInfo();
      await saveDocument(doc);

      // 删除原文件
      await file.delete();

      return true;
    } catch (e) {
      debugPrint('加密PDF文件失败: $e');
      return false;
    }
  }

  /// 解密PDF文件
  Future<bool> decryptPdf(
    String documentId,
    String password,
  ) async {
    try {
      final docMap = _pdfBox.get(documentId);
      if (docMap == null) {
        debugPrint('文档未找到: $documentId');
        return false;
      }

      final doc = PdfDocument.fromMap(Map<String, dynamic>.from(docMap));
      final file = File(doc.filePath);
      
      if (!await file.exists()) {
        debugPrint('文件不存在: ${doc.filePath}');
        return false;
      }

      // 读取加密的PDF文件
      final bytes = await file.readAsBytes();
      final pdfDocument = syncfusion.PdfDocument(inputBytes: bytes, password: password);

      // 保存解密后的文档
      final decryptedBytes = await pdfDocument.save();
      pdfDocument.dispose();

      // 创建解密文件
      final pdfDir = await _pdfDirectory;
      final decryptedFileName = 'decrypted_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final decryptedFilePath = '$pdfDir/$decryptedFileName';
      
      final decryptedFile = File(decryptedFilePath);
      await decryptedFile.writeAsBytes(decryptedBytes);

      // 更新文档信息
      doc.filePath = decryptedFilePath;
      doc.fileName = decryptedFileName;
      doc.setEncryption(
        isEncrypted: false,
        userPassword: null,
        ownerPassword: null,
      );
      doc.setPermissions(PdfPermissions.all);
      
      await doc.updateFileInfo();
      await saveDocument(doc);

      // 删除加密文件
      await file.delete();

      return true;
    } catch (e) {
      debugPrint('解密PDF文件失败: $e');
      return false;
    }
  }

  /// 设置PDF权限
  Future<bool> setPdfPermissions(
    String documentId,
    PdfPermissions permissions,
    String? ownerPassword,
  ) async {
    try {
      final docMap = _pdfBox.get(documentId);
      if (docMap == null) {
        debugPrint('文档未找到: $documentId');
        return false;
      }

      final doc = PdfDocument.fromMap(Map<String, dynamic>.from(docMap));
      final file = File(doc.filePath);
      
      if (!await file.exists()) {
        debugPrint('文件不存在: ${doc.filePath}');
        return false;
      }

      // 读取PDF文件
      final bytes = await file.readAsBytes();
      final pdfDocument = syncfusion.PdfDocument(inputBytes: bytes);

      // 设置权限
      final security = pdfDocument.security;
      
      // 设置所有者密码（如果提供）
      if (ownerPassword != null && ownerPassword.isNotEmpty) {
        security.ownerPassword = ownerPassword;
      }

      // 设置权限
      security.permissions.clear();
      security.permissions.addAll(_convertPermissions(permissions));

      // 保存修改后的文档
      final modifiedBytes = await pdfDocument.save();
      pdfDocument.dispose();

      // 创建修改后的文件
      final pdfDir = await _pdfDirectory;
      final modifiedFileName = 'modified_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final modifiedFilePath = '$pdfDir/$modifiedFileName';
      
      final modifiedFile = File(modifiedFilePath);
      await modifiedFile.writeAsBytes(modifiedBytes);

      // 更新文档信息
      doc.filePath = modifiedFilePath;
      doc.fileName = modifiedFileName;
      doc.setPermissions(permissions);
      if (ownerPassword != null && ownerPassword.isNotEmpty) {
        doc.security.ownerPassword = ownerPassword;
      }
      
      await doc.updateFileInfo();
      await saveDocument(doc);

      // 删除原文件
      await file.delete();

      return true;
    } catch (e) {
      debugPrint('设置PDF权限失败: $e');
      return false;
    }
  }

  /// 验证PDF密码
  Future<bool> verifyPdfPassword(
    String documentId,
    String password,
  ) async {
    try {
      final docMap = _pdfBox.get(documentId);
      if (docMap == null) {
        return false;
      }

      final doc = PdfDocument.fromMap(Map<String, dynamic>.from(docMap));
      final file = File(doc.filePath);
      
      if (!await file.exists()) {
        return false;
      }

      // 尝试用密码打开PDF
      final bytes = await file.readAsBytes();
      final pdfDocument = syncfusion.PdfDocument(inputBytes: bytes, password: password);
      pdfDocument.dispose();

      return true;
    } catch (e) {
      debugPrint('验证PDF密码失败: $e');
      return false;
    }
  }

  /// 将权限模型转换为Syncfusion权限
  List<syncfusion.PdfPermissionsFlags> _convertPermissions(PdfPermissions permissions) {
    final List<syncfusion.PdfPermissionsFlags> flags = [];

    if (permissions.allowPrint) {
      flags.add(syncfusion.PdfPermissionsFlags.print);
    }
    if (permissions.allowCopy) {
      flags.add(syncfusion.PdfPermissionsFlags.copyContent);
    }
    if (permissions.allowEdit) {
      flags.add(syncfusion.PdfPermissionsFlags.editContent);
    }
    if (permissions.allowEditAnnotations) {
      flags.add(syncfusion.PdfPermissionsFlags.editAnnotations);
    }
    if (permissions.allowFillForms) {
      flags.add(syncfusion.PdfPermissionsFlags.fillFields);
    }
    if (permissions.allowExtractPages) {
      flags.add(syncfusion.PdfPermissionsFlags.copyContent);
    }
    if (permissions.allowAccessibilityExtract) {
      flags.add(syncfusion.PdfPermissionsFlags.copyContent);
    }
    if (permissions.allowAssembleDocument) {
      flags.add(syncfusion.PdfPermissionsFlags.assembleDocument);
    }
    if (permissions.allowHighQualityPrint) {
      flags.add(syncfusion.PdfPermissionsFlags.fullQualityPrint);
    }

    return flags;
  }
}
