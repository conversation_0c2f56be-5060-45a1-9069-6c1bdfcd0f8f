import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../../config/app_theme.dart';

/// 背景动画绘制器
class BackgroundPainter extends CustomPainter {
  final double animationValue;

  BackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..shader = LinearGradient(
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.2),
              AppTheme.secondaryColor.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    final path = Path();

    // 绘制波浪背景
    for (int i = 0; i < 5; i++) {
      final wavePhase = i * 0.5 + animationValue * math.pi * 2;
      final waveAmplitude = 25.0 - i * 4.0;
      final waveFrequency = 0.015 + i * 0.01;

      path.reset();
      path.moveTo(0, size.height * 0.3 + math.sin(wavePhase) * waveAmplitude);

      for (double x = 0; x <= size.width; x++) {
        double y =
            size.height * 0.3 +
            math.sin(wavePhase + x * waveFrequency) * waveAmplitude;
        path.lineTo(x, y);
      }

      path.lineTo(size.width, size.height);
      path.lineTo(0, size.height);
      path.close();

      canvas.drawPath(
        path,
        paint..color = AppTheme.primaryColor.withValues(alpha: 0.05 - i * 0.01),
      );
    }
  }

  @override
  bool shouldRepaint(BackgroundPainter oldDelegate) =>
      oldDelegate.animationValue != animationValue;
}
