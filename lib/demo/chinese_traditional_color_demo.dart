import 'package:flutter/material.dart';
import '../config/chinese_traditional_colors.dart';
import '../services/theme_manager.dart';
import '../widgets/chinese_traditional_color_theme_selector.dart';

/// 中国传统色演示页面
class ChineseTraditionalColorDemo extends StatefulWidget {
  const ChineseTraditionalColorDemo({super.key});

  @override
  State<ChineseTraditionalColorDemo> createState() => _ChineseTraditionalColorDemoState();
}

class _ChineseTraditionalColorDemoState extends State<ChineseTraditionalColorDemo> {
  ChineseTraditionalColorTheme? _currentTheme;

  @override
  void initState() {
    super.initState();
    // 获取当前主题
    _loadCurrentTheme();
  }

  void _loadCurrentTheme() {
    // 这里可以从设置服务加载当前主题
    setState(() {
      _currentTheme = ChineseTraditionalColorTheme.bambooGreen; // 示例
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeManager().getThemeBackgroundColor(),
      appBar: AppBar(
        title: const Text('中国传统色主题演示'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.palette),
            onPressed: _showThemeSelector,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 当前主题信息
            _buildCurrentThemeInfo(),
            
            const SizedBox(height: 24),
            
            // 颜色展示区域
            _buildColorShowcase(),
            
            const SizedBox(height: 24),
            
            // 内容卡片演示
            _buildContentCardsDemo(),
            
            const SizedBox(height: 24),
            
            // 主题切换按钮
            _buildThemeSwitchButton(),
          ],
        ),
      ),
    );
  }

  /// 构建当前主题信息
  Widget _buildCurrentThemeInfo() {
    final config = _currentTheme != null 
        ? ChineseTraditionalColors.getConfig(_currentTheme!)
        : null;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ThemeManager().getThemeSurfaceColor(),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, 4),
            blurRadius: 12,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  gradient: config?.gradient ?? const LinearGradient(
                    colors: [Color(0xFF6366F1), Color(0xFF4F46E5)],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _currentTheme != null 
                      ? ChineseTraditionalColors.getThemeIcon(_currentTheme!)
                      : Icons.palette,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      config?.name ?? '默认主题',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: ThemeManager().getThemeTextColor(),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      config?.description ?? '使用系统默认主题色彩',
                      style: TextStyle(
                        fontSize: 14,
                        color: ThemeManager().getThemeSubtitleColor(),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建颜色展示区域
  Widget _buildColorShowcase() {
    final config = _currentTheme != null 
        ? ChineseTraditionalColors.getConfig(_currentTheme!)
        : null;
    
    if (config == null) return const SizedBox.shrink();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '色彩展示',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ThemeManager().getThemeTextColor(),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            _buildColorSwatch('主色', config.primaryColor),
            const SizedBox(width: 12),
            _buildColorSwatch('辅色', config.secondaryColor),
            const SizedBox(width: 12),
            _buildColorSwatch('强调色', config.accentColor),
          ],
        ),
      ],
    );
  }

  /// 构建颜色色块
  Widget _buildColorSwatch(String label, Color color) {
    return Expanded(
      child: Column(
        children: [
          Container(
            height: 80,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.3),
                  offset: const Offset(0, 4),
                  blurRadius: 12,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: ThemeManager().getThemeSubtitleColor(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容卡片演示
  Widget _buildContentCardsDemo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '内容卡片演示',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ThemeManager().getThemeTextColor(),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            _buildDemoCard('Markdown', 'markdown', Icons.description),
            const SizedBox(width: 12),
            _buildDemoCard('文本卡片', 'textcard', Icons.credit_card),
            const SizedBox(width: 12),
            _buildDemoCard('图片', 'image', Icons.image),
          ],
        ),
      ],
    );
  }

  /// 构建演示卡片
  Widget _buildDemoCard(String title, String type, IconData icon) {
    return Expanded(
      child: Container(
        height: 120,
        decoration: BoxDecoration(
          gradient: ThemeManager().getContentTypeGradient(type),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              offset: const Offset(0, 4),
              blurRadius: 12,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建主题切换按钮
  Widget _buildThemeSwitchButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _showThemeSelector,
        icon: const Icon(Icons.palette),
        label: const Text('切换主题'),
        style: ElevatedButton.styleFrom(
          backgroundColor: ThemeManager().getContentTypeColor('markdown'),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  /// 显示主题选择器
  void _showThemeSelector() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
          child: ChineseTraditionalColorThemeSelector(
            currentTheme: _currentTheme,
            onThemeChanged: (theme) {
              setState(() {
                _currentTheme = theme;
              });
              Navigator.of(context).pop();
            },
          ),
        ),
      ),
    );
  }
}
