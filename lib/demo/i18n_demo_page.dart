import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/localization_service.dart';
import '../settings/language_settings_page.dart';

/// 国际化演示页面
class I18nDemoPage extends StatelessWidget {
  final LocalizationService localizationService;

  const I18nDemoPage({
    super.key,
    required this.localizationService,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: Text(
          'Internationalization Demo',
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.textDarkColor,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: AppTheme.textDarkColor),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.language, color: AppTheme.textDarkColor),
            onPressed: () => _openLanguageSettings(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 页面标题
            Text(
              'Internationalization Demo',
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: AppTheme.textDarkColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Demonstrating multi-language support',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textMediumColor,
              ),
            ),
            const SizedBox(height: 32),

            // 当前语言信息
            _buildInfoCard(
              title: 'Current Language',
              content: localizationService.currentLocaleName,
              icon: Icons.language,
            ),
            const SizedBox(height: 16),

            // 基础文本演示
            _buildDemoSection(
              title: 'Basic Text',
              children: [
                _buildDemoItem('App Name', l10n.appName),
                _buildDemoItem('App Name (Chinese)', l10n.appNameChinese),
                _buildDemoItem('Description', l10n.appDescription),
                _buildDemoItem('Home', l10n.home),
                _buildDemoItem('Settings', l10n.settings),
                _buildDemoItem('Language', l10n.language),
              ],
            ),
            const SizedBox(height: 24),

            // 按钮文本演示
            _buildDemoSection(
              title: 'Button Text',
              children: [
                _buildDemoItem('Create', l10n.create),
                _buildDemoItem('Edit', l10n.edit),
                _buildDemoItem('Delete', l10n.delete),
                _buildDemoItem('Save', l10n.save),
                _buildDemoItem('Cancel', l10n.cancel),
                _buildDemoItem('Confirm', l10n.confirm),
              ],
            ),
            const SizedBox(height: 24),

            // 模块名称演示
            _buildDemoSection(
              title: 'Module Names',
              children: [
                _buildDemoItem('Markdown', l10n.markdown),
                _buildDemoItem('Text Cards', l10n.textCards),
                _buildDemoItem('PDF', l10n.pdf),
                _buildDemoItem('Voice', l10n.voice),
                _buildDemoItem('HTML', l10n.html),
                _buildDemoItem('SVG', l10n.svg),
              ],
            ),
            const SizedBox(height: 24),

            // PDF模块特定文本演示
            _buildDemoSection(
              title: 'PDF Module',
              children: [
                _buildDemoItem('PDF Professional Tool', l10n.pdfProfessionalTool),
                _buildDemoItem('Tool Description', l10n.pdfToolDescription),
                _buildDemoItem('Security Encryption', l10n.securityEncryption),
                _buildDemoItem('Intelligent Annotation', l10n.intelligentAnnotation),
                _buildDemoItem('Quick Search', l10n.quickSearch),
                _buildDemoItem('Convenient Sharing', l10n.convenientSharing),
              ],
            ),
            const SizedBox(height: 24),

            // 状态文本演示
            _buildDemoSection(
              title: 'Status Text',
              children: [
                _buildDemoItem('Loading', l10n.loading),
                _buildDemoItem('Success', l10n.success),
                _buildDemoItem('Error', l10n.error),
                _buildDemoItem('Warning', l10n.warning),
                _buildDemoItem('Enabled', l10n.enabled),
                _buildDemoItem('Disabled', l10n.disabled),
              ],
            ),
            const SizedBox(height: 32),

            // 语言切换按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _openLanguageSettings(context),
                icon: const Icon(Icons.language),
                label: Text('Change Language (${localizationService.currentLocaleName})'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建信息卡片
  Widget _buildInfoCard({
    required String title,
    required String content,
    required IconData icon,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: Colors.white, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  content,
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建演示区域
  Widget _buildDemoSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.textDarkColor,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                offset: const Offset(0, 2),
                blurRadius: 8,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  /// 构建演示项
  Widget _buildDemoItem(String key, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              key,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textMediumColor,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textDarkColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 打开语言设置
  void _openLanguageSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LanguageSettingsPage(
          localizationService: localizationService,
        ),
      ),
    );
  }
}
