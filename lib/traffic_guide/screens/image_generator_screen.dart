import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../models/traffic_guide_models.dart';
import '../services/traffic_guide_service.dart';

class ImageGeneratorScreen extends StatefulWidget {
  const ImageGeneratorScreen({super.key});

  @override
  State<ImageGeneratorScreen> createState() => _ImageGeneratorScreenState();
}

class _ImageGeneratorScreenState extends State<ImageGeneratorScreen> {
  final TrafficGuideService _service = TrafficGuideService();
  final _formKey = GlobalKey<FormState>();

  late TrafficImageConfig _config;
  Uint8List? _generatedImage;
  bool _isGenerating = false;

  @override
  void initState() {
    super.initState();
    _config = TrafficImageConfig(text: '');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('引流图片生成'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 配置表单
              _buildConfigForm(),
              const SizedBox(height: 24),

              // 生成按钮
              ElevatedButton.icon(
                onPressed: _isGenerating ? null : _generateImage,
                icon:
                    _isGenerating
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.image),
                label: Text(_isGenerating ? '生成中...' : '生成图片'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(height: 24),

              // 预览区域
              if (_generatedImage != null) _buildPreviewSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConfigForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '图片配置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // 文本输入
            TextFormField(
              decoration: const InputDecoration(
                labelText: '文本内容',
                hintText: '输入要显示的文本',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              onChanged: (value) => _config = _config.copyWith(text: value),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入文本内容';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // 字体大小
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: '字体大小',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    initialValue: _config.fontSize.toString(),
                    onChanged: (value) {
                      final size = double.tryParse(value);
                      if (size != null) {
                        _config = _config.copyWith(fontSize: size);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: '字体',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: _config.fontFamily,
                    onChanged:
                        (value) =>
                            _config = _config.copyWith(fontFamily: value),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 颜色选择
            Row(
              children: [
                Expanded(
                  child: _buildColorPicker(
                    label: '背景颜色',
                    initialColor: _config.backgroundColor,
                    onChanged:
                        (color) =>
                            _config = _config.copyWith(backgroundColor: color),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildColorPicker(
                    label: '文字颜色',
                    initialColor: _config.textColor,
                    onChanged:
                        (color) => _config = _config.copyWith(textColor: color),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 干扰设置
            const Text(
              '干扰设置',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            _buildSlider(
              label: '干扰程度',
              value: _config.noiseLevel,
              onChanged: (value) {
                setState(() {
                  _config = _config.copyWith(noiseLevel: value);
                });
              },
            ),

            _buildSlider(
              label: '扭曲程度',
              value: _config.distortionLevel,
              onChanged: (value) {
                setState(() {
                  _config = _config.copyWith(distortionLevel: value);
                });
              },
            ),
            const SizedBox(height: 16),

            // 水印设置
            Row(
              children: [
                Checkbox(
                  value: _config.addWatermark,
                  onChanged:
                      (value) =>
                          _config = _config.copyWith(
                            addWatermark: value ?? false,
                          ),
                ),
                const Text('添加水印'),
              ],
            ),
            if (_config.addWatermark) ...[
              const SizedBox(height: 8),
              TextFormField(
                decoration: const InputDecoration(
                  labelText: '水印文本',
                  hintText: '输入水印内容',
                  border: OutlineInputBorder(),
                ),
                onChanged:
                    (value) => _config = _config.copyWith(watermarkText: value),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildColorPicker({
    required String label,
    required String initialColor,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () => _showColorPicker(initialColor, onChanged),
          child: Container(
            height: 50,
            decoration: BoxDecoration(
              color: _parseColor(initialColor),
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                initialColor,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSlider({
    required String label,
    required double value,
    required Function(double) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [Text(label), Text('${(value * 100).toInt()}%')],
        ),
        Slider(
          value: value,
          min: 0.0,
          max: 1.0,
          divisions: 10,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildPreviewSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '预览',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    IconButton(
                      onPressed: _saveImage,
                      icon: const Icon(Icons.save),
                      tooltip: '保存到相册',
                    ),
                    IconButton(
                      onPressed: _shareImage,
                      icon: const Icon(Icons.share),
                      tooltip: '分享',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Center(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.memory(
                    _generatedImage!,
                    width: 300,
                    height: 225,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final hex = colorString.substring(1);
      return Color(int.parse('FF$hex', radix: 16));
    }
    return Colors.black;
  }

  void _showColorPicker(String initialColor, Function(String) onChanged) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('选择颜色'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildColorOption('#000000', '黑色', onChanged),
                  _buildColorOption('#FFFFFF', '白色', onChanged),
                  _buildColorOption('#FF0000', '红色', onChanged),
                  _buildColorOption('#00FF00', '绿色', onChanged),
                  _buildColorOption('#0000FF', '蓝色', onChanged),
                  _buildColorOption('#FFFF00', '黄色', onChanged),
                  _buildColorOption('#FF00FF', '紫色', onChanged),
                  _buildColorOption('#00FFFF', '青色', onChanged),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
            ],
          ),
    );
  }

  Widget _buildColorOption(
    String color,
    String name,
    Function(String) onChanged,
  ) {
    return ListTile(
      leading: Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: _parseColor(color),
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      title: Text(name),
      onTap: () {
        onChanged(color);
        Navigator.pop(context);
      },
    );
  }

  Future<void> _generateImage() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isGenerating = true);

    try {
      final image = await _service.generateTrafficImage(_config);
      setState(() {
        _generatedImage = image;
        _isGenerating = false;
      });
    } catch (e) {
      setState(() => _isGenerating = false);
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('生成图片失败: $e')));
    }
  }

  Future<void> _saveImage() async {
    if (_generatedImage == null) return;

    try {
      // 显示保存提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('长按图片可保存到相册')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('保存失败: $e')),
      );
    }
  }

  void _shareImage() {
    // 这里可以实现分享功能
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('分享功能开发中...')));
  }
}
