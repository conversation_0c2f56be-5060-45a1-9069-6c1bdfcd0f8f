import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/traffic_guide_models.dart';
import '../services/traffic_guide_service.dart';

class TextTransformerScreen extends StatefulWidget {
  const TextTransformerScreen({super.key});

  @override
  State<TextTransformerScreen> createState() => _TextTransformerScreenState();
}

class _TextTransformerScreenState extends State<TextTransformerScreen> {
  final TrafficGuideService _service = TrafficGuideService();
  final _inputController = TextEditingController();
  final _outputController = TextEditingController();

  late TextTransformConfig _config;
  bool _isTransforming = false;

  @override
  void initState() {
    super.initState();
    _config = TextTransformConfig();
    _inputController.addListener(_transformText);
  }

  @override
  void dispose() {
    _inputController.dispose();
    _outputController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('文本转换'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showSettings,
            icon: const Icon(Icons.settings),
            tooltip: '设置',
          ),
        ],
      ),
      body: Column(
        children: [
          // 配置面板
          _buildConfigPanel(),

          // 输入输出区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 输入区域
                  Expanded(flex: 1, child: _buildInputSection()),

                  // 转换按钮
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isTransforming ? null : _transformText,
                            icon:
                                _isTransforming
                                    ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    )
                                    : const Icon(Icons.transform),
                            label: Text(_isTransforming ? '转换中...' : '转换文本'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        IconButton(
                          onPressed: _copyOutput,
                          icon: const Icon(Icons.copy),
                          tooltip: '复制结果',
                        ),
                        IconButton(
                          onPressed: _clearAll,
                          icon: const Icon(Icons.clear),
                          tooltip: '清空',
                        ),
                      ],
                    ),
                  ),

                  // 输出区域
                  Expanded(flex: 1, child: _buildOutputSection()),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigPanel() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '转换设置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // 功能开关
            _buildSwitchTile(
              title: 'Emoji转换',
              subtitle: '将数字和字母转换为特殊Unicode字符',
              value: _config.enableEmojiConversion,
              onChanged: (value) {
                setState(() {
                  _config = _config.copyWith(enableEmojiConversion: value);
                });
                _transformText();
              },
            ),

            _buildSwitchTile(
              title: 'Unicode变体',
              subtitle: '添加变音字符和特殊Unicode',
              value: _config.enableUnicodeVariation,
              onChanged: (value) {
                setState(() {
                  _config = _config.copyWith(enableUnicodeVariation: value);
                });
                _transformText();
              },
            ),

            _buildSwitchTile(
              title: '不可见字符',
              subtitle: '在文本中插入不可见字符',
              value: _config.enableInvisibleChars,
              onChanged: (value) {
                setState(() {
                  _config = _config.copyWith(enableInvisibleChars: value);
                });
                _transformText();
              },
            ),

            _buildSwitchTile(
              title: '敏感词干扰',
              subtitle: '对敏感词进行字符干扰',
              value: _config.enableSensitiveWordMasking,
              onChanged: (value) {
                setState(() {
                  _config = _config.copyWith(enableSensitiveWordMasking: value);
                });
                _transformText();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildInputSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '输入文本',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${_inputController.text.length} 字符',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Expanded(
              child: TextField(
                controller: _inputController,
                maxLines: null,
                expands: true,
                decoration: const InputDecoration(
                  hintText: '输入要转换的文本...',
                  border: OutlineInputBorder(),
                ),
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOutputSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '转换结果',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${_outputController.text.length} 字符',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Expanded(
              child: TextField(
                controller: _outputController,
                maxLines: null,
                expands: true,
                readOnly: true,
                decoration: const InputDecoration(
                  hintText: '转换后的文本将显示在这里...',
                  border: OutlineInputBorder(),
                  filled: true,
                  fillColor: Color(0xFFF5F5F5),
                ),
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _transformText() {
    if (_inputController.text.isEmpty) {
      _outputController.clear();
      return;
    }

    setState(() => _isTransforming = true);

    try {
      final transformed = _service.transformText(
        _inputController.text,
        _config,
      );
      _outputController.text = transformed;
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('转换失败: $e')));
    } finally {
      setState(() => _isTransforming = false);
    }
  }

  void _copyOutput() {
    if (_outputController.text.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: _outputController.text));
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('已复制到剪贴板')));
    }
  }

  void _clearAll() {
    _inputController.clear();
    _outputController.clear();
  }

  void _showSettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('高级设置'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 敏感词设置
                  const Text(
                    '敏感词列表',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    decoration: const InputDecoration(
                      hintText: '输入敏感词，用逗号分隔',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      final words =
                          value
                              .split(',')
                              .map((e) => e.trim())
                              .where((e) => e.isNotEmpty)
                              .toList();
                      _config = _config.copyWith(sensitiveWords: words);
                    },
                  ),
                  const SizedBox(height: 16),

                  // 自定义emoji映射
                  const Text(
                    '自定义字符映射',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '格式: 原字符=目标字符 (每行一个)',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  const SizedBox(height: 4),
                  TextField(
                    decoration: const InputDecoration(
                      hintText: '例如:\na=ᴀ\nb=ʙ',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 5,
                    onChanged: (value) {
                      final lines = value.split('\n');
                      final map = <String, String>{};
                      for (final line in lines) {
                        final parts = line.split('=');
                        if (parts.length == 2) {
                          map[parts[0].trim()] = parts[1].trim();
                        }
                      }
                      _config = _config.copyWith(customEmojiMap: map);
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }
}
