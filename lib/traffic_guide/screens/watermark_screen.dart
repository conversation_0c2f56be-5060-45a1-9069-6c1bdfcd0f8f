import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/traffic_guide_models.dart';
import '../services/traffic_guide_service.dart';

class WatermarkScreen extends StatefulWidget {
  const WatermarkScreen({super.key});

  @override
  State<WatermarkScreen> createState() => _WatermarkScreenState();
}

class _WatermarkScreenState extends State<WatermarkScreen> {
  final TrafficGuideService _service = TrafficGuideService();
  final _inputController = TextEditingController();
  final _outputController = TextEditingController();
  final _watermarkController = TextEditingController();

  late WatermarkConfig _config;
  bool _isProcessing = false;
  bool _isAddMode = true; // true: 添加水印, false: 移除水印

  @override
  void initState() {
    super.initState();
    _config = WatermarkConfig(text: '');
    _watermarkController.addListener(_updateConfig);
  }

  @override
  void dispose() {
    _inputController.dispose();
    _outputController.dispose();
    _watermarkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('水印处理'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 模式选择
          _buildModeSelector(),

          // 水印配置
          _buildWatermarkConfig(),

          // 输入输出区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 输入区域
                  Expanded(flex: 1, child: _buildInputSection()),

                  // 处理按钮
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isProcessing ? null : _processText,
                            icon:
                                _isProcessing
                                    ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    )
                                    : Icon(
                                      _isAddMode ? Icons.add : Icons.remove,
                                    ),
                            label: Text(
                              _isProcessing
                                  ? '处理中...'
                                  : (_isAddMode ? '添加水印' : '移除水印'),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        IconButton(
                          onPressed: _copyOutput,
                          icon: const Icon(Icons.copy),
                          tooltip: '复制结果',
                        ),
                        IconButton(
                          onPressed: _clearAll,
                          icon: const Icon(Icons.clear),
                          tooltip: '清空',
                        ),
                      ],
                    ),
                  ),

                  // 输出区域
                  Expanded(flex: 1, child: _buildOutputSection()),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModeSelector() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '处理模式',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildModeCard(
                    title: '添加水印',
                    subtitle: '在文本中添加隐形水印',
                    icon: Icons.add_circle,
                    color: Colors.green,
                    isSelected: _isAddMode,
                    onTap: () => setState(() => _isAddMode = true),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildModeCard(
                    title: '移除水印',
                    subtitle: '从文本中移除隐形水印',
                    icon: Icons.remove_circle,
                    color: Colors.red,
                    isSelected: !_isAddMode,
                    onTap: () => setState(() => _isAddMode = false),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModeCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? color.withValues(alpha: 0.1)
                  : Colors.grey.withValues(alpha: 0.05),
          border: Border.all(
            color: isSelected ? color : Colors.grey.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, size: 32, color: isSelected ? color : Colors.grey),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isSelected ? color : Colors.grey,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWatermarkConfig() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '水印配置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // 水印文本
            TextFormField(
              controller: _watermarkController,
              decoration: InputDecoration(
                labelText: _isAddMode ? '水印内容' : '水印标识',
                hintText: _isAddMode ? '输入要添加的水印内容' : '输入要移除的水印标识',
                border: const OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),

            // 水印类型选择
            Row(
              children: [
                Checkbox(
                  value: _config.invisible,
                  onChanged: (value) {
                    setState(() {
                      _config = _config.copyWith(invisible: value ?? false);
                    });
                  },
                ),
                const Text('隐形水印'),
                const SizedBox(width: 16),
                if (!_config.invisible) ...[
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: '透明度',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      initialValue: (_config.opacity * 100).toString(),
                      onChanged: (value) {
                        final opacity = double.tryParse(value);
                        if (opacity != null) {
                          _config = _config.copyWith(opacity: opacity / 100);
                        }
                      },
                    ),
                  ),
                ],
              ],
            ),

            if (!_config.invisible) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: '字体大小',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      initialValue: _config.fontSize.toString(),
                      onChanged: (value) {
                        final size = double.tryParse(value);
                        if (size != null) {
                          _config = _config.copyWith(fontSize: size);
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: '旋转角度',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      initialValue: _config.rotation.toString(),
                      onChanged: (value) {
                        final rotation = double.tryParse(value);
                        if (rotation != null) {
                          _config = _config.copyWith(rotation: rotation);
                        }
                      },
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInputSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _isAddMode ? '原始文本' : '带水印文本',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_inputController.text.length} 字符',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Expanded(
              child: TextField(
                controller: _inputController,
                maxLines: null,
                expands: true,
                decoration: InputDecoration(
                  hintText: _isAddMode ? '输入要添加水印的文本...' : '输入要移除水印的文本...',
                  border: const OutlineInputBorder(),
                ),
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOutputSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _isAddMode ? '带水印文本' : '原始文本',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_outputController.text.length} 字符',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Expanded(
              child: TextField(
                controller: _outputController,
                maxLines: null,
                expands: true,
                readOnly: true,
                decoration: const InputDecoration(
                  hintText: '处理后的文本将显示在这里...',
                  border: OutlineInputBorder(),
                  filled: true,
                  fillColor: Color(0xFFF5F5F5),
                ),
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _updateConfig() {
    _config = _config.copyWith(text: _watermarkController.text);
  }

  void _processText() {
    if (_inputController.text.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入要处理的文本')));
      return;
    }

    if (_isAddMode && _watermarkController.text.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入水印内容')));
      return;
    }

    setState(() => _isProcessing = true);

    try {
      String result;
      if (_isAddMode) {
        result = _service.addTextWatermark(_inputController.text, _config);
      } else {
        result = _service.removeTextWatermark(_inputController.text, _config);
      }
      _outputController.text = result;
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('处理失败: $e')));
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  void _copyOutput() {
    if (_outputController.text.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: _outputController.text));
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('已复制到剪贴板')));
    }
  }

  void _clearAll() {
    _inputController.clear();
    _outputController.clear();
  }
}
