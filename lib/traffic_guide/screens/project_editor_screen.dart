import 'package:flutter/material.dart';
import '../models/traffic_guide_models.dart';
import '../services/traffic_guide_service.dart';

class ProjectEditorScreen extends StatefulWidget {
  final TrafficGuideProject project;

  const ProjectEditorScreen({super.key, required this.project});

  @override
  State<ProjectEditorScreen> createState() => _ProjectEditorScreenState();
}

class _ProjectEditorScreenState extends State<ProjectEditorScreen> {
  final TrafficGuideService _service = TrafficGuideService();
  final _formKey = GlobalKey<FormState>();

  late TrafficGuideProject _project;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _project = widget.project;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('项目编辑'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isSaving ? null : _saveProject,
            child:
                _isSaving
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                    : const Text('保存'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 基本信息
              _buildBasicInfoSection(),
              const SizedBox(height: 24),

              // 图片配置
              _buildImageConfigSection(),
              const SizedBox(height: 24),

              // 文本转换配置
              _buildTextTransformSection(),
              const SizedBox(height: 24),

              // 水印配置
              _buildWatermarkSection(),
              const SizedBox(height: 24),

              // 保存按钮
              ElevatedButton.icon(
                onPressed: _isSaving ? null : _saveProject,
                icon:
                    _isSaving
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.save),
                label: Text(_isSaving ? '保存中...' : '保存项目'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '基本信息',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            TextFormField(
              decoration: const InputDecoration(
                labelText: '项目名称',
                hintText: '输入项目名称',
                border: OutlineInputBorder(),
              ),
              initialValue: _project.name,
              onChanged: (value) {
                _project = _project.copyWith(name: value);
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入项目名称';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            TextFormField(
              decoration: const InputDecoration(
                labelText: '项目描述',
                hintText: '输入项目描述',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              initialValue: _project.description,
              onChanged: (value) {
                _project = _project.copyWith(description: value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageConfigSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '图片配置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            TextFormField(
              decoration: const InputDecoration(
                labelText: '默认文本',
                hintText: '输入默认显示的文本',
                border: OutlineInputBorder(),
              ),
              initialValue: _project.imageConfig.text,
              onChanged: (value) {
                _project = _project.copyWith(
                  imageConfig: _project.imageConfig.copyWith(text: value),
                );
              },
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: '字体大小',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    initialValue: _project.imageConfig.fontSize.toString(),
                    onChanged: (value) {
                      final size = double.tryParse(value);
                      if (size != null) {
                        _project = _project.copyWith(
                          imageConfig: _project.imageConfig.copyWith(
                            fontSize: size,
                          ),
                        );
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: '字体',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: _project.imageConfig.fontFamily,
                    onChanged: (value) {
                      _project = _project.copyWith(
                        imageConfig: _project.imageConfig.copyWith(
                          fontFamily: value,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: '背景颜色',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: _project.imageConfig.backgroundColor,
                    onChanged: (value) {
                      _project = _project.copyWith(
                        imageConfig: _project.imageConfig.copyWith(
                          backgroundColor: value,
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: '文字颜色',
                      border: OutlineInputBorder(),
                    ),
                    initialValue: _project.imageConfig.textColor,
                    onChanged: (value) {
                      _project = _project.copyWith(
                        imageConfig: _project.imageConfig.copyWith(
                          textColor: value,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildSlider(
              label: '干扰程度',
              value: _project.imageConfig.noiseLevel,
              onChanged: (value) {
                _project = _project.copyWith(
                  imageConfig: _project.imageConfig.copyWith(noiseLevel: value),
                );
              },
            ),

            _buildSlider(
              label: '扭曲程度',
              value: _project.imageConfig.distortionLevel,
              onChanged: (value) {
                _project = _project.copyWith(
                  imageConfig: _project.imageConfig.copyWith(
                    distortionLevel: value,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextTransformSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '文本转换配置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            _buildSwitchTile(
              title: 'Emoji转换',
              subtitle: '将数字和字母转换为特殊Unicode字符',
              value: _project.textConfig.enableEmojiConversion,
              onChanged: (value) {
                _project = _project.copyWith(
                  textConfig: _project.textConfig.copyWith(
                    enableEmojiConversion: value,
                  ),
                );
              },
            ),

            _buildSwitchTile(
              title: 'Unicode变体',
              subtitle: '添加变音字符和特殊Unicode',
              value: _project.textConfig.enableUnicodeVariation,
              onChanged: (value) {
                _project = _project.copyWith(
                  textConfig: _project.textConfig.copyWith(
                    enableUnicodeVariation: value,
                  ),
                );
              },
            ),

            _buildSwitchTile(
              title: '不可见字符',
              subtitle: '在文本中插入不可见字符',
              value: _project.textConfig.enableInvisibleChars,
              onChanged: (value) {
                _project = _project.copyWith(
                  textConfig: _project.textConfig.copyWith(
                    enableInvisibleChars: value,
                  ),
                );
              },
            ),

            _buildSwitchTile(
              title: '敏感词干扰',
              subtitle: '对敏感词进行字符干扰',
              value: _project.textConfig.enableSensitiveWordMasking,
              onChanged: (value) {
                _project = _project.copyWith(
                  textConfig: _project.textConfig.copyWith(
                    enableSensitiveWordMasking: value,
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            TextFormField(
              decoration: const InputDecoration(
                labelText: '敏感词列表',
                hintText: '输入敏感词，用逗号分隔',
                border: OutlineInputBorder(),
              ),
              initialValue: _project.textConfig.sensitiveWords.join(', '),
              onChanged: (value) {
                final words =
                    value
                        .split(',')
                        .map((e) => e.trim())
                        .where((e) => e.isNotEmpty)
                        .toList();
                _project = _project.copyWith(
                  textConfig: _project.textConfig.copyWith(
                    sensitiveWords: words,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWatermarkSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '水印配置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            TextFormField(
              decoration: const InputDecoration(
                labelText: '水印文本',
                hintText: '输入水印内容',
                border: OutlineInputBorder(),
              ),
              initialValue: _project.watermarkConfig.text,
              onChanged: (value) {
                _project = _project.copyWith(
                  watermarkConfig: _project.watermarkConfig.copyWith(
                    text: value,
                  ),
                );
              },
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Checkbox(
                  value: _project.watermarkConfig.invisible,
                  onChanged: (value) {
                    _project = _project.copyWith(
                      watermarkConfig: _project.watermarkConfig.copyWith(
                        invisible: value ?? false,
                      ),
                    );
                    setState(() {});
                  },
                ),
                const Text('隐形水印'),
              ],
            ),

            if (!_project.watermarkConfig.invisible) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: '透明度',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      initialValue:
                          (_project.watermarkConfig.opacity * 100).toString(),
                      onChanged: (value) {
                        final opacity = double.tryParse(value);
                        if (opacity != null) {
                          _project = _project.copyWith(
                            watermarkConfig: _project.watermarkConfig.copyWith(
                              opacity: opacity / 100,
                            ),
                          );
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: '字体大小',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      initialValue:
                          _project.watermarkConfig.fontSize.toString(),
                      onChanged: (value) {
                        final size = double.tryParse(value);
                        if (size != null) {
                          _project = _project.copyWith(
                            watermarkConfig: _project.watermarkConfig.copyWith(
                              fontSize: size,
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildSlider({
    required String label,
    required double value,
    required Function(double) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [Text(label), Text('${(value * 100).toInt()}%')],
        ),
        Slider(
          value: value,
          min: 0.0,
          max: 1.0,
          divisions: 10,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Future<void> _saveProject() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSaving = true);

    try {
      final updatedProject = _project.copyWith(updatedAt: DateTime.now());
      await _service.saveProject(updatedProject);

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('项目保存成功')));
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('保存失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }
}
