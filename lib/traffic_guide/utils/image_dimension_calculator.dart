import 'package:flutter/material.dart';
import '../models/traffic_guide_models.dart';
import '../models/image_template.dart';

/// Utility class for calculating optimal image dimensions and text scaling
/// Ensures consistency between preview and export rendering
class ImageDimensionCalculator {
  /// Calculate optimal image size based on text content and configuration
  static Size calculateOptimalSize(
    TrafficImageConfig config, {
    Size? exportSize,
  }) {
    // If export size is specified, use it
    if (exportSize != null) {
      return exportSize;
    }

    // Calculate based on text content
    final textLength = config.text.length;
    final fontSize = config.fontSize;

    // Base dimensions that scale with content
    double baseWidth = 800;
    double baseHeight = 600;

    // Adjust dimensions based on text length
    if (textLength > 50) {
      baseWidth = 900;
      baseHeight = 600;
    }
    if (textLength > 100) {
      baseWidth = 1000;
      baseHeight = 600;
    }
    if (textLength > 200) {
      baseWidth = 1200;
      baseHeight = 600;
    }

    // Adjust for font size
    if (fontSize > 60) {
      baseWidth *= 1.2;
      baseHeight *= 1.1;
    } else if (fontSize < 30) {
      baseWidth *= 0.9;
      baseHeight *= 0.9;
    }

    return Size(baseWidth, baseHeight);
  }

  /// Calculate text dimensions for given configuration
  static Size calculateTextDimensions(
    TrafficImageConfig config,
    Size containerSize, {
    ImageTemplate? template,
  }) {
    final style = TextStyle(
      fontSize: config.fontSize,
      fontWeight: template?.fontWeight ?? FontWeight.bold,
      fontFamily: config.fontFamily,
    );

    final textSpan = TextSpan(text: config.text, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Use 90% of container width for text layout (same as export)
    final maxWidth = containerSize.width * 0.9;
    textPainter.layout(maxWidth: maxWidth);

    return Size(textPainter.width, textPainter.height);
  }

  /// Calculate scaling factor to fit text within container
  static double calculateTextScalingFactor(
    TrafficImageConfig config,
    Size containerSize, {
    ImageTemplate? template,
  }) {
    final textDimensions = calculateTextDimensions(
      config,
      containerSize,
      template: template,
    );

    // Calculate available space (90% width, 80% height for padding)
    final availableWidth = containerSize.width * 0.9;
    final availableHeight = containerSize.height * 0.8;

    // Calculate scaling factors for width and height
    final widthScale =
        textDimensions.width > availableWidth
            ? availableWidth / textDimensions.width
            : 1.0;

    final heightScale =
        textDimensions.height > availableHeight
            ? availableHeight / textDimensions.height
            : 1.0;

    // Use the smaller scaling factor to ensure text fits in both dimensions
    return (widthScale < heightScale ? widthScale : heightScale).clamp(
      0.3,
      1.0,
    );
  }

  /// Calculate preview scaling factor relative to optimal export size
  static double calculatePreviewScalingFactor(
    Size previewSize,
    Size optimalSize,
  ) {
    final widthScale = previewSize.width / optimalSize.width;
    final heightScale = previewSize.height / optimalSize.height;

    // Use the smaller scale to maintain aspect ratio
    return widthScale < heightScale ? widthScale : heightScale;
  }

  /// Calculate responsive font size for preview
  static double calculatePreviewFontSize(
    TrafficImageConfig config,
    Size previewSize, {
    ImageTemplate? template,
  }) {
    final optimalSize = calculateOptimalSize(config);
    final previewScale = calculatePreviewScalingFactor(
      previewSize,
      optimalSize,
    );

    // Scale the font size proportionally
    double scaledFontSize = config.fontSize * previewScale;

    // Apply additional text fitting if needed
    final textScale = calculateTextScalingFactor(
      config.copyWith(fontSize: scaledFontSize),
      previewSize,
      template: template,
    );

    return scaledFontSize * textScale;
  }

  /// Calculate responsive padding for preview
  static double calculatePreviewPadding(
    Size previewSize,
    Size optimalSize, {
    double basePadding = 20.0,
  }) {
    final scale = calculatePreviewScalingFactor(previewSize, optimalSize);
    return basePadding * scale;
  }

  /// Get optimal aspect ratio for given configuration
  static double getOptimalAspectRatio(TrafficImageConfig config) {
    final optimalSize = calculateOptimalSize(config);
    return optimalSize.width / optimalSize.height;
  }

  /// Validate if text fits within given dimensions
  static bool validateTextFit(
    TrafficImageConfig config,
    Size containerSize, {
    ImageTemplate? template,
  }) {
    final textDimensions = calculateTextDimensions(
      config,
      containerSize,
      template: template,
    );

    final availableWidth = containerSize.width * 0.9;
    final availableHeight = containerSize.height * 0.8;

    return textDimensions.width <= availableWidth &&
        textDimensions.height <= availableHeight;
  }
}
