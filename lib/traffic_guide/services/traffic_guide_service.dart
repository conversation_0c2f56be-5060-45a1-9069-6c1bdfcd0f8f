import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

import '../models/traffic_guide_models.dart';

class TrafficGuideService {
  static final TrafficGuideService _instance = TrafficGuideService._internal();
  factory TrafficGuideService() => _instance;
  TrafficGuideService._internal();

  // 不可见字符映射
  static const Map<String, String> _invisibleChars = {
    '\u200B': '零宽空格',
    '\u200C': '零宽非连接符',
    '\u200D': '零宽连接符',
    '\u2060': '词连接符',
    '\uFEFF': '零宽不换行空格',
    '\u2061': '函数应用',
    '\u2062': '不可见乘号',
    '\u2063': '不可见分隔符',
    '\u2064': '不可见加号',
  };

  // 变音字符映射
  static const Map<String, String> _diacriticChars = {
    '\u0300': '重音符',
    '\u0301': '锐音符',
    '\u0302': '抑扬符',
    '\u0303': '波浪符',
    '\u0304': '长音符',
    '\u0305': '上划线',
    '\u0306': '短音符',
    '\u0307': '上点',
    '\u0308': '分音符',
    '\u0309': '钩音符',
    '\u030A': '上圆圈',
    '\u030B': '双锐音符',
    '\u030C': '抑音符',
    '\u030D': '垂直重音符',
    '\u030E': '双垂直重音符',
    '\u030F': '双抑扬符',
  };

  // 数字到emoji映射
  static const Map<String, String> _numberToEmoji = {
    '0': '🄀',
    '1': '🄁',
    '2': '🄂',
    '3': '🄃',
    '4': '🄄',
    '5': '🄅',
    '6': '🄆',
    '7': '🄇',
    '8': '🄈',
    '9': '🄉',
  };

  // 字母到特殊Unicode映射
  static const Map<String, String> _letterToUnicode = {
    'a': 'ᴀ',
    'b': 'ʙ',
    'c': 'ᴄ',
    'd': 'ᴅ',
    'e': 'ᴇ',
    'f': 'ꜰ',
    'g': 'ɢ',
    'h': 'ʜ',
    'i': 'ɪ',
    'j': 'ᴊ',
    'k': 'ᴋ',
    'l': 'ʟ',
    'm': 'ᴍ',
    'n': 'ɴ',
    'o': 'ᴏ',
    'p': 'ᴘ',
    'q': 'ǫ',
    'r': 'ʀ',
    's': 'ꜱ',
    't': 'ᴛ',
    'u': 'ᴜ',
    'v': 'ᴠ',
    'w': 'ᴡ',
    'x': 'x',
    'y': 'ʏ',
    'z': 'ᴢ',
  };

  /// 生成引流图片
  Future<Uint8List> generateTrafficImage(TrafficImageConfig config) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = Size(800, 600);

    // 绘制高级渐变背景
    _drawAdvancedBackground(canvas, size, config);

    // 添加装饰图层
    _addDecorationLayer(canvas, size, config);

    // 添加高级干扰元素
    _addAdvancedNoise(canvas, size, config.noiseLevel);

    // 绘制主文本
    _drawStylizedText(canvas, size, config);

    // 添加高级扭曲效果
    if (config.distortionLevel > 0) {
      _applyAdvancedDistortion(canvas, size, config.distortionLevel);
    }

    // 添加图层混合效果
    _applyLayerBlending(canvas, size, config);

    // 添加水印
    if (config.addWatermark && config.watermarkText.isNotEmpty) {
      _addStylizedWatermark(canvas, size, config);
    }

    final picture = recorder.endRecording();
    final image = await picture.toImage(
      size.width.toInt(),
      size.height.toInt(),
    );
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    return byteData!.buffer.asUint8List();
  }

  /// 转换文本（添加不可见字符、emoji转换等）
  String transformText(String text, TextTransformConfig config) {
    String result = text;

    if (config.enableEmojiConversion) {
      result = _convertToEmoji(result);
    }

    if (config.enableUnicodeVariation) {
      result = _applyUnicodeVariation(result);
    }

    if (config.enableInvisibleChars) {
      result = _addInvisibleChars(result);
    }

    if (config.enableSensitiveWordMasking) {
      result = _maskSensitiveWords(result, config.sensitiveWords);
    }

    // 应用自定义emoji映射
    for (final entry in config.customEmojiMap.entries) {
      result = result.replaceAll(entry.key, entry.value);
    }

    return result;
  }

  /// 添加水印到文本
  String addTextWatermark(String text, WatermarkConfig config) {
    if (config.invisible) {
      // 使用不可见字符添加隐形水印
      final watermark = _encodeInvisibleWatermark(config.text);
      return text + watermark;
    } else {
      // 可见水印
      return '$text\n${config.text}';
    }
  }

  /// 移除文本水印
  String removeTextWatermark(String text, WatermarkConfig config) {
    if (config.invisible) {
      return _decodeInvisibleWatermark(text);
    } else {
      // 移除可见水印
      final lines = text.split('\n');
      if (lines.isNotEmpty && lines.last == config.text) {
        lines.removeLast();
        return lines.join('\n');
      }
      return text;
    }
  }

  /// 保存项目
  Future<void> saveProject(TrafficGuideProject project) async {
    final dir = await getApplicationDocumentsDirectory();
    final file = File('${dir.path}/traffic_guide_${project.id}.json');
    await file.writeAsString(jsonEncode(project.toJson()));
  }

  /// 加载项目
  Future<TrafficGuideProject?> loadProject(String id) async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final file = File('${dir.path}/traffic_guide_$id.json');
      if (await file.exists()) {
        final content = await file.readAsString();
        return TrafficGuideProject.fromJson(jsonDecode(content));
      }
    } catch (e) {
      debugPrint('加载项目失败: $e');
    }
    return null;
  }

  /// 获取所有项目
  Future<List<TrafficGuideProject>> getAllProjects() async {
    final List<TrafficGuideProject> projects = [];
    try {
      final dir = await getApplicationDocumentsDirectory();
      final files = dir.listSync().where(
        (file) =>
            file.path.contains('traffic_guide_') && file.path.endsWith('.json'),
      );

      for (final file in files) {
        final content = await File(file.path).readAsString();
        projects.add(TrafficGuideProject.fromJson(jsonDecode(content)));
      }
    } catch (e) {
      debugPrint('获取项目列表失败: $e');
    }
    return projects;
  }

  // 私有方法

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final hex = colorString.substring(1);
      return Color(int.parse('FF$hex', radix: 16));
    }
    return Colors.black;
  }

  void _drawStylizedText(Canvas canvas, Size size, TrafficImageConfig config) {
    // 将文本分解为单个字符
    final characters = config.text.split('');
    final random = math.Random();

    // 计算基础字符间距，让文字充分利用整个图片宽度
    final baseCharWidth = config.fontSize * 0.8;
    final totalWidth = characters.length * baseCharWidth;

    // 如果文字总宽度超过图片宽度，则调整字符间距
    final availableWidth = size.width * 0.9; // 留10%边距
    final adjustedCharWidth =
        totalWidth > availableWidth
            ? availableWidth / characters.length
            : baseCharWidth;

    final startX = (size.width - (characters.length * adjustedCharWidth)) / 2;
    final centerY = size.height / 2;

    // 为每个字符创建不规则间距
    double currentX = startX;
    final charPositions = <int, Offset>{};

    for (int i = 0; i < characters.length; i++) {
      // 添加不规则的字符间距
      final spacingVariation =
          (random.nextDouble() - 0.5) * adjustedCharWidth * 0.6;
      currentX += spacingVariation;

      // 添加垂直偏移，让文字可以在整个高度范围内分布
      final verticalRange = size.height * 0.2; // 使用20%的图片高度
      final verticalOffset = (random.nextDouble() - 0.5) * verticalRange;

      charPositions[i] = Offset(currentX, centerY + verticalOffset);
      currentX += adjustedCharWidth;
    }

    // 绘制每个字符
    for (int i = 0; i < characters.length; i++) {
      final char = characters[i];
      final position = charPositions[i]!;

      // 为每个字符添加轻微的旋转
      final rotation = (random.nextDouble() - 0.5) * 0.4; // ±0.2弧度

      // 为每个字符添加轻微的缩放
      final scale = 0.85 + random.nextDouble() * 0.3; // 0.85-1.15倍

      // 为每个字符添加轻微的透明度变化
      final opacity = 0.75 + random.nextDouble() * 0.25; // 0.75-1.0

      canvas.save();
      canvas.translate(position.dx, position.dy);
      canvas.rotate(rotation);
      canvas.scale(scale);

      // 绘制字符阴影
      final shadowStyle = TextStyle(
        fontSize: config.fontSize,
        color: Colors.black.withValues(alpha: 0.3 * opacity),
        fontFamily: config.fontFamily,
        fontWeight: FontWeight.bold,
      );

      final shadowSpan = TextSpan(text: char, style: shadowStyle);
      final shadowPainter = TextPainter(
        text: shadowSpan,
        textDirection: TextDirection.ltr,
      );

      shadowPainter.layout();
      shadowPainter.paint(
        canvas,
        Offset(2, 2) -
            Offset(shadowPainter.width / 2, shadowPainter.height / 2),
      );

      // 绘制主字符
      final charStyle = TextStyle(
        fontSize: config.fontSize,
        color: _parseColor(config.textColor).withValues(alpha: opacity),
        fontFamily: config.fontFamily,
        fontWeight: FontWeight.bold,
        shadows: [
          Shadow(
            offset: const Offset(1, 1),
            blurRadius: 2,
            color: Colors.white.withValues(alpha: 0.3),
          ),
        ],
      );

      final charSpan = TextSpan(text: char, style: charStyle);
      final charPainter = TextPainter(
        text: charSpan,
        textDirection: TextDirection.ltr,
      );

      charPainter.layout();
      charPainter.paint(
        canvas,
        Offset(-charPainter.width / 2, -charPainter.height / 2),
      );

      canvas.restore();
    }

    // 添加文字装饰效果
    _addTextDecoration(canvas, charPositions, config);
  }

  void _addTextDecoration(
    Canvas canvas,
    Map<int, Offset> charPositions,
    TrafficImageConfig config,
  ) {
    if (charPositions.isEmpty) return;

    final paint =
        Paint()
          ..color = _parseColor(config.textColor).withValues(alpha: 0.1)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    // 计算文字区域
    final positions = charPositions.values.toList();
    final minX = positions.map((p) => p.dx).reduce(math.min);
    final maxX = positions.map((p) => p.dx).reduce(math.max);
    final minY = positions.map((p) => p.dy).reduce(math.min);
    final maxY = positions.map((p) => p.dy).reduce(math.max);

    final textRect = Rect.fromLTWH(
      minX - 20,
      minY - 20,
      maxX - minX + 40,
      maxY - minY + 40,
    );

    // 绘制装饰框
    canvas.drawRRect(
      RRect.fromRectAndRadius(textRect, const Radius.circular(8)),
      paint,
    );

    // 添加连接线（连接相邻字符）
    final positionsList =
        charPositions.entries.toList()..sort((a, b) => a.key.compareTo(b.key));

    for (int i = 0; i < positionsList.length - 1; i++) {
      final current = positionsList[i].value;
      final next = positionsList[i + 1].value;

      // 随机决定是否绘制连接线
      if (math.Random().nextDouble() > 0.7) {
        final linePaint =
            Paint()
              ..color = _parseColor(config.textColor).withValues(alpha: 0.05)
              ..strokeWidth = 0.5
              ..style = PaintingStyle.stroke;

        canvas.drawLine(current, next, linePaint);
      }
    }
  }


  String _convertToEmoji(String text) {
    String result = text;

    // 转换数字
    for (final entry in _numberToEmoji.entries) {
      result = result.replaceAll(entry.key, entry.value);
    }

    // 转换字母
    for (final entry in _letterToUnicode.entries) {
      result = result.replaceAll(entry.key, entry.value);
      result = result.replaceAll(entry.key.toUpperCase(), entry.value);
    }

    return result;
  }

  String _applyUnicodeVariation(String text) {
    String result = text;
    final random = math.Random();

    // 随机添加变音字符
    for (int i = 0; i < text.length; i++) {
      if (random.nextDouble() < 0.1) {
        // 10%概率添加变音字符
        final diacritic = _diacriticChars.keys.elementAt(
          random.nextInt(_diacriticChars.length),
        );
        result =
            result.substring(0, i + 1) + diacritic + result.substring(i + 1);
      }
    }

    return result;
  }

  String _addInvisibleChars(String text) {
    String result = text;
    final random = math.Random();

    // 在字符间随机添加不可见字符
    for (int i = 0; i < text.length - 1; i++) {
      if (random.nextDouble() < 0.2) {
        // 20%概率添加不可见字符
        final invisibleChar = _invisibleChars.keys.elementAt(
          random.nextInt(_invisibleChars.length),
        );
        result =
            result.substring(0, i + 1) +
            invisibleChar +
            result.substring(i + 1);
      }
    }

    return result;
  }

  String _maskSensitiveWords(String text, List<String> sensitiveWords) {
    String result = text;
    final random = math.Random();

    for (final word in sensitiveWords) {
      if (result.contains(word)) {
        // 在敏感词中间插入不可见字符
        final invisibleChar = _invisibleChars.keys.elementAt(
          random.nextInt(_invisibleChars.length),
        );
        final maskedWord = word.split('').join(invisibleChar);
        result = result.replaceAll(word, maskedWord);
      }
    }

    return result;
  }

  String _encodeInvisibleWatermark(String watermark) {
    // 将水印文本编码为不可见字符序列
    final bytes = utf8.encode(watermark);
    String encoded = '';

    for (final byte in bytes) {
      final invisibleChar = _invisibleChars.keys.elementAt(
        byte % _invisibleChars.length,
      );
      encoded += invisibleChar;
    }

    return encoded;
  }

  String _decodeInvisibleWatermark(String text) {
    // 从文本中提取隐形水印
    final invisibleChars = _invisibleChars.keys.toList();
    String watermark = '';

    for (int i = 0; i < text.length; i++) {
      final char = text[i];
      if (invisibleChars.contains(char)) {
        watermark += char;
      }
    }

    // 解码水印
    try {
      final bytes = <int>[];
      for (int i = 0; i < watermark.length; i++) {
        final char = watermark[i];
        final index = invisibleChars.indexOf(char);
        bytes.add(index);
      }
      return utf8.decode(bytes);
    } catch (e) {
      return '';
    }
  }

  void _drawAdvancedBackground(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
  ) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // 创建复杂的渐变背景
    final List<Color> gradientColors = [
      _parseColor(config.backgroundColor),
      _parseColor(config.backgroundColor).withValues(alpha: 0.8),
      _parseColor(config.backgroundColor).withValues(alpha: 0.6),
      _parseColor(config.backgroundColor).withValues(alpha: 0.9),
      _parseColor(config.backgroundColor),
    ];

    // 主渐变
    final mainGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: gradientColors,
      stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
    );

    // 绘制主渐变
    canvas.drawRect(rect, Paint()..shader = mainGradient.createShader(rect));

    // 添加径向渐变叠加
    final radialGradient = RadialGradient(
      center: Alignment.center,
      radius: 1.2,
      colors: [
        Colors.white.withValues(alpha: 0.15),
        Colors.white.withValues(alpha: 0.05),
        Colors.transparent,
      ],
      stops: const [0.0, 0.5, 1.0],
    );

    canvas.drawRect(rect, Paint()..shader = radialGradient.createShader(rect));

    // 添加纹理效果
    _addTextureEffect(canvas, size, config);
  }

  void _addTextureEffect(Canvas canvas, Size size, TrafficImageConfig config) {
    final random = math.Random();
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.03)
          ..strokeWidth = 0.5;

    // 添加细线纹理
    for (int i = 0; i < 100; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      canvas.drawLine(
        Offset(x, y),
        Offset(x + random.nextDouble() * 20, y + random.nextDouble() * 20),
        paint,
      );
    }

    // 添加点状纹理
    for (int i = 0; i < 200; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      canvas.drawCircle(Offset(x, y), 0.5, paint);
    }
  }

  void _addAdvancedNoise(Canvas canvas, Size size, double level) {
    final random = math.Random();

    // 添加多层次噪点
    _addNoiseLayer(canvas, size, level, 0.05, 300); // 小噪点
    _addNoiseLayer(canvas, size, level, 0.08, 200); // 中等噪点
    _addNoiseLayer(canvas, size, level, 0.12, 100); // 大噪点

    // 添加扭曲线条
    for (int i = 0; i < 15 * level; i++) {
      final paint =
          Paint()
            ..color = Colors.white.withValues(alpha: 0.1 * level)
            ..strokeWidth = random.nextDouble() * 2 + 0.5
            ..style = PaintingStyle.stroke;

      final path = Path();
      var x = random.nextDouble() * size.width;
      var y = random.nextDouble() * size.height;
      path.moveTo(x, y);

      // 创建贝塞尔曲线
      for (int j = 0; j < 3; j++) {
        final cp1x = x + (random.nextDouble() - 0.5) * 100;
        final cp1y = y + (random.nextDouble() - 0.5) * 100;
        final cp2x = x + (random.nextDouble() - 0.5) * 100;
        final cp2y = y + (random.nextDouble() - 0.5) * 100;
        x += (random.nextDouble() - 0.5) * 100;
        y += (random.nextDouble() - 0.5) * 100;
        path.cubicTo(cp1x, cp1y, cp2x, cp2y, x, y);
      }

      canvas.drawPath(path, paint);
    }

    // 添加干扰形状
    _addInterferenceShapes(canvas, size, level);
  }

  void _addNoiseLayer(
    Canvas canvas,
    Size size,
    double level,
    double opacity,
    int count,
  ) {
    final random = math.Random();
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: opacity * level)
          ..style = PaintingStyle.fill;

    for (int i = 0; i < count; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 2 + 0.5;
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  void _addInterferenceShapes(Canvas canvas, Size size, double level) {
    final random = math.Random();
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.05 * level)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    for (int i = 0; i < 10 * level; i++) {
      final path = Path();
      final centerX = random.nextDouble() * size.width;
      final centerY = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 30 + 10;

      // 创建不规则多边形
      path.moveTo(
        centerX + radius * math.cos(0),
        centerY + radius * math.sin(0),
      );

      for (int j = 1; j <= 6; j++) {
        final angle = j * math.pi / 3;
        final jitter = random.nextDouble() * 10 - 5;
        path.lineTo(
          centerX + (radius + jitter) * math.cos(angle),
          centerY + (radius + jitter) * math.sin(angle),
        );
      }

      path.close();
      canvas.drawPath(path, paint);
    }
  }

  void _applyAdvancedDistortion(Canvas canvas, Size size, double level) {
    final random = math.Random();
    canvas.save();

    // 应用多层扭曲效果
    for (int i = 0; i < 3; i++) {
      final distortionLevel = level * (1 - i * 0.2); // 逐层减小扭曲程度

      // 旋转扭曲
      final rotation = (random.nextDouble() - 0.5) * distortionLevel * 0.1;
      canvas.translate(size.width / 2, size.height / 2);
      canvas.rotate(rotation);
      canvas.translate(-size.width / 2, -size.height / 2);

      // 缩放扭曲
      final scaleX = 1.0 + (random.nextDouble() - 0.5) * distortionLevel * 0.08;
      final scaleY = 1.0 + (random.nextDouble() - 0.5) * distortionLevel * 0.08;
      canvas.scale(scaleX, scaleY);

      // 错切扭曲
      final skewX = (random.nextDouble() - 0.5) * distortionLevel * 0.05;
      final skewY = (random.nextDouble() - 0.5) * distortionLevel * 0.05;
      canvas.skew(skewX, skewY);
    }

    canvas.restore();
  }

  void _applyLayerBlending(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
  ) {
    // 添加高光层
    final highlightPaint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 20);

    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      highlightPaint,
    );

    // 添加暗部层
    final shadowPaint =
        Paint()
          ..color = Colors.black.withValues(alpha: 0.1)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15);

    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), shadowPaint);
  }

  void _addStylizedWatermark(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
  ) {
    final watermarkStyle = TextStyle(
      fontSize: 14,
      color: Colors.white.withValues(alpha: 0.3),
      fontWeight: FontWeight.w500,
      letterSpacing: 2,
    );

    final textSpan = TextSpan(
      text: config.watermarkText,
      style: watermarkStyle,
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // 在四个角落添加水印
    final positions = [
      Offset(10, 10), // 左上
      Offset(size.width - textPainter.width - 10, 10), // 右上
      Offset(10, size.height - textPainter.height - 10), // 左下
      Offset(
        size.width - textPainter.width - 10,
        size.height - textPainter.height - 10,
      ), // 右下
    ];

    for (final position in positions) {
      canvas.save();
      canvas.translate(position.dx, position.dy);
      canvas.rotate(math.pi / 6); // 倾斜水印
      textPainter.paint(canvas, Offset.zero);
      canvas.restore();
    }
  }

  void _addDecorationLayer(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
  ) {
    final random = math.Random();

    // 添加装饰性几何图形
    for (int i = 0; i < 8; i++) {
      final paint =
          Paint()
            ..color = Colors.white.withValues(alpha: 0.05)
            ..style = PaintingStyle.fill
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5);

      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 60 + 30;

      // 随机选择形状类型
      final shapeType = random.nextInt(3);
      switch (shapeType) {
        case 0: // 圆形
          canvas.drawCircle(Offset(x, y), radius, paint);
          break;
        case 1: // 矩形
          canvas.drawRect(
            Rect.fromCenter(
              center: Offset(x, y),
              width: radius * 2,
              height: radius * 1.5,
            ),
            paint,
          );
          break;
        case 2: // 多边形
          final path = Path();
          final sides = random.nextInt(3) + 5; // 5-7边形
          path.moveTo(x + radius * math.cos(0), y + radius * math.sin(0));

          for (int j = 1; j <= sides; j++) {
            final angle = j * 2 * math.pi / sides;
            path.lineTo(
              x + radius * math.cos(angle),
              y + radius * math.sin(angle),
            );
          }

          path.close();
          canvas.drawPath(path, paint);
          break;
      }
    }

    // 添加装饰性线条
    for (int i = 0; i < 5; i++) {
      final paint =
          Paint()
            ..color = Colors.white.withValues(alpha: 0.03)
            ..strokeWidth = random.nextDouble() * 3 + 1
            ..style = PaintingStyle.stroke
            ..strokeCap = StrokeCap.round;

      final path = Path();
      var x = random.nextDouble() * size.width;
      var y = random.nextDouble() * size.height;
      path.moveTo(x, y);

      // 创建平滑曲线
      for (int j = 0; j < 4; j++) {
        x += (random.nextDouble() - 0.5) * 200;
        y += (random.nextDouble() - 0.5) * 200;
        final cp1x = x - 50 + random.nextDouble() * 100;
        final cp1y = y - 50 + random.nextDouble() * 100;
        final cp2x = x - 50 + random.nextDouble() * 100;
        final cp2y = y - 50 + random.nextDouble() * 100;
        path.cubicTo(cp1x, cp1y, cp2x, cp2y, x, y);
      }

      canvas.drawPath(path, paint);
    }

    // 添加光效
    final gradient = RadialGradient(
      center: Alignment(
        random.nextDouble() * 2 - 1,
        random.nextDouble() * 2 - 1,
      ),
      radius: 0.7,
      colors: [
        Colors.white.withValues(alpha: 0.1),
        Colors.white.withValues(alpha: 0.05),
        Colors.transparent,
      ],
      stops: const [0.0, 0.5, 1.0],
    );

    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()
        ..shader = gradient.createShader(
          Rect.fromLTWH(0, 0, size.width, size.height),
        ),
    );
  }
}
