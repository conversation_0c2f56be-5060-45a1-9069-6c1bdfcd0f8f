import 'package:flutter/material.dart';

/// 图片模板类型
enum TemplateType {
  minimal, // 极简风格
  modern, // 现代风格
  creative, // 创意风格
  professional, // 专业风格
  artistic, // 艺术风格
  tech, // 科技风格
}

/// 文字布局类型
enum TextLayout {
  center, // 居中
  left, // 左对齐
  right, // 右对齐
  scattered, // 散布
  curved, // 曲线
  diagonal, // 对角线
}

/// 背景类型
enum BackgroundType {
  solid, // 纯色
  gradient, // 渐变
  pattern, // 图案
  texture, // 纹理
  geometric, // 几何
}

/// 图片模板配置
class ImageTemplate {
  final String id;
  final String name;
  final String description;
  final TemplateType type;
  final String thumbnailPath;

  // 布局配置
  final TextLayout textLayout;
  final BackgroundType backgroundType;
  final Size canvasSize;

  // 颜色配置
  final List<Color> primaryColors;
  final List<Color> secondaryColors;
  final List<List<Color>> gradientPresets;

  // 字体配置
  final List<String> recommendedFonts;
  final double baseFontSize;
  final FontWeight fontWeight;

  // 效果配置
  final bool hasGlow;
  final bool hasShadow;
  final bool hasOutline;
  final double noiseLevel;
  final double distortionLevel;

  // 装饰元素
  final List<DecorationElement> decorations;

  const ImageTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.thumbnailPath,
    this.textLayout = TextLayout.center,
    this.backgroundType = BackgroundType.gradient,
    this.canvasSize = const Size(800, 600),
    this.primaryColors = const [Colors.blue, Colors.purple],
    this.secondaryColors = const [Colors.white, Colors.black],
    this.gradientPresets = const [],
    this.recommendedFonts = const ['Arial', 'Helvetica'],
    this.baseFontSize = 48.0,
    this.fontWeight = FontWeight.bold,
    this.hasGlow = false,
    this.hasShadow = true,
    this.hasOutline = false,
    this.noiseLevel = 0.3,
    this.distortionLevel = 0.2,
    this.decorations = const [],
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'type': type.name,
    'thumbnailPath': thumbnailPath,
    'textLayout': textLayout.name,
    'backgroundType': backgroundType.name,
    'canvasSize': {'width': canvasSize.width, 'height': canvasSize.height},
    'primaryColors': primaryColors.map((c) => c.toARGB32()).toList(),
    'secondaryColors': secondaryColors.map((c) => c.toARGB32()).toList(),
    'gradientPresets':
        gradientPresets
            .map((preset) => preset.map((c) => c.toARGB32()).toList())
            .toList(),
    'recommendedFonts': recommendedFonts,
    'baseFontSize': baseFontSize,
    'fontWeight': fontWeight.index,
    'hasGlow': hasGlow,
    'hasShadow': hasShadow,
    'hasOutline': hasOutline,
    'noiseLevel': noiseLevel,
    'distortionLevel': distortionLevel,
    'decorations': decorations.map((d) => d.toJson()).toList(),
  };

  factory ImageTemplate.fromJson(Map<String, dynamic> json) {
    return ImageTemplate(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      type: TemplateType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => TemplateType.modern,
      ),
      thumbnailPath: json['thumbnailPath'] ?? '',
      textLayout: TextLayout.values.firstWhere(
        (l) => l.name == json['textLayout'],
        orElse: () => TextLayout.center,
      ),
      backgroundType: BackgroundType.values.firstWhere(
        (b) => b.name == json['backgroundType'],
        orElse: () => BackgroundType.gradient,
      ),
      canvasSize: Size(
        json['canvasSize']?['width']?.toDouble() ?? 800.0,
        json['canvasSize']?['height']?.toDouble() ?? 600.0,
      ),
      primaryColors:
          (json['primaryColors'] as List<dynamic>?)
              ?.map((c) => Color(c as int))
              .toList() ??
          [Colors.blue, Colors.purple],
      secondaryColors:
          (json['secondaryColors'] as List<dynamic>?)
              ?.map((c) => Color(c as int))
              .toList() ??
          [Colors.white, Colors.black],
      gradientPresets:
          (json['gradientPresets'] as List<dynamic>?)
              ?.map(
                (preset) =>
                    (preset as List<dynamic>)
                        .map((c) => Color(c as int))
                        .toList(),
              )
              .toList() ??
          [],
      recommendedFonts: List<String>.from(
        json['recommendedFonts'] ?? ['Arial'],
      ),
      baseFontSize: json['baseFontSize']?.toDouble() ?? 48.0,
      fontWeight:
          FontWeight.values[json['fontWeight'] ?? FontWeight.bold.index],
      hasGlow: json['hasGlow'] ?? false,
      hasShadow: json['hasShadow'] ?? true,
      hasOutline: json['hasOutline'] ?? false,
      noiseLevel: json['noiseLevel']?.toDouble() ?? 0.3,
      distortionLevel: json['distortionLevel']?.toDouble() ?? 0.2,
      decorations:
          (json['decorations'] as List<dynamic>?)
              ?.map((d) => DecorationElement.fromJson(d))
              .toList() ??
          [],
    );
  }

  ImageTemplate copyWith({
    String? id,
    String? name,
    String? description,
    TemplateType? type,
    String? thumbnailPath,
    TextLayout? textLayout,
    BackgroundType? backgroundType,
    Size? canvasSize,
    List<Color>? primaryColors,
    List<Color>? secondaryColors,
    List<List<Color>>? gradientPresets,
    List<String>? recommendedFonts,
    double? baseFontSize,
    FontWeight? fontWeight,
    bool? hasGlow,
    bool? hasShadow,
    bool? hasOutline,
    double? noiseLevel,
    double? distortionLevel,
    List<DecorationElement>? decorations,
  }) {
    return ImageTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      textLayout: textLayout ?? this.textLayout,
      backgroundType: backgroundType ?? this.backgroundType,
      canvasSize: canvasSize ?? this.canvasSize,
      primaryColors: primaryColors ?? this.primaryColors,
      secondaryColors: secondaryColors ?? this.secondaryColors,
      gradientPresets: gradientPresets ?? this.gradientPresets,
      recommendedFonts: recommendedFonts ?? this.recommendedFonts,
      baseFontSize: baseFontSize ?? this.baseFontSize,
      fontWeight: fontWeight ?? this.fontWeight,
      hasGlow: hasGlow ?? this.hasGlow,
      hasShadow: hasShadow ?? this.hasShadow,
      hasOutline: hasOutline ?? this.hasOutline,
      noiseLevel: noiseLevel ?? this.noiseLevel,
      distortionLevel: distortionLevel ?? this.distortionLevel,
      decorations: decorations ?? this.decorations,
    );
  }
}

/// 装饰元素
class DecorationElement {
  final String type; // 'circle', 'line', 'polygon', 'pattern'
  final Offset position;
  final Size size;
  final Color color;
  final double opacity;
  final double rotation;
  final Map<String, dynamic> properties;

  const DecorationElement({
    required this.type,
    required this.position,
    required this.size,
    required this.color,
    this.opacity = 1.0,
    this.rotation = 0.0,
    this.properties = const {},
  });

  Map<String, dynamic> toJson() => {
    'type': type,
    'position': {'dx': position.dx, 'dy': position.dy},
    'size': {'width': size.width, 'height': size.height},
    'color': color.toARGB32(),
    'opacity': opacity,
    'rotation': rotation,
    'properties': properties,
  };

  factory DecorationElement.fromJson(Map<String, dynamic> json) {
    return DecorationElement(
      type: json['type'] ?? '',
      position: Offset(
        json['position']?['dx']?.toDouble() ?? 0.0,
        json['position']?['dy']?.toDouble() ?? 0.0,
      ),
      size: Size(
        json['size']?['width']?.toDouble() ?? 0.0,
        json['size']?['height']?.toDouble() ?? 0.0,
      ),
      color: Color(json['color'] ?? Colors.white.toARGB32()),
      opacity: json['opacity']?.toDouble() ?? 1.0,
      rotation: json['rotation']?.toDouble() ?? 0.0,
      properties: Map<String, dynamic>.from(json['properties'] ?? {}),
    );
  }
}

/// 预设模板数据
class TemplatePresets {
  static const List<ImageTemplate> templates = [
    ImageTemplate(
      id: 'minimal_01',
      name: '极简白',
      description: '简洁优雅的白色主题',
      type: TemplateType.minimal,
      thumbnailPath: 'assets/templates/minimal_01.png',
      textLayout: TextLayout.center,
      backgroundType: BackgroundType.gradient,
      primaryColors: [Color(0xFFFFFFFF), Color(0xFFF8F9FA)],
      secondaryColors: [Color(0xFF212529), Color(0xFF6C757D)],
      gradientPresets: [
        [Color(0xFFFFFFFF), Color(0xFFF8F9FA)],
        [Color(0xFFF8F9FA), Color(0xFFE9ECEF)],
      ],
      baseFontSize: 52.0,
      fontWeight: FontWeight.w300,
      hasGlow: false,
      hasShadow: true,
      noiseLevel: 0.1,
      distortionLevel: 0.05,
    ),

    ImageTemplate(
      id: 'modern_01',
      name: '现代渐变',
      description: '时尚的渐变色设计',
      type: TemplateType.modern,
      thumbnailPath: 'assets/templates/modern_01.png',
      textLayout: TextLayout.center,
      backgroundType: BackgroundType.gradient,
      primaryColors: [Color(0xFF667EEA), Color(0xFF764BA2)],
      secondaryColors: [Color(0xFFFFFFFF), Color(0xFFF8F9FA)],
      gradientPresets: [
        [Color(0xFF667EEA), Color(0xFF764BA2)],
        [Color(0xFFF093FB), Color(0xFFF5576C)],
        [Color(0xFF4FACFE), Color(0xFF00F2FE)],
      ],
      baseFontSize: 48.0,
      fontWeight: FontWeight.bold,
      hasGlow: true,
      hasShadow: true,
      noiseLevel: 0.2,
      distortionLevel: 0.15,
    ),

    ImageTemplate(
      id: 'creative_01',
      name: '创意散布',
      description: '创意文字散布效果',
      type: TemplateType.creative,
      thumbnailPath: 'assets/templates/creative_01.png',
      textLayout: TextLayout.scattered,
      backgroundType: BackgroundType.pattern,
      primaryColors: [Color(0xFFFF6B6B), Color(0xFF4ECDC4)],
      secondaryColors: [Color(0xFFFFFFFF), Color(0xFF2C3E50)],
      baseFontSize: 44.0,
      fontWeight: FontWeight.w600,
      hasGlow: false,
      hasShadow: true,
      noiseLevel: 0.3,
      distortionLevel: 0.25,
    ),

    ImageTemplate(
      id: 'professional_01',
      name: '商务专业',
      description: '专业商务风格',
      type: TemplateType.professional,
      thumbnailPath: 'assets/templates/professional_01.png',
      textLayout: TextLayout.left,
      backgroundType: BackgroundType.solid,
      primaryColors: [Color(0xFF2C3E50), Color(0xFF34495E)],
      secondaryColors: [Color(0xFFFFFFFF), Color(0xFFECF0F1)],
      baseFontSize: 50.0,
      fontWeight: FontWeight.w700,
      hasGlow: false,
      hasShadow: false,
      noiseLevel: 0.05,
      distortionLevel: 0.0,
    ),

    ImageTemplate(
      id: 'artistic_01',
      name: '艺术曲线',
      description: '艺术感曲线文字',
      type: TemplateType.artistic,
      thumbnailPath: 'assets/templates/artistic_01.png',
      textLayout: TextLayout.curved,
      backgroundType: BackgroundType.texture,
      primaryColors: [Color(0xFF8E44AD), Color(0xFF3498DB)],
      secondaryColors: [Color(0xFFFFFFFF), Color(0xFFF39C12)],
      baseFontSize: 46.0,
      fontWeight: FontWeight.w500,
      hasGlow: true,
      hasShadow: true,
      noiseLevel: 0.15,
      distortionLevel: 0.1,
    ),

    ImageTemplate(
      id: 'tech_01',
      name: '科技未来',
      description: '科技感几何设计',
      type: TemplateType.tech,
      thumbnailPath: 'assets/templates/tech_01.png',
      textLayout: TextLayout.diagonal,
      backgroundType: BackgroundType.geometric,
      primaryColors: [Color(0xFF00BCD4), Color(0xFF009688)],
      secondaryColors: [Color(0xFFFFFFFF), Color(0xFF263238)],
      baseFontSize: 48.0,
      fontWeight: FontWeight.w800,
      hasGlow: true,
      hasShadow: false,
      noiseLevel: 0.2,
      distortionLevel: 0.1,
    ),
  ];
}
