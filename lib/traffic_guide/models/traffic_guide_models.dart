/// 引流图片生成配置
class TrafficImageConfig {
  final String text;
  final double fontSize;
  final String fontFamily;
  final String backgroundColor;
  final String textColor;
  final double noiseLevel; // 干扰程度 0.0-1.0
  final double distortionLevel; // 扭曲程度 0.0-1.0
  final bool addWatermark;
  final String watermarkText;

  TrafficImageConfig({
    required this.text,
    this.fontSize = 48.0,
    this.fontFamily = 'Arial',
    this.backgroundColor = '#FFFFFF',
    this.textColor = '#000000',
    this.noiseLevel = 0.3,
    this.distortionLevel = 0.2,
    this.addWatermark = false,
    this.watermarkText = '',
  });

  Map<String, dynamic> toJson() => {
    'text': text,
    'fontSize': fontSize,
    'fontFamily': fontFamily,
    'backgroundColor': backgroundColor,
    'textColor': textColor,
    'noiseLevel': noiseLevel,
    'distortionLevel': distortionLevel,
    'addWatermark': addWatermark,
    'watermarkText': watermarkText,
  };

  factory TrafficImageConfig.fromJson(Map<String, dynamic> json) {
    return TrafficImageConfig(
      text: json['text'] ?? '',
      fontSize: json['fontSize']?.toDouble() ?? 48.0,
      fontFamily: json['fontFamily'] ?? 'Arial',
      backgroundColor: json['backgroundColor'] ?? '#FFFFFF',
      textColor: json['textColor'] ?? '#000000',
      noiseLevel: json['noiseLevel']?.toDouble() ?? 0.3,
      distortionLevel: json['distortionLevel']?.toDouble() ?? 0.2,
      addWatermark: json['addWatermark'] ?? false,
      watermarkText: json['watermarkText'] ?? '',
    );
  }

  TrafficImageConfig copyWith({
    String? text,
    double? fontSize,
    String? fontFamily,
    String? backgroundColor,
    String? textColor,
    double? noiseLevel,
    double? distortionLevel,
    bool? addWatermark,
    String? watermarkText,
  }) {
    return TrafficImageConfig(
      text: text ?? this.text,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      noiseLevel: noiseLevel ?? this.noiseLevel,
      distortionLevel: distortionLevel ?? this.distortionLevel,
      addWatermark: addWatermark ?? this.addWatermark,
      watermarkText: watermarkText ?? this.watermarkText,
    );
  }
}

/// 文本转换配置
class TextTransformConfig {
  final bool enableEmojiConversion;
  final bool enableUnicodeVariation;
  final bool enableInvisibleChars;
  final bool enableSensitiveWordMasking;
  final Map<String, String> customEmojiMap;
  final List<String> sensitiveWords;

  TextTransformConfig({
    this.enableEmojiConversion = true,
    this.enableUnicodeVariation = true,
    this.enableInvisibleChars = true,
    this.enableSensitiveWordMasking = true,
    this.customEmojiMap = const {},
    this.sensitiveWords = const [],
  });

  Map<String, dynamic> toJson() => {
    'enableEmojiConversion': enableEmojiConversion,
    'enableUnicodeVariation': enableUnicodeVariation,
    'enableInvisibleChars': enableInvisibleChars,
    'enableSensitiveWordMasking': enableSensitiveWordMasking,
    'customEmojiMap': customEmojiMap,
    'sensitiveWords': sensitiveWords,
  };

  factory TextTransformConfig.fromJson(Map<String, dynamic> json) {
    return TextTransformConfig(
      enableEmojiConversion: json['enableEmojiConversion'] ?? true,
      enableUnicodeVariation: json['enableUnicodeVariation'] ?? true,
      enableInvisibleChars: json['enableInvisibleChars'] ?? true,
      enableSensitiveWordMasking: json['enableSensitiveWordMasking'] ?? true,
      customEmojiMap: Map<String, String>.from(json['customEmojiMap'] ?? {}),
      sensitiveWords: List<String>.from(json['sensitiveWords'] ?? []),
    );
  }

  TextTransformConfig copyWith({
    bool? enableEmojiConversion,
    bool? enableUnicodeVariation,
    bool? enableInvisibleChars,
    bool? enableSensitiveWordMasking,
    Map<String, String>? customEmojiMap,
    List<String>? sensitiveWords,
  }) {
    return TextTransformConfig(
      enableEmojiConversion:
          enableEmojiConversion ?? this.enableEmojiConversion,
      enableUnicodeVariation:
          enableUnicodeVariation ?? this.enableUnicodeVariation,
      enableInvisibleChars: enableInvisibleChars ?? this.enableInvisibleChars,
      enableSensitiveWordMasking:
          enableSensitiveWordMasking ?? this.enableSensitiveWordMasking,
      customEmojiMap: customEmojiMap ?? this.customEmojiMap,
      sensitiveWords: sensitiveWords ?? this.sensitiveWords,
    );
  }
}

/// 水印配置
class WatermarkConfig {
  final String text;
  final double opacity;
  final double fontSize;
  final String color;
  final double rotation;
  final bool invisible;

  WatermarkConfig({
    required this.text,
    this.opacity = 0.3,
    this.fontSize = 16.0,
    this.color = '#000000',
    this.rotation = 0.0,
    this.invisible = false,
  });

  Map<String, dynamic> toJson() => {
    'text': text,
    'opacity': opacity,
    'fontSize': fontSize,
    'color': color,
    'rotation': rotation,
    'invisible': invisible,
  };

  factory WatermarkConfig.fromJson(Map<String, dynamic> json) {
    return WatermarkConfig(
      text: json['text'] ?? '',
      opacity: json['opacity']?.toDouble() ?? 0.3,
      fontSize: json['fontSize']?.toDouble() ?? 16.0,
      color: json['color'] ?? '#000000',
      rotation: json['rotation']?.toDouble() ?? 0.0,
      invisible: json['invisible'] ?? false,
    );
  }

  WatermarkConfig copyWith({
    String? text,
    double? opacity,
    double? fontSize,
    String? color,
    double? rotation,
    bool? invisible,
  }) {
    return WatermarkConfig(
      text: text ?? this.text,
      opacity: opacity ?? this.opacity,
      fontSize: fontSize ?? this.fontSize,
      color: color ?? this.color,
      rotation: rotation ?? this.rotation,
      invisible: invisible ?? this.invisible,
    );
  }
}

/// 引流项目
class TrafficGuideProject {
  final String id;
  final String name;
  final String description;
  final TrafficImageConfig imageConfig;
  final TextTransformConfig textConfig;
  final WatermarkConfig watermarkConfig;
  final DateTime createdAt;
  final DateTime updatedAt;

  TrafficGuideProject({
    required this.id,
    required this.name,
    required this.description,
    required this.imageConfig,
    required this.textConfig,
    required this.watermarkConfig,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'imageConfig': imageConfig.toJson(),
    'textConfig': textConfig.toJson(),
    'watermarkConfig': watermarkConfig.toJson(),
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
  };

  factory TrafficGuideProject.fromJson(Map<String, dynamic> json) {
    return TrafficGuideProject(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      imageConfig: TrafficImageConfig.fromJson(json['imageConfig'] ?? {}),
      textConfig: TextTransformConfig.fromJson(json['textConfig'] ?? {}),
      watermarkConfig: WatermarkConfig.fromJson(json['watermarkConfig'] ?? {}),
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  TrafficGuideProject copyWith({
    String? id,
    String? name,
    String? description,
    TrafficImageConfig? imageConfig,
    TextTransformConfig? textConfig,
    WatermarkConfig? watermarkConfig,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TrafficGuideProject(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageConfig: imageConfig ?? this.imageConfig,
      textConfig: textConfig ?? this.textConfig,
      watermarkConfig: watermarkConfig ?? this.watermarkConfig,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
