import 'package:flutter/material.dart';
import 'dart:math' as math;

class AdvancedColorPicker extends StatefulWidget {
  final Color initialColor;
  final Function(Color) onColorChanged;
  final List<Color> presetColors;
  final bool showGradientPicker;

  const AdvancedColorPicker({
    super.key,
    required this.initialColor,
    required this.onColorChanged,
    this.presetColors = const [],
    this.showGradientPicker = false,
  });

  @override
  State<AdvancedColorPicker> createState() => _AdvancedColorPickerState();
}

class _AdvancedColorPickerState extends State<AdvancedColorPicker>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late Color _currentColor;
  late HSVColor _hsvColor;

  // 预设颜色
  static const List<Color> _defaultPresets = [
    Color(0xFFFF6B6B),
    Color(0xFF4ECDC4),
    Color(0xFF45B7D1),
    Color(0xFF96CEB4),
    Color(0xFFFECA57),
    Color(0xFFFF9FF3),
    Color(0xFF54A0FF),
    Color(0xFF5F27CD),
    Color(0xFF00D2D3),
    Color(0xFFFF9F43),
    Color(0xFF10AC84),
    Color(0xFFEE5A24),
    Color(0xFF0984E3),
    Color(0xFF6C5CE7),
    Color(0xFFA29BFE),
    Color(0xFFE17055),
    Color(0xFF00B894),
    Color(0xFFE84393),
    Color(0xFF2D3436),
    Color(0xFFDDD3D8),
  ];

  // 渐变预设
  static const List<List<Color>> _gradientPresets = [
    [Color(0xFF667EEA), Color(0xFF764BA2)],
    [Color(0xFFF093FB), Color(0xFFF5576C)],
    [Color(0xFF4FACFE), Color(0xFF00F2FE)],
    [Color(0xFF43E97B), Color(0xFF38F9D7)],
    [Color(0xFFFA709A), Color(0xFFFEE140)],
    [Color(0xFFA8EDEA), Color(0xFFFED6E3)],
    [Color(0xFFFFE53B), Color(0xFFFF2525)],
    [Color(0xFF21D4FD), Color(0xFFB721FF)],
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.showGradientPicker ? 3 : 2,
      vsync: this,
    );
    _currentColor = widget.initialColor;
    _hsvColor = HSVColor.fromColor(_currentColor);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _currentColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '颜色选择器',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getColorHex(_currentColor),
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 标签栏
          TabBar(
            controller: _tabController,
            tabs: [
              const Tab(text: '调色板'),
              const Tab(text: '预设'),
              if (widget.showGradientPicker) const Tab(text: '渐变'),
            ],
          ),

          // 内容区域
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildColorWheel(),
                _buildPresetColors(),
                if (widget.showGradientPicker) _buildGradientPresets(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorWheel() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 色相环
          Expanded(
            flex: 3,
            child: Center(
              child: GestureDetector(
                onPanUpdate: _updateHue,
                child: CustomPaint(
                  size: const Size(200, 200),
                  painter: ColorWheelPainter(_hsvColor.hue),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 饱和度和亮度滑块
          Expanded(
            flex: 2,
            child: Column(
              children: [
                _buildSlider(
                  '饱和度',
                  _hsvColor.saturation,
                  (value) => _updateSaturation(value),
                  Colors.grey,
                  HSVColor.fromAHSV(
                    1,
                    _hsvColor.hue,
                    1,
                    _hsvColor.value,
                  ).toColor(),
                ),
                const SizedBox(height: 12),
                _buildSlider(
                  '亮度',
                  _hsvColor.value,
                  (value) => _updateValue(value),
                  Colors.black,
                  Colors.white,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetColors() {
    final presets =
        widget.presetColors.isNotEmpty ? widget.presetColors : _defaultPresets;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 5,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: presets.length,
        itemBuilder: (context, index) {
          final color = presets[index];
          final isSelected = color.toARGB32() == _currentColor.toARGB32();

          return GestureDetector(
            onTap: () => _selectColor(color),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected ? Colors.white : Colors.transparent,
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.3),
                    blurRadius: isSelected ? 12 : 6,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child:
                  isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 24)
                      : null,
            ),
          );
        },
      ),
    );
  }

  Widget _buildGradientPresets() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 2,
        ),
        itemCount: _gradientPresets.length,
        itemBuilder: (context, index) {
          final gradient = _gradientPresets[index];

          return GestureDetector(
            onTap: () => _selectColor(gradient.first),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: gradient),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSlider(
    String label,
    double value,
    Function(double) onChanged,
    Color startColor,
    Color endColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ${(value * 100).toInt()}%',
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Container(
          height: 30,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [startColor, endColor]),
            borderRadius: BorderRadius.circular(15),
          ),
          child: SliderTheme(
            data: SliderTheme.of(context).copyWith(
              trackHeight: 30,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 20),
              activeTrackColor: Colors.transparent,
              inactiveTrackColor: Colors.transparent,
              thumbColor: Colors.white,
              overlayColor: Colors.white.withValues(alpha: 0.2),
            ),
            child: Slider(value: value, onChanged: onChanged),
          ),
        ),
      ],
    );
  }

  void _updateHue(DragUpdateDetails details) {
    final center = const Offset(100, 100);
    final offset = details.localPosition - center;
    final angle = math.atan2(offset.dy, offset.dx);
    final hue = (angle * 180 / math.pi + 360) % 360;

    setState(() {
      _hsvColor = _hsvColor.withHue(hue);
      _currentColor = _hsvColor.toColor();
    });

    widget.onColorChanged(_currentColor);
  }

  void _updateSaturation(double saturation) {
    setState(() {
      _hsvColor = _hsvColor.withSaturation(saturation);
      _currentColor = _hsvColor.toColor();
    });

    widget.onColorChanged(_currentColor);
  }

  void _updateValue(double value) {
    setState(() {
      _hsvColor = _hsvColor.withValue(value);
      _currentColor = _hsvColor.toColor();
    });

    widget.onColorChanged(_currentColor);
  }

  void _selectColor(Color color) {
    setState(() {
      _currentColor = color;
      _hsvColor = HSVColor.fromColor(color);
    });

    widget.onColorChanged(_currentColor);
  }

  String _getColorHex(Color color) {
    return '#${color.toARGB32().toRadixString(16).substring(2).toUpperCase()}';
  }
}

class ColorWheelPainter extends CustomPainter {
  final double selectedHue;

  ColorWheelPainter(this.selectedHue);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // 绘制色相环
    for (int i = 0; i < 360; i++) {
      final paint =
          Paint()
            ..color = HSVColor.fromAHSV(1, i.toDouble(), 1, 1).toColor()
            ..strokeWidth = 2;

      final angle = i * math.pi / 180;
      final start = Offset(
        center.dx + (radius - 20) * math.cos(angle),
        center.dy + (radius - 20) * math.sin(angle),
      );
      final end = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );

      canvas.drawLine(start, end, paint);
    }

    // 绘制选中指示器
    final selectedAngle = selectedHue * math.pi / 180;
    final indicatorPos = Offset(
      center.dx + (radius - 10) * math.cos(selectedAngle),
      center.dy + (radius - 10) * math.sin(selectedAngle),
    );

    canvas.drawCircle(
      indicatorPos,
      8,
      Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill,
    );

    canvas.drawCircle(
      indicatorPos,
      8,
      Paint()
        ..color = Colors.black
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2,
    );
  }

  @override
  bool shouldRepaint(ColorWheelPainter oldDelegate) {
    return oldDelegate.selectedHue != selectedHue;
  }
}
