import 'package:flutter/material.dart';

enum ExportFormat { png, jpg, webp }

enum ExportSize { small, medium, large, custom }

class ExportOptions extends StatefulWidget {
  final Function(ExportConfig) onExport;
  final bool isExporting;

  const ExportOptions({
    super.key,
    required this.onExport,
    this.isExporting = false,
  });

  @override
  State<ExportOptions> createState() => _ExportOptionsState();
}

class _ExportOptionsState extends State<ExportOptions>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  ExportFormat _selectedFormat = ExportFormat.png;
  ExportSize _selectedSize = ExportSize.medium;
  int _customWidth = 800;
  int _customHeight = 600;
  int _quality = 95;
  bool _addWatermark = false;
  bool _optimizeForSocial = false;

  final Map<ExportSize, Size> _predefinedSizes = {
    ExportSize.small: const Size(400, 300),
    ExportSize.medium: const Size(800, 600),
    ExportSize.large: const Size(1200, 900),
  };

  final Map<String, Size> _socialSizes = {
    '微信朋友圈': const Size(1080, 1080),
    '微博配图': const Size(690, 518),
    '小红书': const Size(1080, 1350),
    'Instagram': const Size(1080, 1080),
    'Twitter': const Size(1200, 675),
  };

  @override
  void initState() {
    super.initState();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _slideController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, -10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 拖拽指示器
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 标题栏
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(
                    Icons.file_download,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      '导出设置',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // 内容区域
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormatSection(),
                    const SizedBox(height: 24),
                    _buildSizeSection(),
                    const SizedBox(height: 24),
                    _buildQualitySection(),
                    const SizedBox(height: 24),
                    _buildOptionsSection(),
                    if (_optimizeForSocial) ...[
                      const SizedBox(height: 24),
                      _buildSocialSizesSection(),
                    ],
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),

            // 导出按钮
            _buildExportButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildFormatSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '文件格式',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children:
              ExportFormat.values.map((format) {
                final isSelected = _selectedFormat == format;
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: GestureDetector(
                      onTap: () => setState(() => _selectedFormat = format),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color:
                                isSelected
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          format.name.toUpperCase(),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.grey[700],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildSizeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '图片尺寸',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              ExportSize.values.map((size) {
                final isSelected = _selectedSize == size;
                return GestureDetector(
                  onTap: () => setState(() => _selectedSize = size),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? Theme.of(context).primaryColor
                              : Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color:
                            isSelected
                                ? Theme.of(context).primaryColor
                                : Colors.grey.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      _getSizeName(size),
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),

        if (_selectedSize == ExportSize.custom) ...[
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  initialValue: _customWidth.toString(),
                  decoration: const InputDecoration(
                    labelText: '宽度',
                    suffixText: 'px',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final width = int.tryParse(value);
                    if (width != null) {
                      setState(() {
                        _customWidth = width;
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              const Text('×', style: TextStyle(fontSize: 18)),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  initialValue: _customHeight.toString(),
                  decoration: const InputDecoration(
                    labelText: '高度',
                    suffixText: 'px',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final height = int.tryParse(value);
                    if (height != null) {
                      setState(() {
                        _customHeight = height;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildQualitySection() {
    if (_selectedFormat == ExportFormat.png) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '图片质量',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Text(
              '$_quality%',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: Theme.of(context).primaryColor,
            thumbColor: Theme.of(context).primaryColor,
            overlayColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),
          ),
          child: Slider(
            value: _quality.toDouble(),
            min: 50,
            max: 100,
            divisions: 10,
            onChanged: (value) => setState(() => _quality = value.toInt()),
          ),
        ),
      ],
    );
  }

  Widget _buildOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '导出选项',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        CheckboxListTile(
          title: const Text('添加水印'),
          subtitle: const Text('在图片上添加水印标识'),
          value: _addWatermark,
          onChanged: (value) => setState(() => _addWatermark = value ?? false),
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        ),
        CheckboxListTile(
          title: const Text('社交媒体优化'),
          subtitle: const Text('针对社交平台优化尺寸'),
          value: _optimizeForSocial,
          onChanged:
              (value) => setState(() => _optimizeForSocial = value ?? false),
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildSocialSizesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '社交平台尺寸',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              _socialSizes.entries.map((entry) {
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedSize = ExportSize.custom;
                      _customWidth = entry.value.width.toInt();
                      _customHeight = entry.value.height.toInt();
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.grey.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          entry.key,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '${entry.value.width.toInt()}×${entry.value.height.toInt()}',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildExportButton() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(20)),
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: widget.isExporting ? null : _handleExport,
          icon:
              widget.isExporting
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Icon(Icons.download),
          label: Text(widget.isExporting ? '导出中...' : '开始导出'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
            textStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  void _handleExport() {
    final size =
        _selectedSize == ExportSize.custom
            ? Size(_customWidth.toDouble(), _customHeight.toDouble())
            : _predefinedSizes[_selectedSize]!;

    final config = ExportConfig(
      format: _selectedFormat,
      size: size,
      quality: _quality,
      addWatermark: _addWatermark,
      optimizeForSocial: _optimizeForSocial,
    );

    widget.onExport(config);
  }

  String _getSizeName(ExportSize size) {
    switch (size) {
      case ExportSize.small:
        return '小 (400×300)';
      case ExportSize.medium:
        return '中 (800×600)';
      case ExportSize.large:
        return '大 (1200×900)';
      case ExportSize.custom:
        return '自定义';
    }
  }
}

class ExportConfig {
  final ExportFormat format;
  final Size size;
  final int quality;
  final bool addWatermark;
  final bool optimizeForSocial;

  const ExportConfig({
    required this.format,
    required this.size,
    this.quality = 95,
    this.addWatermark = false,
    this.optimizeForSocial = false,
  });
}
