import 'package:flutter/material.dart';
import 'markdown_render_style.dart';
import 'markdown_watermark.dart';

/// Markdown渲染模板配置
class MarkdownTemplate {
  /// 模板ID
  final String id;

  /// 模板名称
  final String name;

  /// 模板描述
  final String description;

  /// 模板样式
  final MarkdownRenderStyle style;

  /// 模板水印
  final MarkdownWatermark watermark;

  /// 是否显示头部信息（标题、日期等）
  final bool showHeader;

  /// 是否显示边框
  final bool showBorder;

  /// 边框颜色
  final Color borderColor;

  /// 边框宽度
  final double borderWidth;

  /// 是否显示阴影
  final bool showShadow;

  /// 阴影颜色
  final Color shadowColor;

  /// 阴影偏移
  final Offset shadowOffset;

  /// 阴影模糊半径
  final double shadowBlurRadius;

  /// 是否显示内容区域内阴影
  final bool showInnerShadow;

  /// 内容区域圆角系数（相对于整体圆角的比例）
  final double contentRadiusRatio;

  const MarkdownTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.style,
    required this.watermark,
    required this.showHeader,
    required this.showBorder,
    required this.borderColor,
    required this.borderWidth,
    required this.showShadow,
    required this.shadowColor,
    required this.shadowOffset,
    required this.shadowBlurRadius,
    this.showInnerShadow = false,
    this.contentRadiusRatio = 0.8,
  });

  /// 创建一个模板的副本并修改部分属性
  MarkdownTemplate copyWith({
    String? id,
    String? name,
    String? description,
    MarkdownRenderStyle? style,
    MarkdownWatermark? watermark,
    bool? showHeader,
    bool? showBorder,
    Color? borderColor,
    double? borderWidth,
    bool? showShadow,
    Color? shadowColor,
    Offset? shadowOffset,
    double? shadowBlurRadius,
    bool? showInnerShadow,
    double? contentRadiusRatio,
  }) {
    return MarkdownTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      style: style ?? this.style,
      watermark: watermark ?? this.watermark,
      showHeader: showHeader ?? this.showHeader,
      showBorder: showBorder ?? this.showBorder,
      borderColor: borderColor ?? this.borderColor,
      borderWidth: borderWidth ?? this.borderWidth,
      showShadow: showShadow ?? this.showShadow,
      shadowColor: shadowColor ?? this.shadowColor,
      shadowOffset: shadowOffset ?? this.shadowOffset,
      shadowBlurRadius: shadowBlurRadius ?? this.shadowBlurRadius,
      showInnerShadow: showInnerShadow ?? this.showInnerShadow,
      contentRadiusRatio: contentRadiusRatio ?? this.contentRadiusRatio,
    );
  }

  /// 获取预定义模板列表
  static List<MarkdownTemplate> getPredefinedTemplates() {
    return [
      simple(),
      modern(),
      elegant(),
      code(),
      card(),
      morandiStyle(),
      chineseBlueWhite(),
      chineseVermilion(),
      gradientPurple(),
      festiveRed(),
      bambooSlip(),
    ];
  }

  /// 简约模板
  static MarkdownTemplate simple() {
    return MarkdownTemplate(
      id: 'simple',
      name: '简约',
      description: '简洁清爽的设计风格',
      style: MarkdownRenderStyle.light(),
      watermark: MarkdownWatermark.defaultWatermark(),
      showHeader: false,
      showBorder: false,
      borderColor: Colors.transparent,
      borderWidth: 0,
      showShadow: false,
      shadowColor: Colors.transparent,
      shadowOffset: const Offset(0, 0),
      shadowBlurRadius: 0,
    );
  }

  /// 现代模板
  static MarkdownTemplate modern() {
    return MarkdownTemplate(
      id: 'modern',
      name: '现代',
      description: '现代化的设计风格，带有阴影效果',
      style: MarkdownRenderStyle.light(),
      watermark: MarkdownWatermark.defaultWatermark(),
      showHeader: true,
      showBorder: false,
      borderColor: Colors.transparent,
      borderWidth: 0,
      showShadow: true,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shadowOffset: const Offset(0, 2),
      shadowBlurRadius: 10,
      showInnerShadow: false,
    );
  }

  /// 优雅模板
  static MarkdownTemplate elegant() {
    return MarkdownTemplate(
      id: 'elegant',
      name: '优雅',
      description: '优雅的设计风格，带有细边框',
      style: MarkdownRenderStyle.light().copyWith(
        backgroundColor: Colors.white,
        padding: const EdgeInsets.all(24.0),
      ),
      watermark: MarkdownWatermark.defaultWatermark(),
      showHeader: true,
      showBorder: true,
      borderColor: Colors.grey.withValues(alpha: 0.3),
      borderWidth: 1,
      showShadow: false,
      shadowColor: Colors.transparent,
      shadowOffset: const Offset(0, 0),
      shadowBlurRadius: 0,
      showInnerShadow: false,
    );
  }

  /// 代码模板
  static MarkdownTemplate code() {
    return MarkdownTemplate(
      id: 'code',
      name: '代码',
      description: '适合展示代码的暗色主题',
      style: MarkdownRenderStyle.dark(),
      watermark: MarkdownWatermark.defaultWatermark().copyWith(
        textColor: Colors.grey.withValues(alpha: 0.5),
      ),
      showHeader: false,
      showBorder: false,
      borderColor: Colors.transparent,
      borderWidth: 0,
      showShadow: true,
      shadowColor: Colors.black.withValues(alpha: 0.3),
      shadowOffset: const Offset(0, 3),
      shadowBlurRadius: 15,
      showInnerShadow: false,
    );
  }

  /// 卡片模板
  static MarkdownTemplate card() {
    return MarkdownTemplate(
      id: 'card',
      name: '卡片',
      description: '类似社交媒体卡片的风格设计',
      style: MarkdownRenderStyle.light().copyWith(
        backgroundColor: Colors.white,
        padding: const EdgeInsets.all(24.0),
        borderRadius: 20.0,
      ),
      watermark: MarkdownWatermark.defaultWatermark(),
      showHeader: true,
      showBorder: false,
      borderColor: Colors.transparent,
      borderWidth: 0,
      showShadow: true,
      shadowColor: Colors.black.withValues(alpha: 0.08),
      shadowOffset: const Offset(0, 8),
      shadowBlurRadius: 20,
      showInnerShadow: true,
      contentRadiusRatio: 0.9,
    );
  }

  /// 莫兰迪风格模板
  static MarkdownTemplate morandiStyle() {
    return MarkdownTemplate(
      id: 'morandi',
      name: '莫兰迪',
      description: '高级莫兰迪配色，柔和雅致',
      style: MarkdownRenderStyle.morandi(),
      watermark: MarkdownWatermark.defaultWatermark().copyWith(
        textColor: const Color(0xFFA39E93).withValues(alpha: 0.15),
        fontSize: 80,
        position: WatermarkPosition.bottomRight,
      ),
      showHeader: true,
      showBorder: true,
      borderColor: const Color(0xFFD5CEC5),
      borderWidth: 1.5,
      showShadow: true,
      shadowColor: const Color(0xFFBBB5AC).withValues(alpha: 0.2),
      shadowOffset: const Offset(0, 4),
      shadowBlurRadius: 15,
      showInnerShadow: false,
      contentRadiusRatio: 0.85,
    );
  }

  /// 青花瓷风格模板
  static MarkdownTemplate chineseBlueWhite() {
    return MarkdownTemplate(
      id: 'chinese_blue_white',
      name: '青花',
      description: '中国传统青花瓷配色与纹样设计',
      style: MarkdownRenderStyle.chineseBlueWhite(),
      watermark: MarkdownWatermark.defaultWatermark().copyWith(
        textColor: const Color(0xFF6CA3D4).withValues(alpha: 0.07),
        fontSize: 100,
        position: WatermarkPosition.bottomRight,
        text: '青花',
      ),
      showHeader: true,
      showBorder: true,
      borderColor: const Color(0xFF6CA3D4),
      borderWidth: 2,
      showShadow: true,
      shadowColor: const Color(0xFF6CA3D4).withValues(alpha: 0.15),
      shadowOffset: const Offset(0, 5),
      shadowBlurRadius: 20,
      showInnerShadow: false,
      contentRadiusRatio: 0.9,
    );
  }

  /// 胭脂红风格模板
  static MarkdownTemplate chineseVermilion() {
    return MarkdownTemplate(
      id: 'chinese_vermilion',
      name: '胭脂',
      description: '中国传统胭脂红配色，优雅庄重',
      style: MarkdownRenderStyle.chineseVermilion(),
      watermark: MarkdownWatermark.defaultWatermark().copyWith(
        textColor: const Color(0xFFCF4F50).withValues(alpha: 0.06),
        fontSize: 120,
        position: WatermarkPosition.tiled,
        text: '赤',
      ),
      showHeader: true,
      showBorder: true,
      borderColor: const Color(0xFFECBEA9),
      borderWidth: 2,
      showShadow: false,
      shadowColor: Colors.transparent,
      shadowOffset: const Offset(0, 0),
      shadowBlurRadius: 0,
      showInnerShadow: true,
      contentRadiusRatio: 0.9,
    );
  }

  /// 渐变紫色风格模板
  static MarkdownTemplate gradientPurple() {
    return MarkdownTemplate(
      id: 'gradient_purple',
      name: '渐变紫',
      description: '现代紫蓝渐变背景，时尚优雅',
      style: MarkdownRenderStyle.gradient(),
      watermark: MarkdownWatermark.defaultWatermark().copyWith(
        textColor: Colors.white.withValues(alpha: 0.1),
        fontSize: 100,
        position: WatermarkPosition.tiled,
      ),
      showHeader: true,
      showBorder: false,
      borderColor: Colors.transparent,
      borderWidth: 0,
      showShadow: true,
      shadowColor: const Color(0xFF6A11CB).withValues(alpha: 0.3),
      shadowOffset: const Offset(0, 10),
      shadowBlurRadius: 30,
      showInnerShadow: false,
      contentRadiusRatio: 0.95,
    );
  }

  /// 节日红风格模板
  static MarkdownTemplate festiveRed() {
    return MarkdownTemplate(
      id: 'festive_red',
      name: '节日红',
      description: '喜庆节日主题，适合春节等场合',
      style: MarkdownRenderStyle.festiveRed(),
      watermark: MarkdownWatermark.defaultWatermark().copyWith(
        textColor: const Color(0xFFE34D59).withValues(alpha: 0.08),
        fontSize: 120,
        position: WatermarkPosition.bottomRight,
        text: '福',
      ),
      showHeader: true,
      showBorder: true,
      borderColor: const Color(0xFFEF7A82),
      borderWidth: 3,
      showShadow: true,
      shadowColor: const Color(0xFFE34D59).withValues(alpha: 0.2),
      shadowOffset: const Offset(0, 5),
      shadowBlurRadius: 15,
      showInnerShadow: false,
      contentRadiusRatio: 0.9,
    );
  }

  /// 竹简风格模板
  static MarkdownTemplate bambooSlip() {
    return MarkdownTemplate(
      id: 'bamboo_slip',
      name: '竹简',
      description: '传统竹简文风，古韵浓厚',
      style: MarkdownRenderStyle.bambooSlip(),
      watermark: MarkdownWatermark.defaultWatermark().copyWith(
        textColor: const Color(0xFF8C6E3E).withValues(alpha: 0.1),
        fontSize: 140,
        position: WatermarkPosition.topRight,
        text: '竹',
      ),
      showHeader: true,
      showBorder: true,
      borderColor: const Color(0xFFA38B60),
      borderWidth: 2,
      showShadow: true,
      shadowColor: const Color(0xFF8C6E3E).withValues(alpha: 0.15),
      shadowOffset: const Offset(2, 2),
      shadowBlurRadius: 10,
      showInnerShadow: false,
      contentRadiusRatio: 0.0, // 方形边角模拟竹简
    );
  }
}
