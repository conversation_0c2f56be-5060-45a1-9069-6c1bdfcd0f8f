

/// HTML模板类型
enum HtmlTemplateType {
  /// 文章类
  article,

  /// 卡片类
  card,

  /// 海报类
  poster,

  /// 简报类
  brief,

  /// 自定义
  custom,
}

/// HTML模板模型
class HtmlTemplate {
  /// 模板ID
  final String id;

  /// 模板名称
  final String name;

  /// 模板描述
  final String description;

  /// 模板类型
  final HtmlTemplateType type;

  /// 模板HTML内容
  final String htmlContent;

  /// 模板缩略图URL
  final String? thumbnailUrl;

  /// 模板创建时间
  final DateTime createdAt;

  /// 模板是否为系统预设
  final bool isSystem;

  /// 创建一个HTML模板
  const HtmlTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.htmlContent,
    this.thumbnailUrl,
    required this.createdAt,
    this.isSystem = false,
  });

  /// 创建一个模板的副本并修改部分属性
  HtmlTemplate copyWith({
    String? id,
    String? name,
    String? description,
    HtmlTemplateType? type,
    String? htmlContent,
    String? thumbnailUrl,
    DateTime? createdAt,
    bool? isSystem,
  }) {
    return HtmlTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      htmlContent: htmlContent ?? this.htmlContent,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      createdAt: createdAt ?? this.createdAt,
      isSystem: isSystem ?? this.isSystem,
    );
  }

  /// 从Map创建模板
  factory HtmlTemplate.fromJson(Map<String, dynamic> json) {
    return HtmlTemplate(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: HtmlTemplateType.values[json['type'] as int],
      htmlContent: json['htmlContent'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      isSystem: json['isSystem'] as bool? ?? false,
    );
  }

  /// 转换为Map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.index,
      'htmlContent': htmlContent,
      'thumbnailUrl': thumbnailUrl,
      'createdAt': createdAt.toIso8601String(),
      'isSystem': isSystem,
    };
  }

  /// 获取预设HTML模板列表
  static List<HtmlTemplate> getPredefinedTemplates() {
    return [simpleArticle(), modernCard(), elegantPoster(), simpleBrief()];
  }

  /// 简约文章模板
  static HtmlTemplate simpleArticle() {
    return HtmlTemplate(
      id: 'simple_article',
      name: '简约文章',
      description: '简洁清爽的文章排版风格',
      type: HtmlTemplateType.article,
      htmlContent: '''
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #fff;
    }
    h1 {
      font-size: 28px;
      margin-bottom: 16px;
      color: #222;
    }
    h2 {
      font-size: 22px;
      margin-top: 24px;
      margin-bottom: 12px;
      color: #333;
    }
    p {
      margin-bottom: 16px;
      font-size: 16px;
    }
    img {
      max-width: 100%;
      height: auto;
      border-radius: 6px;
    }
    code {
      background-color: #f5f5f5;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: monospace;
      font-size: 14px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 12px;
      border-radius: 6px;
      overflow-x: auto;
    }
    blockquote {
      border-left: 4px solid #ddd;
      padding-left: 16px;
      margin-left: 0;
      color: #666;
    }
    a {
      color: #0070f3;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    .content-container {
      padding: 24px;
      background-color: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
    .footer {
      margin-top: 40px;
      text-align: center;
      color: #999;
      font-size: 14px;
    }
    .watermark {
      position: fixed;
      bottom: 10px;
      right: 10px;
      opacity: 0.5;
      font-size: 12px;
      color: #999;
    }
  </style>
</head>
<body>
  <div class="content-container">
    <!-- 标题区域 -->
    <h1>{{title}}</h1>
    
    <!-- 内容区域 -->
    <div class="markdown-content">
      {{content}}
    </div>
    
    <!-- 页脚区域 -->
    <div class="footer">
      <p>{{footer}}</p>
    </div>
  </div>
  
  <!-- 水印 -->
  <div class="watermark">{{watermark}}</div>
</body>
</html>
''',
      createdAt: DateTime.now(),
      isSystem: true,
    );
  }

  /// 现代卡片模板
  static HtmlTemplate modernCard() {
    return HtmlTemplate(
      id: 'modern_card',
      name: '现代卡片',
      description: '现代化的卡片设计风格',
      type: HtmlTemplateType.card,
      htmlContent: '''
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f7f9fc;
      margin: 0;
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }
    .card {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      width: 100%;
      max-width: 500px;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    }
    .card-header {
      background: linear-gradient(135deg, #6e8efb, #a777e3);
      color: white;
      padding: 20px;
    }
    .card-header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
    .card-content {
      padding: 24px;
    }
    p {
      margin: 0 0 16px;
      font-size: 16px;
      line-height: 1.6;
      color: #4a5568;
    }
    img {
      max-width: 100%;
      border-radius: 8px;
      margin: 16px 0;
    }
    .card-footer {
      border-top: 1px solid #eee;
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #718096;
      font-size: 14px;
    }
    code {
      background-color: #edf2f7;
      padding: 2px 4px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 14px;
      color: #5a67d8;
    }
    blockquote {
      border-left: 4px solid #e2e8f0;
      padding-left: 16px;
      margin-left: 0;
      color: #718096;
    }
  </style>
</head>
<body>
  <div class="card">
    <div class="card-header">
      <h1>{{title}}</h1>
    </div>
    <div class="card-content">
      {{content}}
    </div>
    <div class="card-footer">
      <div>{{date}}</div>
      <div>{{watermark}}</div>
    </div>
  </div>
</body>
</html>
''',
      createdAt: DateTime.now(),
      isSystem: true,
    );
  }

  /// 优雅海报模板
  static HtmlTemplate elegantPoster() {
    return HtmlTemplate(
      id: 'elegant_poster',
      name: '优雅海报',
      description: '适合制作精美海报的模板',
      type: HtmlTemplateType.poster,
      htmlContent: '''
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: 'Helvetica Neue', Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8f9fa;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }
    .poster {
      width: 100%;
      max-width: 800px;
      aspect-ratio: 16 / 9;
      background: linear-gradient(to right, #ffffff, #f5f7fa);
      position: relative;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border-radius: 24px;
    }
    .poster-content {
      display: flex;
      height: 100%;
    }
    .poster-left {
      width: 55%;
      padding: 40px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .poster-right {
      width: 45%;
      background-size: cover;
      background-position: center;
      background-image: url('{{image_url}}');
    }
    h1 {
      font-size: 36px;
      font-weight: 800;
      margin: 0 0 20px;
      background: linear-gradient(135deg, #6366F1, #4F46E5);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.2;
    }
    .subtitle {
      font-size: 18px;
      color: #64748b;
      margin: 0 0 30px;
      line-height: 1.6;
    }
    .highlights {
      margin: 20px 0;
    }
    .highlight-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }
    .highlight-icon {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #4F46E5;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      color: white;
      font-weight: bold;
    }
    .highlight-text {
      font-size: 16px;
      color: #334155;
    }
    .poster-footer {
      position: absolute;
      bottom: 20px;
      left: 40px;
      font-size: 14px;
      color: #94a3b8;
    }
    .overlay {
      position: absolute;
      top: 0;
      right: 0;
      width: 45%;
      height: 100%;
      background: linear-gradient(to right, rgba(255,255,255,0.9), rgba(255,255,255,0.5));
      z-index: 1;
    }
  </style>
</head>
<body>
  <div class="poster">
    <div class="poster-content">
      <div class="poster-left">
        <h1>{{title}}</h1>
        <div class="subtitle">{{subtitle}}</div>
        <div class="highlights">
          {{content}}
        </div>
      </div>
      <div class="poster-right"></div>
      <div class="overlay"></div>
    </div>
    <div class="poster-footer">{{footer}} | {{watermark}}</div>
  </div>
</body>
</html>
''',
      createdAt: DateTime.now(),
      isSystem: true,
    );
  }

  /// 简洁简报模板
  static HtmlTemplate simpleBrief() {
    return HtmlTemplate(
      id: 'simple_brief',
      name: '简洁简报',
      description: '适合制作简洁简报的模板',
      type: HtmlTemplateType.brief,
      htmlContent: '''
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: 'Helvetica Neue', Arial, sans-serif;
      margin: 0;
      padding: 30px;
      background-color: #ffffff;
      color: #333;
    }
    .brief {
      max-width: 800px;
      margin: 0 auto;
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      overflow: hidden;
    }
    .brief-header {
      padding: 24px;
      background-color: #f9fafb;
      border-bottom: 1px solid #e5e7eb;
    }
    .brief-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #111827;
    }
    .brief-date {
      margin-top: 8px;
      font-size: 14px;
      color: #6b7280;
    }
    .brief-content {
      padding: 24px;
    }
    .brief-section {
      margin-bottom: 24px;
    }
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #374151;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #e5e7eb;
    }
    p {
      margin: 0 0 16px;
      font-size: 16px;
      line-height: 1.6;
    }
    .brief-footer {
      padding: 16px 24px;
      background-color: #f9fafb;
      border-top: 1px solid #e5e7eb;
      font-size: 14px;
      color: #6b7280;
      text-align: center;
    }
    ul {
      padding-left: 24px;
    }
    li {
      margin-bottom: 8px;
    }
  </style>
</head>
<body>
  <div class="brief">
    <div class="brief-header">
      <h1 class="brief-title">{{title}}</h1>
      <div class="brief-date">{{date}}</div>
    </div>
    <div class="brief-content">
      {{content}}
    </div>
    <div class="brief-footer">
      {{footer}} | {{watermark}}
    </div>
  </div>
</body>
</html>
''',
      createdAt: DateTime.now(),
      isSystem: true,
    );
  }
}
