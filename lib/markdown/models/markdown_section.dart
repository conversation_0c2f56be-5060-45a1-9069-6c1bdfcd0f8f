

/// Markdown分段标识类型
enum SectionSeparatorType {
  /// 水平分割线
  horizontalRule,

  /// 主标题 (# 一级标题)
  h1,

  /// 自定义分隔符
  custom,
}

/// Markdown内容段落
class MarkdownSection {
  /// 段落标识符
  final String id;

  /// 段落标题（从内容中提取）
  final String title;

  /// 段落内容
  final String content;

  /// 段落原始内容（包含标题）
  final String rawContent;

  /// 在原始文档中的起始位置
  final int startPosition;

  /// 在原始文档中的结束位置
  final int endPosition;

  /// 段落的顺序编号
  final int index;

  /// 创建时间
  final DateTime createdAt;

  /// 段落关联的图片URL (可选)
  final String? imageUrl;

  /// 创建一个Markdown段落
  const MarkdownSection({
    required this.id,
    required this.title,
    required this.content,
    required this.rawContent,
    required this.startPosition,
    required this.endPosition,
    required this.index,
    required this.createdAt,
    this.imageUrl,
  });

  /// 创建一个段落的副本并修改部分属性
  MarkdownSection copyWith({
    String? id,
    String? title,
    String? content,
    String? rawContent,
    int? startPosition,
    int? endPosition,
    int? index,
    DateTime? createdAt,
    String? imageUrl,
  }) {
    return MarkdownSection(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      rawContent: rawContent ?? this.rawContent,
      startPosition: startPosition ?? this.startPosition,
      endPosition: endPosition ?? this.endPosition,
      index: index ?? this.index,
      createdAt: createdAt ?? this.createdAt,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  /// 从Map创建段落
  factory MarkdownSection.fromJson(Map<String, dynamic> json) {
    return MarkdownSection(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      rawContent: json['rawContent'] as String,
      startPosition: json['startPosition'] as int,
      endPosition: json['endPosition'] as int,
      index: json['index'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      imageUrl: json['imageUrl'] as String?,
    );
  }

  /// 转换为Map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'rawContent': rawContent,
      'startPosition': startPosition,
      'endPosition': endPosition,
      'index': index,
      'createdAt': createdAt.toIso8601String(),
      'imageUrl': imageUrl,
    };
  }
}

/// Markdown文档段落集合
class MarkdownSectionDocument {
  /// 文档标题
  final String title;

  /// 文档子标题
  final String? subtitle;

  /// 创建时间
  final DateTime createdAt;

  /// 最后更新时间
  final DateTime updatedAt;

  /// 文档段落列表
  final List<MarkdownSection> sections;

  /// 原始Markdown内容
  final String rawContent;

  /// 创建一个段落文档
  const MarkdownSectionDocument({
    required this.title,
    this.subtitle,
    required this.createdAt,
    required this.updatedAt,
    required this.sections,
    required this.rawContent,
  });

  /// 创建一个段落文档的副本并修改部分属性
  MarkdownSectionDocument copyWith({
    String? title,
    String? subtitle,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<MarkdownSection>? sections,
    String? rawContent,
  }) {
    return MarkdownSectionDocument(
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      sections: sections ?? this.sections,
      rawContent: rawContent ?? this.rawContent,
    );
  }

  /// 从Map创建段落文档
  factory MarkdownSectionDocument.fromJson(Map<String, dynamic> json) {
    return MarkdownSectionDocument(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      sections:
          (json['sections'] as List)
              .map((e) => MarkdownSection.fromJson(e as Map<String, dynamic>))
              .toList(),
      rawContent: json['rawContent'] as String,
    );
  }

  /// 转换为Map
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'subtitle': subtitle,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'sections': sections.map((e) => e.toJson()).toList(),
      'rawContent': rawContent,
    };
  }
}
