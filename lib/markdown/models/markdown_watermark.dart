import 'package:flutter/material.dart';

import '../../config/constants.dart';

/// Markdown渲染水印配置
class MarkdownWatermark {
  /// 水印文本
  final String text;

  /// 水印文本颜色
  final Color textColor;

  /// 水印文本大小
  final double fontSize;

  /// 水印文本字体
  final String fontFamily;

  /// 水印文本样式
  final FontStyle fontStyle;

  /// 水印文本粗细
  final FontWeight fontWeight;

  /// 是否显示水印
  final bool isVisible;

  /// 水印位置
  final WatermarkPosition position;

  /// 水印透明度
  final double opacity;

  /// 水印旋转角度（弧度）
  final double rotation;

  /// 平铺水印的水平间距
  final double tileHorizontalGap;

  /// 平铺水印的垂直间距
  final double tileVerticalGap;

  /// 平铺水印的行数
  final int tileRows;

  /// 平铺水印的列数
  final int tileColumns;

  const MarkdownWatermark({
    required this.text,
    required this.textColor,
    required this.fontSize,
    required this.fontFamily,
    required this.fontStyle,
    required this.fontWeight,
    required this.isVisible,
    required this.position,
    required this.opacity,
    this.rotation = 0.0,
    this.tileHorizontalGap = 100.0,
    this.tileVerticalGap = 100.0,
    this.tileRows = 3,
    this.tileColumns = 3,
  });

  /// 创建一个水印的副本并修改部分属性
  MarkdownWatermark copyWith({
    String? text,
    Color? textColor,
    double? fontSize,
    String? fontFamily,
    FontStyle? fontStyle,
    FontWeight? fontWeight,
    bool? isVisible,
    WatermarkPosition? position,
    double? opacity,
    double? rotation,
    double? tileHorizontalGap,
    double? tileVerticalGap,
    int? tileRows,
    int? tileColumns,
  }) {
    return MarkdownWatermark(
      text: text ?? this.text,
      textColor: textColor ?? this.textColor,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      fontStyle: fontStyle ?? this.fontStyle,
      fontWeight: fontWeight ?? this.fontWeight,
      isVisible: isVisible ?? this.isVisible,
      position: position ?? this.position,
      opacity: opacity ?? this.opacity,
      rotation: rotation ?? this.rotation,
      tileHorizontalGap: tileHorizontalGap ?? this.tileHorizontalGap,
      tileVerticalGap: tileVerticalGap ?? this.tileVerticalGap,
      tileRows: tileRows ?? this.tileRows,
      tileColumns: tileColumns ?? this.tileColumns,
    );
  }

  /// 默认水印
  static MarkdownWatermark defaultWatermark() {
    return MarkdownWatermark(
      text: AppConstants.appNameChinese,
      textColor: Colors.grey,
      fontSize: 12.0,
      fontFamily: 'Roboto',
      fontStyle: FontStyle.italic,
      fontWeight: FontWeight.normal,
      isVisible: false,
      position: WatermarkPosition.bottomCenter,
      opacity: 0.7,
      rotation: -0.2, // 默认轻微倾斜
    );
  }
}

/// 水印位置枚举
enum WatermarkPosition {
  topLeft,
  topCenter,
  topRight,
  bottomLeft,
  bottomCenter,
  bottomRight,

  /// 平铺
  tiled,
}

/// 水印位置扩展
extension WatermarkPositionExtension on WatermarkPosition {
  String get displayName {
    switch (this) {
      case WatermarkPosition.topLeft:
        return '左上角';
      case WatermarkPosition.topCenter:
        return '顶部居中';
      case WatermarkPosition.topRight:
        return '右上角';
      case WatermarkPosition.bottomLeft:
        return '左下角';
      case WatermarkPosition.bottomCenter:
        return '底部居中';
      case WatermarkPosition.bottomRight:
        return '右下角';
      case WatermarkPosition.tiled:
        return '平铺';
    }
  }

  Alignment get alignment {
    switch (this) {
      case WatermarkPosition.topLeft:
        return Alignment.topLeft;
      case WatermarkPosition.topCenter:
        return Alignment.topCenter;
      case WatermarkPosition.topRight:
        return Alignment.topRight;
      case WatermarkPosition.bottomLeft:
        return Alignment.bottomLeft;
      case WatermarkPosition.bottomCenter:
        return Alignment.bottomCenter;
      case WatermarkPosition.bottomRight:
        return Alignment.bottomRight;
      case WatermarkPosition.tiled:
        return Alignment.center; // 平铺时使用中心对齐
    }
  }
}
