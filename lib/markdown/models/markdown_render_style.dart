import 'package:flutter/material.dart';

/// Markdown渲染样式配置
class MarkdownRenderStyle {
  /// 背景颜色
  final Color backgroundColor;

  /// 文本颜色
  final Color textColor;

  /// 代码块背景颜色
  final Color codeBackgroundColor;

  /// 代码文本颜色
  final Color codeTextColor;

  /// 引用块背景颜色
  final Color quoteBackgroundColor;

  /// 引用文本颜色
  final Color quoteTextColor;

  /// 引用边框颜色
  final Color quoteBorderColor;

  /// 主字体
  final String fontFamily;

  /// 代码字体
  final String codeFontFamily;

  /// 基础字体大小
  final double baseFontSize;

  /// 内边距
  final EdgeInsets padding;

  /// 圆角大小
  final double borderRadius;

  /// 样式名称
  final String name;

  /// 样式ID
  final String id;

  /// 背景图片资源路径
  final String? backgroundImage;

  /// 是否使用渐变背景
  final bool useGradientBackground;

  /// 渐变背景颜色列表
  final List<Color>? gradientColors;

  /// 渐变开始位置
  final Alignment gradientBegin;

  /// 渐变结束位置
  final Alignment gradientEnd;

  /// 标题对齐方式
  final TextAlign headingAlignment;

  /// 列表项标记样式
  final String listItemStyle;

  /// 复选框未选中样式
  final String checkboxUncheckedStyle;

  /// 复选框已选中样式
  final String checkboxCheckedStyle;

  /// 表格边框样式
  final String tableStyle;

  const MarkdownRenderStyle({
    required this.backgroundColor,
    required this.textColor,
    required this.codeBackgroundColor,
    required this.codeTextColor,
    required this.quoteBackgroundColor,
    required this.quoteTextColor,
    required this.quoteBorderColor,
    required this.fontFamily,
    required this.codeFontFamily,
    required this.baseFontSize,
    required this.padding,
    required this.borderRadius,
    required this.name,
    required this.id,
    this.backgroundImage,
    this.useGradientBackground = false,
    this.gradientColors,
    this.gradientBegin = Alignment.topLeft,
    this.gradientEnd = Alignment.bottomRight,
    this.headingAlignment = TextAlign.left,
    this.listItemStyle = '•',
    this.checkboxUncheckedStyle = '☐',
    this.checkboxCheckedStyle = '☑',
    this.tableStyle = 'default',
  });

  /// 创建一个样式的副本并修改部分属性
  MarkdownRenderStyle copyWith({
    Color? backgroundColor,
    Color? textColor,
    Color? codeBackgroundColor,
    Color? codeTextColor,
    Color? quoteBackgroundColor,
    Color? quoteTextColor,
    Color? quoteBorderColor,
    String? fontFamily,
    String? codeFontFamily,
    double? baseFontSize,
    EdgeInsets? padding,
    double? borderRadius,
    String? name,
    String? id,
    String? backgroundImage,
    bool? useGradientBackground,
    List<Color>? gradientColors,
    Alignment? gradientBegin,
    Alignment? gradientEnd,
    TextAlign? headingAlignment,
    String? listItemStyle,
    String? checkboxUncheckedStyle,
    String? checkboxCheckedStyle,
    String? tableStyle,
  }) {
    return MarkdownRenderStyle(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      codeBackgroundColor: codeBackgroundColor ?? this.codeBackgroundColor,
      codeTextColor: codeTextColor ?? this.codeTextColor,
      quoteBackgroundColor: quoteBackgroundColor ?? this.quoteBackgroundColor,
      quoteTextColor: quoteTextColor ?? this.quoteTextColor,
      quoteBorderColor: quoteBorderColor ?? this.quoteBorderColor,
      fontFamily: fontFamily ?? this.fontFamily,
      codeFontFamily: codeFontFamily ?? this.codeFontFamily,
      baseFontSize: baseFontSize ?? this.baseFontSize,
      padding: padding ?? this.padding,
      borderRadius: borderRadius ?? this.borderRadius,
      name: name ?? this.name,
      id: id ?? this.id,
      backgroundImage: backgroundImage ?? this.backgroundImage,
      useGradientBackground:
          useGradientBackground ?? this.useGradientBackground,
      gradientColors: gradientColors ?? this.gradientColors,
      gradientBegin: gradientBegin ?? this.gradientBegin,
      gradientEnd: gradientEnd ?? this.gradientEnd,
      headingAlignment: headingAlignment ?? this.headingAlignment,
      listItemStyle: listItemStyle ?? this.listItemStyle,
      checkboxUncheckedStyle:
          checkboxUncheckedStyle ?? this.checkboxUncheckedStyle,
      checkboxCheckedStyle: checkboxCheckedStyle ?? this.checkboxCheckedStyle,
      tableStyle: tableStyle ?? this.tableStyle,
    );
  }

  /// 预定义的样式 - 浅色主题
  static MarkdownRenderStyle light() {
    return MarkdownRenderStyle(
      backgroundColor: const Color(0xFFF5F5F5),
      textColor: const Color(0xFF333333),
      codeBackgroundColor: const Color(0xFFEEEEEE),
      codeTextColor: const Color(0xFF333333),
      quoteBackgroundColor: const Color(0xFFF5F5F5),
      quoteTextColor: const Color(0xFF666666),
      quoteBorderColor: const Color(0xFFDDDDDD),
      fontFamily: 'Roboto',
      codeFontFamily: 'monospace',
      baseFontSize: 16.0,
      padding: const EdgeInsets.all(20.0),
      borderRadius: 16.0,
      name: '浅色主题',
      id: 'light',
      listItemStyle: '•',
    );
  }

  /// 预定义的样式 - 深色主题
  static MarkdownRenderStyle dark() {
    return MarkdownRenderStyle(
      backgroundColor: const Color(0xFF1E1E1E),
      textColor: const Color(0xFFE0E0E0),
      codeBackgroundColor: const Color(0xFF2D3748),
      codeTextColor: const Color(0xFFE2E8F0),
      quoteBackgroundColor: const Color(0xFF2D3748),
      quoteTextColor: const Color(0xFFAAAAAA),
      quoteBorderColor: const Color(0xFF444444),
      fontFamily: 'Roboto',
      codeFontFamily: 'monospace',
      baseFontSize: 16.0,
      padding: const EdgeInsets.all(20.0),
      borderRadius: 16.0,
      name: '深色主题',
      id: 'dark',
      listItemStyle: '•',
    );
  }

  /// 预定义的样式 - 蓝色主题
  static MarkdownRenderStyle blue() {
    return MarkdownRenderStyle(
      backgroundColor: const Color(0xFFE3F2FD),
      textColor: const Color(0xFF0D47A1),
      codeBackgroundColor: const Color(0xFFBBDEFB),
      codeTextColor: const Color(0xFF1565C0),
      quoteBackgroundColor: const Color(0xFFE3F2FD),
      quoteTextColor: const Color(0xFF1976D2),
      quoteBorderColor: const Color(0xFF90CAF9),
      fontFamily: 'Roboto',
      codeFontFamily: 'monospace',
      baseFontSize: 16.0,
      padding: const EdgeInsets.all(20.0),
      borderRadius: 16.0,
      name: '蓝色主题',
      id: 'blue',
      listItemStyle: '•',
    );
  }

  /// 预定义的样式 - 绿色主题
  static MarkdownRenderStyle green() {
    return MarkdownRenderStyle(
      backgroundColor: const Color(0xFFE8F5E9),
      textColor: const Color(0xFF1B5E20),
      codeBackgroundColor: const Color(0xFFC8E6C9),
      codeTextColor: const Color(0xFF2E7D32),
      quoteBackgroundColor: const Color(0xFFE8F5E9),
      quoteTextColor: const Color(0xFF388E3C),
      quoteBorderColor: const Color(0xFFA5D6A7),
      fontFamily: 'Roboto',
      codeFontFamily: 'monospace',
      baseFontSize: 16.0,
      padding: const EdgeInsets.all(20.0),
      borderRadius: 16.0,
      name: '绿色主题',
      id: 'green',
      listItemStyle: '•',
    );
  }

  /// 莫兰迪风格
  static MarkdownRenderStyle morandi() {
    return MarkdownRenderStyle(
      backgroundColor: const Color(0xFFEFEBE7),
      textColor: const Color(0xFF5D5B56),
      codeBackgroundColor: const Color(0xFFE1DCD7),
      codeTextColor: const Color(0xFF6D695F),
      quoteBackgroundColor: const Color(0xFFE5E2DD),
      quoteTextColor: const Color(0xFF8C857A),
      quoteBorderColor: const Color(0xFFD2CCC6),
      fontFamily: 'Noto Serif',
      codeFontFamily: 'monospace',
      baseFontSize: 16.0,
      padding: const EdgeInsets.all(24.0),
      borderRadius: 12.0,
      name: '莫兰迪风格',
      id: 'morandi',
      headingAlignment: TextAlign.center,
      listItemStyle: '○',
      checkboxUncheckedStyle: '◯',
      checkboxCheckedStyle: '●',
      tableStyle: 'minimal',
    );
  }

  /// 中国传统色 - 青花
  static MarkdownRenderStyle chineseBlueWhite() {
    return MarkdownRenderStyle(
      backgroundColor: const Color(0xFFF8FDFF),
      textColor: const Color(0xFF10487A),
      codeBackgroundColor: const Color(0xFFE8F4FF),
      codeTextColor: const Color(0xFF003972),
      quoteBackgroundColor: const Color(0xFFECF6FF),
      quoteTextColor: const Color(0xFF1A3A6C),
      quoteBorderColor: const Color(0xFF6CA3D4),
      fontFamily: 'Noto Serif SC',
      codeFontFamily: 'Fira Code',
      baseFontSize: 16.0,
      padding: const EdgeInsets.all(24.0),
      borderRadius: 8.0,
      name: '青花瓷风格',
      id: 'chinese_blue_white',
      headingAlignment: TextAlign.center,
      listItemStyle: '•',
      checkboxUncheckedStyle: '□',
      checkboxCheckedStyle: '■',
      tableStyle: 'classic',
    );
  }

  /// 中国传统色 - 胭脂
  static MarkdownRenderStyle chineseVermilion() {
    return MarkdownRenderStyle(
      backgroundColor: const Color(0xFFFFF8EF),
      textColor: const Color(0xFF8C3B3A),
      codeBackgroundColor: const Color(0xFFF9E9E4),
      codeTextColor: const Color(0xFF9A4639),
      quoteBackgroundColor: const Color(0xFFFAEEE5),
      quoteTextColor: const Color(0xFFBA5140),
      quoteBorderColor: const Color(0xFFD08B75),
      fontFamily: 'Noto Serif SC',
      codeFontFamily: 'Fira Code',
      baseFontSize: 16.0,
      padding: const EdgeInsets.all(24.0),
      borderRadius: 16.0,
      name: '胭脂红风格',
      id: 'chinese_vermilion',
      headingAlignment: TextAlign.center,
      listItemStyle: '❧',
      checkboxUncheckedStyle: '❏',
      checkboxCheckedStyle: '❐',
      tableStyle: 'elegant',
    );
  }

  /// 渐变背景主题
  static MarkdownRenderStyle gradient() {
    return MarkdownRenderStyle(
      backgroundColor: Colors.white, // 渐变模式下仅作为fallback
      textColor: Colors.black,
      codeBackgroundColor: Colors.black.withValues(alpha: 0.3),
      codeTextColor: Colors.white,
      quoteBackgroundColor: Colors.black.withValues(alpha: 0.2),
      quoteTextColor: Colors.white.withValues(alpha: 0.9),
      quoteBorderColor: Colors.white.withValues(alpha: 0.5),
      fontFamily: 'Roboto',
      codeFontFamily: 'Fira Code',
      baseFontSize: 16.0,
      padding: const EdgeInsets.all(28.0),
      borderRadius: 20.0,
      name: '渐变背景',
      id: 'gradient',
      useGradientBackground: true,
      gradientColors: [const Color(0xFF6A11CB), const Color(0xFF2575FC)],
      gradientBegin: Alignment.topLeft,
      gradientEnd: Alignment.bottomRight,
      headingAlignment: TextAlign.center,
      listItemStyle: '✧',
      checkboxUncheckedStyle: '☆',
      checkboxCheckedStyle: '★',
      tableStyle: 'modern',
    );
  }

  /// 节日风格 - 春节红
  static MarkdownRenderStyle festiveRed() {
    return MarkdownRenderStyle(
      backgroundColor: const Color(0xFFFFF1F0),
      textColor: const Color(0xFF9D2933),
      codeBackgroundColor: const Color(0xFFFFE4E1),
      codeTextColor: const Color(0xFFC83C23),
      quoteBackgroundColor: const Color(0xFFFFE6E0),
      quoteTextColor: const Color(0xFFCB3A56),
      quoteBorderColor: const Color(0xFFEF7A82),
      fontFamily: 'Ma Shan Zheng',
      codeFontFamily: 'Fira Code',
      baseFontSize: 16.0,
      padding: const EdgeInsets.all(24.0),
      borderRadius: 16.0,
      name: '春节红',
      id: 'festive_red',
      headingAlignment: TextAlign.center,
      listItemStyle: '❁',
      checkboxUncheckedStyle: '❍',
      checkboxCheckedStyle: '✓',
      tableStyle: 'festive',
    );
  }

  /// 竹简风格
  static MarkdownRenderStyle bambooSlip() {
    return MarkdownRenderStyle(
      backgroundColor: const Color(0xFFF5ECD7),
      textColor: const Color(0xFF4A3113),
      codeBackgroundColor: const Color(0xFFECDFC2),
      codeTextColor: const Color(0xFF62461B),
      quoteBackgroundColor: const Color(0xFFEDE3CB),
      quoteTextColor: const Color(0xFF7D6C46),
      quoteBorderColor: const Color(0xFFC3AD7C),
      fontFamily: 'Noto Serif SC',
      codeFontFamily: 'monospace',
      baseFontSize: 16.0,
      padding: const EdgeInsets.all(28.0),
      borderRadius: 0.0, // 方形边角模拟竹简
      name: '竹简风格',
      id: 'bamboo_slip',
      headingAlignment: TextAlign.right,
      listItemStyle: '•',
      checkboxUncheckedStyle: '□',
      checkboxCheckedStyle: '■',
      tableStyle: 'bamboo',
    );
  }

  /// 获取所有预定义样式
  static List<MarkdownRenderStyle> getPredefinedStyles() {
    return [
      light(),
      dark(),
      blue(),
      green(),
      morandi(),
      chineseBlueWhite(),
      chineseVermilion(),
      gradient(),
      festiveRed(),
      bambooSlip(),
    ];
  }
}
