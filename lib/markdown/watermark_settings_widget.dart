import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';

import '../../../services/service_locator.dart';
import '../../../services/settings_service.dart';
import '../config/constants.dart';
import 'models/markdown_watermark.dart';

/// 水印设置组件
class WatermarkSettingsWidget extends StatefulWidget {
  /// 当前水印配置
  final MarkdownWatermark watermark;

  /// 水印更新回调
  final Function(MarkdownWatermark) onWatermarkUpdated;

  const WatermarkSettingsWidget({
    super.key,
    required this.watermark,
    required this.onWatermarkUpdated,
  });

  @override
  State<WatermarkSettingsWidget> createState() =>
      _WatermarkSettingsWidgetState();
}

class _WatermarkSettingsWidgetState extends State<WatermarkSettingsWidget> {
  /// 水印文本控制器
  late TextEditingController _textController;

  /// 设置服务
  late final SettingsService _settingsService;

  @override
  void initState() {
    super.initState();
    _settingsService = ServiceLocator().settingsService;
    _textController = TextEditingController(text: widget.watermark.text);
  }

  @override
  void didUpdateWidget(WatermarkSettingsWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.watermark.text != widget.watermark.text) {
      _textController.text = widget.watermark.text;
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  /// 更新水印并持久化
  void _updateWatermark(MarkdownWatermark watermark) {
    widget.onWatermarkUpdated(watermark);
    _settingsService.updateWatermark(watermark);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 8.0,
                  horizontal: 12.0,
                ),
                decoration: BoxDecoration(
                  border: Border(
                    left: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 3,
                    ),
                  ),
                ),
                child: const Text(
                  '水印设置',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
              const Spacer(),
              Row(
                children: [
                  Text(
                    widget.watermark.isVisible ? '显示水印' : '隐藏水印',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color:
                          widget.watermark.isVisible
                              ? Theme.of(context).primaryColor
                              : Colors.grey,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Switch(
                    value: widget.watermark.isVisible,
                    activeColor: Theme.of(context).primaryColor,
                    onChanged: (value) {
                      _updateWatermark(
                        widget.watermark.copyWith(isVisible: value),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
        if (widget.watermark.isVisible) _buildWatermarkSettings(context),
      ],
    );
  }

  /// 构建水印设置部分
  Widget _buildWatermarkSettings(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 水印文本输入
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '水印内容',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: _textController,
                    decoration: InputDecoration(
                      labelText: '水印文本',
                      hintText: AppConstants.appNameChinese,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Colors.grey.withValues(alpha: 0.2),
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      filled: true,
                      fillColor: Colors.grey.withValues(alpha: 0.05),
                      suffixIcon: IconButton(
                        icon: const Icon(Icons.refresh),
                        tooltip: '重置为应用名称',
                        onPressed: () {
                          _textController.text = AppConstants.appNameChinese;
                          _updateWatermark(
                            widget.watermark.copyWith(
                              text: AppConstants.appNameChinese,
                            ),
                          );
                        },
                      ),
                    ),
                    onChanged: (value) {
                      if (_textController.value.composing.isValid &&
                          !_textController.value.composing.isCollapsed) {
                        return;
                      }
                      _updateWatermark(widget.watermark.copyWith(text: value));
                    },
                    onEditingComplete: () {
                      FocusScope.of(context).unfocus();
                      _updateWatermark(
                        widget.watermark.copyWith(text: _textController.text),
                      );
                    },
                  ),

                  const SizedBox(height: 16),

                  // 水印字体样式选择
                  const Text(
                    '字体样式',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      _buildStyleChoiceChip(
                        '常规',
                        widget.watermark.fontStyle == FontStyle.normal &&
                            widget.watermark.fontWeight == FontWeight.normal,
                        () {
                          _updateWatermark(
                            widget.watermark.copyWith(
                              fontStyle: FontStyle.normal,
                              fontWeight: FontWeight.normal,
                            ),
                          );
                        },
                        Icons.text_format,
                      ),
                      _buildStyleChoiceChip(
                        '粗体',
                        widget.watermark.fontWeight == FontWeight.bold,
                        () {
                          _updateWatermark(
                            widget.watermark.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        },
                        Icons.format_bold,
                      ),
                      _buildStyleChoiceChip(
                        '斜体',
                        widget.watermark.fontStyle == FontStyle.italic,
                        () {
                          _updateWatermark(
                            widget.watermark.copyWith(
                              fontStyle: FontStyle.italic,
                            ),
                          );
                        },
                        Icons.format_italic,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 水印位置和外观设置
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '位置与外观',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 16),

                  // 水印位置选择
                  const Text(
                    '显示位置',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Colors.grey.withValues(alpha: 0.2),
                      ),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey.withValues(alpha: 0.05),
                    ),
                    child: DropdownButton<WatermarkPosition>(
                      value: widget.watermark.position,
                      isExpanded: true,
                      underline: const SizedBox(),
                      icon: const Icon(Icons.arrow_drop_down_rounded),
                      borderRadius: BorderRadius.circular(8),
                      onChanged: (value) {
                        if (value != null) {
                          _updateWatermark(
                            widget.watermark.copyWith(position: value),
                          );
                        }
                      },
                      items:
                          WatermarkPosition.values.map((position) {
                            return DropdownMenuItem<WatermarkPosition>(
                              value: position,
                              child: Row(
                                children: [
                                  Icon(
                                    _getPositionIcon(position),
                                    size: 18,
                                    color: Colors.grey.shade600,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(position.displayName),
                                ],
                              ),
                            );
                          }).toList(),
                    ),
                  ),
                  const SizedBox(height: 20),

                  // 水印颜色选择
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Text(
                        '文本颜色',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 16),
                      GestureDetector(
                        onTap: () {
                          _showColorPicker(
                            context,
                            widget.watermark.textColor,
                            (color) {
                              _updateWatermark(
                                widget.watermark.copyWith(textColor: color),
                              );
                            },
                          );
                        },
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: widget.watermark.textColor,
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              color: Colors.grey.withValues(alpha: 0.3),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Icon(
                              Icons.colorize,
                              color:
                                  widget.watermark.textColor
                                              .computeLuminance() >
                                          0.5
                                      ? Colors.black54
                                      : Colors.white70,
                              size: 18,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '#${widget.watermark.textColor.toARGB32().toRadixString(16).substring(2).toUpperCase()}',
                        style: TextStyle(
                          fontSize: 12,
                          fontFamily: 'monospace',
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),

                  // 水印透明度
                  _buildSliderItem('透明度', widget.watermark.opacity, 0.1, 1.0, (
                    value,
                  ) {
                    _updateWatermark(widget.watermark.copyWith(opacity: value));
                  }, icon: Icons.opacity),

                  const SizedBox(height: 16),

                  // 水印字体大小
                  _buildSliderItem(
                    '字体大小',
                    widget.watermark.fontSize,
                    8.0,
                    20.0,
                    (value) {
                      _updateWatermark(
                        widget.watermark.copyWith(fontSize: value),
                      );
                    },
                    icon: Icons.format_size,
                  ),

                  const SizedBox(height: 16),

                  // 水印旋转角度
                  _buildSliderItem(
                    '旋转角度',
                    widget.watermark.rotation,
                    -1.0,
                    1.0,
                    (value) {
                      _updateWatermark(
                        widget.watermark.copyWith(rotation: value),
                      );
                    },
                    valueFormat:
                        (value) =>
                            '${(value * 180 / 3.14159).toStringAsFixed(0)}°',
                    icon: Icons.rotate_90_degrees_ccw,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 平铺设置（仅当选择平铺模式时显示）
          if (widget.watermark.position == WatermarkPosition.tiled)
            _buildTileSettings(),
        ],
      ),
    );
  }

  /// 构建样式选项芯片
  Widget _buildStyleChoiceChip(
    String label,
    bool selected,
    VoidCallback onTap,
    IconData icon,
  ) {
    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: selected ? Colors.white : null),
          const SizedBox(width: 4),
          Text(label),
        ],
      ),
      selected: selected,
      onSelected: (bool selected) {
        if (selected) {
          onTap();
        }
      },
      backgroundColor: Colors.grey.withValues(alpha: 0.1),
      selectedColor: Theme.of(context).primaryColor,
      labelStyle: TextStyle(
        color: selected ? Colors.white : null,
        fontWeight: selected ? FontWeight.bold : null,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
    );
  }

  /// 根据水印位置获取图标
  IconData _getPositionIcon(WatermarkPosition position) {
    switch (position) {
      case WatermarkPosition.topLeft:
        return Icons.align_horizontal_left;
      case WatermarkPosition.topCenter:
        return Icons.align_horizontal_center;
      case WatermarkPosition.topRight:
        return Icons.align_horizontal_right;
      case WatermarkPosition.bottomLeft:
        return Icons.vertical_align_bottom;
      case WatermarkPosition.bottomCenter:
        return Icons.vertical_align_center;
      case WatermarkPosition.bottomRight:
        return Icons.vertical_align_top;
      case WatermarkPosition.tiled:
        return Icons.grid_on;
    }
  }

  /// 构建平铺设置
  Widget _buildTileSettings() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.grid_on, size: 18),
                const SizedBox(width: 8),
                const Text(
                  '平铺设置',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 水平间距
            _buildSliderItem(
              '水平间距',
              widget.watermark.tileHorizontalGap,
              50.0,
              200.0,
              (value) {
                _updateWatermark(
                  widget.watermark.copyWith(tileHorizontalGap: value),
                );
              },
              valueFormat: (value) => '${value.toStringAsFixed(0)}px',
              icon: Icons.swap_horiz,
            ),

            const SizedBox(height: 16),

            // 垂直间距
            _buildSliderItem(
              '垂直间距',
              widget.watermark.tileVerticalGap,
              50.0,
              200.0,
              (value) {
                _updateWatermark(
                  widget.watermark.copyWith(tileVerticalGap: value),
                );
              },
              valueFormat: (value) => '${value.toStringAsFixed(0)}px',
              icon: Icons.swap_vert,
            ),
          ],
        ),
      ),
    );
  }

  /// 显示颜色选择器对话框
  void _showColorPicker(
    BuildContext context,
    Color currentColor,
    Function(Color) onColorChanged,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        Color pickedColor = currentColor;
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.palette, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              const Text('选择水印颜色'),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ColorPicker(
                  pickerColor: currentColor,
                  onColorChanged: (color) {
                    pickedColor = color;
                  },
                  pickerAreaHeightPercent: 0.8,
                  enableAlpha: true,
                  displayThumbColor: true,
                  portraitOnly: true,
                  hexInputBar: true,
                  pickerAreaBorderRadius: const BorderRadius.all(
                    Radius.circular(10),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                onColorChanged(pickedColor);
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  /// 构建滑块项
  Widget _buildSliderItem(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged, {
    String Function(double)? valueFormat,
    IconData? icon,
  }) {
    // 确保值在范围内，防止运行时错误
    double safeValue = value;
    if (safeValue < min) safeValue = min;
    if (safeValue > max) safeValue = max;

    final formattedValue =
        valueFormat != null
            ? valueFormat(safeValue)
            : safeValue.toStringAsFixed(2);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (icon != null) ...[
              Icon(icon, size: 16, color: Colors.grey.shade700),
              const SizedBox(width: 8),
            ],
            Text(
              label,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                formattedValue,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderThemeData(
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
            activeTrackColor: Theme.of(context).primaryColor,
            inactiveTrackColor: Colors.grey.withValues(alpha: 0.2),
            thumbColor: Theme.of(context).primaryColor,
            activeTickMarkColor: Colors.transparent,
            inactiveTickMarkColor: Colors.transparent,
          ),
          child: Slider(
            value: safeValue,
            min: min,
            max: max,
            divisions: ((max - min) * 10).toInt(),
            label: formattedValue,
            onChanged: (newValue) {
              onChanged(newValue);
            },
          ),
        ),
      ],
    );
  }
}
