import 'package:flutter/material.dart';

import '../config/app_theme.dart';
import '../content/content_save_button.dart';
import '../models/content_item.dart';
import 'controllers/markdown_render_controller.dart';
import 'services/markdown_export_service.dart';
import 'widgets/markdown_actions_dialog.dart';
import 'widgets/markdown_render_tabs.dart';

/// Markdown渲染屏幕
class MarkdownRenderScreen extends StatefulWidget {
  /// 初始Markdown文本
  final String? initialMarkdown;

  const MarkdownRenderScreen({super.key, this.initialMarkdown});

  @override
  State<MarkdownRenderScreen> createState() => _MarkdownRenderScreenState();
}

class _MarkdownRenderScreenState extends State<MarkdownRenderScreen>
    with SingleTickerProviderStateMixin {
  /// 标签控制器
  late TabController _tabController;

  /// 渲染组件的全局键，用于截图
  final GlobalKey _renderKey = GlobalKey();

  /// 内容区域的全局键
  final GlobalKey _contentAreaKey = GlobalKey();

  /// 滚动控制器
  final ScrollController _scrollController = ScrollController();

  /// 控制器
  late MarkdownRenderController _controller;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this); // 增加到5个标签页
    _controller = MarkdownRenderController(
      initialMarkdown: widget.initialMarkdown,
    );

    // 延迟计算内容高度，确保渲染完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateContentHeight();
    });
  }

  /// 计算内容区域高度
  void _calculateContentHeight() {
    final RenderBox? renderBox =
        _contentAreaKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      _controller.setContentHeight(renderBox.size.height);
    } else {
      _controller.setContentHeight(1000);
      // 延迟再次尝试计算
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _calculateContentHeight();
      });
    }
  }

  @override
  void dispose() {
    // 安全处理ScrollController
    if (_scrollController.hasClients) {
      _scrollController.removeListener(() {});
    }
    _scrollController.dispose();
    _tabController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击空白处隐藏键盘
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: AppTheme.bgLightColor,
        appBar: AppBar(
          title: const Text(
            'Markdown',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 22),
          ),
          centerTitle: false,
          backgroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.blueGradient.colors.first.withValues(alpha: 0.1),
                  AppTheme.blueGradient.colors.last.withValues(alpha: 0.05),
                ],
              ),
            ),
          ),
          actions: [
            ContentSaveButton(
              title: '保存的 Markdown',
              content: _controller.markdownController.text,
              contentType: ContentType.markdown,
              onSaved: (item) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('内容已保存到内容库'),
                    backgroundColor: AppTheme.blueGradient.colors.first,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                );
              },
            ),
            // 更多操作按钮
            Container(
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                color: AppTheme.blueGradient.colors.first.withValues(
                  alpha: 0.1,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.more_vert,
                  color: AppTheme.blueGradient.colors.first,
                ),
                tooltip: '更多操作',
                onPressed: () => _showActionsDialog(),
              ),
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(52),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    offset: const Offset(0, 2),
                    blurRadius: 4,
                  ),
                ],
                border: const Border(
                  bottom: BorderSide(color: AppTheme.borderColor, width: 0.5),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                child: TabBar(
                  controller: _tabController,
                  labelStyle: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  unselectedLabelStyle: const TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: AppTheme.textMediumColor,
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    gradient: AppTheme.blueGradient,
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.blueGradient.colors.first.withValues(
                          alpha: 0.3,
                        ),
                        offset: const Offset(0, 2),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicatorPadding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 6,
                  ),
                  tabs: const [
                    Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.edit, size: 16),
                          SizedBox(width: 4),
                          Text('编辑'),
                        ],
                      ),
                    ),
                    Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.description, size: 16),
                          SizedBox(width: 4),
                          Text('模板'),
                        ],
                      ),
                    ),
                    Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.palette, size: 16),
                          SizedBox(width: 4),
                          Text('样式'),
                        ],
                      ),
                    ),
                    Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.branding_watermark, size: 16),
                          SizedBox(width: 4),
                          Text('水印'),
                        ],
                      ),
                    ),
                    Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.view_module, size: 16),
                          SizedBox(width: 4),
                          Text('分块'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [AppTheme.bgLightColor, AppTheme.bgWhiteColor],
            ),
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(0),
              topRight: Radius.circular(0),
            ),
            child: MarkdownRenderTabs(
              controller: _controller,
              renderKey: _renderKey,
              contentAreaKey: _contentAreaKey,
              scrollController: _scrollController,
              tabController: _tabController,
            ),
          ),
        ),
      ),
    );
  }

  /// 处理分享
  Future<void> _handleShare() async {
    await MarkdownExportService.handleShare(
      markdownText: _controller.markdownController.text,
      renderKey: _renderKey,
      context: context,
      tabController: _tabController,
      showSnackBar: _showSnackBar,
    );
  }

  /// 处理复制
  Future<void> _handleCopy() async {
    await MarkdownExportService.handleCopy(
      markdownText: _controller.markdownController.text,
      renderKey: _renderKey,
      showSnackBar: _showSnackBar,
    );
  }

  /// 处理保存
  Future<void> _handleSave() async {
    await MarkdownExportService.handleSave(
      markdownText: _controller.markdownController.text,
      renderKey: _renderKey,
      context: context,
      showSnackBar: _showSnackBar,
    );
  }

  /// 显示提示消息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  /// 显示操作对话框
  void _showActionsDialog() {
    showDialog(
      context: context,
      builder: (context) => MarkdownActionsDialog(
        onActionSelected: (action) async {
          switch (action) {
            case 'share':
              await _handleShare();
              break;
            case 'copy':
              await _handleCopy();
              break;
            case 'save':
              await _handleSave();
              break;
          }
        },
      ),
    );
  }

}
