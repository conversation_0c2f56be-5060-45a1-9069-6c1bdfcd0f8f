import 'package:flutter/material.dart';

import 'models/markdown_template.dart';
import 'widgets/markdown_renderer_widget.dart';

class TestMarkdownScreen extends StatelessWidget {
  const TestMarkdownScreen({super.key});

  @override
  Widget build(BuildContext context) {
    const String markdownContent = '''
# Markdown渲染测试

这是一段普通文本，用于测试Markdown的渲染效果。

## 文本样式测试

**这是加粗文本**

*这是斜体文本*

***这是加粗斜体文本***

~~这是删除线文本~~

__这是下划线文本__（注意：Markdown标准中没有下划线标记，这里使用双下划线表示）

## 引用格式测试

> 这是一个简单的引用文本。

> 这是一个包含**加粗文本**的引用。
> 
> 这是引用的第二行，包含*斜体文本*和__下划线文本__。
> 
> 可以在引用中嵌套其他元素：
> 
> * 引用中的列表项1
> * 引用中的列表项2
>   * 引用中的嵌套列表项

> 还可以嵌套引用
> > 这是嵌套的引用
> > 
> > 嵌套引用中的**加粗文本**和*斜体文本*

## 无序列表测试

* 列表项1
* 列表项2
  * 嵌套列表项1
  * 嵌套列表项2
* 列表项3

## 有序列表测试

1. 第一项
2. 第二项
   1. 嵌套有序列表1
   2. 嵌套有序列表2
3. 第三项

## 表格测试

| 表头1 | 表头2 | 表头3 |
| ----- | ----- | ----- |
| 单元格1 | 单元格2 | 单元格3 |
| 第二行1 | 第二行2 | 第二行3 |
| 第三行1 | 第三行2 | 第三行3 |

## 混合内容测试

以下是一个包含多种元素的混合内容:

1. **粗体文本**
2. *斜体文本*
3. 表格与列表:
   
   | 项目 | 描述 |
   | ---- | ---- |
   | 列表 | 包含有序和无序列表 |
   | 表格 | 支持多列数据展示 |

4. 代码块:

```dart
void main() {
  print('Hello World!');
}
```

以上是对Markdown渲染的基本测试。
''';

    return Scaffold(
      appBar: AppBar(title: const Text('Markdown渲染测试')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: MarkdownRendererWidget(
            markdownText: markdownContent,
            template: MarkdownTemplate.modern(),
            selectable: true,
          ),
        ),
      ),
    );
  }
}
