import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';

import 'models/markdown_render_style.dart';


/// 样式选择器组件
class StyleSelectorWidget extends StatelessWidget {
  /// 当前选中的样式
  final MarkdownRenderStyle selectedStyle;

  /// 样式列表
  final List<MarkdownRenderStyle> styles;

  /// 样式选择回调
  final Function(MarkdownRenderStyle) onStyleSelected;

  /// 样式更新回调
  final Function(MarkdownRenderStyle) onStyleUpdated;

  const StyleSelectorWidget({
    super.key,
    required this.selectedStyle,
    required this.styles,
    required this.onStyleSelected,
    required this.onStyleUpdated,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          child: Text(
            '选择主题',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ),
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: styles.length,
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            itemBuilder: (context, index) {
              final style = styles[index];
              final isSelected = style.id == selectedStyle.id;

              return GestureDetector(
                onTap: () => onStyleSelected(style),
                child: Container(
                  width: 100,
                  margin: const EdgeInsets.symmetric(
                    horizontal: 8.0,
                    vertical: 6.0,
                  ),
                  decoration: BoxDecoration(
                    color: style.backgroundColor,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color:
                          isSelected
                              ? Theme.of(context).primaryColor
                              : Colors.grey.withAlpha(51),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(15),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        style.name,
                        style: TextStyle(
                          color: style.textColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 24),
        _buildStyleCustomizationSection(context),
      ],
    );
  }

  /// 构建样式自定义部分
  Widget _buildStyleCustomizationSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(
              vertical: 8.0,
              horizontal: 12.0,
            ),
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(
                  color: Theme.of(context).primaryColor,
                  width: 3,
                ),
              ),
            ),
            child: const Text(
              '自定义样式',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 16),

          // 颜色选择部分
          const Text(
            '颜色',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 20,
            runSpacing: 20,
            children: [
              _buildColorPickerItem(
                context,
                '背景色',
                selectedStyle.backgroundColor,
                (color) {
                  onStyleUpdated(
                    selectedStyle.copyWith(backgroundColor: color),
                  );
                },
              ),
              _buildColorPickerItem(context, '文本色', selectedStyle.textColor, (
                color,
              ) {
                onStyleUpdated(selectedStyle.copyWith(textColor: color));
              }),
              _buildColorPickerItem(
                context,
                '代码背景',
                selectedStyle.codeBackgroundColor,
                (color) {
                  onStyleUpdated(
                    selectedStyle.copyWith(codeBackgroundColor: color),
                  );
                },
              ),
              _buildColorPickerItem(
                context,
                '代码文本',
                selectedStyle.codeTextColor,
                (color) {
                  onStyleUpdated(selectedStyle.copyWith(codeTextColor: color));
                },
              ),
              _buildColorPickerItem(
                context,
                '引用边框',
                selectedStyle.quoteBorderColor,
                (color) {
                  onStyleUpdated(
                    selectedStyle.copyWith(quoteBorderColor: color),
                  );
                },
              ),
              _buildColorPickerItem(
                context,
                '引用文本',
                selectedStyle.quoteTextColor,
                (color) {
                  onStyleUpdated(selectedStyle.copyWith(quoteTextColor: color));
                },
              ),
            ],
          ),

          const SizedBox(height: 24),

          // 排版设置部分
          const Text(
            '排版',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),

          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: Colors.grey.withAlpha(51)),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildSliderItem(
                          '字体大小',
                          selectedStyle.baseFontSize,
                          12.0,
                          24.0,
                          (value) {
                            onStyleUpdated(
                              selectedStyle.copyWith(baseFontSize: value),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildSliderItem(
                          '圆角大小',
                          selectedStyle.borderRadius,
                          0.0,
                          32.0,
                          (value) {
                            onStyleUpdated(
                              selectedStyle.copyWith(borderRadius: value),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // 标题对齐方式选择
                  const Text(
                    '标题对齐方式',
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  _buildHeadingAlignmentSelector(),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // 列表样式设置部分
          const Text(
            '列表样式',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: Colors.grey.withAlpha(51)),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildListItemStyleSelector(),
                  const SizedBox(height: 16),
                  _buildCheckboxStyleSelector(),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // 字体设置部分
          const Text(
            '字体',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),
          _buildFontFamilySelector(),
        ],
      ),
    );
  }

  /// 构建颜色选择器项
  Widget _buildColorPickerItem(
    BuildContext context,
    String label,
    Color color,
    Function(Color) onColorChanged,
  ) {
    return GestureDetector(
      onTap: () {
        _showColorPicker(context, color, onColorChanged);
      },
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withAlpha(51)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(15),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                  spreadRadius: 0.5,
                ),
              ],
            ),
            child: Center(
              child: Icon(
                Icons.colorize,
                color:
                    color.computeLuminance() > 0.5
                        ? Colors.black54
                        : Colors.white70,
                size: 24,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
          // 显示颜色值
          Text(
            "${color.r.round().toString().padLeft(2, '0')},${color.g.round().toString().padLeft(2, '0')},${color.b.round().toString().padLeft(2, '0')}",
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  /// 显示颜色选择器对话框
  void _showColorPicker(
    BuildContext context,
    Color currentColor,
    Function(Color) onColorChanged,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        Color pickedColor = currentColor;
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.palette_outlined,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              const Text('选择颜色'),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ColorPicker(
                  pickerColor: currentColor,
                  onColorChanged: (color) {
                    pickedColor = color;
                  },
                  pickerAreaHeightPercent: 0.8,
                  enableAlpha: true,
                  displayThumbColor: true,
                  portraitOnly: true,
                  hexInputBar: true,
                  pickerAreaBorderRadius: const BorderRadius.all(
                    Radius.circular(10),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                onColorChanged(pickedColor);
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  /// 构建滑块项
  Widget _buildSliderItem(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey.withAlpha(26),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                value.toStringAsFixed(1),
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: const SliderThemeData(
            trackHeight: 4,
            thumbShape: RoundSliderThumbShape(enabledThumbRadius: 8),
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: ((max - min) * 2).toInt(),
            label: value.toStringAsFixed(1),
            onChanged: (newValue) {
              onChanged(newValue);
            },
          ),
        ),
      ],
    );
  }

  /// 构建字体选择器
  Widget _buildFontFamilySelector() {
    // 确保列表中的字体值没有重复
    final Set<String> uniqueFontFamilies = {
      'Roboto',
      'Noto Serif',
      'Noto Serif SC',
      'Arial',
      'Times New Roman',
      'Georgia',
      'Courier New',
    };

    // 确保当前选中的字体在列表中
    String currentFont = selectedStyle.fontFamily;
    if (currentFont == 'Ma Shan Zheng') {
      // 为Ma Shan Zheng字体提供回退选项
      currentFont = 'Roboto';
      // 延迟更新字体以避免构建周期内修改状态
      Future.microtask(() {
        onStyleUpdated(selectedStyle.copyWith(fontFamily: 'Roboto'));
      });
    }

    // 将当前字体添加到列表中
    if (!uniqueFontFamilies.contains(currentFont)) {
      uniqueFontFamilies.add(currentFont);
    }

    // 将Set转换为List以便排序
    final fontFamilies = uniqueFontFamilies.toList()..sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('字体', style: TextStyle(fontSize: 14)),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withAlpha(77)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButton<String>(
            value: currentFont,
            isExpanded: true,
            underline: const SizedBox(),
            onChanged: (value) {
              if (value != null) {
                onStyleUpdated(selectedStyle.copyWith(fontFamily: value));
              }
            },
            items:
                fontFamilies.map((font) {
                  return DropdownMenuItem<String>(
                    value: font,
                    child: Text(font, style: TextStyle(fontFamily: font)),
                  );
                }).toList(),
          ),
        ),
        const SizedBox(height: 16),
        const Text('代码字体', style: TextStyle(fontSize: 14)),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withAlpha(77)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButton<String>(
            value: selectedStyle.codeFontFamily,
            isExpanded: true,
            underline: const SizedBox(),
            onChanged: (value) {
              if (value != null) {
                onStyleUpdated(selectedStyle.copyWith(codeFontFamily: value));
              }
            },
            items:
                [
                  'monospace',
                  'Courier New',
                  'Consolas',
                  'Roboto Mono',
                  'Fira Code',
                ].map((font) {
                  return DropdownMenuItem<String>(
                    value: font,
                    child: Text(font, style: TextStyle(fontFamily: font)),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }

  /// 构建标题对齐方式选择器
  Widget _buildHeadingAlignmentSelector() {
    // 获取对齐方式的显示文本
    String getAlignmentDisplayText(TextAlign align) {
      switch (align) {
        case TextAlign.left:
          return '左对齐';
        case TextAlign.center:
          return '居中对齐';
        case TextAlign.right:
          return '右对齐';
        default:
          return '左对齐';
      }
    }

    // 获取对齐方式的图标
    IconData getAlignmentIcon(TextAlign align) {
      switch (align) {
        case TextAlign.left:
          return Icons.format_align_left;
        case TextAlign.center:
          return Icons.format_align_center;
        case TextAlign.right:
          return Icons.format_align_right;
        default:
          return Icons.format_align_left;
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withAlpha(77)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButton<TextAlign>(
        value: selectedStyle.headingAlignment,
        isExpanded: true,
        underline: const SizedBox(),
        onChanged: (value) {
          if (value != null) {
            onStyleUpdated(selectedStyle.copyWith(headingAlignment: value));
          }
        },
        items:
            [TextAlign.left, TextAlign.center, TextAlign.right].map((
              alignment,
            ) {
              return DropdownMenuItem<TextAlign>(
                value: alignment,
                child: Row(
                  children: [
                    Icon(getAlignmentIcon(alignment), size: 18),
                    const SizedBox(width: 8),
                    Text(getAlignmentDisplayText(alignment)),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  /// 构建列表项样式选择器
  Widget _buildListItemStyleSelector() {
    // 列表符号选项
    final listSymbols = [
      {'value': '•', 'label': '圆点 •'},
      {'value': '○', 'label': '空心圆 ○'},
      {'value': '■', 'label': '方块 ■'},
      {'value': '□', 'label': '空心方块 □'},
      {'value': '◆', 'label': '菱形 ◆'},
      {'value': '❧', 'label': '叶子 ❧'},
      {'value': '✧', 'label': '星星 ✧'},
      {'value': '❁', 'label': '花朵 ❁'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('R', style: TextStyle(fontSize: 14)),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withAlpha(77)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButton<String>(
            value: selectedStyle.listItemStyle,
            isExpanded: true,
            underline: const SizedBox(),
            onChanged: (value) {
              if (value != null) {
                onStyleUpdated(selectedStyle.copyWith(listItemStyle: value));
              }
            },
            items:
                listSymbols.map((symbol) {
                  return DropdownMenuItem<String>(
                    value: symbol['value'],
                    child: Text(symbol['label']!),
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }

  /// 构建复选框样式选择器
  Widget _buildCheckboxStyleSelector() {
    // 复选框符号选项
    final checkboxStyles = [
      {'unchecked': '☐', 'checked': '☑', 'label': '方形 ☐/☑'},
      {'unchecked': '◯', 'checked': '●', 'label': '圆形 ◯/●'},
      {'unchecked': '□', 'checked': '■', 'label': '方块 □/■'},
      {'unchecked': '❏', 'checked': '❐', 'label': '粗方形 ❏/❐'},
      {'unchecked': '☆', 'checked': '★', 'label': '星形 ☆/★'},
    ];

    // 确保value匹配列表中的某一项，防止不匹配错误
    String currentValue = selectedStyle.checkboxUncheckedStyle;
    // 检查当前值是否在选项中存在
    bool valueExists = checkboxStyles.any(
      (style) => style['unchecked'] == currentValue,
    );
    // 如果不存在，使用默认选项
    if (!valueExists) {
      currentValue = checkboxStyles.first['unchecked']!;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('复选框样式', style: TextStyle(fontSize: 14)),
        const SizedBox(height: 8),
        Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.withAlpha(77)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButton<String>(
                value: currentValue,
                isExpanded: true,
                underline: const SizedBox(),
                onChanged: (uncheckedValue) {
                  if (uncheckedValue != null) {
                    // 找到匹配的样式对
                    final selectedStyle = checkboxStyles.firstWhere(
                      (style) => style['unchecked'] == uncheckedValue,
                      orElse: () => checkboxStyles.first,
                    );

                    // 更新未选中和已选中的样式
                    onStyleUpdated(
                      this.selectedStyle.copyWith(
                        checkboxUncheckedStyle: uncheckedValue,
                        checkboxCheckedStyle: selectedStyle['checked'],
                      ),
                    );
                  }
                },
                items:
                    checkboxStyles.map((style) {
                      return DropdownMenuItem<String>(
                        value: style['unchecked'],
                        child: Text('${style['label']}'),
                      );
                    }).toList(),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
