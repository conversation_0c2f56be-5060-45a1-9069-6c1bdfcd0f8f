import 'package:flutter/material.dart';

import '../controllers/markdown_block_controller.dart';
import '../models/markdown_block.dart';
import '../models/markdown_template.dart';
import '../services/markdown_block_export_service.dart';
import '../utils/markdown_block_utils.dart';

/// Markdown 分块管理面板
class MarkdownBlockManagerPanel extends StatefulWidget {
  /// 分块控制器
  final MarkdownBlockController controller;
  
  /// 渲染模板
  final MarkdownTemplate template;

  const MarkdownBlockManagerPanel({
    super.key,
    required this.controller,
    required this.template,
  });

  @override
  State<MarkdownBlockManagerPanel> createState() => _MarkdownBlockManagerPanelState();
}

class _MarkdownBlockManagerPanelState extends State<MarkdownBlockManagerPanel> {
  /// 选中的分块ID列表
  final Set<String> _selectedBlockIds = {};
  
  /// 是否显示隐藏的分块
  bool _showHiddenBlocks = true;
  
  /// 排序方式
  BlockSortOrder _sortOrder = BlockSortOrder.byIndex;

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        final blocks = _getFilteredAndSortedBlocks();
        final statistics = MarkdownBlockUtils.calculateBlockStatistics(widget.controller.blocks);
        
        return Card(
          margin: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题栏
              _buildHeader(statistics),
              
              // 工具栏
              _buildToolbar(),
              
              const Divider(height: 1),
              
              // 分块列表
              Expanded(
                child: blocks.isEmpty
                    ? _buildEmptyState()
                    : _buildBlockList(blocks),
              ),
              
              // 底部操作栏
              if (_selectedBlockIds.isNotEmpty)
                _buildBottomActionBar(),
            ],
          ),
        );
      },
    );
  }

  /// 构建标题栏
  Widget _buildHeader(BlockStatistics statistics) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.view_module,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          const Text(
            '分块管理',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          _buildStatisticsChips(statistics),
        ],
      ),
    );
  }

  /// 构建统计信息芯片
  Widget _buildStatisticsChips(BlockStatistics statistics) {
    return Row(
      children: [
        _buildStatChip('总计', '${statistics.totalBlocks}', Colors.blue),
        const SizedBox(width: 8),
        _buildStatChip('可见', '${statistics.visibleBlocks}', Colors.green),
        const SizedBox(width: 8),
        _buildStatChip('隐藏', '${statistics.hiddenBlocks}', Colors.orange),
      ],
    );
  }

  /// 构建统计芯片
  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建工具栏
  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // 显示/隐藏切换
          FilterChip(
            label: Text(_showHiddenBlocks ? '显示全部' : '仅显示可见'),
            selected: _showHiddenBlocks,
            onSelected: (selected) {
              setState(() {
                _showHiddenBlocks = selected;
              });
            },
          ),
          const SizedBox(width: 8),
          
          // 排序选择
          DropdownButton<BlockSortOrder>(
            value: _sortOrder,
            items: const [
              DropdownMenuItem(
                value: BlockSortOrder.byIndex,
                child: Text('按索引排序'),
              ),
              DropdownMenuItem(
                value: BlockSortOrder.byTitle,
                child: Text('按标题排序'),
              ),
              DropdownMenuItem(
                value: BlockSortOrder.byType,
                child: Text('按类型排序'),
              ),
              DropdownMenuItem(
                value: BlockSortOrder.byLength,
                child: Text('按长度排序'),
              ),
            ],
            onChanged: (order) {
              if (order != null) {
                setState(() {
                  _sortOrder = order;
                });
              }
            },
          ),
          
          const Spacer(),
          
          // 全选/取消全选
          TextButton.icon(
            onPressed: _toggleSelectAll,
            icon: Icon(
              _selectedBlockIds.length == widget.controller.blocks.length
                  ? Icons.deselect
                  : Icons.select_all,
              size: 16,
            ),
            label: Text(
              _selectedBlockIds.length == widget.controller.blocks.length
                  ? '取消全选'
                  : '全选',
            ),
          ),
          
          // 导出按钮
          IconButton(
            onPressed: _showExportOptions,
            icon: const Icon(Icons.download),
            tooltip: '导出选项',
          ),
        ],
      ),
    );
  }

  /// 构建分块列表
  Widget _buildBlockList(List<MarkdownBlock> blocks) {
    return ListView.builder(
      itemCount: blocks.length,
      itemBuilder: (context, index) {
        final block = blocks[index];
        return _buildBlockItem(block, index);
      },
    );
  }

  /// 构建分块项
  Widget _buildBlockItem(MarkdownBlock block, int index) {
    final isSelected = _selectedBlockIds.contains(block.id);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(
          color: isSelected 
              ? Theme.of(context).primaryColor 
              : Colors.grey.shade300,
          width: isSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color: isSelected 
            ? Theme.of(context).primaryColor.withValues(alpha: 0.05)
            : null,
      ),
      child: ListTile(
        leading: Checkbox(
          value: isSelected,
          onChanged: (selected) {
            setState(() {
              if (selected == true) {
                _selectedBlockIds.add(block.id);
              } else {
                _selectedBlockIds.remove(block.id);
              }
            });
          },
        ),
        title: Row(
          children: [
            Icon(
              _getBlockTypeIcon(block.separatorType),
              size: 16,
              color: _getBlockTypeColor(block.separatorType),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                block.title,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: block.isVisible ? null : Colors.grey,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              MarkdownBlockUtils.generateBlockSummary(block),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: block.isVisible ? Colors.grey.shade600 : Colors.grey.shade400,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  '${block.content.length} 字符',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade500,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  MarkdownBlockUtils.formatBlockType(block.separatorType),
                  style: TextStyle(
                    fontSize: 12,
                    color: _getBlockTypeColor(block.separatorType),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 可见性切换
            IconButton(
              icon: Icon(
                block.isVisible ? Icons.visibility : Icons.visibility_off,
                size: 20,
              ),
              onPressed: () {
                widget.controller.toggleBlockVisibility(block.id);
              },
              tooltip: block.isVisible ? '隐藏分块' : '显示分块',
            ),
            
            // 更多操作
            PopupMenuButton<String>(
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'export_image',
                  child: Row(
                    children: [
                      Icon(Icons.image, size: 16),
                      SizedBox(width: 8),
                      Text('导出为图片'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'export_markdown',
                  child: Row(
                    children: [
                      Icon(Icons.description, size: 16),
                      SizedBox(width: 8),
                      Text('导出为Markdown'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'copy_content',
                  child: Row(
                    children: [
                      Icon(Icons.copy, size: 16),
                      SizedBox(width: 8),
                      Text('复制内容'),
                    ],
                  ),
                ),
              ],
              onSelected: (action) => _handleBlockAction(action, block),
            ),
          ],
        ),
        onTap: () {
          setState(() {
            if (isSelected) {
              _selectedBlockIds.remove(block.id);
            } else {
              _selectedBlockIds.add(block.id);
            }
          });
        },
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.view_module_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无分块',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '启用分块渲染来查看分块列表',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Text(
            '已选择 ${_selectedBlockIds.length} 个分块',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          const Spacer(),
          TextButton.icon(
            onPressed: _showSelectedBlocks,
            icon: const Icon(Icons.visibility, size: 16),
            label: const Text('显示选中'),
          ),
          const SizedBox(width: 8),
          TextButton.icon(
            onPressed: _hideSelectedBlocks,
            icon: const Icon(Icons.visibility_off, size: 16),
            label: const Text('隐藏选中'),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: _exportSelectedBlocks,
            icon: const Icon(Icons.download, size: 16),
            label: const Text('导出选中'),
          ),
        ],
      ),
    );
  }

  /// 获取过滤和排序后的分块列表
  List<MarkdownBlock> _getFilteredAndSortedBlocks() {
    var blocks = widget.controller.blocks.where((block) {
      if (!_showHiddenBlocks && !block.isVisible) {
        return false;
      }
      return true;
    }).toList();

    // 排序
    switch (_sortOrder) {
      case BlockSortOrder.byIndex:
        blocks.sort((a, b) => a.index.compareTo(b.index));
        break;
      case BlockSortOrder.byTitle:
        blocks.sort((a, b) => a.title.compareTo(b.title));
        break;
      case BlockSortOrder.byType:
        blocks.sort((a, b) => a.separatorType.index.compareTo(b.separatorType.index));
        break;
      case BlockSortOrder.byLength:
        blocks.sort((a, b) => b.content.length.compareTo(a.content.length));
        break;
    }

    return blocks;
  }

  /// 获取分块类型图标
  IconData _getBlockTypeIcon(BlockSeparatorType type) {
    switch (type) {
      case BlockSeparatorType.h1:
        return Icons.title;
      case BlockSeparatorType.h2:
        return Icons.subtitles;
      case BlockSeparatorType.custom:
        return Icons.horizontal_rule;
      case BlockSeparatorType.manual:
        return Icons.content_cut;
    }
  }

  /// 获取分块类型颜色
  Color _getBlockTypeColor(BlockSeparatorType type) {
    switch (type) {
      case BlockSeparatorType.h1:
        return Colors.blue;
      case BlockSeparatorType.h2:
        return Colors.green;
      case BlockSeparatorType.custom:
        return Colors.orange;
      case BlockSeparatorType.manual:
        return Colors.grey;
    }
  }

  /// 切换全选状态
  void _toggleSelectAll() {
    setState(() {
      if (_selectedBlockIds.length == widget.controller.blocks.length) {
        _selectedBlockIds.clear();
      } else {
        _selectedBlockIds.addAll(widget.controller.blocks.map((b) => b.id));
      }
    });
  }

  /// 显示选中的分块
  void _showSelectedBlocks() {
    for (final blockId in _selectedBlockIds) {
      final block = widget.controller.blocks.firstWhere((b) => b.id == blockId);
      if (!block.isVisible) {
        widget.controller.toggleBlockVisibility(blockId);
      }
    }
  }

  /// 隐藏选中的分块
  void _hideSelectedBlocks() {
    for (final blockId in _selectedBlockIds) {
      final block = widget.controller.blocks.firstWhere((b) => b.id == blockId);
      if (block.isVisible) {
        widget.controller.toggleBlockVisibility(blockId);
      }
    }
  }

  /// 导出选中的分块
  void _exportSelectedBlocks() {
    final selectedBlocks = widget.controller.blocks
        .where((b) => _selectedBlockIds.contains(b.id))
        .toList();
    
    if (selectedBlocks.isNotEmpty) {
      _showExportDialog(selectedBlocks);
    }
  }

  /// 显示导出选项
  void _showExportOptions() {
    _showExportDialog(widget.controller.visibleBlocks);
  }

  /// 显示导出对话框
  void _showExportDialog(List<MarkdownBlock> blocks) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('导出分块'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.image),
                title: const Text('导出为图片'),
                onTap: () {
                  Navigator.pop(context);
                  _exportAsImage(blocks);
                },
              ),
              ListTile(
                leading: const Icon(Icons.description),
                title: const Text('导出为Markdown'),
                onTap: () {
                  Navigator.pop(context);
                  _exportAsMarkdown(blocks);
                },
              ),
              ListTile(
                leading: const Icon(Icons.assessment),
                title: const Text('生成摘要报告'),
                onTap: () {
                  Navigator.pop(context);
                  _generateSummaryReport(blocks);
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  /// 处理分块操作
  void _handleBlockAction(String action, MarkdownBlock block) {
    switch (action) {
      case 'export_image':
        _exportAsImage([block]);
        break;
      case 'export_markdown':
        _exportAsMarkdown([block]);
        break;
      case 'copy_content':
        // 这里需要实现复制到剪贴板的功能
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('内容已复制到剪贴板')),
        );
        break;
    }
  }

  /// 导出为图片
  void _exportAsImage(List<MarkdownBlock> blocks) async {
    try {
      final filePath = await MarkdownBlockExportService.exportBlocksAsImage(
        blocks,
        widget.template,
      );
      
      if (filePath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('图片已导出: $filePath')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导出失败: $e')),
        );
      }
    }
  }

  /// 导出为Markdown
  void _exportAsMarkdown(List<MarkdownBlock> blocks) async {
    try {
      final filePath = await MarkdownBlockExportService.exportBlocksAsMarkdown(blocks);
      
      if (filePath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Markdown文件已导出: $filePath')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导出失败: $e')),
        );
      }
    }
  }

  /// 生成摘要报告
  void _generateSummaryReport(List<MarkdownBlock> blocks) async {
    try {
      final filePath = await MarkdownBlockExportService.createBlockSummaryReport(
        blocks,
        includeStatistics: true,
        includeContent: true,
      );
      
      if (filePath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('摘要报告已生成: $filePath')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('生成报告失败: $e')),
        );
      }
    }
  }
}

/// 分块排序方式
enum BlockSortOrder {
  /// 按索引排序
  byIndex,

  /// 按标题排序
  byTitle,

  /// 按类型排序
  byType,

  /// 按长度排序
  byLength,
}
