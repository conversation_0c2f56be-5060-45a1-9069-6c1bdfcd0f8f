import 'package:flutter/material.dart';

import '../models/markdown_block.dart';

/// 简化的 Markdown 分块配置组件
class SimpleMarkdownBlockConfig extends StatefulWidget {
  /// 当前配置
  final BlockRenderConfig config;
  
  /// 配置更新回调
  final ValueChanged<BlockRenderConfig> onConfigChanged;

  const SimpleMarkdownBlockConfig({
    super.key,
    required this.config,
    required this.onConfigChanged,
  });

  @override
  State<SimpleMarkdownBlockConfig> createState() => _SimpleMarkdownBlockConfigState();
}

class _SimpleMarkdownBlockConfigState extends State<SimpleMarkdownBlockConfig> {
  late BlockRenderConfig _currentConfig;

  @override
  void initState() {
    super.initState();
    _currentConfig = widget.config;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Row(
              children: [
                const Icon(Icons.view_module, size: 20),
                const SizedBox(width: 8),
                const Text(
                  '分块显示',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Switch(
                  value: _currentConfig.enabled,
                  onChanged: (value) {
                    _updateConfig(_currentConfig.copyWith(enabled: value));
                  },
                ),
              ],
            ),
            
            if (_currentConfig.enabled) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              
              // 分块模式选择
              const Text(
                '分块方式',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              _buildModeSelector(),
              
              const SizedBox(height: 16),
              
              // 显示选项
              const Text(
                '显示选项',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              _buildDisplayOptions(),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建模式选择器
  Widget _buildModeSelector() {
    return Column(
      children: [
        _buildModeOption(
          BlockMode.auto,
          '智能分块',
          '自动识别标题和分隔符进行分块',
          Icons.auto_awesome,
        ),
        _buildModeOption(
          BlockMode.headings,
          '按标题分块',
          '仅按 Markdown 标题进行分块',
          Icons.title,
        ),
        _buildModeOption(
          BlockMode.separators,
          '按分隔符分块',
          '仅按分隔符（如 ---）进行分块',
          Icons.horizontal_rule,
        ),
        _buildModeOption(
          BlockMode.manual,
          '手动分块',
          '不自动分块，可手动添加分隔',
          Icons.edit,
        ),
      ],
    );
  }

  /// 构建模式选项
  Widget _buildModeOption(BlockMode mode, String title, String description, IconData icon) {
    final isSelected = _currentConfig.mode == mode;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(
          color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade300,
          width: isSelected ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color: isSelected ? Theme.of(context).primaryColor.withValues(alpha: 0.05) : null,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade600,
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? Theme.of(context).primaryColor : null,
          ),
        ),
        subtitle: Text(
          description,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        onTap: () {
          _updateConfig(_currentConfig.copyWith(mode: mode));
        },
        selected: isSelected,
        dense: true,
      ),
    );
  }

  /// 构建显示选项
  Widget _buildDisplayOptions() {
    return Column(
      children: [
        SwitchListTile(
          title: const Text('显示分块标题'),
          subtitle: const Text('在每个分块顶部显示标题'),
          value: _currentConfig.showBlockTitles,
          onChanged: (value) {
            _updateConfig(_currentConfig.copyWith(showBlockTitles: value));
          },
          dense: true,
        ),
        SwitchListTile(
          title: const Text('显示分块边框'),
          subtitle: const Text('为每个分块添加边框'),
          value: _currentConfig.showBlockBorders,
          onChanged: (value) {
            _updateConfig(_currentConfig.copyWith(showBlockBorders: value));
          },
          dense: true,
        ),
        
        // 间距调整
        ListTile(
          title: const Text('分块间距'),
          subtitle: Slider(
            value: _currentConfig.blockSpacing,
            min: 8.0,
            max: 32.0,
            divisions: 6,
            label: '${_currentConfig.blockSpacing.round()}px',
            onChanged: (value) {
              _updateConfig(_currentConfig.copyWith(blockSpacing: value));
            },
          ),
          dense: true,
        ),
      ],
    );
  }

  /// 更新配置
  void _updateConfig(BlockRenderConfig newConfig) {
    setState(() {
      _currentConfig = newConfig;
    });
    widget.onConfigChanged(newConfig);
  }
}
