import 'package:flutter/material.dart';

import '../models/markdown_block.dart';

/// Markdown 分块配置面板
class MarkdownBlockConfigPanel extends StatefulWidget {
  /// 当前配置
  final BlockRenderConfig config;
  
  /// 配置更新回调
  final ValueChanged<BlockRenderConfig> onConfigChanged;

  const MarkdownBlockConfigPanel({
    super.key,
    required this.config,
    required this.onConfigChanged,
  });

  @override
  State<MarkdownBlockConfigPanel> createState() => _MarkdownBlockConfigPanelState();
}

class _MarkdownBlockConfigPanelState extends State<MarkdownBlockConfigPanel> {
  late TextEditingController _customPatternController;
  late BlockRenderConfig _currentConfig;

  @override
  void initState() {
    super.initState();
    _currentConfig = widget.config;
    _customPatternController = TextEditingController(
      text: _currentConfig.customSeparatorPattern,
    );
  }

  @override
  void dispose() {
    _customPatternController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Row(
              children: [
                const Icon(Icons.settings, size: 20),
                const SizedBox(width: 8),
                const Text(
                  '分块渲染设置',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                // 重置按钮
                TextButton.icon(
                  onPressed: _resetToDefault,
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('重置'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 基本设置
            _buildBasicSettings(),
            const SizedBox(height: 16),
            
            // 分隔符设置
            _buildSeparatorSettings(),
            const SizedBox(height: 16),
            
            // 外观设置
            _buildAppearanceSettings(),
          ],
        ),
      ),
    );
  }

  /// 构建基本设置
  Widget _buildBasicSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '基本设置',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        // 启用分块渲染
        SwitchListTile(
          title: const Text('启用分块渲染'),
          subtitle: const Text('开启后将按照设定规则对 Markdown 内容进行分块显示'),
          value: _currentConfig.enabled,
          onChanged: (value) {
            _updateConfig(_currentConfig.copyWith(enabled: value));
          },
        ),
        
        // 移除了操纵杆相关的配置选项
      ],
    );
  }

  /// 构建分隔符设置
  Widget _buildSeparatorSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '分隔符设置',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        // 按一级标题分隔
        CheckboxListTile(
          title: const Text('按一级标题分隔'),
          subtitle: const Text('使用 # 一级标题作为分块分隔符'),
          value: _currentConfig.splitByH1,
          onChanged: _currentConfig.enabled ? (value) {
                    _updateConfig(
                      _currentConfig.copyWith(splitByH1: value ?? false),
                    );
          } : null,
        ),

        // 按二级标题分隔
        CheckboxListTile(
          title: const Text('按二级标题分隔'),
          subtitle: const Text('使用 ## 二级标题作为分块分隔符'),
          value: _currentConfig.splitByH2,
          onChanged: _currentConfig.enabled ? (value) {
                    _updateConfig(
                      _currentConfig.copyWith(splitByH2: value ?? false),
                    );
          } : null,
        ),
        
        // 自定义分隔符
        const SizedBox(height: 8),
        const Text(
          '自定义分隔符模式（正则表达式）',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _customPatternController,
          enabled: _currentConfig.enabled,
          decoration: const InputDecoration(
            hintText: r'^\s*[-*_]{3,}\s*$',
            helperText: '例如：连续三个或更多短横线',
            border: OutlineInputBorder(),
            isDense: true,
          ),
          onChanged: (value) {
            // 自定义分隔符模式现在通过 mode 控制
            final newMode = value.isNotEmpty ? BlockMode.separators : BlockMode.manual;
            _updateConfig(_currentConfig.copyWith(mode: newMode));
          },
        ),
      ],
    );
  }

  /// 构建外观设置
  Widget _buildAppearanceSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '外观设置',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        // 分块间距
        ListTile(
          title: const Text('分块间距'),
          subtitle: Slider(
            value: _currentConfig.blockSpacing,
            min: 8.0,
            max: 32.0,
            divisions: 6,
            label: '${_currentConfig.blockSpacing.round()}px',
            onChanged: _currentConfig.enabled ? (value) {
              _updateConfig(_currentConfig.copyWith(blockSpacing: value));
            } : null,
          ),
        ),
        
        // 移除了操纵杆外观相关的配置选项
      ],
    );
  }

  // 移除了颜色选择器方法

  /// 重置到默认配置
  void _resetToDefault() {
    final defaultConfig = const BlockRenderConfig();
    _customPatternController.text = defaultConfig.customSeparatorPattern;
    _updateConfig(defaultConfig);
  }

  /// 更新配置
  void _updateConfig(BlockRenderConfig newConfig) {
    setState(() {
      _currentConfig = newConfig;
    });
    widget.onConfigChanged(newConfig);
  }
}
