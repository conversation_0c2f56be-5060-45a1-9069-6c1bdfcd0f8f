import 'package:flutter/material.dart';

import '../controllers/markdown_block_controller.dart';
import '../models/markdown_block.dart';
import '../models/markdown_template.dart';
import '../services/markdown_block_export_service.dart';
import 'markdown_renderer_widget.dart';

/// 简化的 Markdown 分块渲染组件
class SimpleMarkdownBlockRenderer extends StatefulWidget {
  /// 分块控制器
  final MarkdownBlockController controller;
  
  /// 渲染模板
  final MarkdownTemplate template;
  
  /// 是否可选择文本
  final bool selectable;
  
  /// 渲染组件的全局键，用于截图
  final GlobalKey? renderKey;
  
  /// 最大高度
  final double? maxHeight;

  const SimpleMarkdownBlockRenderer({
    super.key,
    required this.controller,
    required this.template,
    this.selectable = false,
    this.renderKey,
    this.maxHeight,
  });

  @override
  State<SimpleMarkdownBlockRenderer> createState() => _SimpleMarkdownBlockRendererState();
}

class _SimpleMarkdownBlockRendererState extends State<SimpleMarkdownBlockRenderer> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        if (widget.controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (!widget.controller.config.enabled) {
          // 非分块模式，使用原始渲染器
          return _buildNormalRenderer();
        }

        return _buildBlockRenderer();
      },
    );
  }

  /// 构建普通渲染器（非分块模式）
  Widget _buildNormalRenderer() {
    return MarkdownRendererWidget(
      renderKey: widget.renderKey,
      markdownText: widget.controller.markdownText,
      template: widget.template,
      selectable: widget.selectable,
      maxHeight: widget.maxHeight,
    );
  }

  /// 构建分块渲染器
  Widget _buildBlockRenderer() {
    final blocks = widget.controller.visibleBlocks;
    final config = widget.controller.config;

    Widget content = RepaintBoundary(
      key: widget.renderKey,
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            for (int i = 0; i < blocks.length; i++) ...[
              _buildBlock(blocks[i], i),
              if (i < blocks.length - 1)
                SizedBox(height: config.blockSpacing),
            ],
          ],
        ),
      ),
    );

    // 如果设置了最大高度，添加高度限制
    if (widget.maxHeight != null) {
      content = ConstrainedBox(
        constraints: BoxConstraints(maxHeight: widget.maxHeight!),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: content,
        ),
      );
    }

    return content;
  }

  /// 构建单个分块
  Widget _buildBlock(MarkdownBlock block, int index) {
    final config = widget.controller.config;

    // 为每个分块创建独立的GlobalKey，用于单独导出
    final blockRenderKey = GlobalKey();

    return RepaintBoundary(
      key: blockRenderKey,
      child: Container(
        width: double.infinity,
        decoration:
            config.showBlockBorders
                ? BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300, width: 1),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.03),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                )
                : null,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 分块标题（可选）
            if (config.showBlockTitles)
              _buildBlockTitle(block, index, blockRenderKey),

            // 分块内容
            Padding(
              padding:
                  config.showBlockBorders
                      ? const EdgeInsets.all(16)
                      : const EdgeInsets.symmetric(vertical: 8),
              child: MarkdownRendererWidget(
                markdownText: block.content,
                template: widget.template,
                selectable: widget.selectable,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建分块标题
  Widget _buildBlockTitle(
    MarkdownBlock block,
    int index,
    GlobalKey blockRenderKey,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 分块类型图标
          Icon(
            _getBlockTypeIcon(block.separatorType),
            size: 16,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),

          // 分块标题
          Expanded(
            child: Text(
              block.title.isNotEmpty ? block.title : '分块 ${index + 1}',
              style: TextStyle(
                color: Colors.grey.shade700,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // 分块信息
          Text(
            '${block.content.length} 字符',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade500,
            ),
          ),

          const SizedBox(width: 8),

          // 导出按钮
          IconButton(
            onPressed: () => _exportSingleBlock(block, blockRenderKey),
            icon: const Icon(Icons.download, size: 16),
            tooltip: '导出此分块为图片',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            iconSize: 16,
            color: Colors.grey.shade600,
          ),
        ],
      ),
    );
  }

  /// 导出单个分块为图片
  Future<void> _exportSingleBlock(
    MarkdownBlock block,
    GlobalKey blockRenderKey,
  ) async {
    try {
      // 显示加载提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('正在导出分块图片...'),
            duration: Duration(seconds: 1),
          ),
        );
      }

      // 使用导出服务导出分块图片
      final imagePath =
          await MarkdownBlockExportService.exportBlockAsImageFromWidget(
            blockRenderKey,
            blockId: block.id,
          );

      if (mounted) {
        if (imagePath != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('分块图片已保存: ${imagePath.split('/').last}'),
              duration: const Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('导出失败，请稍后重试'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('导出分块图片失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('导出失败: $e'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// 获取分块类型图标
  IconData _getBlockTypeIcon(BlockSeparatorType type) {
    switch (type) {
      case BlockSeparatorType.h1:
        return Icons.title;
      case BlockSeparatorType.h2:
        return Icons.subtitles;
      case BlockSeparatorType.custom:
        return Icons.horizontal_rule;
      case BlockSeparatorType.manual:
        return Icons.content_cut;
    }
  }
}
