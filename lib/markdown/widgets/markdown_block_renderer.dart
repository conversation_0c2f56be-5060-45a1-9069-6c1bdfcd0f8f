import 'package:flutter/material.dart';

import '../controllers/markdown_block_controller.dart';
import '../models/markdown_block.dart';
import '../models/markdown_template.dart';
// 移除了操纵杆组件的导入
import 'markdown_renderer_widget.dart';

/// Markdown 分块渲染组件
class MarkdownBlockRenderer extends StatefulWidget {
  /// 分块控制器
  final MarkdownBlockController controller;
  
  /// 渲染模板
  final MarkdownTemplate template;
  
  /// 是否可选择文本
  final bool selectable;
  
  /// 渲染组件的全局键，用于截图
  final GlobalKey? renderKey;
  
  /// 最大高度
  final double? maxHeight;

  const MarkdownBlockRenderer({
    super.key,
    required this.controller,
    required this.template,
    this.selectable = false,
    this.renderKey,
    this.maxHeight,
  });

  @override
  State<MarkdownBlockRenderer> createState() => _MarkdownBlockRendererState();
}

class _MarkdownBlockRendererState extends State<MarkdownBlockRenderer> {
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _containerKey = GlobalKey();
  // 移除了未使用的 _containerWidth 字段

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateContainerSize();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// 更新容器尺寸
  void _updateContainerSize() {
    final renderBox = _containerKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final size = renderBox.size;
      // 移除了 _containerWidth 的设置
      widget.controller.setContainerHeight(size.height);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        if (widget.controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (!widget.controller.config.enabled) {
          // 非分块模式，使用原始渲染器
          return _buildNormalRenderer();
        }

        return _buildBlockRenderer();
      },
    );
  }

  /// 构建普通渲染器（非分块模式）
  Widget _buildNormalRenderer() {
    return MarkdownRendererWidget(
      renderKey: widget.renderKey,
      markdownText: widget.controller.markdownText,
      template: widget.template,
      selectable: widget.selectable,
      maxHeight: widget.maxHeight,
    );
  }

  /// 构建分块渲染器
  Widget _buildBlockRenderer() {
    final blocks = widget.controller.visibleBlocks;
    final config = widget.controller.config;

    Widget content = RepaintBoundary(
      key: widget.renderKey,
      child: SizedBox(
        key: _containerKey,
        width: double.infinity,
        child: SingleChildScrollView(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              for (int i = 0; i < blocks.length; i++) ...[
                _buildBlock(blocks[i], i),
                if (i < blocks.length - 1)
                  SizedBox(height: config.blockSpacing),
              ],
            ],
          ),
        ),
      ),
    );

    // 如果设置了最大高度，添加高度限制
    if (widget.maxHeight != null) {
      content = ConstrainedBox(
        constraints: BoxConstraints(maxHeight: widget.maxHeight!),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: content,
        ),
      );
    }

    return content;
  }

  /// 构建单个分块
  Widget _buildBlock(MarkdownBlock block, int index) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: MarkdownRendererWidget(
        markdownText: block.content,
        template: widget.template,
        selectable: widget.selectable,
      ),
    );
  }


}
