import 'package:flutter/material.dart';

import '../controllers/markdown_render_controller.dart';
import '../controllers/markdown_block_controller.dart';
import '../models/markdown_render_style.dart';
import '../models/markdown_template.dart';
import '../models/markdown_block.dart';
import '../style_selector_widget.dart';
import '../template_selector_widget.dart';
import '../watermark_settings_widget.dart';
import 'markdown_renderer_widget.dart';
import 'markdown_block_renderer.dart';
import 'markdown_block_config_panel.dart';
import 'markdown_block_manager_panel.dart';

/// Markdown渲染标签页组件
class MarkdownRenderTabs extends StatefulWidget {
  final MarkdownRenderController controller;
  final GlobalKey renderKey;
  final GlobalKey contentAreaKey;
  final ScrollController scrollController;
  final TabController tabController;

  const MarkdownRenderTabs({
    super.key,
    required this.controller,
    required this.renderKey,
    required this.contentAreaKey,
    required this.scrollController,
    required this.tabController,
  });

  @override
  State<MarkdownRenderTabs> createState() => _MarkdownRenderTabsState();
}

class _MarkdownRenderTabsState extends State<MarkdownRenderTabs> {
  /// 分块渲染控制器
  late final MarkdownBlockController _blockController;

  /// 是否启用分块模式
  bool _isBlockModeEnabled = false;

  @override
  void initState() {
    super.initState();
    _blockController = MarkdownBlockController();

    // 监听原始控制器的文本变化
    widget.controller.addListener(_onMarkdownTextChanged);

    // 初始化分块控制器
    _initializeBlockController();
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onMarkdownTextChanged);
    _blockController.dispose();
    super.dispose();
  }

  /// 初始化分块控制器
  void _initializeBlockController() {
    _blockController.initialize(
      markdownText: widget.controller.markdownController.text,
      config: const BlockRenderConfig(enabled: false),
    );
  }

  /// 处理 Markdown 文本变化
  void _onMarkdownTextChanged() {
    _blockController.updateMarkdownText(widget.controller.markdownController.text);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        return TabBarView(
          controller: widget.tabController,
          children: [
            _buildEditTab(),
            _buildTemplateTab(),
            _buildStyleTab(),
            _buildWatermarkTab(),
            _buildBlockTab(), // 新增分块标签页
          ],
        );
      },
    );
  }

  /// 构建编辑标签页
  Widget _buildEditTab() {
    return Column(children: [Expanded(child: _buildContentArea())]);
  }

  /// 构建内容区域 - 支持分块和普通模式
  Widget _buildContentArea() {
    return SingleChildScrollView(
      controller: widget.scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 模式切换
          Row(
            children: [
              const Text(
                '渲染模式：',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
              const SizedBox(width: 8),
              SegmentedButton<bool>(
                segments: const [
                  ButtonSegment(
                    value: false,
                    label: Text('普通模式'),
                    icon: Icon(Icons.article, size: 16),
                  ),
                  ButtonSegment(
                    value: true,
                    label: Text('分块模式'),
                    icon: Icon(Icons.view_module, size: 16),
                  ),
                ],
                selected: {_isBlockModeEnabled},
                onSelectionChanged: (Set<bool> selection) {
                  setState(() {
                    _isBlockModeEnabled = selection.first;
                  });
                  _toggleBlockMode(_isBlockModeEnabled);
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Markdown输入
          TextField(
            controller: widget.controller.markdownController,
            decoration: const InputDecoration(
              labelText: 'Markdown内容',
              border: OutlineInputBorder(),
              alignLabelWithHint: true,
            ),
            maxLines: 10,
            onChanged: (_) => widget.controller.triggerUpdate(),
          ),
          const SizedBox(height: 24),

          // 预览
          const Text(
            '预览',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Center(
            child: Container(
              key: widget.contentAreaKey,
              child: _isBlockModeEnabled
                  ? MarkdownBlockRenderer(
                      renderKey: widget.renderKey,
                      controller: _blockController,
                      template: widget.controller.selectedTemplate,
                      selectable: true,
                    )
                  : MarkdownRendererWidget(
                      renderKey: widget.renderKey,
                      markdownText: widget.controller.markdownController.text,
                      template: widget.controller.selectedTemplate,
                      selectable: true,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建模板标签页
  Widget _buildTemplateTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TemplateSelectorWidget(
            selectedTemplate: widget.controller.selectedTemplate,
            templates: MarkdownTemplate.getPredefinedTemplates(),
            onTemplateSelected: widget.controller.updateTemplate,
          ),
          const SizedBox(height: 24),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              '预览',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 16),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _isBlockModeEnabled
                  ? MarkdownBlockRenderer(
                      controller: _blockController,
                      template: widget.controller.selectedTemplate,
                      selectable: false,
                    )
                  : MarkdownRendererWidget(
                      markdownText: widget.controller.markdownController.text,
                      template: widget.controller.selectedTemplate,
                      selectable: false,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建样式标签页
  Widget _buildStyleTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          StyleSelectorWidget(
            selectedStyle: widget.controller.selectedTemplate.style,
            styles: MarkdownRenderStyle.getPredefinedStyles(),
            onStyleSelected: widget.controller.updateStyle,
            onStyleUpdated: widget.controller.updateStyle,
          ),
          const SizedBox(height: 24),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              '预览',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 16),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _isBlockModeEnabled
                  ? MarkdownBlockRenderer(
                      controller: _blockController,
                      template: widget.controller.selectedTemplate,
                      selectable: false,
                    )
                  : MarkdownRendererWidget(
                      markdownText: widget.controller.markdownController.text,
                      template: widget.controller.selectedTemplate,
                      selectable: false,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建水印标签页
  Widget _buildWatermarkTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          WatermarkSettingsWidget(
            watermark: widget.controller.selectedTemplate.watermark,
            onWatermarkUpdated: widget.controller.updateWatermark,
          ),
          const SizedBox(height: 24),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              '预览',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 16),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _isBlockModeEnabled
                  ? MarkdownBlockRenderer(
                      controller: _blockController,
                      template: widget.controller.selectedTemplate,
                      selectable: false,
                    )
                  : MarkdownRendererWidget(
                      markdownText: widget.controller.markdownController.text,
                      template: widget.controller.selectedTemplate,
                      selectable: false,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分块标签页
  Widget _buildBlockTab() {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          // 子标签栏
          Container(
            color: Colors.grey.shade50,
            child: const TabBar(
              labelColor: Colors.black87,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.blue,
              tabs: [
                Tab(text: '配置', icon: Icon(Icons.settings, size: 16)),
                Tab(text: '管理', icon: Icon(Icons.list, size: 16)),
                Tab(text: '预览', icon: Icon(Icons.preview, size: 16)),
              ],
            ),
          ),

          // 子标签页内容
          Expanded(
            child: TabBarView(
              children: [
                // 配置标签页
                SingleChildScrollView(
                  child: Column(
                    children: [
                      MarkdownBlockConfigPanel(
                        config: _blockController.config,
                        onConfigChanged: (config) {
                          _blockController.updateConfig(config);
                          if (config.enabled != _isBlockModeEnabled) {
                            setState(() {
                              _isBlockModeEnabled = config.enabled;
                            });
                          }
                        },
                      ),
                      if (_isBlockModeEnabled) ...[
                        const SizedBox(height: 16),
                        _buildBlockInfo(),
                      ],
                    ],
                  ),
                ),

                // 管理标签页
                _isBlockModeEnabled
                    ? MarkdownBlockManagerPanel(
                        controller: _blockController,
                        template: widget.controller.selectedTemplate,
                      )
                    : const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: 48,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              '请先启用分块模式',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),

                // 预览标签页
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: MarkdownBlockRenderer(
                    controller: _blockController,
                    template: widget.controller.selectedTemplate,
                    selectable: false,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分块信息
  Widget _buildBlockInfo() {
    return AnimatedBuilder(
      animation: _blockController,
      builder: (context, child) {
        final blocks = _blockController.blocks;

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.info_outline, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      '分块信息',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    // 移除了清除分隔杆的按钮
                  ],
                ),
                const SizedBox(height: 12),

                // 统计信息
                Row(
                  children: [
                    _buildInfoChip('总分块', '${blocks.length}', Colors.blue),
                    const SizedBox(width: 8),
                    _buildInfoChip('可见分块', '${_blockController.visibleBlocks.length}', Colors.green),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建信息芯片
  Widget _buildInfoChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 切换分块模式
  void _toggleBlockMode(bool enabled) {
    final newConfig = _blockController.config.copyWith(enabled: enabled);
    _blockController.updateConfig(newConfig);
  }
}
