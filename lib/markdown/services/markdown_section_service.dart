import 'package:uuid/uuid.dart';

import '../models/markdown_section.dart';

/// Markdown段落拆分服务
class MarkdownSectionService {
  /// UUID生成器
  final Uuid _uuid = const Uuid();

  /// 根据分隔符拆分Markdown文本
  List<MarkdownSection> splitByRule(
    String markdownText, {
    bool splitByHorizontalRule = true,
    bool splitByH1 = true,
    int? maxSectionLength,
  }) {
    if (markdownText.isEmpty) {
      return [];
    }

    // 拆分规则，根据水平分割线和/或一级标题
    final RegExp splitPattern = RegExp(
      [
        if (splitByHorizontalRule) r'^\s*[-*_]{3,}\s*$', // 水平分割线
        if (splitByH1) r'^\s*#\s+.+$', // 一级标题
      ].join('|'),
      multiLine: true,
    );

    // 找到所有分隔符的位置
    final matches = splitPattern.allMatches(markdownText);

    if (matches.isEmpty) {
      // 无分隔符，整个文档作为一个段落
      return [
        _createSection(
          markdownText,
          0,
          markdownText.length,
          0,
          extractTitle(markdownText),
        ),
      ];
    }

    List<MarkdownSection> sections = [];
    int lastPosition = 0;
    int sectionIndex = 0;

    // 处理每个分隔符并创建段落
    for (var match in matches) {
      final matchStart = match.start;

      // 检查是否为文档开头，避免创建空段落
      if (matchStart > lastPosition) {
        // 创建前一个分隔符到当前分隔符的段落
        final sectionContent = markdownText.substring(lastPosition, matchStart);
        sections.add(
          _createSection(
            sectionContent,
            lastPosition,
            matchStart,
            sectionIndex++,
            extractTitle(sectionContent),
          ),
        );
      }

      // 设置下一个段落的起始位置
      lastPosition = matchStart;
    }

    // 添加最后一个段落
    if (lastPosition < markdownText.length) {
      final sectionContent = markdownText.substring(lastPosition);
      sections.add(
        _createSection(
          sectionContent,
          lastPosition,
          markdownText.length,
          sectionIndex,
          extractTitle(sectionContent),
        ),
      );
    }

    // 处理段落长度限制
    if (maxSectionLength != null && maxSectionLength > 0) {
      sections = _limitSectionLength(sections, maxSectionLength);
    }

    return sections;
  }

  /// 根据段落最大长度限制拆分段落
  List<MarkdownSection> _limitSectionLength(
    List<MarkdownSection> sections,
    int maxLength,
  ) {
    List<MarkdownSection> result = [];

    for (var section in sections) {
      if (section.content.length <= maxLength) {
        result.add(section);
        continue;
      }

      // 需要进一步拆分的段落
      final lines = section.content.split('\n');
      String currentContent = '';
      int currentStartPos = section.startPosition;
      int subSectionIndex = 0;

      for (var line in lines) {
        // 检查添加当前行后是否超出长度限制
        if (currentContent.isEmpty ||
            currentContent.length + line.length + 1 <= maxLength) {
          currentContent += (currentContent.isEmpty ? '' : '\n') + line;
        } else {
          // 达到长度限制，创建一个子段落
          result.add(
            _createSection(
              currentContent,
              currentStartPos,
              currentStartPos + currentContent.length,
              section.index * 100 + subSectionIndex++,
              extractTitle(currentContent),
            ),
          );

          // 重置当前内容
          currentContent = line;
          currentStartPos =
              section.startPosition +
              section.content.indexOf(
                line,
                currentStartPos - section.startPosition,
              );
        }
      }

      // 添加最后一个子段落
      if (currentContent.isNotEmpty) {
        result.add(
          _createSection(
            currentContent,
            currentStartPos,
            currentStartPos + currentContent.length,
            section.index * 100 + subSectionIndex,
            extractTitle(currentContent),
          ),
        );
      }
    }

    return result;
  }

  /// 创建一个Markdown段落
  MarkdownSection _createSection(
    String content,
    int startPosition,
    int endPosition,
    int index,
    String title,
  ) {
    return MarkdownSection(
      id: _uuid.v4(),
      title: title,
      content: content,
      rawContent: content,
      startPosition: startPosition,
      endPosition: endPosition,
      index: index,
      createdAt: DateTime.now(),
    );
  }

  /// 从Markdown内容中提取标题
  String extractTitle(String markdownText) {
    // 尝试查找第一个标题（任何级别的标题）
    final titleMatch = RegExp(
      r'^\s*#{1,6}\s+(.+)$',
      multiLine: true,
    ).firstMatch(markdownText);

    if (titleMatch != null && titleMatch.group(1) != null) {
      return titleMatch.group(1)!.trim();
    }

    // 如果没有标题，尝试使用第一行非空文本
    final firstLineMatch = RegExp(
      r'^\s*(.+)$',
      multiLine: true,
    ).firstMatch(markdownText);

    if (firstLineMatch != null && firstLineMatch.group(1) != null) {
      String firstLine = firstLineMatch.group(1)!.trim();
      // 如果第一行太长，截断它
      if (firstLine.length > 50) {
        firstLine = '${firstLine.substring(0, 47)}...';
      }
      return firstLine;
    }

    // 默认标题
    return '未命名段落';
  }

  /// 创建一个Markdown文档段落集合
  MarkdownSectionDocument createDocument({
    required String title,
    String? subtitle,
    required String rawContent,
    required List<MarkdownSection> sections,
  }) {
    return MarkdownSectionDocument(
      title: title,
      subtitle: subtitle,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      sections: sections,
      rawContent: rawContent,
    );
  }

  /// 从原始Markdown内容创建文档
  MarkdownSectionDocument createDocumentFromContent({
    required String title,
    String? subtitle,
    required String markdownContent,
    bool splitByHorizontalRule = true,
    bool splitByH1 = true,
    int? maxSectionLength,
  }) {
    final sections = splitByRule(
      markdownContent,
      splitByHorizontalRule: splitByHorizontalRule,
      splitByH1: splitByH1,
      maxSectionLength: maxSectionLength,
    );

    return createDocument(
      title: title,
      subtitle: subtitle,
      rawContent: markdownContent,
      sections: sections,
    );
  }

  /// 将段落合并回Markdown文本
  String combineToMarkdown(List<MarkdownSection> sections) {
    return sections.map((section) => section.rawContent).join('\n\n');
  }
}
