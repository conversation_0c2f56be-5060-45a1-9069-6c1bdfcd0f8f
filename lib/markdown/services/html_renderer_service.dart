import 'dart:io';

import 'package:flutter/material.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:markdown/markdown.dart' as md;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../models/html_template.dart';
import '../models/markdown_section.dart';
import '../models/markdown_watermark.dart';

/// HTML渲染服务
class HtmlRendererService {
  /// UUID生成器
  final Uuid _uuid = const Uuid();

  /// 渲染Markdown为HTML
  String renderMarkdownToHtml(String markdown) {
    return md.markdownToHtml(markdown, extensionSet: md.ExtensionSet.gitHubWeb);
  }

  /// 使用HTML模板渲染Markdown段落
  String renderSectionWithTemplate(
    MarkdownSection section,
    HtmlTemplate template, {
    String? title,
    String? subtitle,
    String? footer,
    MarkdownWatermark? watermark,
    String? imageUrl,
    String? date,
  }) {
    // 首先将Markdown转换为HTML
    final htmlContent = renderMarkdownToHtml(section.content);

    // 准备替换变量
    Map<String, String> variables = {
      '{{content}}': htmlContent,
      '{{title}}': title ?? section.title,
      '{{subtitle}}': subtitle ?? '',
      '{{footer}}': footer ?? '由ContentPal生成',
      '{{watermark}}': watermark?.text ?? '',
      '{{date}}': date ?? DateTime.now().toString().substring(0, 10),
      '{{image_url}}': imageUrl ?? '',
    };

    // 替换模板中的变量
    String renderedHtml = template.htmlContent;
    variables.forEach((key, value) {
      renderedHtml = renderedHtml.replaceAll(key, value);
    });

    return renderedHtml;
  }

  /// 渲染整个文档为HTML片段列表
  List<String> renderDocumentToHtmlList(
    MarkdownSectionDocument document,
    HtmlTemplate template, {
    String? footer,
    MarkdownWatermark? watermark,
  }) {
    List<String> htmlList = [];

    for (var section in document.sections) {
      final sectionHtml = renderSectionWithTemplate(
        section,
        template,
        title: section.title,
        subtitle: document.subtitle,
        footer: footer,
        watermark: watermark,
        date: document.createdAt.toString().substring(0, 10),
        imageUrl: section.imageUrl,
      );

      htmlList.add(sectionHtml);
    }

    return htmlList;
  }

  /// 创建HTML片段预览缓存文件
  Future<String> createHtmlPreviewFile(String htmlContent) async {
    final tempDir = await getTemporaryDirectory();
    final filePath = '${tempDir.path}/preview_${_uuid.v4()}.html';

    final file = File(filePath);
    await file.writeAsString(htmlContent);

    return filePath;
  }

  /// 清理HTML预览缓存文件
  Future<void> cleanupHtmlPreviewFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final files = tempDir.listSync();

      for (var file in files) {
        if (file is File &&
            file.path.contains('preview_') &&
            file.path.endsWith('.html')) {
          await file.delete();
        }
      }
    } catch (e) {
      debugPrint('清理HTML预览文件失败: $e');
    }
  }

  /// 提取HTML中的纯文本内容
  String extractTextFromHtml(String htmlContent) {
    final document = html_parser.parse(htmlContent);
    return document.body?.text ?? '';
  }

  /// 创建WebView控制器
  WebViewController createWebViewController(String htmlContent) {
    return WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..loadHtmlString(htmlContent);
  }
}
