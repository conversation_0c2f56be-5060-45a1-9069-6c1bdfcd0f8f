import 'package:uuid/uuid.dart';

import '../models/markdown_block.dart';

/// Markdown 分块处理服务
class MarkdownBlockService {
  /// UUID 生成器
  final Uuid _uuid = const Uuid();

  /// 根据配置将 Markdown 文本分块
  List<MarkdownBlock> splitMarkdownIntoBlocks(
    String markdownText,
    BlockRenderConfig config,
  ) {
    if (markdownText.isEmpty || !config.enabled) {
      return [_createSingleBlock(markdownText)];
    }

    // 构建分隔符正则表达式
    final patterns = <String>[];
    
    // 添加自定义分隔符
    if (config.customSeparatorPattern.isNotEmpty) {
      patterns.add(config.customSeparatorPattern);
    }
    
    // 添加标题分隔符
    if (config.splitByH1) {
      patterns.add(r'^\s*#\s+.+$');
    }
    if (config.splitByH2) {
      patterns.add(r'^\s*##\s+.+$');
    }

    if (patterns.isEmpty) {
      return [_createSingleBlock(markdownText)];
    }

    final splitPattern = RegExp(
      patterns.join('|'),
      multiLine: true,
    );

    return _splitByPattern(markdownText, splitPattern);
  }

  /// 根据正则表达式分割文本
  List<MarkdownBlock> _splitByPattern(String text, RegExp pattern) {
    final matches = pattern.allMatches(text).toList();

    if (matches.isEmpty) {
      return [_createSingleBlock(text)];
    }

    final blocks = <MarkdownBlock>[];
    int lastPosition = 0;
    int blockIndex = 0;

    for (int i = 0; i < matches.length; i++) {
      final match = matches[i];
      final matchStart = match.start;
      // final matchEnd = match.end; // 暂时不需要使用

      // 创建分隔符之前的内容块（如果有的话）
      if (matchStart > lastPosition) {
        final content = text.substring(lastPosition, matchStart);
        if (content.trim().isNotEmpty) {
          blocks.add(_createBlock(
            content,
            lastPosition,
            matchStart,
            blockIndex++,
            BlockSeparatorType.manual,
          ));
        }
      }

      // 找到下一个分隔符的位置（或文本结尾）
      final nextMatchStart = i + 1 < matches.length ? matches[i + 1].start : text.length;

      // 创建从当前分隔符到下一个分隔符的内容块
      final blockContent = text.substring(matchStart, nextMatchStart);
      if (blockContent.trim().isNotEmpty) {
        blocks.add(_createBlock(
          blockContent,
          matchStart,
          nextMatchStart,
          blockIndex++,
          _determineSeparatorType(match.group(0) ?? ''),
        ));
      }

      lastPosition = nextMatchStart;
    }

    return blocks;
  }

  /// 创建单个块（当没有分隔符时）
  MarkdownBlock _createSingleBlock(String content) {
    return _createBlock(content, 0, content.length, 0, BlockSeparatorType.manual);
  }

  /// 创建分块
  MarkdownBlock _createBlock(
    String content,
    int startPosition,
    int endPosition,
    int index,
    BlockSeparatorType separatorType,
  ) {
    return MarkdownBlock(
      id: _uuid.v4(),
      content: content.trim(),
      startPosition: startPosition,
      endPosition: endPosition,
      index: index,
      title: _extractTitle(content),
      separatorType: separatorType,
      createdAt: DateTime.now(),
    );
  }

  /// 从内容中提取标题
  String _extractTitle(String content) {
    // 尝试查找第一个标题
    final titleMatch = RegExp(
      r'^\s*#{1,6}\s+(.+)$',
      multiLine: true,
    ).firstMatch(content);

    if (titleMatch != null && titleMatch.group(1) != null) {
      return titleMatch.group(1)!.trim();
    }

    // 如果没有标题，使用第一行非空文本
    final firstLineMatch = RegExp(
      r'^\s*(.+)$',
      multiLine: true,
    ).firstMatch(content);

    if (firstLineMatch != null && firstLineMatch.group(1) != null) {
      String firstLine = firstLineMatch.group(1)!.trim();
      // 限制标题长度
      if (firstLine.length > 30) {
        firstLine = '${firstLine.substring(0, 27)}...';
      }
      return firstLine;
    }

    return '未命名块';
  }

  /// 确定分隔符类型
  BlockSeparatorType _determineSeparatorType(String separator) {
    final trimmed = separator.trim();
    if (trimmed.startsWith('# ') && !trimmed.startsWith('## ')) {
      return BlockSeparatorType.h1;
    } else if (trimmed.startsWith('## ')) {
      return BlockSeparatorType.h2;
    } else if (RegExp(r'^\s*[-*_]{3,}\s*$').hasMatch(separator)) {
      return BlockSeparatorType.custom;
    }
    return BlockSeparatorType.manual;
  }

  // 移除了手动分隔杆的分块方法

  /// 将分块合并回 Markdown 文本
  String combineBlocksToMarkdown(List<MarkdownBlock> blocks) {
    return blocks
        .where((block) => block.isVisible)
        .map((block) => block.content)
        .join('\n\n');
  }

  // 移除了操纵杆位置计算相关的方法
}
