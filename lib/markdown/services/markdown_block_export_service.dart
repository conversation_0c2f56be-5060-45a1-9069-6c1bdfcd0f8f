import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:share_plus/share_plus.dart'; // TODO: 重新启用分享功能时取消注释

import '../models/markdown_block.dart';
import '../models/markdown_template.dart';
import '../widgets/markdown_renderer_widget.dart';

/// Markdown 分块导出服务
class MarkdownBlockExportService {
  /// 导出单个分块为图片（需要在UI上下文中调用）
  static Future<String?> exportBlockAsImage(
    MarkdownBlock block,
    MarkdownTemplate template, {
    double? width,
    double? pixelRatio,
  }) async {
    try {
      debugPrint('分块导出需要在UI上下文中调用，请使用 exportBlockAsImageFromWidget 方法');
      return null;
    } catch (e) {
      debugPrint('导出分块图片失败: $e');
      return null;
    }
  }

  /// 从已渲染的Widget导出分块图片（与普通模式使用相同的逻辑）
  static Future<String?> exportBlockAsImageFromWidget(
    GlobalKey renderKey, {
    String? blockId,
  }) async {
    try {
      // 使用与普通模式相同的图片捕获逻辑
      final imageBytes = await _captureWidgetFromKey(renderKey);
      if (imageBytes == null) return null;

      // 保存到应用文档目录，与普通模式保持一致
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'block_${blockId ?? 'unknown'}_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(imageBytes);

      debugPrint('分块图片已保存到: ${file.path}');
      return file.path;
    } catch (e) {
      debugPrint('导出分块图片失败: $e');
      return null;
    }
  }

  /// 导出多个分块为单个图片
  static Future<String?> exportBlocksAsImage(
    List<MarkdownBlock> blocks,
    MarkdownTemplate template, {
    double? width,
    double? pixelRatio,
    double blockSpacing = 16.0,
  }) async {
    try {
      final renderKey = GlobalKey();
      
      final widget = RepaintBoundary(
        key: renderKey,
        child: Container(
          width: width ?? 800,
          color: Colors.white,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              for (int i = 0; i < blocks.length; i++) ...[
                MarkdownRendererWidget(
                  markdownText: blocks[i].content,
                  template: template,
                  selectable: false,
                ),
                if (i < blocks.length - 1)
                  SizedBox(height: blockSpacing),
              ],
            ],
          ),
        ),
      );

      final image = await _captureWidget(widget, renderKey, pixelRatio: pixelRatio);
      if (image == null) return null;

      final tempDir = await getTemporaryDirectory();
      final fileName = 'blocks_${DateTime.now().millisecondsSinceEpoch}.png';
      final file = File('${tempDir.path}/$fileName');
      await file.writeAsBytes(image);

      return file.path;
    } catch (e) {
      debugPrint('导出多个分块图片失败: $e');
      return null;
    }
  }

  /// 导出分块为 Markdown 文件
  static Future<String?> exportBlocksAsMarkdown(
    List<MarkdownBlock> blocks, {
    String? fileName,
    bool includeMetadata = true,
  }) async {
    try {
      final buffer = StringBuffer();
      
      // 添加元数据
      if (includeMetadata) {
        buffer.writeln('<!-- 导出时间: ${DateTime.now().toIso8601String()} -->');
        buffer.writeln('<!-- 分块数量: ${blocks.length} -->');
        buffer.writeln();
      }

      // 添加分块内容
      for (int i = 0; i < blocks.length; i++) {
        final block = blocks[i];
        
        if (includeMetadata) {
          buffer.writeln('<!-- 分块 ${i + 1}: ${block.title} -->');
          buffer.writeln('<!-- 类型: ${_formatBlockType(block.separatorType)} -->');
        }
        
        buffer.writeln(block.content);
        
        if (i < blocks.length - 1) {
          buffer.writeln();
          buffer.writeln();
        }
      }

      // 保存到文件
      final tempDir = await getTemporaryDirectory();
      final finalFileName = fileName ?? 'exported_blocks_${DateTime.now().millisecondsSinceEpoch}.md';
      final file = File('${tempDir.path}/$finalFileName');
      await file.writeAsString(buffer.toString());

      return file.path;
    } catch (e) {
      debugPrint('导出 Markdown 文件失败: $e');
      return null;
    }
  }

  /// 分享分块内容
  static Future<void> shareBlocks(
    List<MarkdownBlock> blocks, {
    String? subject,
    ShareFormat format = ShareFormat.markdown,
  }) async {
    try {
      switch (format) {
        case ShareFormat.markdown:
          final filePath = await exportBlocksAsMarkdown(blocks);
          if (filePath != null) {
            // TODO: 使用新的 Share API
            debugPrint('导出文件路径: $filePath');
            // await Share.shareXFiles([XFile(filePath)]);
          }
          break;

        case ShareFormat.text:
          final content = blocks.map((b) => b.content).join('\n\n');
          // TODO: 使用新的 Share API
          debugPrint('分享内容: $content');
          // await Share.share(content);
          break;

        case ShareFormat.image:
          // 这里需要传入模板，暂时使用默认模板
          final template = MarkdownTemplate.getPredefinedTemplates().first;
          final imagePath = await exportBlocksAsImage(blocks, template);
          if (imagePath != null) {
            // TODO: 使用新的 Share API
            debugPrint('导出图片路径: $imagePath');
            // await Share.shareXFiles([XFile(imagePath)]);
          }
          break;
      }
    } catch (e) {
      debugPrint('分享失败: $e');
    }
  }

  /// 批量导出分块
  static Future<List<String>> batchExportBlocks(
    List<MarkdownBlock> blocks,
    MarkdownTemplate template, {
    ExportFormat format = ExportFormat.image,
    String? outputDirectory,
  }) async {
    final exportedFiles = <String>[];
    
    try {
      // final outputDir = outputDirectory ?? (await getTemporaryDirectory()).path;

      for (int i = 0; i < blocks.length; i++) {
        final block = blocks[i];
        String? filePath;
        
        switch (format) {
          case ExportFormat.image:
            filePath = await exportBlockAsImage(block, template);
            break;
            
          case ExportFormat.markdown:
            filePath = await exportBlocksAsMarkdown(
              [block],
              fileName: 'block_${i + 1}_${block.id}.md',
              includeMetadata: false,
            );
            break;
        }
        
        if (filePath != null) {
          exportedFiles.add(filePath);
        }
      }
    } catch (e) {
      debugPrint('批量导出失败: $e');
    }
    
    return exportedFiles;
  }

  /// 创建分块摘要报告
  static Future<String?> createBlockSummaryReport(
    List<MarkdownBlock> blocks, {
    bool includeStatistics = true,
    bool includeContent = false,
  }) async {
    try {
      final buffer = StringBuffer();
      
      // 标题
      buffer.writeln('# Markdown 分块摘要报告');
      buffer.writeln();
      buffer.writeln('生成时间: ${DateTime.now().toString()}');
      buffer.writeln();
      
      // 统计信息
      if (includeStatistics) {
        buffer.writeln('## 统计信息');
        buffer.writeln();
        buffer.writeln('- 总分块数: ${blocks.length}');
        buffer.writeln('- 可见分块数: ${blocks.where((b) => b.isVisible).length}');
        buffer.writeln('- 隐藏分块数: ${blocks.where((b) => !b.isVisible).length}');
        
        final totalChars = blocks.fold<int>(0, (sum, b) => sum + b.content.length);
        buffer.writeln('- 总字符数: $totalChars');
        
        if (blocks.isNotEmpty) {
          buffer.writeln('- 平均分块长度: ${(totalChars / blocks.length).round()} 字符');
        }
        
        // 分块类型统计
        final typeCounts = <BlockSeparatorType, int>{};
        for (final block in blocks) {
          typeCounts[block.separatorType] = (typeCounts[block.separatorType] ?? 0) + 1;
        }
        
        buffer.writeln();
        buffer.writeln('### 分块类型分布');
        for (final entry in typeCounts.entries) {
          buffer.writeln('- ${_formatBlockType(entry.key)}: ${entry.value}');
        }
        
        buffer.writeln();
      }
      
      // 分块列表
      buffer.writeln('## 分块列表');
      buffer.writeln();
      
      for (int i = 0; i < blocks.length; i++) {
        final block = blocks[i];
        buffer.writeln('### ${i + 1}. ${block.title}');
        buffer.writeln();
        buffer.writeln('- **类型**: ${_formatBlockType(block.separatorType)}');
        buffer.writeln('- **状态**: ${block.isVisible ? "可见" : "隐藏"}');
        buffer.writeln('- **长度**: ${block.content.length} 字符');
        buffer.writeln('- **位置**: ${block.startPosition}-${block.endPosition}');
        buffer.writeln('- **创建时间**: ${block.createdAt.toString()}');
        
        if (includeContent) {
          buffer.writeln();
          buffer.writeln('**内容预览**:');
          buffer.writeln();
          final preview = block.content.length > 200 
              ? '${block.content.substring(0, 200)}...'
              : block.content;
          buffer.writeln('```');
          buffer.writeln(preview);
          buffer.writeln('```');
        }
        
        buffer.writeln();
      }
      
      // 保存报告
      final tempDir = await getTemporaryDirectory();
      final fileName = 'block_summary_${DateTime.now().millisecondsSinceEpoch}.md';
      final file = File('${tempDir.path}/$fileName');
      await file.writeAsString(buffer.toString());
      
      return file.path;
    } catch (e) {
      debugPrint('创建摘要报告失败: $e');
      return null;
    }
  }



  /// 从GlobalKey捕获Widget为图片（与普通模式相同的逻辑）
  static Future<Uint8List?> _captureWidgetFromKey(
    GlobalKey renderKey, {
    double? pixelRatio,
  }) async {
    try {
      // 等待足够的时间让组件完成渲染
      await Future.delayed(const Duration(milliseconds: 500));

      // 等待下一帧完成渲染
      await SchedulerBinding.instance.endOfFrame;

      // 使用 RenderRepaintBoundary 捕获图像
      final renderObject =
          renderKey.currentContext?.findRenderObject()
              as RenderRepaintBoundary?;

      if (renderObject == null) {
        debugPrint('无法获取渲染对象');
        return null;
      }

      // 捕获图像 - 保持高质量，与普通模式相同
      final image = await renderObject.toImage(pixelRatio: pixelRatio ?? 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        debugPrint('无法转换图像数据');
        return null;
      }

      final pngBytes = byteData.buffer.asUint8List();

      // 检查图片大小
      final imageSizeInMB = pngBytes.length / (1024 * 1024);
      debugPrint('生成的分块图片大小: ${imageSizeInMB.toStringAsFixed(2)} MB');

      return pngBytes;
    } catch (e) {
      debugPrint('捕获 Widget 图片失败: $e');
      return null;
    }
  }

  /// 捕获 Widget 为图片（保留原方法用于兼容性）
  static Future<Uint8List?> _captureWidget(
    Widget widget,
    GlobalKey renderKey, {
    double? pixelRatio,
  }) async {
    // 直接调用新的方法
    return _captureWidgetFromKey(renderKey, pixelRatio: pixelRatio);
  }

  /// 格式化分块类型
  static String _formatBlockType(BlockSeparatorType type) {
    switch (type) {
      case BlockSeparatorType.h1:
        return '一级标题';
      case BlockSeparatorType.h2:
        return '二级标题';
      case BlockSeparatorType.custom:
        return '自定义分隔符';
      case BlockSeparatorType.manual:
        return '手动分隔';
    }
  }
}

/// 分享格式
enum ShareFormat {
  /// Markdown 文件
  markdown,
  
  /// 纯文本
  text,
  
  /// 图片
  image,
}

/// 导出格式
enum ExportFormat {
  /// 图片
  image,
  
  /// Markdown 文件
  markdown,
}
