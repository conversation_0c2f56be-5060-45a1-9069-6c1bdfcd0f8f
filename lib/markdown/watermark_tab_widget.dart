import 'package:flutter/material.dart';

import 'models/markdown_template.dart';
import 'models/markdown_watermark.dart';
import 'watermark_settings_widget.dart';
import 'widgets/markdown_renderer_widget.dart';

class WatermarkTabWidget extends StatelessWidget {
  final TextEditingController markdownController;
  final TextEditingController titleController;
  final TextEditingController subtitleController;
  final MarkdownTemplate template;
  final Function(MarkdownWatermark) onWatermarkUpdated;

  const WatermarkTabWidget({
    super.key,
    required this.markdownController,
    required this.titleController,
    required this.subtitleController,
    required this.template,
    required this.onWatermarkUpdated,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          WatermarkSettingsWidget(
            watermark: template.watermark,
            onWatermarkUpdated: onWatermarkUpdated,
          ),
          const SizedBox(height: 24),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              '预览',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 16),
          Center(
            child: MarkdownRendererWidget(
              markdownText: markdownController.text,
              template: template,
              selectable: false,
            ),
          ),
        ],
      ),
    );
  }
}
