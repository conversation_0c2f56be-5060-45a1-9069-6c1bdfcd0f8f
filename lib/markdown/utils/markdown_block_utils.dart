import '../models/markdown_block.dart';

/// Markdown 分块渲染工具类
class MarkdownBlockUtils {
  /// 私有构造函数，防止实例化
  MarkdownBlockUtils._();

  /// 预定义的分隔符模式
  static const Map<String, String> predefinedSeparators = {
    '水平线（短横线）': r'^\s*[-]{3,}\s*$',
    '水平线（星号）': r'^\s*[*]{3,}\s*$',
    '水平线（下划线）': r'^\s*[_]{3,}\s*$',
    '水平线（混合）': r'^\s*[-*_]{3,}\s*$',
    '等号分隔线': r'^\s*[=]{3,}\s*$',
    '井号分隔线': r'^\s*[#]{3,}\s*$',
    '波浪线分隔线': r'^\s*[~]{3,}\s*$',
  };

  /// 预定义的分块配置模板
  static const Map<String, BlockRenderConfig> configTemplates = {
    '默认配置': BlockRenderConfig(),
    '仅标题分隔': BlockRenderConfig(
      mode: BlockMode.headings,
    ),
    '仅自定义分隔符': BlockRenderConfig(
      mode: BlockMode.separators,
    ),
    '紧凑模式': BlockRenderConfig(
      blockSpacing: 8.0,
    ),
    '宽松模式': BlockRenderConfig(
      blockSpacing: 24.0,
    ),
  };

  /// 验证正则表达式模式是否有效
  static bool isValidRegexPattern(String pattern) {
    if (pattern.isEmpty) return true;
    
    try {
      RegExp(pattern);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取分隔符模式的描述
  static String getSeparatorDescription(String pattern) {
    for (final entry in predefinedSeparators.entries) {
      if (entry.value == pattern) {
        return entry.key;
      }
    }
    return '自定义模式';
  }

  /// 估算文本的行数
  static int estimateLineCount(String text) {
    if (text.isEmpty) return 0;
    return text.split('\n').length;
  }

  /// 估算文本的渲染高度
  static double estimateTextHeight(String text, double lineHeight) {
    return estimateLineCount(text) * lineHeight;
  }

  /// 计算分块的统计信息
  static BlockStatistics calculateBlockStatistics(List<MarkdownBlock> blocks) {
    if (blocks.isEmpty) {
      return const BlockStatistics(
        totalBlocks: 0,
        visibleBlocks: 0,
        hiddenBlocks: 0,
        totalCharacters: 0,
        averageBlockLength: 0,
        blockTypeCounts: {},
      );
    }

    final visibleBlocks = blocks.where((b) => b.isVisible).length;
    final hiddenBlocks = blocks.length - visibleBlocks;
    final totalCharacters = blocks.fold<int>(0, (sum, block) => sum + block.content.length);
    final averageBlockLength = totalCharacters / blocks.length;

    final blockTypeCounts = <BlockSeparatorType, int>{};
    for (final block in blocks) {
      blockTypeCounts[block.separatorType] = 
          (blockTypeCounts[block.separatorType] ?? 0) + 1;
    }

    return BlockStatistics(
      totalBlocks: blocks.length,
      visibleBlocks: visibleBlocks,
      hiddenBlocks: hiddenBlocks,
      totalCharacters: totalCharacters,
      averageBlockLength: averageBlockLength.round(),
      blockTypeCounts: blockTypeCounts,
    );
  }

  /// 查找包含指定位置的分块
  static MarkdownBlock? findBlockAtPosition(
    List<MarkdownBlock> blocks,
    int position,
  ) {
    for (final block in blocks) {
      if (position >= block.startPosition && position < block.endPosition) {
        return block;
      }
    }
    return null;
  }

  /// 查找两个分块之间的最佳分隔位置
  static int findOptimalSplitPosition(
    String text,
    int startPosition,
    int endPosition,
  ) {
    final section = text.substring(startPosition, endPosition);
    final lines = section.split('\n');
    
    // 尝试在段落边界分隔（空行）
    for (int i = 1; i < lines.length - 1; i++) {
      if (lines[i].trim().isEmpty && lines[i - 1].trim().isNotEmpty) {
        int position = startPosition;
        for (int j = 0; j <= i; j++) {
          position += lines[j].length + 1; // +1 for newline
        }
        return position;
      }
    }
    
    // 如果没有找到段落边界，返回中间位置
    return startPosition + (endPosition - startPosition) ~/ 2;
  }

  /// 生成分块的摘要信息
  static String generateBlockSummary(MarkdownBlock block) {
    final content = block.content.trim();
    if (content.length <= 100) {
      return content;
    }
    
    // 尝试在句号处截断
    final sentences = content.split('。');
    if (sentences.isNotEmpty && sentences[0].length <= 100) {
      return '${sentences[0]}。...';
    }
    
    // 否则简单截断
    return '${content.substring(0, 97)}...';
  }

  /// 验证分块配置的合理性
  static List<String> validateBlockConfig(BlockRenderConfig config) {
    final issues = <String>[];
    
    if (!config.enabled) {
      return issues; // 如果未启用，不需要验证其他选项
    }
    
    if (!config.splitByH1 && !config.splitByH2 && config.customSeparatorPattern.isEmpty) {
      issues.add('至少需要启用一种分隔方式');
    }
    
    if (config.customSeparatorPattern.isNotEmpty && 
        !isValidRegexPattern(config.customSeparatorPattern)) {
      issues.add('自定义分隔符的正则表达式无效');
    }
    
    if (config.blockSpacing < 0) {
      issues.add('分块间距不能为负数');
    }
    
    // 移除了操纵杆高度的验证
    
    return issues;
  }

  /// 计算两个位置之间的距离（用于分隔杆冲突检测）
  static double calculatePositionDistance(
    String text,
    int position1,
    int position2,
    double lineHeight,
  ) {
    final line1 = _getLineNumberAtPosition(text, position1);
    final line2 = _getLineNumberAtPosition(text, position2);
    return (line1 - line2).abs() * lineHeight;
  }

  /// 获取指定位置所在的行号
  static int _getLineNumberAtPosition(String text, int position) {
    if (position <= 0) return 0;
    if (position >= text.length) return text.split('\n').length - 1;
    
    return text.substring(0, position).split('\n').length - 1;
  }

  /// 格式化分块类型为可读字符串
  static String formatBlockType(BlockSeparatorType type) {
    switch (type) {
      case BlockSeparatorType.h1:
        return '一级标题';
      case BlockSeparatorType.h2:
        return '二级标题';
      case BlockSeparatorType.custom:
        return '自定义分隔符';
      case BlockSeparatorType.manual:
        return '手动分隔';
    }
  }

  /// 生成分块的调试信息
  static String generateDebugInfo(
    List<MarkdownBlock> blocks,
  ) {
    final buffer = StringBuffer();

    buffer.writeln('=== 分块调试信息 ===');
    buffer.writeln('分块总数: ${blocks.length}');
    buffer.writeln();

    for (int i = 0; i < blocks.length; i++) {
      final block = blocks[i];
      buffer.writeln('分块 $i:');
      buffer.writeln('  ID: ${block.id}');
      buffer.writeln('  标题: ${block.title}');
      buffer.writeln('  类型: ${formatBlockType(block.separatorType)}');
      buffer.writeln('  位置: ${block.startPosition}-${block.endPosition}');
      buffer.writeln('  可见: ${block.isVisible}');
      buffer.writeln('  长度: ${block.content.length} 字符');
      buffer.writeln();
    }

    return buffer.toString();
  }
}

/// 分块统计信息
class BlockStatistics {
  /// 总分块数
  final int totalBlocks;
  
  /// 可见分块数
  final int visibleBlocks;
  
  /// 隐藏分块数
  final int hiddenBlocks;
  
  /// 总字符数
  final int totalCharacters;
  
  /// 平均分块长度
  final int averageBlockLength;
  
  /// 各类型分块数量
  final Map<BlockSeparatorType, int> blockTypeCounts;

  const BlockStatistics({
    required this.totalBlocks,
    required this.visibleBlocks,
    required this.hiddenBlocks,
    required this.totalCharacters,
    required this.averageBlockLength,
    required this.blockTypeCounts,
  });

  @override
  String toString() {
    return 'BlockStatistics(total: $totalBlocks, visible: $visibleBlocks, '
           'hidden: $hiddenBlocks, chars: $totalCharacters, avg: $averageBlockLength)';
  }
}
