import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../common/utils/permission_helper.dart';
import '../../common/widgets/app_loading_indicator.dart';
import '../models/html_template.dart';
import '../models/markdown_section.dart';
import '../services/html_renderer_service.dart';

/// Markdown段落预览屏幕
class MarkdownSectionPreviewScreen extends StatefulWidget {
  /// Markdown文档
  final MarkdownSectionDocument document;

  /// HTML模板
  final HtmlTemplate template;

  /// 是否只显示单个段落
  final bool showSingleSection;

  const MarkdownSectionPreviewScreen({
    super.key,
    required this.document,
    required this.template,
    this.showSingleSection = false,
  });

  @override
  State<MarkdownSectionPreviewScreen> createState() =>
      _MarkdownSectionPreviewScreenState();
}

class _MarkdownSectionPreviewScreenState
    extends State<MarkdownSectionPreviewScreen> {
  /// HTML渲染服务
  final HtmlRendererService _htmlRendererService = HtmlRendererService();

  /// 页面控制器
  late final PageController _pageController;

  /// 当前页面索引
  int _currentPage = 0;

  /// WebView控制器列表
  final List<WebViewController> _webViewControllers = [];

  /// 加载状态
  final List<bool> _isPageLoaded = [];

  /// 页面HTML
  List<String> _pagesHtml = [];

  /// 是否正在生成图片
  bool _isGeneratingImages = false;

  /// 图片数据列表
  final List<Uint8List?> _capturedImages = [];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _initPages();
  }

  @override
  void dispose() {
    _pageController.dispose();
    // 清理HTML预览文件
    _htmlRendererService.cleanupHtmlPreviewFiles();
    super.dispose();
  }

  /// 初始化页面
  Future<void> _initPages() async {
    // 渲染HTML
    _pagesHtml = _htmlRendererService.renderDocumentToHtmlList(
      widget.document,
      widget.template,
    );

    // 初始化WebView控制器和加载状态
    for (var i = 0; i < _pagesHtml.length; i++) {
      final controller = _htmlRendererService.createWebViewController(
        _pagesHtml[i],
      );
      _webViewControllers.add(controller);
      _isPageLoaded.add(false);
      _capturedImages.add(null);
    }

    setState(() {});
  }

  /// 处理页面变化
  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  /// 页面标题
  String get _pageTitle {
    return widget.showSingleSection
        ? widget.document.sections[0].title
        : '预览 (${_currentPage + 1}/${_pagesHtml.length})';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_pageTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.save_alt),
            onPressed: _isGeneratingImages ? null : _generateAndSaveImages,
            tooltip: '保存图片',
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _isGeneratingImages ? null : _shareCurrentPage,
            tooltip: '分享',
          ),
        ],
      ),
      body:
          _pagesHtml.isEmpty
              ? const Center(child: Text('没有内容可预览'))
              : _buildPreviewPages(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// 构建预览页面
  Widget _buildPreviewPages() {
    return Stack(
      children: [
        PageView.builder(
          controller: _pageController,
          itemCount: _pagesHtml.length,
          onPageChanged: _onPageChanged,
          itemBuilder: (context, index) {
            if (_webViewControllers.length <= index) {
              return const Center(child: Text('加载中...'));
            }

            return Stack(
              children: [
                WebViewWidget(controller: _webViewControllers[index]),

                // 加载指示器
                if (!_isPageLoaded[index])
                  const Center(child: AppLoadingIndicator()),

                // 监听WebView加载状态
                Positioned.fill(
                  child: Opacity(
                    opacity: 0,
                    child: WebViewWidget(
                      controller:
                          WebViewController()
                            ..setJavaScriptMode(JavaScriptMode.unrestricted)
                            ..setNavigationDelegate(
                              NavigationDelegate(
                                onPageFinished: (String url) {
                                  if (mounted) {
                                    setState(() {
                                      _isPageLoaded[index] = true;
                                    });
                                  }
                                },
                              ),
                            )
                            ..loadHtmlString(_pagesHtml[index]),
                    ),
                  ),
                ),
              ],
            );
          },
        ),

        // 生成图片指示器
        if (_isGeneratingImages)
          Container(
            color: Colors.black.withValues(alpha: 0.5),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const AppLoadingIndicator(),
                  const SizedBox(height: 16),
                  Text(
                    '正在生成图片 ($_currentPage/${_pagesHtml.length})',
                    style: const TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  /// 构建底部栏
  Widget _buildBottomBar() {
    if (_pagesHtml.length <= 1) {
      return const SizedBox.shrink();
    }

    return BottomAppBar(
      height: 56,
      padding: EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed:
                _currentPage == 0
                    ? null
                    : () {
                      _pageController.previousPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
          ),
          Text(
            '${_currentPage + 1} / ${_pagesHtml.length}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward_ios),
            onPressed:
                _currentPage == _pagesHtml.length - 1
                    ? null
                    : () {
                      _pageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    },
          ),
        ],
      ),
    );
  }

  /// 生成并保存所有页面为图片
  Future<void> _generateAndSaveImages() async {
    // 检查权限
    final hasPermission = await PermissionHelper.requestStoragePermission();
    if (!hasPermission) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('需要存储权限才能保存图片')));
      }
      return;
    }

    setState(() {
      _isGeneratingImages = true;
    });

    try {
      // 创建临时目录保存图片
      final tempDir = await getTemporaryDirectory();
      final saveDir = Directory('${tempDir.path}/markdown_sections');

      if (!await saveDir.exists()) {
        await saveDir.create(recursive: true);
      }

      // 生成每个页面的图片
      List<String> savedPaths = [];

      for (var i = 0; i < _webViewControllers.length; i++) {
        setState(() {
          _currentPage = i;
        });

        // 等待确保页面加载完成
        if (!_isPageLoaded[i]) {
          await Future.delayed(const Duration(milliseconds: 500));
        }

        // 暂时跳过截图功能
        debugPrint('WebView截图功能暂不可用，跳过第${i + 1}页');
      }

      setState(() {
        _isGeneratingImages = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('成功保存${savedPaths.length}张图片到相册')),
        );
      }
    } catch (e) {
      setState(() {
        _isGeneratingImages = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('保存图片失败: $e')));
      }
    }
  }

  /// 分享当前页面
  Future<void> _shareCurrentPage() async {
    try {
      // 如果当前页面图片未生成，先生成
      if (_capturedImages[_currentPage] == null) {
        setState(() {
          _isGeneratingImages = true;
        });

        // WebView截图功能在当前版本中不可用
        // final imageData = await _htmlRendererService.captureHtmlAsImage(
        //   _webViewControllers[_currentPage],
        // );

        // 暂时设置为null
        Uint8List? imageData;
        debugPrint('WebView截图功能暂不可用，无法生成分享图片');

        _capturedImages[_currentPage] = imageData;

        setState(() {
          _isGeneratingImages = false;
        });
      }

      // 分享图片
      if (_capturedImages[_currentPage] != null) {
        final tempDir = await getTemporaryDirectory();
        final imagePath = '${tempDir.path}/share_image.png';

        await File(imagePath).writeAsBytes(_capturedImages[_currentPage]!);

        await SharePlus.instance.share(
          ShareParams(
            files: [XFile(imagePath)],
            text: widget.document.sections[_currentPage].title,
          ),
        );
      } else {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('生成图片失败，无法分享')));
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('分享失败: $e')));
      }
    }
  }
}
