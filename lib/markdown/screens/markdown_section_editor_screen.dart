import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import '../../common/widgets/app_loading_indicator.dart';
import '../../services/service_locator.dart';
import '../../services/storage_service.dart';
import '../models/html_template.dart';
import '../models/markdown_section.dart';
import '../services/markdown_section_service.dart';
import 'markdown_section_preview_screen.dart';

/// Markdown分段编辑器屏幕
class MarkdownSectionEditorScreen extends StatefulWidget {
  /// 初始Markdown文本
  final String? initialMarkdown;

  /// 初始标题
  final String? initialTitle;

  /// 初始子标题
  final String? initialSubtitle;

  const MarkdownSectionEditorScreen({
    super.key,
    this.initialMarkdown,
    this.initialTitle,
    this.initialSubtitle,
  });

  @override
  State<MarkdownSectionEditorScreen> createState() =>
      _MarkdownSectionEditorScreenState();
}

class _MarkdownSectionEditorScreenState
    extends State<MarkdownSectionEditorScreen> {
  /// 服务定位器
  final _serviceLocator = ServiceLocator();

  /// 文本编辑控制器
  late TextEditingController _markdownController;

  /// 标题编辑控制器
  late TextEditingController _titleController;

  /// 子标题编辑控制器
  late TextEditingController _subtitleController;

  /// UUID生成器
  final Uuid _uuid = const Uuid();

  /// Markdown段落拆分服务
  late final MarkdownSectionService _sectionService;

  /// 存储服务
  late final StorageService _storageService;

  /// 段落文档
  MarkdownSectionDocument? _document;

  /// HTML模板
  late HtmlTemplate _selectedTemplate;

  /// 是否正在加载
  bool _isLoading = false;

  /// 拆分选项 - 按水平分割线
  bool _splitByHorizontalRule = true;

  /// 拆分选项 - 按一级标题
  bool _splitByH1 = true;

  /// 拆分选项 - 最大段落长度
  int? _maxSectionLength = 2000;

  @override
  void initState() {
    super.initState();

    // 初始化控制器
    _markdownController = TextEditingController(
      text: widget.initialMarkdown ?? '',
    );
    _titleController = TextEditingController(text: widget.initialTitle ?? '');
    _subtitleController = TextEditingController(
      text: widget.initialSubtitle ?? '',
    );

    // 初始化服务
    _sectionService = MarkdownSectionService();
    _storageService = _serviceLocator.storageService;

    // 加载默认模板
    _selectedTemplate = HtmlTemplate.simpleArticle();

    // 如果有初始Markdown，尝试拆分
    if (widget.initialMarkdown != null && widget.initialMarkdown!.isNotEmpty) {
      _processSplitMarkdown();
    }
  }

  @override
  void dispose() {
    _markdownController.dispose();
    _titleController.dispose();
    _subtitleController.dispose();
    super.dispose();
  }

  /// 处理Markdown拆分
  Future<void> _processSplitMarkdown() async {
    if (_markdownController.text.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请先输入Markdown内容')));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final document = _sectionService.createDocumentFromContent(
        title: _titleController.text.isEmpty ? '未命名文档' : _titleController.text,
        subtitle:
            _subtitleController.text.isEmpty ? null : _subtitleController.text,
        markdownContent: _markdownController.text,
        splitByHorizontalRule: _splitByHorizontalRule,
        splitByH1: _splitByH1,
        maxSectionLength: _maxSectionLength,
      );

      setState(() {
        _document = document;
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('成功拆分为${document.sections.length}个段落')),
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('拆分段落时出错: $e')));
    }
  }

  /// 预览拆分后的段落
  void _previewSections() {
    if (_document == null || _document!.sections.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('没有可预览的段落')));
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => MarkdownSectionPreviewScreen(
              document: _document!,
              template: _selectedTemplate,
            ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Markdown分段编辑器'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: _showSplitOptionsDialog,
            tooltip: '分段设置',
          ),
          IconButton(
            icon: const Icon(Icons.style_outlined),
            onPressed: _showTemplateSelector,
            tooltip: '选择模板',
          ),
          IconButton(
            icon: const Icon(Icons.preview_outlined),
            onPressed: _previewSections,
            tooltip: '预览',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: AppLoadingIndicator())
              : _buildMainContent(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// 构建主内容区域
  Widget _buildMainContent() {
    return Column(
      children: [
        // 标题输入区域
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              TextField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: '标题',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: _subtitleController,
                decoration: const InputDecoration(
                  labelText: '副标题 (可选)',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),

        // 分隔栏
        const Divider(height: 1),

        // Markdown编辑器和段落列表
        Expanded(
          child: Row(
            children: [
              // Markdown编辑器
              Expanded(
                flex: 3,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: _markdownController,
                    maxLines: null,
                    expands: true,
                    decoration: const InputDecoration(
                      hintText: '输入Markdown内容...',
                      border: OutlineInputBorder(),
                    ),
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 14,
                    ),
                  ),
                ),
              ),

              // 分隔线
              const VerticalDivider(width: 1),

              // 段落列表
              Expanded(
                flex: 2,
                child:
                    _document == null || _document!.sections.isEmpty
                        ? const Center(child: Text('点击拆分按钮将Markdown拆分为段落'))
                        : _buildSectionsList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建段落列表
  Widget _buildSectionsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: _document!.sections.length,
      itemBuilder: (context, index) {
        final section = _document!.sections[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8.0),
          child: ListTile(
            title: Text(
              section.title.isEmpty ? '未命名段落' : section.title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Text(
              section.content,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            leading: CircleAvatar(child: Text('${index + 1}')),
            onTap: () => _showSectionDetails(section),
          ),
        );
      },
    );
  }

  /// 构建底部栏
  Widget _buildBottomBar() {
    return BottomAppBar(
      height: 56,
      padding: EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: TextButton.icon(
              icon: const Icon(Icons.content_cut),
              label: const Text('拆分段落'),
              onPressed: _processSplitMarkdown,
            ),
          ),
          const VerticalDivider(width: 1, indent: 8, endIndent: 8),
          Expanded(
            child: TextButton.icon(
              icon: const Icon(Icons.save_outlined),
              label: const Text('保存文档'),
              onPressed: _saveDocument,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示拆分选项对话框
  void _showSplitOptionsDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('段落拆分设置'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CheckboxListTile(
                    title: const Text('按水平分割线拆分'),
                    subtitle: const Text('遇到三个以上的 - 或 * 或 _ 符号时拆分'),
                    value: _splitByHorizontalRule,
                    onChanged: (value) {
                      setState(() {
                        _splitByHorizontalRule = value ?? true;
                      });
                    },
                  ),
                  CheckboxListTile(
                    title: const Text('按一级标题拆分'),
                    subtitle: const Text('遇到 # 开头的一级标题时拆分'),
                    value: _splitByH1,
                    onChanged: (value) {
                      setState(() {
                        _splitByH1 = value ?? true;
                      });
                    },
                  ),
                  const Divider(),
                  ListTile(
                    title: const Text('最大段落长度'),
                    subtitle: Text(
                      _maxSectionLength == null
                          ? '不限制'
                          : '$_maxSectionLength个字符',
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () {
                        _editMaxSectionLength(setState);
                      },
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  child: const Text('取消'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                TextButton(
                  child: const Text('确定'),
                  onPressed: () {
                    Navigator.of(context).pop();
                    this.setState(() {});
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// 编辑最大段落长度
  void _editMaxSectionLength(StateSetter setState) {
    final controller = TextEditingController(
      text: _maxSectionLength?.toString() ?? '',
    );

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('设置最大段落长度'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: controller,
                decoration: const InputDecoration(
                  labelText: '最大字符数',
                  hintText: '留空表示不限制',
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 8),
              const Text(
                '将过长的段落自动拆分为多个段落',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('确定'),
              onPressed: () {
                setState(() {
                  if (controller.text.isEmpty) {
                    _maxSectionLength = null;
                  } else {
                    _maxSectionLength = int.tryParse(controller.text);
                  }
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  /// 显示模板选择器
  void _showTemplateSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          maxChildSize: 0.9,
          minChildSize: 0.4,
          builder: (context, scrollController) {
            return Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      '选择HTML模板',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Expanded(
                    child: GridView.builder(
                      controller: scrollController,
                      padding: const EdgeInsets.all(16),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                            childAspectRatio: 0.8,
                          ),
                      itemCount: HtmlTemplate.getPredefinedTemplates().length,
                      itemBuilder: (context, index) {
                        final template =
                            HtmlTemplate.getPredefinedTemplates()[index];
                        return _buildTemplateItem(template);
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// 构建模板项
  Widget _buildTemplateItem(HtmlTemplate template) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTemplate = template;
        });
        Navigator.pop(context);
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('已选择"${template.name}"模板')));
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color:
                _selectedTemplate.id == template.id
                    ? Theme.of(context).primaryColor
                    : Colors.transparent,
            width: 2,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(10),
                  ),
                ),
                child: Center(
                  child: Icon(
                    _getTemplateIcon(template.type),
                    size: 48,
                    color: _getTemplateColor(template.type),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    template.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    template.description,
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取模板图标
  IconData _getTemplateIcon(HtmlTemplateType type) {
    switch (type) {
      case HtmlTemplateType.article:
        return Icons.article_outlined;
      case HtmlTemplateType.card:
        return Icons.style_outlined;
      case HtmlTemplateType.poster:
        return Icons.image_outlined;
      case HtmlTemplateType.brief:
        return Icons.description_outlined;
      case HtmlTemplateType.custom:
        return Icons.code_outlined;
    }
  }

  /// 获取模板颜色
  Color _getTemplateColor(HtmlTemplateType type) {
    switch (type) {
      case HtmlTemplateType.article:
        return Colors.blue;
      case HtmlTemplateType.card:
        return Colors.purple;
      case HtmlTemplateType.poster:
        return Colors.orange;
      case HtmlTemplateType.brief:
        return Colors.teal;
      case HtmlTemplateType.custom:
        return Colors.blueGrey;
    }
  }

  /// 显示段落详情
  void _showSectionDetails(MarkdownSection section) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.5,
          maxChildSize: 0.9,
          minChildSize: 0.3,
          builder: (context, scrollController) {
            return Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Row(
                      children: [
                        Text(
                          section.title.isEmpty ? '未命名段落' : section.title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          icon: const Icon(Icons.preview_outlined),
                          onPressed: () {
                            _previewSingleSection(section);
                          },
                          tooltip: '预览',
                        ),
                      ],
                    ),
                  ),
                  const Divider(),
                  Expanded(
                    child: SingleChildScrollView(
                      controller: scrollController,
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            section.content,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// 预览单个段落
  void _previewSingleSection(MarkdownSection section) {
    final tempDoc = MarkdownSectionDocument(
      title: _titleController.text,
      subtitle: _subtitleController.text,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      sections: [section],
      rawContent: section.content,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => MarkdownSectionPreviewScreen(
              document: tempDoc,
              template: _selectedTemplate,
              showSingleSection: true,
            ),
      ),
    );
  }

  /// 保存文档
  Future<void> _saveDocument() async {
    if (_document == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请先拆分Markdown内容')));
      return;
    }

    final docId = _uuid.v4();
    final key = 'markdown_section_doc_$docId';

    try {
      await _storageService.setJson(key, _document!.toJson());

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('文档保存成功')));
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('保存文档失败: $e')));
    }
  }
}
