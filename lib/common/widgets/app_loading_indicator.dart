import 'package:flutter/material.dart';

/// 应用加载指示器
class AppLoadingIndicator extends StatelessWidget {
  /// 指示器大小
  final double size;

  /// 指示器颜色
  final Color? color;

  /// 创建加载指示器
  const AppLoadingIndicator({super.key, this.size = 36.0, this.color});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 3.0,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }
}
