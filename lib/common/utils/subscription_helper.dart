import 'package:flutter/material.dart';
import '../../services/service_locator.dart';
import '../../subscription/subscription_model.dart';
import '../../subscription/subscription_screen.dart' show SubscriptionScreen;

/// 订阅工具类，提供订阅相关的辅助方法
class SubscriptionHelper {
  // 私有构造函数，防止实例化
  SubscriptionHelper._();

  /// 检查用户是否已订阅（包括任何付费计划）
  static bool isSubscribed() {
    final subscriptionService = ServiceLocator().subscriptionService;
    final subscription = subscriptionService.subscription;

    return subscription.isActive && subscription.isPaid;
  }

  /// 检查用户是否有终身订阅
  static bool hasLifetimeSubscription() {
    final subscriptionService = ServiceLocator().subscriptionService;
    final subscription = subscriptionService.subscription;

    return subscription.isActive && subscription.isLifetime;
  }

  /// 检查订阅是否已过期
  static bool isSubscriptionExpired() {
    final subscriptionService = ServiceLocator().subscriptionService;
    final subscription = subscriptionService.subscription;

    return subscription.isExpired;
  }

  /// 获取当前订阅类型
  static SubscriptionType getCurrentSubscriptionType() {
    final subscriptionService = ServiceLocator().subscriptionService;
    return subscriptionService.subscription.type;
  }

  /// 获取订阅剩余天数（如果适用）
  static int? getRemainingDays() {
    final subscriptionService = ServiceLocator().subscriptionService;
    return subscriptionService.subscription.remainingDays;
  }

  /// 检查用户是否可以访问特定功能
  static bool canAccessFeature(String featureId) {
    final subscriptionService = ServiceLocator().subscriptionService;
    return subscriptionService.canAccessFeature(featureId);
  }

  /// 显示订阅页面
  static Future<bool?> showSubscriptionScreen(BuildContext context) async {
    return await Navigator.of(context).push<bool>(
      MaterialPageRoute(builder: (context) => const SubscriptionScreen()),
    );
  }

  /// 显示续订页面
  static Future<bool?> showRenewalScreen(BuildContext context) async {
    return await Navigator.of(context).push<bool>(
      MaterialPageRoute(builder: (context) => const SubscriptionScreen()),
    );
  }

  /// 检查并处理订阅状态，如果需要则显示订阅页面
  /// 返回是否可以继续（true表示已订阅或用户取消了订阅页面，false表示需要订阅但用户取消了操作）
  static Future<bool> checkAndHandleSubscription(
    BuildContext context, {
    bool forceShow = false,
    String? requiredFeatureId,
  }) async {
    final subscriptionService = ServiceLocator().subscriptionService;

    // 检查是否需要显示订阅页面
    bool needsSubscription = forceShow;

    // 如果指定了特定功能，检查是否可以访问
    if (requiredFeatureId != null && !needsSubscription) {
      needsSubscription =
          !subscriptionService.canAccessFeature(requiredFeatureId);
    }

    // 如果没有指定特定功能，检查是否需要显示订阅页面
    if (!needsSubscription && !forceShow && requiredFeatureId == null) {
      needsSubscription = subscriptionService.shouldShowSubscriptionScreen();
    }

    // 如果需要订阅，显示订阅页面
    if (needsSubscription) {
      final result = await showSubscriptionScreen(context);

      // 如果用户完成了订阅，返回true
      if (result == true) {
        return true;
      }

      // 如果用户取消了订阅，返回false
      return false;
    }

    // 检查是否需要续订
    final needsRenewal = subscriptionService.shouldShowRenewalScreen();
    if (needsRenewal) {
      final result = await showRenewalScreen(context);

      // 如果用户完成了续订，返回true
      if (result == true) {
        return true;
      }

      // 如果用户取消了续订，返回false
      return false;
    }

    // 默认情况下，返回true（已订阅或不需要订阅）
    return true;
  }

  /// 获取订阅状态文本描述
  static String getSubscriptionStatusText() {
    final subscriptionService = ServiceLocator().subscriptionService;
    final subscription = subscriptionService.subscription;
    if (subscription.isActive) {
      switch (subscription.type) {
        case SubscriptionType.monthly:
          return '月度订阅';
        case SubscriptionType.yearly:
          return '年度订阅';
        case SubscriptionType.lifetime:
          return '终身订阅';
        case SubscriptionType.free:
        return '免费版';
      }
    } else if (subscription.isExpired) {
      return '订阅已过期';
    } else {
      return '未订阅';
    }
  }

  /// 获取订阅到期日期文本（如果适用）
  static String? getExpiryDateText() {
    final subscriptionService = ServiceLocator().subscriptionService;
    final subscription = subscriptionService.subscription;

    if (subscription.isActive &&
        subscription.type != SubscriptionType.free &&
        subscription.type != SubscriptionType.lifetime &&
        subscription.endDate != null) {
      final endDate = subscription.endDate!;
      return '${endDate.year}年${endDate.month}月${endDate.day}日到期';
    }

    return null;
  }

  /// 获取订阅计划的特性列表
  static List<SubscriptionFeature> getFeaturesByType(SubscriptionType type) {
    final subscriptionService = ServiceLocator().subscriptionService;
    final plan = subscriptionService.getPlanByType(type);
    return plan?.features ?? [];
  }

  /// 获取所有订阅计划
  static List<SubscriptionPlan> getAllPlans() {
    final subscriptionService = ServiceLocator().subscriptionService;
    return subscriptionService.plans;
  }

  /// 获取付费订阅计划
  static List<SubscriptionPlan> getPaidPlans() {
    final subscriptionService = ServiceLocator().subscriptionService;
    return subscriptionService.plans
        .where((plan) => plan.type != SubscriptionType.free)
        .toList();
  }
}
