import 'dart:io';

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// 权限帮助工具
class PermissionHelper {
  /// 请求存储权限
  static Future<bool> requestStoragePermission() async {
    try {
      // 检查是否已有权限
      if (await Permission.storage.isGranted) {
        return true;
      }

      // 请求权限
      PermissionStatus status = await Permission.storage.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('请求存储权限失败: $e');
      return false;
    }
  }

  /// 请求相机权限
  static Future<bool> requestCameraPermission() async {
    try {
      // 检查是否已有权限
      if (await Permission.camera.isGranted) {
        return true;
      }

      // 请求权限
      PermissionStatus status = await Permission.camera.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('请求相机权限失败: $e');
      return false;
    }
  }

  /// 请求多个权限
  static Future<Map<Permission, PermissionStatus>> requestMultiplePermissions(
    List<Permission> permissions,
  ) async {
    try {
      return await permissions.request();
    } catch (e) {
      debugPrint('请求多个权限失败: $e');
      return {
        for (var permission in permissions) permission: PermissionStatus.denied,
      };
    }
  }

  static Future<void> checkIosPermissions() async {
    if (Platform.isIOS) {
      try {
        debugPrint("===== 应用启动时强制注册iOS权限 =====");

        // 检查麦克风权限状态（不请求，只检查）
        final micStatus = await Permission.microphone.status;
        debugPrint("iOS麦克风权限状态: $micStatus");

        // 检查语音识别权限状态（不请求，只检查）
        final speechStatus = await Permission.speech.status;
        debugPrint("iOS语音识别权限状态: $speechStatus");

        // 检查照片权限状态
        final photosStatus = await Permission.photos.status;
        debugPrint("iOS照片权限状态: $photosStatus");

        // 注意：这里仅记录权限状态，不主动请求，确保应用首次运行时能正确识别权限
      } catch (e) {
        debugPrint("启动时权限状态检查失败: $e");
      }
    }
  }

}
