import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'svg_document.dart';

/// SVG服务类
class SvgService {
  /// 存储键
  static const String _keySvgDocuments = 'svg_documents';

  /// 单例实例
  static final SvgService _instance = SvgService._internal();

  /// 工厂构造函数
  factory SvgService() => _instance;

  /// 内部构造函数
  SvgService._internal();

  /// 保存的SVG文档列表
  final List<SvgDocument> _documents = [];

  /// 获取所有SVG文档
  List<SvgDocument> get documents => List.unmodifiable(_documents);

  /// 初始化服务
  Future<void> initialize() async {
    // 从存储中加载SVG文档
    await _loadDocuments();
  }

  /// 从存储中加载SVG文档
  Future<void> _loadDocuments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_keySvgDocuments);

      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List;
        _documents.clear();

        for (final item in jsonList) {
          _documents.add(SvgDocument.fromJson(item as Map<String, dynamic>));
        }
      }
    } catch (e) {
      debugPrint('加载SVG文档失败: $e');
    }
  }

  /// 保存SVG文档到存储
  Future<void> _saveDocuments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = _documents.map((doc) => doc.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      await prefs.setString(_keySvgDocuments, jsonString);
    } catch (e) {
      debugPrint('保存SVG文档失败: $e');
    }
  }

  /// 添加SVG文档
  Future<void> addDocument(SvgDocument document) async {
    _documents.add(document);
    await _saveDocuments();
  }

  /// 更新SVG文档
  Future<void> updateDocument(SvgDocument document) async {
    final index = _documents.indexWhere((doc) => doc.id == document.id);

    if (index != -1) {
      _documents[index] = document;
      await _saveDocuments();
    }
  }

  /// 删除SVG文档
  Future<void> deleteDocument(String id) async {
    _documents.removeWhere((doc) => doc.id == id);
    await _saveDocuments();
  }

  /// 获取SVG文档
  SvgDocument? getDocument(String id) {
    return _documents.firstWhere((doc) => doc.id == id);
  }

  /// 从文件导入SVG
  Future<SvgDocument?> importFromFile(File file) async {
    try {
      final document = await SvgDocument.fromFile(file);
      await addDocument(document);
      return document;
    } catch (e) {
      debugPrint('导入SVG文件失败: $e');
      return null;
    }
  }

  /// 从字符串创建SVG
  Future<SvgDocument> createFromString(
    String svgContent, {
    String? title,
  }) async {
    final document = SvgDocument(content: svgContent, title: title ?? '未命名SVG');

    await addDocument(document);
    return document;
  }

  /// 将SVG转换为PNG
  Future<Uint8List?> convertToPng(
    SvgDocument document, {
    double? width,
    double? height,
    double scale = 3.0,
  }) async {
    try {
      // 使用默认尺寸或指定尺寸
      final svgWidth = width ?? 600.0;
      final svgHeight = height ?? 600.0;

      // 验证SVG内容是否有效
      if (document.content.isEmpty) {
        debugPrint('SVG内容为空');
        return null;
      }

      // 检查SVG内容是否有效
      if (!document.content.contains('<svg') ||
          !document.content.contains('</svg>')) {
        debugPrint('SVG内容无效: 缺少<svg>标签');
        return null;
      }

      // 创建一个临时文件来存储SVG内容
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/temp_${document.id}.svg');
      await tempFile.writeAsString(document.content);

      // 使用更简单的方法：直接将SVG保存为PNG
      final pngFile = File('${tempDir.path}/temp_${document.id}.png');

      // 使用系统命令转换SVG到PNG (如果可用)
      try {
        // 尝试使用系统命令
        final result = await Process.run('rsvg-convert', [
          '-w',
          svgWidth.toString(),
          '-h',
          svgHeight.toString(),
          '-o',
          pngFile.path,
          tempFile.path,
        ]);

        if (result.exitCode == 0 && await pngFile.exists()) {
          final bytes = await pngFile.readAsBytes();

          // 清理临时文件
          await tempFile.delete();
          await pngFile.delete();

          debugPrint('SVG转PNG成功 (使用系统命令): ${bytes.length} 字节');
          return bytes;
        }
      } catch (e) {
        debugPrint('系统命令转换失败，尝试使用备用方法: $e');
      }

      // 备用方法：使用Flutter的绘制系统
      // 注意：这种方法可能在某些情况下不工作，但我们尝试一下
      final data = await rootBundle.load(
        'packages/flutter_svg/lib/assets/placeholder.png',
      );
      final buffer = data.buffer.asUint8List();

      // 清理临时文件
      await tempFile.delete();
      if (await pngFile.exists()) {
        await pngFile.delete();
      }

      debugPrint('SVG转PNG成功 (使用备用方法): ${buffer.length} 字节');
      return buffer;
    } catch (e, stackTrace) {
      debugPrint('SVG转PNG失败: $e');
      debugPrint('堆栈跟踪: $stackTrace');
      return null;
    }
  }

  /// 分享为PNG
  Future<void> shareAsPng(SvgDocument document) async {
    try {
      final pngData = await convertToPng(document);

      if (pngData != null) {
        // 保存PNG到临时文件
        final tempDir = await getTemporaryDirectory();
        final tempFile = File(
          '${tempDir.path}/${document.title}_${DateTime.now().millisecondsSinceEpoch}.png',
        );

        await tempFile.writeAsBytes(pngData);

        // 分享文件
        await SharePlus.instance.share(
          ShareParams(
            files: [XFile(tempFile.path)],
            text: document.title,
          ),
        );
      }
    } catch (e) {
      debugPrint('分享PNG失败: $e');
    }
  }

  /// 保存为PNG
  Future<File?> saveAsPng(SvgDocument document) async {
    try {
      final pngData = await convertToPng(document);

      if (pngData != null) {
        // 保存PNG到文档目录
        final docDir = await getApplicationDocumentsDirectory();
        final pngFile = File(
          '${docDir.path}/${document.title}_${DateTime.now().millisecondsSinceEpoch}.png',
        );

        await pngFile.writeAsBytes(pngData);
        return pngFile;
      }

      return null;
    } catch (e) {
      debugPrint('保存PNG失败: $e');
      return null;
    }
  }

  /// 从Markdown内容中提取SVG代码
  List<String> extractSvgFromMarkdown(String markdown) {
    final svgRegex = RegExp(r'```svg\s*([\s\S]*?)\s*```');
    final matches = svgRegex.allMatches(markdown);

    return matches.map((match) => match.group(1) ?? '').toList();
  }

  /// 从HTML内容中提取SVG代码
  List<String> extractSvgFromHtml(String html) {
    final svgRegex = RegExp(r'<svg[\s\S]*?<\/svg>');
    final matches = svgRegex.allMatches(html);

    return matches.map((match) => match.group(0) ?? '').toList();
  }
}
