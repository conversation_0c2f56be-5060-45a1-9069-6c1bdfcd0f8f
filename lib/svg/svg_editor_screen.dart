import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../services/service_locator.dart';
import '../config/app_theme.dart';
import 'svg_document.dart';


/// SVG编辑器屏幕
class SvgEditorScreen extends StatefulWidget {
  /// 初始SVG内容
  final String? initialSvgContent;

  /// 初始标题
  final String? initialTitle;

  /// 文档ID（如果是编辑现有文档）
  final String? documentId;

  /// 构造函数
  const SvgEditorScreen({
    super.key,
    this.initialSvgContent,
    this.initialTitle,
    this.documentId,
  });

  @override
  State<SvgEditorScreen> createState() => _SvgEditorScreenState();
}

class _SvgEditorScreenState extends State<SvgEditorScreen>
    with SingleTickerProviderStateMixin {
  /// SVG服务
  final _svgService = ServiceLocator().svgService;

  /// 编辑控制器
  late TextEditingController _svgController;

  /// 标题控制器
  late TextEditingController _titleController;

  /// 当前文档
  SvgDocument? _document;

  /// 是否正在处理
  bool _isProcessing = false;

  /// 是否有错误
  bool _hasError = false;

  /// 错误消息
  String _errorMessage = '';

  /// 是否已修改
  bool _isDirty = false;

  /// Tab控制器
  late TabController _tabController;

  @override
  void initState() {
    super.initState();

    _svgController = TextEditingController(
      text: widget.initialSvgContent ?? '',
    );
    _titleController = TextEditingController(
      text: widget.initialTitle ?? '未命名SVG',
    );

    _svgController.addListener(_onSvgChanged);

    // 初始化Tab控制器
    _tabController = TabController(length: 2, vsync: this);

    // 如果有文档ID，加载文档
    if (widget.documentId != null) {
      _loadDocument(widget.documentId!);
    } else if (widget.initialSvgContent != null) {
      // 如果有初始内容，创建新文档
      _createNewDocument();
    }
  }

  @override
  void dispose() {
    _svgController.removeListener(_onSvgChanged);
    _svgController.dispose();
    _titleController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  /// 加载文档
  Future<void> _loadDocument(String id) async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final document = _svgService.getDocument(id);

      if (document != null) {
        setState(() {
          _document = document;
          _svgController.text = document.content;
          _titleController.text = document.title;
          _isProcessing = false;
          _hasError = false;
        });
      } else {
        setState(() {
          _isProcessing = false;
          _hasError = true;
          _errorMessage = '找不到文档';
        });
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '加载文档失败: $e';
      });
    }
  }

  /// 创建新文档
  Future<void> _createNewDocument() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final document = await _svgService.createFromString(
        _svgController.text,
        title: _titleController.text,
      );

      setState(() {
        _document = document;
        _isProcessing = false;
        _hasError = false;
        _isDirty = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '创建文档失败: $e';
      });
    }
  }

  /// 保存文档
  Future<void> _saveDocument() async {
    // 如果是新文档或需要重命名，显示输入对话框
    if (_document == null || _document!.title == '未命名SVG') {
      final newTitle = await _showTitleInputDialog();
      if (newTitle == null) {
        // 用户取消了输入
        return;
      }
      _titleController.text = newTitle;
    }

    if (_document == null) {
      await _createNewDocument();
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      _document!.updateContent(_svgController.text);
      _document!.updateTitle(_titleController.text);

      await _svgService.updateDocument(_document!);

      setState(() {
        _isProcessing = false;
        _hasError = false;
        _isDirty = false;
      });

      _showSnackBar('保存成功');
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '保存文档失败: $e';
      });
    }
  }

  /// 显示标题输入对话框
  Future<String?> _showTitleInputDialog() async {
    return showDialog<String>(
      context: context,
      builder: (context) {
        final textController = TextEditingController(
          text: _titleController.text,
        );
        return AlertDialog(
          title: const Text('输入SVG文件名'),
          content: TextField(
            controller: textController,
            decoration: const InputDecoration(
              labelText: '文件名',
              hintText: '请输入SVG文件名',
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                if (textController.text.trim().isNotEmpty) {
                  Navigator.pop(context, textController.text.trim());
                } else {
                  _showSnackBar('文件名不能为空');
                }
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  /// 导入SVG文件
  Future<void> _importSvgFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['svg'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        final document = await SvgDocument.fromFile(file);

        setState(() {
          _svgController.text = document.content;
          _titleController.text = document.title;
          _hasError = false;
          _isDirty = true;
        });
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = '导入SVG文件失败: $e';
      });
    }
  }

  /// 导出为PNG
  Future<void> _exportAsPng() async {
    if (_document == null) {
      await _saveDocument();
    }

    if (_document == null) {
      _showSnackBar('请先保存文档');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final file = await _svgService.saveAsPng(_document!);

      setState(() {
        _isProcessing = false;
      });

      if (file != null) {
        _showSnackBar('导出PNG成功: ${file.path}');
      } else {
        _showSnackBar('导出PNG失败');
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '导出PNG失败: $e';
      });
    }
  }

  /// 分享SVG
  Future<void> _shareSvg() async {
    if (_document == null) {
      await _saveDocument();
    }

    if (_document == null) {
      _showSnackBar('请先保存文档');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      await _document!.share();

      setState(() {
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '分享SVG失败: $e';
      });
    }
  }

  /// 分享为PNG
  Future<void> _shareAsPng() async {
    if (_document == null) {
      await _saveDocument();
    }

    if (_document == null) {
      _showSnackBar('请先保存文档');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      await _svgService.shareAsPng(_document!);

      setState(() {
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '分享PNG失败: $e';
      });
    }
  }

  /// SVG内容变更处理
  void _onSvgChanged() {
    setState(() {
      _isDirty = true;
      _hasError = false;
    });

    // 尝试解析SVG，检查是否有错误
    try {
      if (_svgController.text.isNotEmpty) {
        SvgPicture.string(_svgController.text);
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = '无效的SVG: $e';
      });
    }
  }

  /// 显示提示消息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        scrolledUnderElevation: 2,
        title: const Text('SVG编辑器'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.code), text: '编辑'),
            Tab(icon: Icon(Icons.visibility), text: '预览'),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            tooltip: '更多操作',
            onSelected: (value) {
              switch (value) {
                case 'import':
                  _importSvgFile();
                  break;
                case 'save':
                  _saveDocument();
                  break;
                case 'export_png':
                  _exportAsPng();
                  break;
                case 'share_png':
                  _shareAsPng();
                  break;
                case 'share_svg':
                  _shareSvg();
                  break;
                case 'rename':
                  _showTitleInputDialog().then((newTitle) {
                    if (newTitle != null) {
                      setState(() {
                        _titleController.text = newTitle;
                        _isDirty = true;
                      });
                    }
                  });
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'import',
                    child: Row(
                      children: [
                        Icon(Icons.file_upload_outlined),
                        SizedBox(width: 12),
                        Text('导入SVG文件'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'save',
                    child: Row(
                      children: [
                        Icon(Icons.save_outlined),
                        SizedBox(width: 12),
                        Text('保存'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'export_png',
                    child: Row(
                      children: [
                        Icon(Icons.image_outlined),
                        SizedBox(width: 12),
                        Text('导出为PNG'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'share_png',
                    child: Row(
                      children: [
                        Icon(Icons.share_outlined),
                        SizedBox(width: 12),
                        Text('分享为PNG'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'share_svg',
                    child: Row(
                      children: [
                        Icon(Icons.code),
                        SizedBox(width: 12),
                        Text('分享SVG'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'rename',
                    child: Row(
                      children: [
                        Icon(Icons.edit),
                        SizedBox(width: 12),
                        Text('重命名'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body:
          _isProcessing
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text('处理中...', style: theme.textTheme.bodyMedium),
                  ],
                ),
              )
              : Column(
                children: [
                  if (_hasError)
                    Container(
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.redLight,
                        borderRadius: BorderRadius.circular(
                          AppTheme.borderRadiusMD,
                        ),
                      ),
                      width: double.infinity,
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: AppTheme.redDark),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _errorMessage,
                              style: TextStyle(color: AppTheme.redDark),
                            ),
                          ),
                        ],
                      ),
                    ),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        // 编辑区域
                        _buildEditorTab(isDarkMode),

                        // 预览区域
                        _buildPreviewTab(isDarkMode),
                      ],
                    ),
                  ),
                ],
              ),
      floatingActionButton:
          _isDirty && !_hasError
              ? FloatingActionButton.extended(
                onPressed: _saveDocument,
                icon: const Icon(Icons.save),
                label: const Text('保存更改'),
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              )
              : null,
    );
  }

  /// 构建编辑器Tab
  Widget _buildEditorTab(bool isDarkMode) {
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        side: BorderSide(
          color: isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: TextField(
          controller: _svgController,
          maxLines: null,
          expands: true,
          decoration: InputDecoration(
            hintText: '输入SVG代码',
            border: InputBorder.none,
            fillColor:
                isDarkMode ? AppTheme.darkBgColor : AppTheme.bgLightColor,
            filled: true,
            contentPadding: const EdgeInsets.all(12),
          ),
          style: TextStyle(
            fontFamily: 'monospace',
            fontSize: 14,
            color: isDarkMode ? AppTheme.darkTextColor : AppTheme.textDarkColor,
          ),
        ),
      ),
    );
  }

  /// 构建预览Tab
  Widget _buildPreviewTab(bool isDarkMode) {
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        side: BorderSide(
          color: isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
        ),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        color: isDarkMode ? AppTheme.darkBgColor : AppTheme.bgLightColor,
        child:
            _svgController.text.isEmpty
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.image_not_supported_outlined,
                        size: 80,
                        color:
                            isDarkMode
                                ? AppTheme.darkTextLightColor
                                : AppTheme.textLightColor,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        '无SVG内容',
                        style: TextStyle(
                          fontSize: 18,
                          color:
                              isDarkMode
                                  ? AppTheme.darkTextLightColor
                                  : AppTheme.textLightColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '请在编辑标签页中输入SVG代码',
                        style: TextStyle(
                          color:
                              isDarkMode
                                  ? AppTheme.darkTextLightColor
                                  : AppTheme.textLightColor,
                        ),
                      ),
                    ],
                  ),
                )
                : _buildInteractiveSvgPreview(),
      ),
    );
  }

  /// 构建可交互的SVG预览
  Widget _buildInteractiveSvgPreview() {
    return InteractiveViewer(
      boundaryMargin: const EdgeInsets.all(20),
      minScale: 0.5,
      maxScale: 4.0,
      child: Center(
        child: SvgPicture.string(_svgController.text, fit: BoxFit.contain),
      ),
    );
  }
}
