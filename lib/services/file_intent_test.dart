import 'package:flutter/material.dart';
import 'file_intent_service.dart';

/// 文件意图测试页面
class FileIntentTestPage extends StatefulWidget {
  const FileIntentTestPage({super.key});

  @override
  State<FileIntentTestPage> createState() => _FileIntentTestPageState();
}

class _FileIntentTestPageState extends State<FileIntentTestPage> {
  final FileIntentService _fileIntentService = FileIntentService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('文件意图测试')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              '支持的文件格式：',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text('• Markdown文件 (.md, .markdown)'),
            const Text('• 文本文件 (.txt)'),
            const Text('• SVG图像 (.svg)'),
            const Text('• HTML文件 (.html, .htm)'),
            const SizedBox(height: 32),
            const Text(
              '使用说明：',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text('1. 在文件管理器中找到支持的文件类型'),
            const Text('2. 长按文件选择"打开方式"'),
            const Text('3. 选择 ContentPal 应用'),
            const Text('4. 应用会自动跳转到对应的编辑器'),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => _testTextContent(),
              child: const Text('测试文本内容处理'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _testMarkdownContent(),
              child: const Text('测试Markdown内容处理'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _testSvgContent(),
              child: const Text('测试SVG内容处理'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _testHtmlContent(),
              child: const Text('测试HTML内容处理'),
            ),
          ],
        ),
      ),
    );
  }

  void _testTextContent() {
    const testText = '''这是一个测试文本内容。

支持的功能：
• 文本编辑
• 格式化
• 导出为PDF
• 分享功能

点击保存按钮可以保存这个文本卡片。''';

    _fileIntentService.handleTextContent(context, testText, title: '测试文本内容');
  }

  void _testMarkdownContent() {
    const testMarkdown = '''# 测试Markdown文档

这是一个**测试**的Markdown文档。

## 支持的功能

1. **标题**：支持多级标题
2. **强调**：*斜体*和**粗体**
3. **列表**：有序和无序列表
4. **链接**：[Flutter官网](https://flutter.dev)
5. **代码**：`inline code`和代码块

```dart
void main() {
  print('Hello, World!');
}
```

## 表格

| 功能 | 状态 | 说明 |
|------|------|------|
| 编辑 | ✅ | 支持实时编辑 |
| 预览 | ✅ | 支持实时预览 |
| 导出 | ✅ | 支持PDF导出 |

> 这是一个引用文本示例。

---

**ContentPal** - 让内容创作更简单！''';

    // 创建临时文件来测试
    _simulateMarkdownFile(testMarkdown);
  }

  void _testSvgContent() {
    const testSvg =
        '''<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <circle cx="100" cy="100" r="80" fill="#3498db" stroke="#2c3e50" stroke-width="3"/>
  <text x="100" y="105" text-anchor="middle" fill="white" font-size="16" font-family="Arial">ContentPal</text>
</svg>''';

    _simulateSvgFile(testSvg);
  }

  void _testHtmlContent() {
    const testHtml = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ContentPal 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .content {
            margin-top: 20px;
        }
        .feature {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>ContentPal 测试页面</h1>
        <p>专业的内容创作工具</p>
    </div>
    
    <div class="content">
        <h2>主要功能</h2>
        
        <div class="feature">
            <h3>📝 文本编辑</h3>
            <p>支持富文本编辑，格式化文本，创建精美的文本卡片。</p>
        </div>
        
        <div class="feature">
            <h3>📄 Markdown编辑</h3>
            <p>支持Markdown语法，实时预览，一键导出PDF。</p>
        </div>
        
        <div class="feature">
            <h3>🎨 SVG编辑</h3>
            <p>可视化SVG编辑器，支持图形绘制和代码编辑。</p>
        </div>
        
        <div class="feature">
            <h3>🌐 HTML编辑</h3>
            <p>HTML代码编辑器，支持实时预览和响应式设计。</p>
        </div>
    </div>
    
    <footer style="text-align: center; margin-top: 40px; color: #666;">
        <p>&copy; 2024 ContentPal. 让内容创作更简单！</p>
    </footer>
</body>
</html>''';

    _simulateHtmlFile(testHtml);
  }

  void _simulateMarkdownFile(String content) {
    // 模拟从文件读取内容并导航到Markdown编辑器
    // 在实际应用中，这会由原生代码处理
    _fileIntentService.handleTextContent(
      context,
      content,
      title: '测试Markdown文档',
    );
  }

  void _simulateSvgFile(String content) {
    // 模拟从文件读取内容并导航到SVG编辑器
    // 在实际应用中，这会由原生代码处理
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('SVG内容处理演示 - 实际使用时会打开SVG编辑器')));
  }

  void _simulateHtmlFile(String content) {
    // 模拟从文件读取内容并导航到HTML编辑器
    // 在实际应用中，这会由原生代码处理
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('HTML内容处理演示 - 实际使用时会打开HTML编辑器')),
    );
  }
}
