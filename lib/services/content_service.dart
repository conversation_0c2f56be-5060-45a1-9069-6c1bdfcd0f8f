import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

import '../models/content_item.dart';
import 'content_render_service.dart';

/// 内容服务 - 负责内容项的CRUD操作
class ContentService {
  static const String _contentBoxName = 'content_items';
  static const String _contentFolderName = 'content_files';

  late Box<String> _contentBox;
  late Directory _contentFolder;
  late ContentRenderService _renderService;

  final List<ContentItem> _cachedItems = [];
  bool _isInitialized = false;

  /// 单例实例
  static final ContentService _instance = ContentService._internal();

  /// 获取单例实例
  factory ContentService() => _instance;

  ContentService._internal();

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    // 打开Hive盒子
    _contentBox = await Hive.openBox<String>(_contentBoxName);

    // 创建内容文件夹
    final appDocDir = await getApplicationDocumentsDirectory();
    _contentFolder = Directory(path.join(appDocDir.path, _contentFolderName));

    if (!await _contentFolder.exists()) {
      await _contentFolder.create(recursive: true);
    }

    // 初始化渲染服务
    _renderService = ContentRenderService();
    await _renderService.initialize();

    // 加载所有缓存项
    await _loadAllItems();

    _isInitialized = true;
  }

  /// 加载所有内容项到缓存
  Future<void> _loadAllItems() async {
    _cachedItems.clear();

    for (final jsonStr in _contentBox.values) {
      try {
        final json = jsonDecode(jsonStr);
        final item = ContentItem.fromJson(json);
        _cachedItems.add(item);
      } catch (e) {
        debugPrint('加载内容项失败: $e');
      }
    }

    // 按更新时间排序（最新的在前面）
    _cachedItems.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  /// 获取所有内容项
  List<ContentItem> getAllItems() {
    return List.unmodifiable(_cachedItems);
  }

  /// 按类型获取内容项
  List<ContentItem> getItemsByType(ContentType type) {
    return _cachedItems.where((item) => item.type == type).toList();
  }

  /// 按标签获取内容项
  List<ContentItem> getItemsByTag(String tag) {
    return _cachedItems.where((item) => item.tags.contains(tag)).toList();
  }

  /// 获取收藏的内容项
  List<ContentItem> getFavoriteItems() {
    return _cachedItems.where((item) => item.isFavorite).toList();
  }

  /// 按ID获取内容项
  ContentItem? getItemById(String id) {
    try {
      return _cachedItems.firstWhere((item) => item.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 创建新的文本内容项
  Future<ContentItem> createTextContent({
    required String title,
    required ContentType type,
    required String content,
    List<String>? tags,
  }) async {
    assert(
      type == ContentType.markdown || type == ContentType.html,
      '必须是文本类型内容',
    );

    final item = ContentItem(
      title: title,
      type: type,
      content: content,
      tags: tags,
    );

    await _saveItem(item);
    return item;
  }

  /// 创建新的图像内容项
  Future<ContentItem> createImageContent({
    required String title,
    required Uint8List imageData,
    required ContentType type,
    List<String>? tags,
    String? filePath,
  }) async {
    assert(type == ContentType.image || type == ContentType.svg, '必须是图像类型内容');

    // 保存图像文件
    final imagePath = await _saveContentFile(
      data: imageData,
      fileExt: type == ContentType.svg ? 'svg' : 'png',
    );

    // 创建内容项
    final item = ContentItem(
      title: title,
      type: type,
      content: imagePath, // 保存文件路径而不是二进制数据
      tags: tags,
      filePath: filePath,
      fileSize: imageData.length,
    );

    await _saveItem(item);
    return item;
  }

  /// 更新内容项
  Future<ContentItem> updateItem(ContentItem updatedItem) async {
    // 确认项目存在
    final existingIndex = _cachedItems.indexWhere(
      (i) => i.id == updatedItem.id,
    );
    if (existingIndex == -1) {
      throw Exception('内容项不存在');
    }

    // 更新更新时间
    final itemToUpdate = updatedItem.copyWith(updatedAt: DateTime.now());

    await _saveItem(itemToUpdate);

    // 更新缓存
    _cachedItems[existingIndex] = itemToUpdate;

    return itemToUpdate;
  }

  /// 删除内容项
  Future<void> deleteItem(String id) async {
    final item = getItemById(id);
    if (item == null) return;

    // 删除关联的文件（如果有）
    if (item.type == ContentType.image || item.type == ContentType.svg) {
      if (item.content is String &&
          (item.content as String).startsWith(_contentFolderName)) {
        final file = File(item.content as String);
        if (await file.exists()) {
          await file.delete();
        }
      }
    }

    // 从Hive中删除
    await _contentBox.delete(id);

    // 从缓存中删除
    _cachedItems.removeWhere((item) => item.id == id);
  }

  /// 切换收藏状态
  Future<ContentItem> toggleFavorite(String id) async {
    final item = getItemById(id);
    if (item == null) {
      throw Exception('内容项不存在');
    }

    final updatedItem = item.copyWith(isFavorite: !item.isFavorite);
    return await updateItem(updatedItem);
  }

  /// 添加标签
  Future<ContentItem> addTag(String id, String tag) async {
    final item = getItemById(id);
    if (item == null) {
      throw Exception('内容项不存在');
    }

    if (!item.tags.contains(tag)) {
      final tags = List<String>.from(item.tags)..add(tag);
      final updatedItem = item.copyWith(tags: tags);
      return await updateItem(updatedItem);
    }

    return item;
  }

  /// 删除标签
  Future<ContentItem> removeTag(String id, String tag) async {
    final item = getItemById(id);
    if (item == null) {
      throw Exception('内容项不存在');
    }

    if (item.tags.contains(tag)) {
      final tags = List<String>.from(item.tags)..remove(tag);
      final updatedItem = item.copyWith(tags: tags);
      return await updateItem(updatedItem);
    }

    return item;
  }

  /// 保存内容文件到磁盘
  Future<String> _saveContentFile({
    required Uint8List data,
    required String fileExt,
  }) async {
    final fileName = '${const Uuid().v4()}.$fileExt';
    final filePath = path.join(_contentFolder.path, fileName);

    final file = File(filePath);
    await file.writeAsBytes(data);

    return filePath;
  }

  /// 加载内容文件
  Future<Uint8List?> loadContentFile(String filePath) async {
    final file = File(filePath);

    if (await file.exists()) {
      return await file.readAsBytes();
    }

    return null;
  }

  /// 保存内容项到Hive
  Future<void> _saveItem(ContentItem item) async {
    final json = jsonEncode(item.toJson());
    await _contentBox.put(item.id, json);

    // 更新缓存
    final existingIndex = _cachedItems.indexWhere((i) => i.id == item.id);
    if (existingIndex != -1) {
      _cachedItems[existingIndex] = item;
    } else {
      _cachedItems.add(item);
    }

    // 重新排序
    _cachedItems.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  /// 获取所有唯一标签
  Set<String> getAllTags() {
    final tags = <String>{};
    for (final item in _cachedItems) {
      tags.addAll(item.tags);
    }
    return tags;
  }

  /// 搜索内容项
  List<ContentItem> searchItems(String query) {
    final lowerQuery = query.toLowerCase();

    return _cachedItems.where((item) {
      return item.title.toLowerCase().contains(lowerQuery) ||
          (item.type == ContentType.markdown ||
                  item.type == ContentType.html) &&
              (item.content as String).toLowerCase().contains(lowerQuery) ||
          item.tags.any((tag) => tag.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// 为内容项生成渲染结果
  Future<ContentItem> generateRenderResult(
    ContentItem item, {
    bool isBlockMode = false,
  }) async {
    if (!_isInitialized) {
      throw Exception('ContentService 未初始化');
    }

    try {
      switch (item.type) {
        case ContentType.markdown:
        case ContentType.markdownBlocks:
          final renderData = await _renderService.renderMarkdown(
            content: item.content as String,
            isBlockMode: isBlockMode || item.type == ContentType.markdownBlocks,
          );

          final updatedItem = item.copyWith(
            type:
                isBlockMode ? ContentType.markdownBlocks : ContentType.markdown,
            renderData: renderData,
          );

          return await updateItem(updatedItem);

        case ContentType.textCard:
        case ContentType.textCardCollection:
          // 解析文本卡片数据
          final cardData = jsonDecode(item.content as String);
          final isCollection = item.type == ContentType.textCardCollection;

          final renderData = await _renderService.renderTextCard(
            cardData: cardData,
            isCollection: isCollection,
          );

          final updatedItem = item.copyWith(renderData: renderData);
          return await updateItem(updatedItem);

        case ContentType.voice:
          final renderData = await _renderService.renderVoiceWaveform(
            audioPath: item.content as String,
          );

          final updatedItem = item.copyWith(renderData: renderData);
          return await updateItem(updatedItem);

        case ContentType.pdf:
          final renderData = await _renderService.renderPdfPreview(
            pdfPath: item.content as String,
          );

          final updatedItem = item.copyWith(renderData: renderData);
          return await updateItem(updatedItem);

        default:
          // 其他类型不需要渲染
          return item;
      }
    } catch (e) {
      debugPrint('渲染失败: $e');
      return item;
    }
  }

  /// 重新渲染内容项
  Future<ContentItem> reRenderItem(
    String itemId, {
    bool isBlockMode = false,
  }) async {
    final item = getItemById(itemId);
    if (item == null) {
      throw Exception('内容项不存在');
    }

    // 删除旧的渲染文件
    if (item.renderData != null) {
      await _renderService.deleteRenderFiles(item.renderData!);
    }

    // 生成新的渲染结果
    return await generateRenderResult(item, isBlockMode: isBlockMode);
  }

  /// 清理所有渲染文件
  Future<void> clearAllRenderFiles() async {
    if (_isInitialized) {
      await _renderService.clearAllRenderFiles();
    }
  }
}
