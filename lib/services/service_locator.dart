import 'package:flutter/material.dart';

import '../html/html_service.dart';
import '../subscription/subscription_service.dart';
import '../svg/svg_service.dart';
import 'content_service.dart';
import 'settings_service.dart';
import 'storage_service.dart';

/// 服务定位器/管理器，负责初始化和管理所有服务
class ServiceLocator {
  // 单例模式
  static final ServiceLocator _instance = ServiceLocator._internal();
  factory ServiceLocator() => _instance;
  ServiceLocator._internal();

  // 全局key，用于重建整个应用
  final GlobalKey<NavigatorState> globalKey = GlobalKey<NavigatorState>();

  // 服务实例 - 使用懒加载方式初始化
  late final StorageService _storageService = StorageService();
  late final SettingsService _settingsService = SettingsService();
  late final SvgService _svgService = SvgService();
  late final HtmlService _htmlService = HtmlService();
  late final SubscriptionService _subscriptionService = SubscriptionService();
  final ContentService _contentService = ContentService();

  bool _isInitialized = false;

  /// 初始化所有服务
  Future<void> initServices() async {
    if (_isInitialized) return;

    try {
      debugPrint('正在初始化服务...');

      // 初始化内容服务
      await _contentService.initialize();
      debugPrint('内容服务初始化完成');

      // 按依赖顺序初始化
      await _storageService.init();
      await _settingsService.init();
      await _subscriptionService.init();
      await _svgService.initialize();
      await _htmlService.initialize();
      // API管理器服务不需要额外的初始化
      // 记忆服务不需要额外的初始化
      // 文件上传服务不需要额外的初始化

      _isInitialized = true;
      debugPrint('所有服务初始化完成');
    } catch (e) {
      debugPrint('服务初始化失败: $e');
    }
  }

  /// 获取存储服务
  StorageService get storageService => _storageService;

  /// 获取设置服务
  SettingsService get settingsService => _settingsService;

  /// 获取SVG服务
  SvgService get svgService => _svgService;

  /// 获取HTML服务
  HtmlService get htmlService => _htmlService;

  /// 获取订阅服务
  SubscriptionService get subscriptionService => _subscriptionService;

  /// 获取内容服务
  ContentService get contentService => _contentService;

  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;
}
