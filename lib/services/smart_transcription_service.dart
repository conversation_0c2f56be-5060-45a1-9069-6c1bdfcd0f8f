import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_recognition_error.dart';
import 'package:permission_handler/permission_handler.dart';

/// 转录状态
enum TranscriptionState { idle, transcribing, completed, error }

/// 简化的智能转录服务
class SmartTranscriptionService {
  static final SmartTranscriptionService _instance =
      SmartTranscriptionService._internal();
  factory SmartTranscriptionService() => _instance;
  SmartTranscriptionService._internal();

  // 语音识别实例
  SpeechToText? _speechToText;
  bool _isInitialized = false;

  // 状态管理
  final ValueNotifier<TranscriptionState> state =
      ValueNotifier<TranscriptionState>(TranscriptionState.idle);

  // 转录结果流
  final StreamController<String> _resultController =
      StreamController<String>.broadcast();
  Stream<String> get resultStream => _resultController.stream;

  // 错误信息流
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();
  Stream<String> get errorStream => _errorController.stream;

  // 当前转录文本
  String _currentText = '';
  String get currentText => _currentText;

  /// 初始化服务
  Future<bool> initialize() async {
    try {
      debugPrint('SmartTranscriptionService: 开始初始化');

      // 简单的权限检查
      await _requestPermissions();

      // 初始化语音识别
      _speechToText = SpeechToText();
      final success = await _speechToText!.initialize(
        onError: _handleError,
        onStatus: _handleStatus,
      );

      if (success) {
        _isInitialized = true;
        debugPrint('SmartTranscriptionService: 初始化成功');
        return true;
      } else {
        debugPrint('SmartTranscriptionService: 语音识别初始化失败');
        _errorController.add('语音识别服务初始化失败');
        return false;
      }
    } catch (e) {
      debugPrint('SmartTranscriptionService: 初始化异常: $e');
      _errorController.add('初始化失败: $e');
      return false;
    }
  }

  /// 请求权限
  Future<void> _requestPermissions() async {
    try {
      if (Platform.isIOS) {
        await Permission.microphone.request();
        await Permission.speech.request();
      } else if (Platform.isAndroid) {
        await Permission.microphone.request();
      }
    } catch (e) {
      debugPrint('权限请求异常: $e');
    }
  }

  /// 开始实时转录
  Future<bool> startRealtimeTranscription({String language = 'zh_CN'}) async {
    if (!_isInitialized || _speechToText == null) {
      debugPrint('SmartTranscriptionService: 服务未初始化');
      return false;
    }

    try {
      state.value = TranscriptionState.transcribing;

      // listen方法可能返回void或其他类型，我们直接调用不获取返回值
      await _speechToText!.listen(
        onResult: _handleResult,
        localeId: language,
        listenFor: const Duration(minutes: 30),
        pauseFor: const Duration(seconds: 3),
        listenOptions: SpeechListenOptions(
          partialResults: true,
          cancelOnError: false,
          listenMode: ListenMode.dictation,
        ),
      );

      debugPrint('SmartTranscriptionService: 实时转录已开始');
      return true;
    } catch (e) {
      debugPrint('SmartTranscriptionService: 语音识别启动异常: $e');
      state.value = TranscriptionState.error;
      _errorController.add('启动转录失败: $e');
      return false;
    }
  }

  /// 停止实时转录
  Future<void> stopRealtimeTranscription() async {
    if (_speechToText != null) {
      try {
        await _speechToText!.stop();
        state.value = TranscriptionState.completed;
        debugPrint('SmartTranscriptionService: 实时转录已停止');
      } catch (e) {
        debugPrint('SmartTranscriptionService: 停止转录异常: $e');
      }
    }
  }

  /// 处理识别结果
  void _handleResult(SpeechRecognitionResult result) {
    _currentText = result.recognizedWords;
    _resultController.add(_currentText);
    debugPrint('SmartTranscriptionService: 识别结果: $_currentText');
  }

  /// 处理错误
  void _handleError(SpeechRecognitionError error) {
    debugPrint('SmartTranscriptionService: 识别错误: ${error.errorMsg}');
    state.value = TranscriptionState.error;
    _errorController.add('语音识别错误: ${error.errorMsg}');
  }

  /// 处理状态变化
  void _handleStatus(String status) {
    debugPrint('SmartTranscriptionService: 状态变化: $status');

    switch (status) {
      case 'listening':
        state.value = TranscriptionState.transcribing;
        break;
      case 'notListening':
        if (state.value == TranscriptionState.transcribing) {
          state.value = TranscriptionState.completed;
        }
        break;
      case 'done':
        state.value = TranscriptionState.completed;
        break;
    }
  }

  /// 清除结果
  void clearResults() {
    _currentText = '';
    _resultController.add('');
    state.value = TranscriptionState.idle;
  }

  /// 获取支持的语言
  List<String> getSupportedLanguages() {
    return ['zh_CN', 'en_US', 'ja_JP', 'ko_KR'];
  }

  /// 释放资源
  void dispose() {
    _speechToText?.stop();
    _speechToText?.cancel();
    _resultController.close();
    _errorController.close();
  }
}
