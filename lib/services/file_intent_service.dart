import 'dart:io';

import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;

import '../html/html_editor_screen.dart';
import '../markdown/markdown_render_screen.dart';
import '../svg/svg_editor_screen.dart';
import '../text_cards/models/text_card_model.dart';
import '../text_cards/text_card_editor_page.dart';

enum FileType { markdown, text, svg, html, unknown }

class FileIntentService {
  static final FileIntentService _instance = FileIntentService._internal();
  factory FileIntentService() => _instance;
  FileIntentService._internal();

  /// 获取文件类型
  FileType getFileType(String filePath) {
    final extension = path.extension(filePath).toLowerCase();

    switch (extension) {
      case '.md':
      case '.markdown':
        return FileType.markdown;
      case '.txt':
      case '.text':
        return FileType.text;
      case '.svg':
        return FileType.svg;
      case '.html':
      case '.htm':
        return FileType.html;
      default:
        return FileType.unknown;
    }
  }

  /// 根据文件类型导航到对应的编辑器
  Future<void> navigateToEditor(
    BuildContext context,
    String filePath,
    FileType fileType,
  ) async {
    // 读取文件内容
    String? fileContent;
    try {
      final file = File(filePath);
      if (file.existsSync()) {
        fileContent = await file.readAsString();

        // 验证文件内容
        if (fileContent.isEmpty) {
          _showErrorDialog(context, '文件为空', '选择的文件没有内容');
          return;
        }

        // 检查文件大小（限制为10MB）
        final fileSize = file.lengthSync();
        if (fileSize > 10 * 1024 * 1024) {
          _showErrorDialog(context, '文件过大', '文件大小超过10MB，无法打开');
          return;
        }

        // 清理内容
        fileContent = _cleanFileContent(fileContent);
      }
    } catch (e) {
      debugPrint('读取文件失败: $e');
      _showErrorDialog(context, '读取文件失败', '无法读取文件: $e');
      return;
    }

    if (fileContent == null || fileContent.trim().isEmpty) {
      _showErrorDialog(context, '文件内容为空', '无法读取文件内容或文件为空');
      return;
    }

    final fileName = path.basenameWithoutExtension(filePath);

    switch (fileType) {
      case FileType.markdown:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => MarkdownRenderScreen(initialMarkdown: fileContent),
          ),
        );
        break;

      case FileType.text:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => TextCardEditorPage(
                  existingCard:
                      fileContent != null
                          ? TextCardModel(
                            id:
                                DateTime.now().millisecondsSinceEpoch
                                    .toString(),
                            title: fileName,
                            content: fileContent,
                            templateId: 'card_shadow',
                            createdAt: DateTime.now(),
                            updatedAt: DateTime.now(),
                          )
                          : null,
                ),
          ),
        );
        break;

      case FileType.svg:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => SvgEditorScreen(
                  initialSvgContent: fileContent,
                  initialTitle: fileName,
                ),
          ),
        );
        break;

      case FileType.html:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => HtmlEditorScreen(
                  initialHtmlContent: fileContent,
                  initialTitle: fileName,
                ),
          ),
        );
        break;

      case FileType.unknown:
        _showUnsupportedFileDialog(context, filePath);
        break;
    }
  }

  /// 清理文件内容
  String _cleanFileContent(String content) {
    // 移除BOM字符
    if (content.startsWith('\uFEFF')) {
      content = content.substring(1);
    }

    // 统一换行符
    content = content.replaceAll('\r\n', '\n').replaceAll('\r', '\n');

    // 移除null字符和其他控制字符
    content = content
        .replaceAll('\u0000', '')
        .replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');

    return content.trim();
  }

  /// 处理文件打开请求
  Future<void> handleFileOpen(BuildContext context, String filePath) async {
    final fileType = getFileType(filePath);
    await navigateToEditor(context, filePath, fileType);
  }

  /// 处理文本内容
  void handleTextContent(
    BuildContext context,
    String content, {
    String? title,
  }) {
    final textCard = TextCardModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title ?? '新建文本',
      content: content,
      templateId: 'card_shadow',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TextCardEditorPage(existingCard: textCard),
      ),
    );
  }

  /// 显示不支持的文件类型对话框
  void _showUnsupportedFileDialog(BuildContext context, String filePath) {
    final fileName = path.basename(filePath);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('不支持的文件类型'),
          content: Text(
            '文件 "$fileName" 的类型暂时不支持。\n\n支持的文件类型：\n• Markdown (.md, .markdown)\n• 文本文件 (.txt)\n• SVG图像 (.svg)\n• HTML文件 (.html, .htm)',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  /// 显示错误对话框
  void _showErrorDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  /// 清理资源
  void dispose() {
    // 这里可以添加资源清理逻辑
  }
}
