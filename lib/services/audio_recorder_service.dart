import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:uuid/uuid.dart';

class AudioRecorderService {
  static final AudioRecorderService _instance =
      AudioRecorderService._internal();
  factory AudioRecorderService() => _instance;

  // 延迟初始化，避免在构造函数中创建录音机实例
  AudioRecorderService._internal();

  // 录音机 - 延迟初始化
  FlutterSoundRecorder? _recorder;
  bool _isRecorderInitialized = false;

  // 录音状态
  final ValueNotifier<bool> isRecording = ValueNotifier<bool>(false);

  // 音频文件目录
  late Directory _audioDirectory;

  // 当前录音文件路径
  String? _currentRecordingPath;

  // 录音时长
  final ValueNotifier<Duration> recordingDuration = ValueNotifier<Duration>(
    Duration.zero,
  );
  Timer? _recordingTimer;

  // 检查麦克风权限，不请求，只检查现有状态
  Future<bool> hasPermission() async {
    try {
      if (Platform.isIOS) {
        // iOS需要检查麦克风和语音识别权限
        PermissionStatus micStatus = await Permission.microphone.status;
        PermissionStatus speechStatus = await Permission.speech.status;
        debugPrint(
          "hasPermission检查 - iOS麦克风权限状态: $micStatus, 语音识别权限: $speechStatus",
        );

        return micStatus.isGranted && speechStatus.isGranted;
      } else if (Platform.isAndroid) {
        // Android需要检查麦克风权限和可能的音频权限
        PermissionStatus micStatus = await Permission.microphone.status;
        PermissionStatus audioStatus = await Permission.audio.status;
        debugPrint(
          "hasPermission检查 - Android麦克风权限状态: $micStatus, 音频权限: $audioStatus",
        );

        // 对于Android 13+，需要两个权限都通过
        // 对于较早的Android版本，只需要麦克风权限
        int? androidVersion = int.tryParse(
          Platform.operatingSystemVersion.split(' ').first,
        );
        bool needsAudioPermission =
            androidVersion != null && androidVersion >= 13;

        if (needsAudioPermission) {
          return micStatus.isGranted && audioStatus.isGranted;
        } else {
          return micStatus.isGranted;
        }
      }

      // 其他平台只检查麦克风权限
      PermissionStatus micStatus = await Permission.microphone.status;
      return micStatus.isGranted;
    } catch (e) {
      debugPrint("权限检查失败: $e");
      return false;
    }
  }

  // 检查并请求麦克风权限
  Future<bool> checkPermission() async {
    try {
      // 首先检查是否已有权限
      if (await hasPermission()) {
        debugPrint("已有必要权限");
        return true;
      }

      debugPrint("没有必要权限，尝试请求");

      if (Platform.isIOS) {
        // iOS麦克风权限检查
        PermissionStatus micStatus = await Permission.microphone.status;
        PermissionStatus speechStatus = await Permission.speech.status;
        debugPrint("iOS麦克风权限状态: $micStatus, 语音识别权限: $speechStatus");

        // 请求麦克风权限
        if (!micStatus.isGranted) {
          debugPrint("尝试请求iOS麦克风权限");
          micStatus = await Permission.microphone.request();
          debugPrint("请求后iOS麦克风权限状态: $micStatus");
        }

        // 请求语音识别权限
        if (!speechStatus.isGranted) {
          debugPrint("尝试请求iOS语音识别权限");
          speechStatus = await Permission.speech.request();
          debugPrint("请求后iOS语音识别权限状态: $speechStatus");
        }

        return micStatus.isGranted && speechStatus.isGranted;
      } else if (Platform.isAndroid) {
        // Android麦克风权限检查
        PermissionStatus micStatus = await Permission.microphone.status;
        debugPrint("Android麦克风权限状态: $micStatus");

        // 如果权限未被授予且未被永久拒绝，请求权限
        if (!micStatus.isGranted && !micStatus.isPermanentlyDenied) {
          micStatus = await Permission.microphone.request();
          debugPrint("请求后的麦克风权限状态: $micStatus");
        }

        // 如果在Android 13及以上，还需要检查音频权限
        PermissionStatus audioStatus = await Permission.audio.status;
        debugPrint("媒体音频权限状态: $audioStatus");

        if (!audioStatus.isGranted && !audioStatus.isPermanentlyDenied) {
          audioStatus = await Permission.audio.request();
          debugPrint("请求后的媒体音频权限状态: $audioStatus");
        }

        // 检查是否有任一权限被永久拒绝
        if (micStatus.isPermanentlyDenied || audioStatus.isPermanentlyDenied) {
          debugPrint("麦克风或媒体音频权限被永久拒绝");
          return false;
        }

        // 对于Android 13+，需要两个权限都通过
        int? androidVersion = int.tryParse(
          Platform.operatingSystemVersion.split(' ').first,
        );
        bool needsAudioPermission =
            androidVersion != null && androidVersion >= 13;

        if (needsAudioPermission) {
          return micStatus.isGranted && audioStatus.isGranted;
        } else {
          return micStatus.isGranted;
        }
      }

      // 其他平台，只检查麦克风权限
      PermissionStatus status = await Permission.microphone.request();
      return status.isGranted;
    } catch (e) {
      debugPrint("权限检查失败: $e");
      return false;
    }
  }

  // 获取音频文件目录
  Future<void> _createAudioDirectory() async {
    try {
      // 获取应用文档目录
      final appDocDir = await getApplicationDocumentsDirectory();
      _audioDirectory = Directory('${appDocDir.path}/audio_records');

      debugPrint("尝试创建或访问音频目录: ${_audioDirectory.path}");

      // 检查目录是否存在，不存在则创建
      if (!await _audioDirectory.exists()) {
        debugPrint("音频目录不存在，正在创建...");
        await _audioDirectory.create(recursive: true);
        debugPrint("音频目录创建完成: ${_audioDirectory.path}");
      } else {
        debugPrint("音频目录已存在: ${_audioDirectory.path}");

        // 检查目录权限
        try {
          final testFile = File('${_audioDirectory.path}/test.txt');
          await testFile.writeAsString('测试写入权限');
          await testFile.delete();
          debugPrint("目录权限检查通过，可以读写");
        } catch (e) {
          debugPrint("目录权限检查失败: $e");
          // 尝试重新创建目录
          try {
            await _audioDirectory.delete(recursive: true);
            await _audioDirectory.create(recursive: true);
            debugPrint("已重新创建音频目录");
          } catch (e) {
            debugPrint("重新创建音频目录失败: $e");
            throw Exception("无法创建或访问音频目录: $e");
          }
        }
      }
    } catch (e) {
      debugPrint("创建音频目录失败: $e");
      rethrow;
    }
  }

  // 初始化
  Future<void> init() async {
    try {
      // 检查权限
      bool hasPermission = await checkPermission();
      if (!hasPermission) {
        throw Exception('没有获得麦克风权限，请在设置中允许访问麦克风');
      }

      // 处理录音机初始化 - 延迟到此处初始化
      if (!_isRecorderInitialized) {
        debugPrint("正在创建录音机实例...");
        _recorder = FlutterSoundRecorder();

        debugPrint("正在初始化录音机...");
        await _recorder?.openRecorder();
        _isRecorderInitialized = true;
        debugPrint("录音机初始化完成");
      }

      // 获取并创建音频文件目录
      await _createAudioDirectory();
      debugPrint("音频目录路径: ${_audioDirectory.path}");

      // 确保目录存在
      if (!await _audioDirectory.exists()) {
        throw Exception('音频文件目录创建失败');
      }
    } catch (e) {
      debugPrint('初始化录音服务失败: $e');
      rethrow;
    }
  }

  // 开始录音
  Future<void> startRecording() async {
    if (!_isRecorderInitialized) {
      await init();
    }

    // 检查录音机是否已经在录音
    if (isRecording.value || (_recorder?.isRecording ?? false)) {
      debugPrint("录音机已在录音状态，先停止当前录音");
      try {
        await _recorder?.stopRecorder();
        isRecording.value = false;
        _recordingTimer?.cancel();
        _recordingTimer = null;
        // 等待一小段时间确保录音机状态重置
        await Future.delayed(const Duration(milliseconds: 100));
      } catch (e) {
        debugPrint("停止现有录音失败: $e");
      }
    }

    try {
      // 确保目录存在
      if (!await _audioDirectory.exists()) {
        debugPrint("录音前发现音频目录不存在，正在重新创建...");
        await _createAudioDirectory();
      }

      // 创建新的录音文件
      final uuid = const Uuid().v4();
      final filePath = '${_audioDirectory.path}/$uuid.aac';
      _currentRecordingPath = filePath;

      debugPrint("开始录制到文件: $filePath");

      // 确保录音机状态正确
      if (_recorder?.isRecording ?? false) {
        debugPrint("录音机仍在录音状态，强制停止");
        await _recorder?.stopRecorder();
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // 开始录音 - 使用更高质量设置
      await _recorder?.startRecorder(
        toFile: filePath,
        codec: Codec.aacADTS,
        audioSource: AudioSource.microphone,
        // 添加录音质量配置
        sampleRate: 44100, // 更高采样率
        bitRate: 32000, // 更高比特率
      );

      // 验证录音是否真正开始
      await Future.delayed(const Duration(milliseconds: 100));
      if (!(_recorder?.isRecording ?? false)) {
        throw Exception('录音启动失败，录音机状态异常');
      }

      isRecording.value = true;
      debugPrint("录音机状态: ${_recorder?.isRecording}");

      // 启动计时器
      recordingDuration.value = Duration.zero;
      _recordingTimer?.cancel();
      _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (
        timer,
      ) {
        recordingDuration.value = Duration(milliseconds: timer.tick * 100);
      });

      debugPrint("录音开始成功，文件路径: $filePath");
    } catch (e) {
      debugPrint('开始录音失败: $e');
      // 重置状态
      isRecording.value = false;
      _recordingTimer?.cancel();
      _recordingTimer = null;
      _currentRecordingPath = null;
      rethrow;
    }
  }

  // 停止录音
  Future<String?> stopRecording() async {
    if (!_isRecorderInitialized || !isRecording.value) {
      debugPrint("录音机未初始化或未在录音状态");
      return null;
    }

    try {
      // 确保录音至少持续1秒
      final currentDuration = recordingDuration.value;
      if (currentDuration.inMilliseconds < 1000) {
        final waitTime = 1000 - currentDuration.inMilliseconds;
        debugPrint("录音时间过短 ($currentDuration)，等待 $waitTime 毫秒后停止...");
        await Future.delayed(Duration(milliseconds: waitTime));
      }

      debugPrint("停止录音...");
      // 记录当前录音的持续时间，确保录音文件路径保存正确
      final currentPath = _currentRecordingPath;

      // 停止录音
      String? path = await _recorder?.stopRecorder();
      isRecording.value = false;
      debugPrint(
        "录音已停止，返回路径: $path, 预期路径: $currentPath, 持续时间: ${recordingDuration.value}",
      );

      // 停止计时器
      _recordingTimer?.cancel();
      _recordingTimer = null;

      // 优先使用当前路径，如果为空则使用返回的路径
      String? finalPath = currentPath ?? path;

      if (finalPath != null) {
        final file = File(finalPath);
        final exists = await file.exists();
        debugPrint("检查录音文件是否存在: $exists (${file.path})");

        if (exists) {
          final fileSize = await file.length();
          debugPrint("录音文件大小: $fileSize 字节");

          // 检查文件是否为空或过小
          if (fileSize < 100) {
            // 如果文件小于100字节，可能录音失败
            debugPrint("警告: 录音文件过小，可能录音失败。尝试修复...");

            // 文件过小，可能是录音时间太短，删除并返回null
            try {
              await file.delete();
              debugPrint("已删除无效的录音文件");
              return null;
            } catch (e) {
              debugPrint("删除无效文件失败: $e");
            }
          }

          return finalPath;
        } else {
          debugPrint("错误: 录音文件不存在");
          return null;
        }
      }

      debugPrint("错误: 无法获取录音文件路径");
      return null;
    } catch (e) {
      debugPrint('停止录音失败: $e');
      // 重置状态
      isRecording.value = false;
      _recordingTimer?.cancel();
      _recordingTimer = null;
      return null;
    }
  }

  // 暂停录音
  Future<void> pauseRecording() async {
    if (!_isRecorderInitialized || !isRecording.value) {
      return;
    }

    try {
      await _recorder?.pauseRecorder();
      _recordingTimer?.cancel();
    } catch (e) {
      debugPrint('暂停录音失败: $e');
    }
  }

  // 恢复录音
  Future<void> resumeRecording() async {
    if (!_isRecorderInitialized || isRecording.value) {
      return;
    }

    try {
      await _recorder?.resumeRecorder();

      // 重新启动计时器
      _recordingTimer = Timer.periodic(const Duration(milliseconds: 100), (
        timer,
      ) {
        recordingDuration.value =
            recordingDuration.value + const Duration(milliseconds: 100);
      });
    } catch (e) {
      debugPrint('恢复录音失败: $e');
    }
  }

  // 获取所有录音文件
  Future<List<File>> getAllRecordings() async {
    if (!await _audioDirectory.exists()) {
      return [];
    }

    final List<FileSystemEntity> entities =
        await _audioDirectory.list().toList();
    return entities
        .whereType<File>()
        .where((file) => file.path.endsWith('.aac'))
        .toList();
  }

  // 删除录音文件
  Future<void> deleteRecording(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      await file.delete();
    }
  }

  // 重命名录音文件
  Future<String?> renameRecording(String oldPath, String newName) async {
    final file = File(oldPath);
    if (!await file.exists()) {
      return null;
    }

    // 保留原始扩展名
    final extension = oldPath.split('.').last;
    final newPath = '${_audioDirectory.path}/$newName.$extension';

    await file.rename(newPath);
    return newPath;
  }

  // 获取音频文件目录路径
  String get audioDirectoryPath => _audioDirectory.path;

  // 销毁资源
  Future<void> dispose() async {
    _recordingTimer?.cancel();
    recordingDuration.dispose();
    isRecording.dispose();

    if (_isRecorderInitialized) {
      await _recorder?.closeRecorder();
      _isRecorderInitialized = false;
    }
  }
}
