import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../config/constants.dart';

/// 存储服务，使用Hive替代SharedPreferences
/// 提供统一的数据存储和读取接口
class StorageService {
  late Box _box;

  // 单例模式
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  // 初始化
  Future<void> init() async {
    // 初始化Hive
    await Hive.initFlutter();

    // 打开默认Box
    _box = await Hive.openBox(AppConstants.mainBoxName);

    debugPrint('StorageService初始化完成，使用Hive存储');
  }

  // 存储字符串
  Future<void> setString(String key, String value) async {
    try {
      await _box.put(key, value);
      debugPrint('Hive存储字符串成功: $key');
    } catch (e, stackTrace) {
      debugPrint('Hive存储字符串失败: $key, 错误: $e');
      debugPrint('错误堆栈: $stackTrace');
      rethrow;
    }
  }

  // 获取字符串
  String? getString(String key) {
    try {
      final value = _box.get(key);
      return value as String?;
    } catch (e) {
      debugPrint('Hive获取字符串失败: $key, 错误: $e');
      return null;
    }
  }

  // 存储布尔值
  Future<void> setBool(String key, bool value) async {
    try {
      await _box.put(key, value);
    } catch (e) {
      debugPrint('Hive存储布尔值失败: $key, 错误: $e');
      rethrow;
    }
  }

  // 获取布尔值
  bool? getBool(String key) {
    try {
      final value = _box.get(key);
      return value as bool?;
    } catch (e) {
      debugPrint('Hive获取布尔值失败: $key, 错误: $e');
      return null;
    }
  }

  // 存储整数
  Future<void> setInt(String key, int value) async {
    try {
      await _box.put(key, value);
    } catch (e) {
      debugPrint('Hive存储整数失败: $key, 错误: $e');
      rethrow;
    }
  }

  // 获取整数
  int? getInt(String key) {
    try {
      final value = _box.get(key);
      return value as int?;
    } catch (e) {
      debugPrint('Hive获取整数失败: $key, 错误: $e');
      return null;
    }
  }

  // 存储双精度浮点数
  Future<void> setDouble(String key, double value) async {
    try {
      await _box.put(key, value);
    } catch (e) {
      debugPrint('Hive存储浮点数失败: $key, 错误: $e');
      rethrow;
    }
  }

  // 获取双精度浮点数
  double? getDouble(String key) {
    try {
      final value = _box.get(key);
      return value as double?;
    } catch (e) {
      debugPrint('Hive获取浮点数失败: $key, 错误: $e');
      return null;
    }
  }

  // 存储字符串列表
  Future<void> setStringList(String key, List<String> value) async {
    try {
      await _box.put(key, value);
    } catch (e) {
      debugPrint('Hive存储字符串列表失败: $key, 错误: $e');
      rethrow;
    }
  }

  // 获取字符串列表
  List<String>? getStringList(String key) {
    try {
      final value = _box.get(key);
      if (value is List) {
        return value.cast<String>();
      }
      return null;
    } catch (e) {
      debugPrint('Hive获取字符串列表失败: $key, 错误: $e');
      return null;
    }
  }

  // 存储JSON对象
  Future<void> setJson(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = json.encode(value);
      await _box.put(key, jsonString);
    } catch (e) {
      debugPrint('Hive存储JSON失败: $key, 错误: $e');
      rethrow;
    }
  }

  // 获取JSON对象
  Map<String, dynamic>? getJson(String key) {
    try {
      final jsonString = _box.get(key);
      if (jsonString != null && jsonString is String) {
        return json.decode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      debugPrint('Hive获取JSON失败: $key, 错误: $e');
      return null;
    }
  }

  // 存储JSON列表
  Future<void> setJsonList(String key, List<Map<String, dynamic>> value) async {
    try {
      final jsonString = json.encode(value);
      await _box.put(key, jsonString);
    } catch (e) {
      debugPrint('Hive存储JSON列表失败: $key, 错误: $e');
      rethrow;
    }
  }

  // 获取JSON列表
  List<Map<String, dynamic>>? getJsonList(String key) {
    try {
      final jsonString = _box.get(key);
      if (jsonString != null && jsonString is String) {
        final List<dynamic> list = json.decode(jsonString);
        return list.cast<Map<String, dynamic>>();
      }
      return null;
    } catch (e) {
      debugPrint('Hive获取JSON列表失败: $key, 错误: $e');
      return null;
    }
  }

  // 检查键是否存在
  bool containsKey(String key) {
    return _box.containsKey(key);
  }

  // 删除指定键的数据
  Future<void> remove(String key) async {
    try {
      await _box.delete(key);
    } catch (e) {
      debugPrint('Hive删除数据失败: $key, 错误: $e');
      rethrow;
    }
  }

  // 清除所有数据
  Future<void> clear() async {
    try {
      await _box.clear();
      debugPrint('Hive清除所有数据成功');
    } catch (e) {
      debugPrint('Hive清除所有数据失败: $e');
      rethrow;
    }
  }

  // 强制刷新
  Future<void> reload() async {
    try {
      // Hive不需要显式刷新，但我们可以关闭并重新打开Box
      final boxName = _box.name;
      await _box.close();
      _box = await Hive.openBox(boxName);
      debugPrint('Hive重新加载成功');
    } catch (e) {
      debugPrint('Hive重新加载失败: $e');
      rethrow;
    }
  }
}
