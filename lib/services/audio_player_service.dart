import 'dart:io';
import 'package:just_audio/just_audio.dart';
import 'package:audio_session/audio_session.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

class AudioPlayerService {
  static final AudioPlayerService _instance = AudioPlayerService._internal();
  factory AudioPlayerService() => _instance;
  AudioPlayerService._internal();

  // 音频播放器
  late AudioPlayer _audioPlayer;

  // 播放列表
  late ConcatenatingAudioSource _playlist;

  // 当前播放索引
  final ValueNotifier<int> currentIndexNotifier = ValueNotifier<int>(0);

  // 播放状态
  final ValueNotifier<PlayerState> playerStateNotifier =
      ValueNotifier<PlayerState>(PlayerState(false, ProcessingState.idle));

  // 播放进度
  final ValueNotifier<Duration> positionNotifier = ValueNotifier<Duration>(
    Duration.zero,
  );
  final ValueNotifier<Duration> durationNotifier = ValueNotifier<Duration>(
    Duration.zero,
  );

  // 音频文件目录
  late Directory _audioDirectory;

  // 初始化
  Future<void> init() async {
    _audioPlayer = AudioPlayer();
    _playlist = ConcatenatingAudioSource(children: []);

    // 配置音频会话
    final session = await AudioSession.instance;
    await session.configure(const AudioSessionConfiguration.speech());

    // 获取音频文件目录
    final appDocDir = await getApplicationDocumentsDirectory();
    _audioDirectory = Directory('${appDocDir.path}/audio_records');
    if (!await _audioDirectory.exists()) {
      await _audioDirectory.create(recursive: true);
    }

    // 监听播放状态变化
    _audioPlayer.playerStateStream.listen((state) {
      playerStateNotifier.value = state;

      // 当播放结束且不是最后一个时，播放下一个
      if (state.processingState == ProcessingState.completed) {
        if (_audioPlayer.currentIndex != null &&
            _audioPlayer.currentIndex! < _playlist.children.length - 1) {
          _audioPlayer.seekToNext();
        }
      }
    });

    // 监听当前播放索引变化
    _audioPlayer.currentIndexStream.listen((index) {
      if (index != null) {
        currentIndexNotifier.value = index;
      }
    });

    // 监听播放进度变化
    _audioPlayer.positionStream.listen((position) {
      positionNotifier.value = position;
    });

    // 监听总时长变化
    _audioPlayer.durationStream.listen((duration) {
      if (duration != null) {
        durationNotifier.value = duration;
      }
    });
  }

  // 加载播放列表
  Future<void> loadPlaylist(List<String> audioFilePaths) async {
    // 清空当前播放列表
    await _playlist.clear();

    // 添加音频文件到播放列表
    for (var path in audioFilePaths) {
      await _playlist.add(AudioSource.uri(Uri.file(path)));
    }

    // 加载播放列表
    await _audioPlayer.setAudioSource(_playlist);
  }

  // 添加单个音频到播放列表
  Future<void> addToPlaylist(String audioFilePath) async {
    await _playlist.add(AudioSource.uri(Uri.file(audioFilePath)));

    // 如果是第一个添加的音频，加载播放列表
    if (_playlist.children.length == 1) {
      await _audioPlayer.setAudioSource(_playlist);
    }
  }

  // 移除播放列表中的某一项
  Future<void> removeFromPlaylist(int index) async {
    if (index >= 0 && index < _playlist.children.length) {
      await _playlist.removeAt(index);
    }
  }

  // 清空播放列表
  Future<void> clearPlaylist() async {
    await _playlist.clear();
  }

  // 播放
  Future<void> play() async {
    await _audioPlayer.play();
  }

  // 暂停
  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  // 停止
  Future<void> stop() async {
    await _audioPlayer.stop();
  }

  // 跳转到指定索引播放
  Future<void> skipToIndex(int index) async {
    if (index >= 0 && index < _playlist.children.length) {
      await _audioPlayer.seek(Duration.zero, index: index);
      await _audioPlayer.play();
    }
  }

  // 播放下一个
  Future<void> next() async {
    await _audioPlayer.seekToNext();
  }

  // 播放上一个
  Future<void> previous() async {
    await _audioPlayer.seekToPrevious();
  }

  // 跳转到指定位置
  Future<void> seekTo(Duration position) async {
    await _audioPlayer.seek(position);
  }

  // 设置循环模式
  Future<void> setLoopMode(LoopMode mode) async {
    await _audioPlayer.setLoopMode(mode);
  }

  // 设置随机播放
  Future<void> setShuffleModeEnabled(bool enabled) async {
    await _audioPlayer.setShuffleModeEnabled(enabled);
  }

  // 设置音量
  Future<void> setVolume(double volume) async {
    await _audioPlayer.setVolume(volume);
  }

  // 设置速度
  Future<void> setSpeed(double speed) async {
    await _audioPlayer.setSpeed(speed);
  }

  // 获取音频文件目录路径
  String get audioDirectoryPath => _audioDirectory.path;

  // 获取当前播放索引
  int? get currentIndex => _audioPlayer.currentIndex;

  // 获取播放列表长度
  int get playlistLength => _playlist.children.length;

  // 销毁资源
  void dispose() {
    _audioPlayer.dispose();
    currentIndexNotifier.dispose();
    playerStateNotifier.dispose();
    positionNotifier.dispose();
    durationNotifier.dispose();
  }
}
