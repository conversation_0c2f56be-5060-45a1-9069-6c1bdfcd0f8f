import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import 'package:speech_to_text/speech_to_text.dart';

/// 转录服务 - 提供专业的语音转文字功能
class TranscriptionService {
  static final TranscriptionService _instance =
      TranscriptionService._internal();

  factory TranscriptionService() => _instance;

  TranscriptionService._internal();

  // 语音识别实例
  SpeechToText? _speechToText;

  // 音频播放器（用于文件转录）
  final AudioPlayer _audioPlayer = AudioPlayer();

  // 转录状态
  final ValueNotifier<TranscriptionStatus> status =
      ValueNotifier<TranscriptionStatus>(TranscriptionStatus.idle);

  // 转录进度
  final ValueNotifier<double> progress = ValueNotifier<double>(0.0);

  // 转录结果流
  final StreamController<String> _resultController =
      StreamController<String>.broadcast();

  Stream<String> get resultStream => _resultController.stream;

  // 转录设置
  TranscriptionSettings _settings = TranscriptionSettings();

  // 当前转录任务
  TranscriptionTask? _currentTask;

  /// 初始化转录服务
  Future<bool> initialize() async {
    try {
      debugPrint('TranscriptionService: 初始化转录服务');

      // 初始化语音识别
      _speechToText = SpeechToText();
      final speechEnabled = await _speechToText!.initialize(
        onError: (error) => _handleSpeechError(error),
        onStatus: (status) => _handleSpeechStatus(status),
      );

      if (!speechEnabled) {
        debugPrint('TranscriptionService: 语音识别初始化失败');
        return false;
      }

      debugPrint('TranscriptionService: 转录服务初始化成功');
      return true;
    } catch (e) {
      debugPrint('TranscriptionService: 初始化失败: $e');
      return false;
    }
  }

  /// 开始实时转录
  Future<bool> startRealtimeTranscription({
    String language = 'zh_CN',
    TranscriptionSettings? settings,
  }) async {
    try {
      if (_speechToText == null) {
        debugPrint('TranscriptionService: 语音识别未初始化');
        return false;
      }

      // 如果已经在转录，先停止
      if (status.value == TranscriptionStatus.transcribing) {
        debugPrint('TranscriptionService: 已在转录状态，先停止当前转录');
        await stopRealtimeTranscription();
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // 更新设置
      if (settings != null) {
        _settings = settings;
      }

      status.value = TranscriptionStatus.transcribing;
      progress.value = 0.0;

      // 开始语音识别
      final success = await _speechToText!.listen(
        onResult: (result) {
          if (result.finalResult) {
            _resultController.add(result.recognizedWords);
            progress.value = 1.0;
          } else {
            _resultController.add(result.recognizedWords);
            progress.value = 0.5;
          }
        },
        localeId: language,
        listenFor: const Duration(seconds: 30),
        // 最大监听30秒
        pauseFor: const Duration(seconds: 3),
        // 暂停3秒后停止
        listenOptions: SpeechListenOptions(
          partialResults: true,
          cancelOnError: false,
          listenMode: ListenMode.confirmation,
        ),
      );

      if (success) {
        debugPrint('TranscriptionService: 实时转录已开始');
        return true;
      } else {
        debugPrint('TranscriptionService: 实时转录启动失败');
        status.value = TranscriptionStatus.idle;
        return false;
      }
    } catch (e) {
      debugPrint('TranscriptionService: 开始实时转录失败: $e');
      status.value = TranscriptionStatus.idle;
      return false;
    }
  }

  /// 停止实时转录
  Future<void> stopRealtimeTranscription() async {
    try {
      if (_speechToText != null &&
          status.value == TranscriptionStatus.transcribing) {
        await _speechToText!.stop();
        debugPrint('TranscriptionService: 实时转录已停止');
      }
      status.value = TranscriptionStatus.idle;
      progress.value = 0.0;
    } catch (e) {
      debugPrint('TranscriptionService: 停止实时转录失败: $e');
      status.value = TranscriptionStatus.idle;
    }
  }

  /// 转录音频文件
  Future<String?> transcribeAudioFile({
    required String filePath,
    String? language,
    TranscriptionSettings? settings,
  }) async {
    try {
      debugPrint('TranscriptionService: 开始转录音频文件: $filePath');

      // 检查文件是否存在
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('音频文件不存在: $filePath');
      }

      // 更新设置
      if (settings != null) {
        _settings = settings;
      }

      // 创建转录任务
      _currentTask = TranscriptionTask(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: TranscriptionType.file,
        language: language ?? _settings.defaultLanguage,
        settings: _settings,
        filePath: filePath,
      );

      status.value = TranscriptionStatus.transcribing;
      progress.value = 0.0;

      // 对于文件转录，我们使用模拟实现
      // 在实际应用中，这里应该调用专业的转录API
      final result = await _simulateFileTranscription(filePath);

      status.value = TranscriptionStatus.completed;
      progress.value = 1.0;

      debugPrint('TranscriptionService: 文件转录完成');
      return result;
    } catch (e) {
      debugPrint('TranscriptionService: 文件转录失败: $e');
      status.value = TranscriptionStatus.error;
      return null;
    }
  }

  /// 批量转录
  Future<List<TranscriptionResult>> transcribeBatchFiles({
    required List<String> filePaths,
    String? language,
    TranscriptionSettings? settings,
  }) async {
    try {
      debugPrint('TranscriptionService: 开始批量转录 ${filePaths.length} 个文件');

      final results = <TranscriptionResult>[];

      for (int i = 0; i < filePaths.length; i++) {
        final filePath = filePaths[i];

        // 更新进度
        progress.value = i / filePaths.length;

        try {
          final transcription = await transcribeAudioFile(
            filePath: filePath,
            language: language,
            settings: settings,
          );

          results.add(
            TranscriptionResult(
              filePath: filePath,
              transcription: transcription ?? '',
              success: transcription != null,
              error: transcription == null ? '转录失败' : null,
            ),
          );
        } catch (e) {
          results.add(
            TranscriptionResult(
              filePath: filePath,
              transcription: '',
              success: false,
              error: e.toString(),
            ),
          );
        }
      }

      progress.value = 1.0;
      status.value = TranscriptionStatus.completed;

      debugPrint('TranscriptionService: 批量转录完成');
      return results;
    } catch (e) {
      debugPrint('TranscriptionService: 批量转录失败: $e');
      status.value = TranscriptionStatus.error;
      return [];
    }
  }

  /// 处理语音识别错误
  void _handleSpeechError(dynamic error) {
    debugPrint('TranscriptionService: 语音识别错误: $error');
    status.value = TranscriptionStatus.error;
  }

  /// 处理语音识别状态
  void _handleSpeechStatus(String status) {
    debugPrint('TranscriptionService: 语音识别状态: $status');

    switch (status) {
      case 'listening':
        this.status.value = TranscriptionStatus.transcribing;
        break;
      case 'notListening':
        this.status.value = TranscriptionStatus.completed;
        break;
      case 'done':
        this.status.value = TranscriptionStatus.completed;
        break;
      default:
        break;
    }
  }

  /// 模拟文件转录（实际应用中应替换为真实的转录API）
  Future<String> _simulateFileTranscription(String filePath) async {
    // 模拟处理时间
    await Future.delayed(const Duration(seconds: 2));

    // 根据文件路径生成模拟转录结果
    final fileName = filePath.split('/').last;
    final timestamp = DateTime.now().toString();

    return '''
这是从音频文件 "$fileName" 转录出的文本内容。

转录时间: $timestamp
文件路径: $filePath

转录设置:
- 语言: ${_currentTask?.language ?? 'zh_CN'}
- 启用标点符号: ${_settings.enablePunctuation}
- 说话人检测: ${_settings.enableSpeakerDetection}
- 置信度阈值: ${_settings.confidenceThreshold}

转录结果会根据音频质量和语音清晰度有所不同。在实际应用中，这里会显示真实的转录内容。
    '''.trim();
  }

  /// 获取支持的语言列表
  List<String> getSupportedLanguages() {
    return [
      'zh_CN', // 中文（简体）
      'zh_TW', // 中文（繁体）
      'en_US', // 英语（美国）
      'ja_JP', // 日语
      'ko_KR', // 韩语
      'fr_FR', // 法语
      'de_DE', // 德语
      'es_ES', // 西班牙语
    ];
  }

  /// 获取当前转录任务
  TranscriptionTask? get currentTask => _currentTask;

  /// 获取转录设置
  TranscriptionSettings get settings => _settings;

  /// 更新转录设置
  void updateSettings(TranscriptionSettings newSettings) {
    _settings = newSettings;
  }

  /// 取消当前转录任务
  Future<void> cancelTranscription() async {
    try {
      await _speechToText?.cancel();
      await _audioPlayer.stop();

      status.value = TranscriptionStatus.cancelled;
      progress.value = 0.0;
      _currentTask = null;

      debugPrint('TranscriptionService: 转录任务已取消');
    } catch (e) {
      debugPrint('TranscriptionService: 取消转录任务失败: $e');
    }
  }

  /// 释放资源
  void dispose() {
    _speechToText?.cancel();
    _audioPlayer.dispose();
    _resultController.close();
    status.dispose();
    progress.dispose();
  }
}

/// 转录状态枚举
enum TranscriptionStatus {
  idle, // 空闲
  transcribing, // 转录中
  completed, // 已完成
  error, // 错误
  cancelled, // 已取消
}

/// 转录类型枚举
enum TranscriptionType {
  realtime, // 实时转录
  file, // 文件转录
  batch, // 批量转录
}

/// 转录任务
class TranscriptionTask {
  final String id;
  final TranscriptionType type;
  final String language;
  final TranscriptionSettings settings;
  final String? filePath;
  final DateTime createdAt;

  TranscriptionTask({
    required this.id,
    required this.type,
    required this.language,
    required this.settings,
    this.filePath,
  }) : createdAt = DateTime.now();

  @override
  String toString() {
    return 'TranscriptionTask{id: $id, type: $type, language: $language, filePath: $filePath}';
  }
}

/// 转录设置
class TranscriptionSettings {
  final String defaultLanguage;
  final bool enablePunctuation;
  final bool enableSpeakerDetection;
  final double confidenceThreshold;
  final bool enableProfanityFilter;
  final bool enableNumberFormatting;

  const TranscriptionSettings({
    this.defaultLanguage = 'zh_CN',
    this.enablePunctuation = true,
    this.enableSpeakerDetection = false,
    this.confidenceThreshold = 0.7,
    this.enableProfanityFilter = false,
    this.enableNumberFormatting = true,
  });

  TranscriptionSettings copyWith({
    String? defaultLanguage,
    bool? enablePunctuation,
    bool? enableSpeakerDetection,
    double? confidenceThreshold,
    bool? enableProfanityFilter,
    bool? enableNumberFormatting,
  }) {
    return TranscriptionSettings(
      defaultLanguage: defaultLanguage ?? this.defaultLanguage,
      enablePunctuation: enablePunctuation ?? this.enablePunctuation,
      enableSpeakerDetection:
          enableSpeakerDetection ?? this.enableSpeakerDetection,
      confidenceThreshold: confidenceThreshold ?? this.confidenceThreshold,
      enableProfanityFilter:
          enableProfanityFilter ?? this.enableProfanityFilter,
      enableNumberFormatting:
          enableNumberFormatting ?? this.enableNumberFormatting,
    );
  }
}

/// 转录结果
class TranscriptionResult {
  final String filePath;
  final String transcription;
  final bool success;
  final String? error;
  final DateTime? completedAt;

  TranscriptionResult({
    required this.filePath,
    required this.transcription,
    required this.success,
    this.error,
  }) : completedAt = DateTime.now();

  @override
  String toString() {
    return 'TranscriptionResult{filePath: $filePath, success: $success, error: $error}';
  }
}
