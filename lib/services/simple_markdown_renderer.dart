import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 简单的Markdown渲染器
/// 当flutter_markdown包出现问题时的备用方案
class SimpleMarkdownRenderer extends StatelessWidget {
  final String markdownText;
  final TextStyle? baseTextStyle;
  final bool selectable;

  const SimpleMarkdownRenderer({
    super.key,
    required this.markdownText,
    this.baseTextStyle,
    this.selectable = false,
  });

  @override
  Widget build(BuildContext context) {
    final TextStyle defaultStyle =
        baseTextStyle ??
        const TextStyle(fontSize: 16, color: Colors.black87, height: 1.6);

    final spans = _parseMarkdown(markdownText, defaultStyle);

    if (selectable) {
      return SelectableText.rich(
        TextSpan(children: spans),
        style: defaultStyle,
      );
    } else {
      return RichText(text: TextSpan(children: spans, style: defaultStyle));
    }
  }

  List<TextSpan> _parseMarkdown(String text, TextStyle baseStyle) {
    final List<TextSpan> spans = [];
    final lines = text.split('\n');

    for (String line in lines) {
      spans.addAll(_parseLine(line, baseStyle));
      spans.add(TextSpan(text: '\n', style: baseStyle));
    }

    return spans;
  }

  List<TextSpan> _parseLine(String line, TextStyle baseStyle) {
    final List<TextSpan> spans = [];

    // 处理标题
    if (line.startsWith('#')) {
      final headerLevel = line.indexOf(' ');
      if (headerLevel > 0) {
        final headerText = line.substring(headerLevel + 1);
        final fontSize = math.max(20.0, 28.0 - (headerLevel * 2));
        spans.add(
          TextSpan(
            text: headerText,
            style: baseStyle.copyWith(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
        );
        return spans;
      }
    }

    // 处理列表项
    if (line.trim().startsWith('- ') || line.trim().startsWith('* ')) {
      final listText = line.trim().substring(2);
      spans.add(TextSpan(text: '• ', style: baseStyle));
      spans.addAll(_parseInlineMarkdown(listText, baseStyle));
      return spans;
    }

    // 处理有序列表
    final orderedListMatch = RegExp(r'^\s*\d+\.\s+').firstMatch(line);
    if (orderedListMatch != null) {
      final number = orderedListMatch.group(0)!;
      final listText = line.substring(orderedListMatch.end);
      spans.add(TextSpan(text: number, style: baseStyle));
      spans.addAll(_parseInlineMarkdown(listText, baseStyle));
      return spans;
    }

    // 处理引用
    if (line.trim().startsWith('> ')) {
      final quoteText = line.trim().substring(2);
      spans.add(
        TextSpan(
          text: quoteText,
          style: baseStyle.copyWith(
            fontStyle: FontStyle.italic,
            color: baseStyle.color?.withValues(alpha: 0.8),
          ),
        ),
      );
      return spans;
    }

    // 处理代码块
    if (line.trim().startsWith('```')) {
      spans.add(
        TextSpan(
          text: line,
          style: baseStyle.copyWith(
            fontFamily: 'monospace',
            backgroundColor: Colors.grey[100],
          ),
        ),
      );
      return spans;
    }

    // 处理普通文本
    spans.addAll(_parseInlineMarkdown(line, baseStyle));
    return spans;
  }

  List<TextSpan> _parseInlineMarkdown(String text, TextStyle baseStyle) {
    final List<TextSpan> spans = [];
    final RegExp markdownPattern = RegExp(
      r'(\*\*[^*]+\*\*)|(\*[^*]+\*)|(`[^`]+`)|([^*`]+)',
    );

    final matches = markdownPattern.allMatches(text);

    for (final match in matches) {
      final matchText = match.group(0)!;

      if (matchText.startsWith('**') && matchText.endsWith('**')) {
        // 粗体
        spans.add(
          TextSpan(
            text: matchText.substring(2, matchText.length - 2),
            style: baseStyle.copyWith(fontWeight: FontWeight.bold),
          ),
        );
      } else if (matchText.startsWith('*') && matchText.endsWith('*')) {
        // 斜体
        spans.add(
          TextSpan(
            text: matchText.substring(1, matchText.length - 1),
            style: baseStyle.copyWith(fontStyle: FontStyle.italic),
          ),
        );
      } else if (matchText.startsWith('`') && matchText.endsWith('`')) {
        // 行内代码
        spans.add(
          TextSpan(
            text: matchText.substring(1, matchText.length - 1),
            style: baseStyle.copyWith(
              fontFamily: 'monospace',
              backgroundColor: Colors.grey[200],
            ),
          ),
        );
      } else {
        // 普通文本
        spans.add(TextSpan(text: matchText, style: baseStyle));
      }
    }

    return spans;
  }
}
