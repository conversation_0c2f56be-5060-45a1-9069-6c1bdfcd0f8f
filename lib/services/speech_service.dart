import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';

class SpeechService {
  static final SpeechService _instance = SpeechService._internal();
  factory SpeechService() => _instance;
  SpeechService._internal();

  // 延迟初始化
  SpeechToText? _speechToText;
  bool _speechEnabled = false;
  String _lastWords = '';

  // 控制器，用于监听语音识别的结果
  final StreamController<String> _resultController =
      StreamController<String>.broadcast();
  Stream<String> get resultStream => _resultController.stream;

  // 语音识别状态
  final ValueNotifier<bool> isListening = ValueNotifier<bool>(false);

  // 初始化语音识别
  Future<bool> initSpeech() async {
    try {
      // 创建实例
      if (_speechToText == null) {
        debugPrint("SpeechService: 创建SpeechToText实例");
        _speechToText = SpeechToText();
      }

      // 直接初始化，让系统弹出权限请求对话框
      // 注意：这是关键改动，不再依赖permission_handler来请求权限
      // 而是让speech_to_text插件直接向系统请求，这样在iOS上更可靠
      debugPrint("SpeechService: 开始直接初始化语音识别");
      _speechEnabled = await _speechToText!.initialize(
        onError: (error) => debugPrint("SpeechService: 初始化错误: $error"),
        onStatus: (status) => debugPrint("SpeechService: 状态: $status"),
      );

      debugPrint("SpeechService: 语音识别初始化结果: $_speechEnabled");
      return _speechEnabled;
    } catch (e) {
      debugPrint("SpeechService: 语音识别初始化失败: $e");
      return false;
    }
  }

  // 开始监听
  Future<void> startListening({String? localeId}) async {
    if (_speechToText == null) {
      debugPrint("SpeechService: 语音识别未初始化，尝试初始化");
      bool initialized = await initSpeech();
      if (!initialized) {
        debugPrint("SpeechService: 初始化失败，无法开始监听");
        return;
      }
    }

    if (!_speechEnabled) {
      debugPrint("SpeechService: 语音识别未启用，尝试重新初始化");
      bool initialized = await initSpeech();
      if (!initialized) {
        debugPrint("SpeechService: 重新初始化失败，无法开始监听");
        return;
      }
    }

    try {
      debugPrint("SpeechService: 开始监听");
      await _speechToText!.listen(
        onResult: _onSpeechResult,
        localeId: localeId,
        listenOptions: SpeechListenOptions(
          listenMode: ListenMode.dictation,
          cancelOnError: false,
          partialResults: true,
        ),
        listenFor: const Duration(minutes: 30), // 支持长时间录音
      );

      isListening.value = true;
      debugPrint("SpeechService: 监听已开始");
    } catch (e) {
      debugPrint("SpeechService: 开始监听失败: $e");
    }
  }

  // 停止监听
  Future<void> stopListening() async {
    try {
      if (_speechToText != null) {
        await _speechToText!.stop();
        debugPrint("SpeechService: 监听已停止");
      }
      isListening.value = false;
    } catch (e) {
      debugPrint("SpeechService: 停止监听失败: $e");
    }
  }

  // 处理语音识别结果
  void _onSpeechResult(SpeechRecognitionResult result) {
    _lastWords = result.recognizedWords;
    _resultController.add(_lastWords);
  }

  // 获取最后识别的文字
  String getLastWords() => _lastWords;

  // 清除识别结果
  void clearWords() {
    _lastWords = '';
  }

  // 销毁资源
  void dispose() {
    _resultController.close();
    isListening.dispose();
    stopListening();
    _speechToText = null;
  }
}
