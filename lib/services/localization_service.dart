import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 本地化服务，管理应用的语言设置
class LocalizationService extends ChangeNotifier {
  static const String _localeKey = 'app_locale';
  
  // 支持的语言列表
  static const List<Locale> supportedLocales = [
    Locale('en', ''), // English
    Locale('zh', ''), // Chinese (Simplified)
    Locale('ja', ''), // Japanese
  ];

  // 语言显示名称映射
  static const Map<String, String> localeNames = {
    'en': 'English',
    'zh': '中文',
    'ja': '日本語',
  };

  Locale? _currentLocale;
  
  /// 获取当前语言设置
  Locale? get currentLocale => _currentLocale;
  
  /// 获取当前语言的显示名称
  String get currentLocaleName {
    if (_currentLocale == null) return 'System';
    return localeNames[_currentLocale!.languageCode] ?? 'Unknown';
  }

  /// 初始化本地化服务
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final localeCode = prefs.getString(_localeKey);
    
    if (localeCode != null) {
      _currentLocale = Locale(localeCode);
    } else {
      // 如果没有保存的语言设置，使用系统语言
      _currentLocale = _getSystemLocale();
    }
    
    notifyListeners();
  }

  /// 设置语言
  Future<void> setLocale(Locale? locale) async {
    if (_currentLocale == locale) return;
    
    _currentLocale = locale;
    
    final prefs = await SharedPreferences.getInstance();
    if (locale != null) {
      await prefs.setString(_localeKey, locale.languageCode);
    } else {
      await prefs.remove(_localeKey);
    }
    
    notifyListeners();
  }

  /// 获取系统语言
  Locale _getSystemLocale() {
    final systemLocale = PlatformDispatcher.instance.locale;
    
    // 检查系统语言是否在支持的语言列表中
    for (final supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == systemLocale.languageCode) {
        return supportedLocale;
      }
    }
    
    // 如果系统语言不支持，返回英语作为默认语言
    return const Locale('en', '');
  }

  /// 检查是否支持指定语言
  bool isLocaleSupported(Locale locale) {
    return supportedLocales.any(
      (supportedLocale) => supportedLocale.languageCode == locale.languageCode,
    );
  }

  /// 获取语言选择列表（包含"跟随系统"选项）
  List<LocaleOption> getLocaleOptions() {
    final options = <LocaleOption>[
      const LocaleOption(null, 'System', '跟随系统'),
    ];
    
    for (final locale in supportedLocales) {
      final name = localeNames[locale.languageCode] ?? locale.languageCode;
      options.add(LocaleOption(locale, name, name));
    }
    
    return options;
  }

  /// 根据语言代码获取Locale
  Locale? getLocaleByCode(String code) {
    if (code == 'system') return null;
    
    for (final locale in supportedLocales) {
      if (locale.languageCode == code) {
        return locale;
      }
    }
    
    return null;
  }
}

/// 语言选项数据类
class LocaleOption {
  final Locale? locale;
  final String name;
  final String displayName;
  
  const LocaleOption(this.locale, this.name, this.displayName);
  
  String get code => locale?.languageCode ?? 'system';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocaleOption && other.locale == locale;
  }
  
  @override
  int get hashCode => locale.hashCode;
}
