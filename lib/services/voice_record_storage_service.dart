import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import '../models/voice_record.dart';

class VoiceRecordStorageService {
  static final VoiceRecordStorageService _instance =
      VoiceRecordStorageService._internal();
  factory VoiceRecordStorageService() => _instance;
  VoiceRecordStorageService._internal();

  static const String _recordsKey = 'voice_records';

  // 记录列表
  final ValueNotifier<List<VoiceRecord>> recordsNotifier =
      ValueNotifier<List<VoiceRecord>>([]);

  // 初始化
  Future<void> init() async {
    await _loadRecords();
  }

  // 加载记录
  Future<void> _loadRecords() async {
    final prefs = await SharedPreferences.getInstance();
    final recordsJson = prefs.getStringList(_recordsKey) ?? [];

    final records =
        recordsJson.map((json) {
          return VoiceRecord.fromJson(jsonDecode(json));
        }).toList();

    // 按创建时间降序排列
    records.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    recordsNotifier.value = records;
  }

  // 保存记录
  Future<void> _saveRecords() async {
    final prefs = await SharedPreferences.getInstance();
    final recordsJson =
        recordsNotifier.value.map((record) {
          return jsonEncode(record.toJson());
        }).toList();

    await prefs.setStringList(_recordsKey, recordsJson);
  }

  // 添加录音记录
  Future<String> addRecord(
    String filePath,
    Duration duration, {
    String? title,
    String? transcription,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('录音文件不存在');
      }

      // 检查文件大小
      final fileSize = await file.length();
      debugPrint('添加录音记录，文件路径: $filePath, 文件大小: $fileSize 字节');

      // 如果文件过小，可能录制失败
      if (fileSize < 100) {
        debugPrint('警告: 录音文件过小 ($fileSize 字节)，可能录制失败');
        throw Exception('录音文件大小异常 ($fileSize 字节)，请重试录音');
      }

      // 确保持续时间有效，如果传入的是零则设置为一个默认值
      Duration recordDuration = duration;
      if (recordDuration == Duration.zero) {
        // 尝试获取文件的实际时长
        try {
          // 使用文件大小估算时长 (粗略估计)
          // 假设一秒钟的AAC录音大约是16KB
          final estimatedDuration = Duration(
            milliseconds: (fileSize / 16000 * 1000).round(),
          );
          recordDuration =
              estimatedDuration > Duration.zero
                  ? estimatedDuration
                  : const Duration(seconds: 1);
          debugPrint('估算录音时长: $recordDuration, 文件大小: $fileSize 字节');
        } catch (e) {
          // 如果无法估算，设置一个默认值
          recordDuration = const Duration(seconds: 1);
          debugPrint('无法估算录音时长，使用默认值: $recordDuration');
        }
      }

      debugPrint('添加录音记录，文件路径: $filePath, 时长: $recordDuration');

      // 生成ID
      final id = const Uuid().v4();

      // 创建记录
      final record = VoiceRecord(
        id: id,
        filePath: filePath,
        title: title ?? '录音 ${DateTime.now().toString().substring(0, 19)}',
        duration: recordDuration,
        createdAt: DateTime.now(),
        transcription: transcription,
      );

      // 添加到列表
      final currentRecords = List<VoiceRecord>.from(recordsNotifier.value);
      currentRecords.insert(0, record);
      recordsNotifier.value = currentRecords;

      // 保存到本地存储
      await _saveRecords();

      return id;
    } catch (e) {
      debugPrint('添加录音记录失败: $e');
      rethrow;
    }
  }

  // 更新记录
  Future<void> updateRecord(VoiceRecord record) async {
    final records = List<VoiceRecord>.from(recordsNotifier.value);
    final index = records.indexWhere((r) => r.id == record.id);

    if (index != -1) {
      records[index] = record;
      recordsNotifier.value = records;
      await _saveRecords();
    }
  }

  // 删除记录
  Future<void> deleteRecord(String id) async {
    final records = List<VoiceRecord>.from(recordsNotifier.value);
    final index = records.indexWhere((r) => r.id == id);

    if (index != -1) {
      final record = records[index];

      // 删除文件
      final file = File(record.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      // 从列表中删除
      records.removeAt(index);
      recordsNotifier.value = records;
      await _saveRecords();
    }
  }

  // 获取记录
  VoiceRecord? getRecord(String id) {
    return recordsNotifier.value.firstWhere(
      (record) => record.id == id,
    );
  }

  // 更新转录文本
  Future<void> updateTranscription(String id, String transcription) async {
    final records = List<VoiceRecord>.from(recordsNotifier.value);
    final index = records.indexWhere((r) => r.id == id);

    if (index != -1) {
      final record = records[index];
      records[index] = record.copyWithTranscription(transcription);
      recordsNotifier.value = records;
      await _saveRecords();
    }
  }

  // 更新标题
  Future<void> updateTitle(String id, String title) async {
    final records = List<VoiceRecord>.from(recordsNotifier.value);
    final index = records.indexWhere((r) => r.id == id);

    if (index != -1) {
      final record = records[index];
      records[index] = record.copyWithTitle(title);
      recordsNotifier.value = records;
      await _saveRecords();
    }
  }

  // 销毁资源
  void dispose() {
    recordsNotifier.dispose();
  }
}
