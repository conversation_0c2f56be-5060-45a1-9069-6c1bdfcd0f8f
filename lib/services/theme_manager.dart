import 'package:flutter/material.dart';
import '../config/chinese_traditional_colors.dart';
import '../services/service_locator.dart';

/// 主题管理器 - 负责管理动态主题切换
class ThemeManager {
  static final ThemeManager _instance = ThemeManager._internal();
  factory ThemeManager() => _instance;
  ThemeManager._internal();

  /// 获取当前选中的中国传统色主题配置
  ChineseTraditionalColorConfig? getCurrentChineseTraditionalColorConfig() {
    final settings = ServiceLocator().settingsService.settings;
    final theme = settings.chineseTraditionalColorTheme;
    if (theme != null) {
      return ChineseTraditionalColors.getConfig(theme);
    }
    return null;
  }

  /// 获取内容类型对应的颜色（支持动态主题）
  Color getContentTypeColor(String contentType) {
    final config = getCurrentChineseTraditionalColorConfig();
    
    // 如果用户选择了中国传统色主题，使用传统色
    if (config != null) {
      switch (contentType.toLowerCase()) {
        case 'markdown':
        case 'markdownblocks':
          return config.primaryColor;
        case 'textcard':
        case 'textcardcollection':
          return config.secondaryColor;
        case 'image':
        case 'svg':
          return config.accentColor;
        default:
          return config.primaryColor;
      }
    }
    
    // 否则使用默认颜色
    return _getDefaultContentTypeColor(contentType);
  }

  /// 获取内容类型对应的渐变色（支持动态主题）
  LinearGradient getContentTypeGradient(String contentType) {
    final config = getCurrentChineseTraditionalColorConfig();
    
    // 如果用户选择了中国传统色主题，使用传统色渐变
    if (config != null) {
      switch (contentType.toLowerCase()) {
        case 'markdown':
        case 'markdownblocks':
          return config.gradient;
        case 'textcard':
        case 'textcardcollection':
          return LinearGradient(
            colors: [config.secondaryColor, config.accentColor],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          );
        case 'image':
        case 'svg':
          return LinearGradient(
            colors: [config.accentColor, config.primaryColor.withValues(alpha: 0.8)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          );
        default:
          return config.gradient;
      }
    }
    
    // 否则使用默认渐变
    return _getDefaultContentTypeGradient(contentType);
  }

  /// 获取默认的内容类型颜色
  Color _getDefaultContentTypeColor(String contentType) {
    switch (contentType.toLowerCase()) {
      case 'markdown':
      case 'markdownblocks':
        return const Color(0xFF6366F1); // 蓝紫色
      case 'textcard':
      case 'textcardcollection':
        return const Color(0xFF8B5CF6); // 紫色
      case 'image':
      case 'svg':
        return const Color(0xFFF59E0B); // 橙色
      case 'html':
        return const Color(0xFFEF4444); // 红色
      case 'pdf':
        return const Color(0xFF06B6D4); // 青色
      case 'voice':
        return const Color(0xFF6366F1); // 靛蓝色
      default:
        return const Color(0xFF6B7280); // 灰色
    }
  }

  /// 获取默认的内容类型渐变
  LinearGradient _getDefaultContentTypeGradient(String contentType) {
    switch (contentType.toLowerCase()) {
      case 'markdown':
      case 'markdownblocks':
        return const LinearGradient(
          colors: [Color(0xFF6366F1), Color(0xFF4F46E5)],
        );
      case 'textcard':
      case 'textcardcollection':
        return const LinearGradient(
          colors: [Color(0xFF8B5CF6), Color(0xFFA855F7)],
        );
      case 'image':
      case 'svg':
        return const LinearGradient(
          colors: [Color(0xFFF59E0B), Color(0xFFD97706)],
        );
      case 'html':
        return const LinearGradient(
          colors: [Color(0xFFEF4444), Color(0xFFDC2626)],
        );
      case 'pdf':
        return const LinearGradient(
          colors: [Color(0xFF06B6D4), Color(0xFF0891B2)],
        );
      case 'voice':
        return const LinearGradient(
          colors: [Color(0xFF6366F1), Color(0xFF4F46E5)],
        );
      default:
        return const LinearGradient(
          colors: [Color(0xFF6B7280), Color(0xFF4B5563)],
        );
    }
  }

  /// 获取分类芯片颜色（支持动态主题）
  Color getCategoryChipColor(String categoryType) {
    final config = getCurrentChineseTraditionalColorConfig();
    
    if (config != null) {
      switch (categoryType.toLowerCase()) {
        case 'markdown':
          return config.primaryColor;
        case 'textcard':
          return config.secondaryColor;
        case 'image':
          return config.accentColor;
        default:
          return config.primaryColor;
      }
    }
    
    return _getDefaultContentTypeColor(categoryType);
  }

  /// 检查是否启用了中国传统色主题
  bool isChineseTraditionalColorEnabled() {
    final settings = ServiceLocator().settingsService.settings;
    return settings.chineseTraditionalColorTheme != null;
  }

  /// 获取当前主题名称
  String getCurrentThemeName() {
    final settings = ServiceLocator().settingsService.settings;
    final theme = settings.chineseTraditionalColorTheme;
    if (theme != null) {
      return ChineseTraditionalColors.getThemeName(theme);
    }
    return '默认主题';
  }

  /// 获取主题背景色
  Color getThemeBackgroundColor() {
    final config = getCurrentChineseTraditionalColorConfig();
    return config?.backgroundColor ?? const Color(0xFFF8FAFC);
  }

  /// 获取主题表面色
  Color getThemeSurfaceColor() {
    final config = getCurrentChineseTraditionalColorConfig();
    return config?.surfaceColor ?? Colors.white;
  }

  /// 获取主题文本色
  Color getThemeTextColor() {
    final config = getCurrentChineseTraditionalColorConfig();
    return config?.textColor ?? const Color(0xFF1F2937);
  }

  /// 获取主题副标题色
  Color getThemeSubtitleColor() {
    final config = getCurrentChineseTraditionalColorConfig();
    return config?.subtitleColor ?? const Color(0xFF6B7280);
  }
}
