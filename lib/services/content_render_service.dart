import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../models/content_render_data.dart';

/// 内容渲染服务
/// 负责将不同类型的内容渲染为图片，用于在内容库中展示
class ContentRenderService {
  static const String _renderFolderName = 'content_renders';
  late Directory _renderFolder;

  /// 初始化服务
  Future<void> initialize() async {
    final appDir = await getApplicationDocumentsDirectory();
    _renderFolder = Directory('${appDir.path}/$_renderFolderName');

    if (!await _renderFolder.exists()) {
      await _renderFolder.create(recursive: true);
    }
  }

  /// 渲染Markdown内容
  Future<ContentRenderData> renderMarkdown({
    required String content,
    required bool isBlockMode,
    Map<String, dynamic>? config,
  }) async {
    final renderConfig = {...RenderConfig.defaultMarkdown, ...?config};

    if (isBlockMode) {
      // 分块模式：渲染多张图片
      final blocks = _splitMarkdownIntoBlocks(content);
      final imagePaths = <String>[];

      for (int i = 0; i < blocks.length; i++) {
        final blockImagePath = await _renderMarkdownBlock(
          blocks[i],
          i,
          renderConfig,
        );
        if (blockImagePath != null) {
          imagePaths.add(blockImagePath);
        }
      }

      // 生成缩略图（第一个块的缩略图）
      final thumbnailPath =
          imagePaths.isNotEmpty
              ? await _generateThumbnail(imagePaths.first)
              : null;

      return ContentRenderData(
        renderType: RenderType.markdownBlocks,
        imagePaths: imagePaths,
        thumbnailPath: thumbnailPath,
        originalContent: content,
        renderConfig: renderConfig,
      );
    } else {
      // 普通模式：渲染单张图片
      final imagePath = await _renderMarkdownSingle(content, renderConfig);
      final thumbnailPath =
          imagePath != null ? await _generateThumbnail(imagePath) : null;

      return ContentRenderData(
        renderType: RenderType.markdown,
        primaryImagePath: imagePath,
        thumbnailPath: thumbnailPath,
        originalContent: content,
        renderConfig: renderConfig,
      );
    }
  }

  /// 渲染文本卡片
  Future<ContentRenderData> renderTextCard({
    required Map<String, dynamic> cardData,
    required bool isCollection,
    Map<String, dynamic>? config,
  }) async {
    final renderConfig = {...RenderConfig.defaultTextCard, ...?config};

    if (isCollection) {
      // 卡片合集模式
      final cards = cardData['cards'] as List<dynamic>? ?? [cardData];
      final imagePaths = <String>[];

      for (int i = 0; i < cards.length; i++) {
        final cardImagePath = await _renderSingleTextCard(
          cards[i] as Map<String, dynamic>,
          i,
          renderConfig,
        );
        if (cardImagePath != null) {
          imagePaths.add(cardImagePath);
        }
      }

      final thumbnailPath =
          imagePaths.isNotEmpty
              ? await _generateThumbnail(imagePaths.first)
              : null;

      return ContentRenderData(
        renderType: RenderType.textCardCollection,
        imagePaths: imagePaths,
        thumbnailPath: thumbnailPath,
        originalContent: cardData,
        renderConfig: renderConfig,
      );
    } else {
      // 单卡片模式
      final imagePath = await _renderSingleTextCard(cardData, 0, renderConfig);
      final thumbnailPath =
          imagePath != null ? await _generateThumbnail(imagePath) : null;

      return ContentRenderData(
        renderType: RenderType.textCard,
        primaryImagePath: imagePath,
        thumbnailPath: thumbnailPath,
        originalContent: cardData,
        renderConfig: renderConfig,
      );
    }
  }

  /// 渲染语音波形图
  Future<ContentRenderData> renderVoiceWaveform({
    required String audioPath,
    Map<String, dynamic>? config,
  }) async {
    // TODO: 实现语音波形图渲染
    // 这里需要分析音频文件并生成波形图

    return ContentRenderData(
      renderType: RenderType.voiceWaveform,
      originalContent: audioPath,
      renderConfig: config ?? {},
    );
  }

  /// 渲染PDF预览
  Future<ContentRenderData> renderPdfPreview({
    required String pdfPath,
    int maxPages = 3,
    Map<String, dynamic>? config,
  }) async {
    // TODO: 实现PDF预览渲染
    // 这里需要将PDF的前几页渲染为图片

    return ContentRenderData(
      renderType: RenderType.pdfPreview,
      originalContent: pdfPath,
      renderConfig: config ?? {},
    );
  }

  /// 分割Markdown内容为块
  List<String> _splitMarkdownIntoBlocks(String content) {
    // 简单的分块逻辑，按标题分割
    final lines = content.split('\n');
    final blocks = <String>[];
    var currentBlock = <String>[];

    for (final line in lines) {
      if (line.trim().startsWith('#') && currentBlock.isNotEmpty) {
        // 遇到新标题，保存当前块
        blocks.add(currentBlock.join('\n'));
        currentBlock = [line];
      } else {
        currentBlock.add(line);
      }
    }

    // 添加最后一个块
    if (currentBlock.isNotEmpty) {
      blocks.add(currentBlock.join('\n'));
    }

    return blocks.where((block) => block.trim().isNotEmpty).toList();
  }

  /// 渲染单个Markdown块
  Future<String?> _renderMarkdownBlock(
    String blockContent,
    int index,
    Map<String, dynamic> config,
  ) async {
    // TODO: 实现Markdown块的实际渲染
    // 这里需要使用Flutter的渲染引擎将Markdown渲染为图片

    final fileName = '${const Uuid().v4()}_block_$index.png';
    final filePath = '${_renderFolder.path}/$fileName';

    // 模拟渲染过程
    await Future.delayed(const Duration(milliseconds: 100));

    return filePath;
  }

  /// 渲染单个Markdown文档
  Future<String?> _renderMarkdownSingle(
    String content,
    Map<String, dynamic> config,
  ) async {
    // TODO: 实现完整Markdown文档的渲染

    final fileName = '${const Uuid().v4()}_markdown.png';
    final filePath = '${_renderFolder.path}/$fileName';

    // 模拟渲染过程
    await Future.delayed(const Duration(milliseconds: 200));

    return filePath;
  }

  /// 渲染单个文本卡片
  Future<String?> _renderSingleTextCard(
    Map<String, dynamic> cardData,
    int index,
    Map<String, dynamic> config,
  ) async {
    // TODO: 实现文本卡片的实际渲染

    final fileName = '${const Uuid().v4()}_card_$index.png';
    final filePath = '${_renderFolder.path}/$fileName';

    // 模拟渲染过程
    await Future.delayed(const Duration(milliseconds: 150));

    return filePath;
  }

  /// 生成缩略图
  Future<String?> _generateThumbnail(String imagePath) async {
    // TODO: 实现缩略图生成

    final fileName = '${const Uuid().v4()}_thumb.png';
    final filePath = '${_renderFolder.path}/$fileName';

    // 模拟缩略图生成
    await Future.delayed(const Duration(milliseconds: 50));

    return filePath;
  }

  /// 删除渲染文件
  Future<void> deleteRenderFiles(ContentRenderData renderData) async {
    final filesToDelete = <String>[];

    if (renderData.primaryImagePath != null) {
      filesToDelete.add(renderData.primaryImagePath!);
    }

    filesToDelete.addAll(renderData.imagePaths);

    if (renderData.thumbnailPath != null) {
      filesToDelete.add(renderData.thumbnailPath!);
    }

    for (final filePath in filesToDelete) {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
      }
    }
  }

  /// 清理所有渲染文件
  Future<void> clearAllRenderFiles() async {
    if (await _renderFolder.exists()) {
      await _renderFolder.delete(recursive: true);
      await _renderFolder.create(recursive: true);
    }
  }
}
