import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_tts/flutter_tts.dart';

enum TtsState { playing, stopped, paused, continued }

class TtsService {
  static final TtsService _instance = TtsService._internal();
  factory TtsService() => _instance;
  TtsService._internal();

  FlutterTts flutterTts = FlutterTts();
  TtsState ttsState = TtsState.stopped;

  // 状态监听
  final ValueNotifier<TtsState> stateNotifier = ValueNotifier<TtsState>(
    TtsState.stopped,
  );

  // 播放进度监听
  final ValueNotifier<double> progressNotifier = ValueNotifier<double>(0.0);

  // 初始化
  Future<void> initTts() async {
    await flutterTts.setLanguage("zh-CN"); // 默认设置中文
    await flutterTts.setSpeechRate(0.5); // 语速，范围 0.0-1.0
    await flutterTts.setVolume(1.0); // 音量，范围 0.0-1.0
    await flutterTts.setPitch(1.0); // 音调，范围 0.0-2.0

    flutterTts.setStartHandler(() {
      ttsState = TtsState.playing;
      stateNotifier.value = ttsState;
    });

    flutterTts.setCompletionHandler(() {
      ttsState = TtsState.stopped;
      stateNotifier.value = ttsState;
      progressNotifier.value = 0.0;
    });

    flutterTts.setErrorHandler((msg) {
      ttsState = TtsState.stopped;
      stateNotifier.value = ttsState;
    });

    flutterTts.setProgressHandler((
      String text,
      int start,
      int end,
      String word,
    ) {
      if (text.isNotEmpty) {
        double progress = start / text.length;
        progressNotifier.value = progress;
      }
    });
  }

  // 开始播放
  Future<void> speak(String text) async {
    if (text.isNotEmpty) {
      await flutterTts.speak(text);
    }
  }

  // 停止播放
  Future<void> stop() async {
    var result = await flutterTts.stop();
    if (result == 1) {
      ttsState = TtsState.stopped;
      stateNotifier.value = ttsState;
    }
  }

  // 暂停播放
  Future<void> pause() async {
    var result = await flutterTts.pause();
    if (result == 1) {
      ttsState = TtsState.paused;
      stateNotifier.value = ttsState;
    }
  }

  // 获取可用的语音
  Future<List<String>> getAvailableVoices() async {
    List<dynamic> voices = await flutterTts.getVoices;
    return voices.map((e) => e.toString()).toList();
  }

  // 获取可用的语言
  Future<List<String>> getAvailableLanguages() async {
    List<dynamic> languages = await flutterTts.getLanguages;
    return languages.map((e) => e.toString()).toList();
  }

  // 设置语言
  Future<void> setLanguage(String language) async {
    await flutterTts.setLanguage(language);
  }

  // 设置语速
  Future<void> setSpeechRate(double rate) async {
    await flutterTts.setSpeechRate(rate);
  }

  // 设置音量
  Future<void> setVolume(double volume) async {
    await flutterTts.setVolume(volume);
  }

  // 设置音调
  Future<void> setPitch(double pitch) async {
    await flutterTts.setPitch(pitch);
  }

  // 销毁资源
  void dispose() {
    flutterTts.stop();
    stateNotifier.dispose();
    progressNotifier.dispose();
  }
}
