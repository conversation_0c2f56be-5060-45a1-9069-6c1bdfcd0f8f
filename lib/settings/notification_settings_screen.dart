import 'package:flutter/material.dart';

import '../config/app_theme.dart';
import '../services/service_locator.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  bool _enableNotifications = true;
  bool _enableTaskCompletion = true;
  bool _enableExportCompletion = true;
  bool _enableErrorAlerts = true;
  bool _enableUpdateNotifications = true;
  bool _enableTips = false;
  bool _enableSound = true;
  bool _enableVibration = true;
  
  TimeOfDay _quietHoursStart = const TimeOfDay(hour: 22, minute: 0);
  TimeOfDay _quietHoursEnd = const TimeOfDay(hour: 8, minute: 0);
  bool _enableQuietHours = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    if (ServiceLocator().isInitialized) {
      final settings = ServiceLocator().settingsService.settings;
      setState(() {
        _enableNotifications = settings.enableNotifications;
        // 其他设置可以从设置服务中加载
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: const Text(
          '通知设置',
          style: TextStyle(
            color: AppTheme.textDarkColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppTheme.bgWhiteColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.textDarkColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 总开关
            _buildMasterSwitch(),
            const SizedBox(height: 24),

            // 通知类型
            _buildSectionTitle('通知类型'),
            _buildNotificationTypes(),
            const SizedBox(height: 24),

            // 通知方式
            _buildSectionTitle('通知方式'),
            _buildNotificationMethods(),
            const SizedBox(height: 24),

            // 免打扰时间
            _buildSectionTitle('免打扰时间'),
            _buildQuietHours(),
            const SizedBox(height: 24),

            // 测试通知
            _buildTestSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildMasterSwitch() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: _enableNotifications 
            ? AppTheme.primaryGradient 
            : LinearGradient(
                colors: [Colors.grey.shade400, Colors.grey.shade500],
              ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            _enableNotifications ? Icons.notifications_active : Icons.notifications_off,
            size: 48,
            color: Colors.white,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '推送通知',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _enableNotifications ? '已开启' : '已关闭',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _enableNotifications,
            onChanged: (value) {
              setState(() {
                _enableNotifications = value;
              });
              _saveNotificationSettings();
            },
            activeColor: Colors.white,
            activeTrackColor: Colors.white.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.textDarkColor,
        ),
      ),
    );
  }

  Widget _buildNotificationTypes() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildNotificationOption(
            '任务完成',
            '处理任务完成时通知',
            Icons.task_alt,
            _enableTaskCompletion,
            (value) => setState(() => _enableTaskCompletion = value),
          ),
          _buildDivider(),
          _buildNotificationOption(
            '导出完成',
            '文件导出完成时通知',
            Icons.file_download_done,
            _enableExportCompletion,
            (value) => setState(() => _enableExportCompletion = value),
          ),
          _buildDivider(),
          _buildNotificationOption(
            '错误提醒',
            '发生错误时通知',
            Icons.error_outline,
            _enableErrorAlerts,
            (value) => setState(() => _enableErrorAlerts = value),
          ),
          _buildDivider(),
          _buildNotificationOption(
            '更新提醒',
            '应用有新版本时通知',
            Icons.system_update,
            _enableUpdateNotifications,
            (value) => setState(() => _enableUpdateNotifications = value),
          ),
          _buildDivider(),
          _buildNotificationOption(
            '使用技巧',
            '定期推送使用技巧',
            Icons.lightbulb_outline,
            _enableTips,
            (value) => setState(() => _enableTips = value),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationMethods() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildNotificationOption(
            '声音提醒',
            '通知时播放提示音',
            Icons.volume_up,
            _enableSound,
            (value) => setState(() => _enableSound = value),
          ),
          _buildDivider(),
          _buildNotificationOption(
            '振动提醒',
            '通知时振动设备',
            Icons.vibration,
            _enableVibration,
            (value) => setState(() => _enableVibration = value),
          ),
        ],
      ),
    );
  }

  Widget _buildQuietHours() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildNotificationOption(
            '启用免打扰',
            '在指定时间段内不发送通知',
            Icons.do_not_disturb,
            _enableQuietHours,
            (value) => setState(() => _enableQuietHours = value),
          ),
          if (_enableQuietHours) ...[
            _buildDivider(),
            _buildTimeOption(
              '开始时间',
              _quietHoursStart,
              (time) => setState(() => _quietHoursStart = time),
            ),
            _buildDivider(),
            _buildTimeOption(
              '结束时间',
              _quietHoursEnd,
              (time) => setState(() => _quietHoursEnd = time),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTestSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.notifications_active,
            size: 48,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(height: 12),
          const Text(
            '测试通知',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '发送一条测试通知来检查设置是否正常',
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textMediumColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _enableNotifications ? _sendTestNotification : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('发送测试通知'),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationOption(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    Function(bool) onChanged,
  ) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: AppTheme.primaryColor, size: 24),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value && _enableNotifications,
        onChanged: _enableNotifications ? onChanged : null,
      ),
    );
  }

  Widget _buildTimeOption(
    String title,
    TimeOfDay time,
    Function(TimeOfDay) onChanged,
  ) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(Icons.access_time, color: AppTheme.primaryColor, size: 24),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Text(time.format(context)),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () async {
        final newTime = await showTimePicker(
          context: context,
          initialTime: time,
        );
        if (newTime != null) {
          onChanged(newTime);
        }
      },
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      color: AppTheme.borderColor.withValues(alpha: 0.5),
      indent: 16,
      endIndent: 16,
    );
  }

  void _saveNotificationSettings() async {
    if (ServiceLocator().isInitialized) {
      await ServiceLocator().settingsService.updateSettings(
        enableNotifications: _enableNotifications,
      );
    }
  }

  void _sendTestNotification() {
    // 这里实现发送测试通知的逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('测试通知已发送'),
        backgroundColor: AppTheme.greenDark,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
