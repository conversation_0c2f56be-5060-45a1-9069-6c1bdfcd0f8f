import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'settings_screen.dart';
import 'ios_settings_screen.dart';

class SettingsDemoScreen extends StatelessWidget {
  const SettingsDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.systemGroupedBackground,
      navigationBar: const CupertinoNavigationBar(
        middle: Text('设置页面演示'),
        backgroundColor: CupertinoColors.systemBackground,
      ),
      child: Safe<PERSON>rea(
        child: ListView(
          children: [
            const SizedBox(height: 20),
            
            CupertinoListSection.insetGrouped(
              header: const Text('设置页面风格'),
              children: [
                CupertinoListTile.notched(
                  leading: Container(
                    width: 29,
                    height: 29,
                    decoration: BoxDecoration(
                      color: CupertinoColors.systemBlue,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      CupertinoIcons.settings,
                      color: CupertinoColors.white,
                      size: 18,
                    ),
                  ),
                  title: const Text('iOS 风格设置'),
                  subtitle: const Text('原生iOS设计风格，简洁美观'),
                  trailing: const CupertinoListTileChevron(),
                  onTap: () {
                    Navigator.push(
                      context,
                      CupertinoPageRoute(
                        builder: (context) => const IOSSettingsScreen(),
                      ),
                    );
                  },
                ),
                CupertinoListTile.notched(
                  leading: Container(
                    width: 29,
                    height: 29,
                    decoration: BoxDecoration(
                      color: CupertinoColors.systemPurple,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      CupertinoIcons.gear_alt,
                      color: CupertinoColors.white,
                      size: 18,
                    ),
                  ),
                  title: const Text('Material 风格设置'),
                  subtitle: const Text('Material Design风格，功能丰富'),
                  trailing: const CupertinoListTileChevron(),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SettingsScreen(),
                      ),
                    );
                  },
                ),
              ],
            ),

            const SizedBox(height: 20),

            CupertinoListSection.insetGrouped(
              header: const Text('设计特点'),
              footer: const Text('iOS风格采用系统原生设计语言，更符合iOS用户的使用习惯。'),
              children: [
                const CupertinoListTile.notched(
                  leading: Icon(
                    CupertinoIcons.checkmark_circle_fill,
                    color: CupertinoColors.systemGreen,
                  ),
                  title: Text('原生iOS设计'),
                  subtitle: Text('使用CupertinoPageScaffold和CupertinoListSection'),
                ),
                const CupertinoListTile.notched(
                  leading: Icon(
                    CupertinoIcons.color_filter,
                    color: CupertinoColors.systemBlue,
                  ),
                  title: Text('系统配色'),
                  subtitle: Text('使用iOS系统颜色，支持深色模式'),
                ),
                const CupertinoListTile.notched(
                  leading: Icon(
                    CupertinoIcons.rectangle_3_offgrid,
                    color: CupertinoColors.systemOrange,
                  ),
                  title: Text('分组布局'),
                  subtitle: Text('清晰的分组结构，符合iOS设计规范'),
                ),
                const CupertinoListTile.notched(
                  leading: Icon(
                    CupertinoIcons.hand_point_right,
                    color: CupertinoColors.systemPink,
                  ),
                  title: Text('原生交互'),
                  subtitle: Text('使用CupertinoActionSheet和CupertinoDialog'),
                ),
              ],
            ),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
}
