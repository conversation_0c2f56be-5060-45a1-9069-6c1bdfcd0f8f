import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import '../content/content_library_demo_page.dart';
import '../demo/i18n_demo_page.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/localization_service.dart';
import 'language_settings_page.dart';

/// 设置页面
class SettingsPage extends StatelessWidget {
  final LocalizationService localizationService;

  const SettingsPage({super.key, required this.localizationService});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: Text(
          l10n.settings,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.textDarkColor,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: AppTheme.textDarkColor),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // 页面标题
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.settings,
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textDarkColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '个性化您的应用体验',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.textMediumColor,
                  ),
                ),
              ],
            ),
          ),

          // 设置选项
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: [
                // 外观设置组
                _buildSettingsGroup(
                  title: '外观',
                  children: [
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.language,
                      title: l10n.language,
                      subtitle: localizationService.currentLocaleName,
                      onTap: () => _openLanguageSettings(context),
                    ),
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.palette,
                      title: l10n.theme,
                      subtitle: '跟随系统',
                      onTap: () => _showThemeDialog(context),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // 开发者设置组
                _buildSettingsGroup(
                  title: '开发者',
                  children: [
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.library_books,
                      title: '内容库演示',
                      subtitle: '查看新的内容库功能',
                      onTap: () => _openContentLibraryDemo(context),
                    ),
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.translate,
                      title: '国际化演示',
                      subtitle: '查看多语言支持效果',
                      onTap: () => _openI18nDemo(context),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // 关于设置组
                _buildSettingsGroup(
                  title: '关于',
                  children: [
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.info_outline,
                      title: '版本信息',
                      subtitle: '1.0.0',
                      onTap: () => _showAboutDialog(context),
                    ),
                    _buildSettingsItem(
                      context: context,
                      icon: Icons.help_outline,
                      title: '帮助与反馈',
                      subtitle: '获取帮助或提供反馈',
                      onTap: () => _showHelpDialog(context),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建设置组
  Widget _buildSettingsGroup({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8, bottom: 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: AppTheme.textMediumColor,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                offset: const Offset(0, 2),
                blurRadius: 10,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  /// 构建设置项
  Widget _buildSettingsItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(icon, color: AppTheme.primaryColor, size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppTheme.textDarkColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(fontSize: 14, color: AppTheme.textMediumColor),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppTheme.textLightColor,
      ),
      onTap: onTap,
    );
  }

  /// 打开语言设置
  void _openLanguageSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                LanguageSettingsPage(localizationService: localizationService),
      ),
    );
  }

  /// 打开内容库演示
  void _openContentLibraryDemo(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ContentLibraryDemoPage()),
    );
  }

  /// 打开国际化演示
  void _openI18nDemo(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => I18nDemoPage(localizationService: localizationService),
      ),
    );
  }

  /// 显示主题选择对话框
  void _showThemeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('选择主题'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: const Text('跟随系统'),
                  leading: const Icon(Icons.brightness_auto),
                  onTap: () => Navigator.pop(context),
                ),
                ListTile(
                  title: const Text('浅色模式'),
                  leading: const Icon(Icons.brightness_high),
                  onTap: () => Navigator.pop(context),
                ),
                ListTile(
                  title: const Text('深色模式'),
                  leading: const Icon(Icons.brightness_low),
                  onTap: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
    );
  }

  /// 显示关于对话框
  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'ContentPal',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Icon(Icons.content_paste, color: Colors.white, size: 32),
      ),
      children: [const Text('专业的内容处理工具，让内容创作更轻松。')],
    );
  }

  /// 显示帮助对话框
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('帮助与反馈'),
            content: const Text(
              '如果您有任何问题或建议，请通过以下方式联系我们：\n\n邮箱：<EMAIL>',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }
}
