import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../config/app_theme.dart';
import '../config/chinese_traditional_colors.dart';
import '../config/constants.dart';
import '../services/service_locator.dart';
import '../widgets/chinese_traditional_color_theme_selector.dart';
import 'help_center_screen.dart';
import 'feedback_screen.dart';
import 'storage_management_screen.dart';
import 'notification_settings_screen.dart';

class IOSSettingsScreen extends StatefulWidget {
  const IOSSettingsScreen({super.key});

  @override
  State<IOSSettingsScreen> createState() => _IOSSettingsScreenState();
}

class _IOSSettingsScreenState extends State<IOSSettingsScreen> {
  ThemeMode _themeMode = ThemeMode.system;
  ChineseTraditionalColorTheme? _chineseTraditionalColorTheme;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    if (ServiceLocator().isInitialized) {
      final settings = ServiceLocator().settingsService.settings;
      setState(() {
        _themeMode = settings.themeMode;
        _chineseTraditionalColorTheme = settings.chineseTraditionalColorTheme;
      });
    } else {
      setState(() {
        _themeMode = ThemeMode.system;
        _chineseTraditionalColorTheme = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.systemGroupedBackground,
      navigationBar: const CupertinoNavigationBar(
        middle: Text('设置'),
        backgroundColor: CupertinoColors.systemBackground,
      ),
      child: SafeArea(
        child: ListView(
          children: [
            const SizedBox(height: 20),

            // 通用设置
            CupertinoListSection.insetGrouped(
              header: const Text('通用'),
              children: [
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.bell,
                    CupertinoColors.systemBlue,
                  ),
                  title: const Text('通知'),
                  trailing: const CupertinoListTileChevron(),
                  onTap: () {
                    Navigator.push(
                      context,
                      CupertinoPageRoute(
                        builder:
                            (context) => const NotificationSettingsScreen(),
                      ),
                    );
                  },
                ),
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.globe,
                    CupertinoColors.systemGrey,
                  ),
                  title: const Text('语言与地区'),
                  subtitle: Text(_getCurrentLanguageDisplayName()),
                  trailing: const CupertinoListTileChevron(),
                  onTap: _showLanguageDialog,
                ),
              ],
            ),

            // 显示与亮度
            CupertinoListSection.insetGrouped(
              header: const Text('显示与亮度'),
              children: [
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.brightness,
                    CupertinoColors.systemIndigo,
                  ),
                  title: const Text('外观'),
                  subtitle: Text(_getThemeModeDisplayName()),
                  trailing: const CupertinoListTileChevron(),
                  onTap: _showThemeDialog,
                ),
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.paintbrush,
                    CupertinoColors.systemPink,
                  ),
                  title: const Text('中国传统色'),
                  subtitle: Text(_getChineseColorDisplayName()),
                  trailing: const CupertinoListTileChevron(),
                  onTap: _showChineseTraditionalColorDialog,
                ),
              ],
            ),

            // 隐私与安全
            CupertinoListSection.insetGrouped(
              header: const Text('隐私与安全'),
              children: [
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.lock_shield,
                    CupertinoColors.systemGreen,
                  ),
                  title: const Text('隐私设置'),
                  trailing: const CupertinoListTileChevron(),
                  onTap: _showPrivacySettings,
                ),
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.floppy_disk,
                    CupertinoColors.systemOrange,
                  ),
                  title: const Text('自动保存'),
                  trailing: CupertinoSwitch(
                    value:
                        ServiceLocator().isInitialized
                            ? ServiceLocator()
                                .settingsService
                                .settings
                                .autoSaveChat
                            : true,
                    onChanged: (value) async {
                      if (ServiceLocator().isInitialized) {
                        await ServiceLocator().settingsService.updateSettings(
                          autoSaveChat: value,
                        );
                        setState(() {});
                      }
                    },
                  ),
                ),
              ],
            ),

            // 存储
            CupertinoListSection.insetGrouped(
              header: const Text('存储'),
              children: [
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.folder,
                    CupertinoColors.systemTeal,
                  ),
                  title: const Text('存储管理'),
                  subtitle: const Text('查看存储使用情况'),
                  trailing: const CupertinoListTileChevron(),
                  onTap: () {
                    Navigator.push(
                      context,
                      CupertinoPageRoute(
                        builder: (context) => const StorageManagementScreen(),
                      ),
                    );
                  },
                ),
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.arrow_up_arrow_down_circle,
                    CupertinoColors.systemPurple,
                  ),
                  title: const Text('数据导入导出'),
                  subtitle: const Text('备份和恢复数据'),
                  trailing: const CupertinoListTileChevron(),
                  onTap: _showDataManagement,
                ),
              ],
            ),

            // 支持
            CupertinoListSection.insetGrouped(
              header: const Text('支持'),
              children: [
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.question_circle,
                    CupertinoColors.systemBlue,
                  ),
                  title: const Text('帮助中心'),
                  trailing: const CupertinoListTileChevron(),
                  onTap: () {
                    Navigator.push(
                      context,
                      CupertinoPageRoute(
                        builder: (context) => const HelpCenterScreen(),
                      ),
                    );
                  },
                ),
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.chat_bubble_text,
                    CupertinoColors.systemGreen,
                  ),
                  title: const Text('意见反馈'),
                  trailing: const CupertinoListTileChevron(),
                  onTap: () {
                    Navigator.push(
                      context,
                      CupertinoPageRoute(
                        builder: (context) => const FeedbackScreen(),
                      ),
                    );
                  },
                ),
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.star,
                    CupertinoColors.systemYellow,
                  ),
                  title: const Text('评价应用'),
                  trailing: const CupertinoListTileChevron(),
                  onTap: _showRatingDialog,
                ),
              ],
            ),

            // 关于
            CupertinoListSection.insetGrouped(
              header: const Text('关于'),
              children: [
                CupertinoListTile.notched(
                  leading: _buildIconContainer(
                    CupertinoIcons.info_circle,
                    CupertinoColors.systemGrey,
                  ),
                  title: const Text('关于应用'),
                  subtitle: Text('版本 ${AppConstants.appVersion}'),
                  trailing: const CupertinoListTileChevron(),
                  onTap: _showAboutDialog,
                ),
              ],
            ),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildIconContainer(IconData icon, Color color) {
    return Container(
      width: 29,
      height: 29,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(icon, color: CupertinoColors.white, size: 18),
    );
  }

  String _getCurrentLanguageDisplayName() {
    return '简体中文';
  }

  String _getThemeModeDisplayName() {
    switch (_themeMode) {
      case ThemeMode.light:
        return '浅色';
      case ThemeMode.dark:
        return '深色';
      case ThemeMode.system:
        return '跟随系统';
    }
  }

  String _getChineseColorDisplayName() {
    if (_chineseTraditionalColorTheme != null) {
      return _chineseTraditionalColorTheme!.name;
    }
    return '默认';
  }

  void _showLanguageDialog() {
    showCupertinoModalPopup(
      context: context,
      builder:
          (context) => CupertinoActionSheet(
            title: const Text('选择语言'),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _showLanguageChangeSnackBar('简体中文');
                },
                child: const Text('简体中文'),
              ),
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _showLanguageChangeSnackBar('繁體中文');
                },
                child: const Text('繁體中文'),
              ),
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _showLanguageChangeSnackBar('English');
                },
                child: const Text('English'),
              ),
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
          ),
    );
  }

  void _showLanguageChangeSnackBar(String language) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已选择语言：$language'),
        backgroundColor: CupertinoColors.systemBlue,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showThemeDialog() {
    showCupertinoModalPopup(
      context: context,
      builder:
          (context) => CupertinoActionSheet(
            title: const Text('选择外观'),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _updateThemeMode(ThemeMode.light);
                },
                child: const Text('浅色'),
              ),
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _updateThemeMode(ThemeMode.dark);
                },
                child: const Text('深色'),
              ),
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _updateThemeMode(ThemeMode.system);
                },
                child: const Text('跟随系统'),
              ),
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
          ),
    );
  }

  void _updateThemeMode(ThemeMode themeMode) async {
    setState(() {
      _themeMode = themeMode;
    });
    if (ServiceLocator().isInitialized) {
      await ServiceLocator().settingsService.updateThemeMode(themeMode);
    }
  }

  void _showChineseTraditionalColorDialog() {
    showCupertinoModalPopup(
      context: context,
      builder:
          (context) => CupertinoPageScaffold(
            navigationBar: CupertinoNavigationBar(
              middle: const Text('中国传统色'),
              trailing: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () => Navigator.pop(context),
                child: const Text('完成'),
              ),
            ),
            child: SafeArea(
              child: ChineseTraditionalColorThemeSelector(
                currentTheme: _chineseTraditionalColorTheme,
                onThemeChanged: (theme) {
                  setState(() {
                    _chineseTraditionalColorTheme = theme;
                  });
                  if (ServiceLocator().isInitialized) {
                    ServiceLocator().settingsService
                        .updateChineseTraditionalColorTheme(theme);
                  }
                },
              ),
            ),
          ),
    );
  }

  void _showPrivacySettings() {
    showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: const Text('隐私设置'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 16),
                Text('我们重视您的隐私。所有数据处理都在本地进行，不会上传到服务器。'),
                SizedBox(height: 16),
                Text('您可以随时在设置中管理您的数据。'),
              ],
            ),
            actions: [
              CupertinoDialogAction(
                onPressed: () => Navigator.pop(context),
                child: const Text('了解'),
              ),
            ],
          ),
    );
  }

  void _showDataManagement() {
    showCupertinoModalPopup(
      context: context,
      builder:
          (context) => CupertinoActionSheet(
            title: const Text('数据管理'),
            message: const Text('选择要执行的操作'),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _exportData();
                },
                child: const Text('导出数据'),
              ),
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _importData();
                },
                child: const Text('导入数据'),
              ),
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _createBackup();
                },
                child: const Text('创建备份'),
              ),
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
          ),
    );
  }

  void _exportData() {
    _showInfoDialog('数据导出', '数据导出功能开发中...');
  }

  void _importData() {
    _showInfoDialog('数据导入', '数据导入功能开发中...');
  }

  void _createBackup() {
    _showInfoDialog('创建备份', '备份功能开发中...');
  }

  void _showRatingDialog() {
    showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: const Text('评价应用'),
            content: const Text('您喜欢这个应用吗？请在App Store中为我们评分！'),
            actions: [
              CupertinoDialogAction(
                onPressed: () => Navigator.pop(context),
                child: const Text('稍后'),
              ),
              CupertinoDialogAction(
                onPressed: () {
                  Navigator.pop(context);
                  _openAppStore();
                },
                child: const Text('去评价'),
              ),
            ],
          ),
    );
  }

  void _openAppStore() async {
    final url = 'https://apps.apple.com/app/id123456789'; // 替换为实际的App Store ID
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showInfoDialog('错误', '无法打开App Store');
      }
    } catch (e) {
      _showInfoDialog('错误', '打开App Store时出现错误');
    }
  }

  void _showAboutDialog() {
    showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: Text(AppConstants.appNameChinese),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                Text('版本: ${AppConstants.appVersion}'),
                const SizedBox(height: 8),
                const Text('一款强大的内容管理工具，帮助您更高效地创建和管理各种格式的内容。'),
                const SizedBox(height: 16),
                const Text(
                  '© 2023-2024 ContentPal团队',
                  style: TextStyle(
                    color: CupertinoColors.secondaryLabel,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            actions: [
              CupertinoDialogAction(
                onPressed: () => Navigator.pop(context),
                child: const Text('关闭'),
              ),
            ],
          ),
    );
  }

  void _showInfoDialog(String title, String message) {
    showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: Text(title),
            content: Text(message),
            actions: [
              CupertinoDialogAction(
                onPressed: () => Navigator.pop(context),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }
}
