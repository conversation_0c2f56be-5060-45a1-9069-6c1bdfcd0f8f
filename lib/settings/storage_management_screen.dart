import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

import '../config/app_theme.dart';
import '../services/service_locator.dart';

class StorageManagementScreen extends StatefulWidget {
  const StorageManagementScreen({super.key});

  @override
  State<StorageManagementScreen> createState() => _StorageManagementScreenState();
}

class _StorageManagementScreenState extends State<StorageManagementScreen> {
  bool _isLoading = true;
  StorageInfo? _storageInfo;

  @override
  void initState() {
    super.initState();
    _loadStorageInfo();
  }

  Future<void> _loadStorageInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final storageInfo = await _calculateStorageInfo();
      setState(() {
        _storageInfo = storageInfo;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('加载存储信息失败：$e');
    }
  }

  Future<StorageInfo> _calculateStorageInfo() async {
    final appDir = await getApplicationDocumentsDirectory();
    final cacheDir = await getTemporaryDirectory();
    
    final appSize = await _getDirectorySize(appDir);
    final cacheSize = await _getDirectorySize(cacheDir);
    
    // 计算各类数据大小
    final contentSize = await _getContentDataSize();
    final settingsSize = await _getSettingsDataSize();
    final voiceSize = await _getVoiceDataSize();
    final imageSize = await _getImageDataSize();
    
    return StorageInfo(
      totalAppSize: appSize,
      cacheSize: cacheSize,
      contentDataSize: contentSize,
      settingsDataSize: settingsSize,
      voiceDataSize: voiceSize,
      imageDataSize: imageSize,
    );
  }

  Future<int> _getDirectorySize(Directory directory) async {
    int size = 0;
    try {
      if (await directory.exists()) {
        await for (final entity in directory.list(recursive: true)) {
          if (entity is File) {
            size += await entity.length();
          }
        }
      }
    } catch (e) {
      debugPrint('计算目录大小失败: $e');
    }
    return size;
  }

  Future<int> _getContentDataSize() async {
    // 计算内容数据大小（Hive数据库等）
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final hiveDir = Directory('${appDir.path}/hive');
      return await _getDirectorySize(hiveDir);
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getSettingsDataSize() async {
    // 计算设置数据大小
    try {
      final storage = ServiceLocator().storageService;
      // 这里可以根据实际的存储实现来计算
      return 1024; // 假设设置数据很小
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getVoiceDataSize() async {
    // 计算语音数据大小
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final voiceDir = Directory('${appDir.path}/voice');
      return await _getDirectorySize(voiceDir);
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getImageDataSize() async {
    // 计算图片数据大小
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final imageDir = Directory('${appDir.path}/images');
      return await _getDirectorySize(imageDir);
    } catch (e) {
      return 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: const Text(
          '存储管理',
          style: TextStyle(
            color: AppTheme.textDarkColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppTheme.bgWhiteColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.textDarkColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: AppTheme.textDarkColor),
            onPressed: _loadStorageInfo,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _storageInfo == null
              ? const Center(child: Text('加载存储信息失败'))
              : _buildStorageContent(),
    );
  }

  Widget _buildStorageContent() {
    final info = _storageInfo!;
    final totalSize = info.totalAppSize + info.cacheSize;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 总体存储概览
          _buildStorageOverview(totalSize),
          const SizedBox(height: 24),

          // 存储详情
          _buildSectionTitle('存储详情'),
          _buildStorageItem(
            '应用数据',
            info.totalAppSize,
            Icons.apps,
            AppTheme.blueGradient,
          ),
          _buildStorageItem(
            '缓存文件',
            info.cacheSize,
            Icons.cached,
            AppTheme.orangeGradient,
            onTap: () => _clearCache(),
          ),
          _buildStorageItem(
            '内容数据',
            info.contentDataSize,
            Icons.text_snippet,
            AppTheme.greenGradient,
          ),
          _buildStorageItem(
            '语音文件',
            info.voiceDataSize,
            Icons.mic,
            AppTheme.purpleGradient,
            onTap: () => _manageVoiceFiles(),
          ),
          _buildStorageItem(
            '图片文件',
            info.imageDataSize,
            Icons.image,
            AppTheme.chineseGradient,
            onTap: () => _manageImageFiles(),
          ),
          _buildStorageItem(
            '设置数据',
            info.settingsDataSize,
            Icons.settings,
            AppTheme.primaryGradient,
          ),

          const SizedBox(height: 24),

          // 清理选项
          _buildSectionTitle('清理选项'),
          _buildCleanupOptions(),

          const SizedBox(height: 24),

          // 数据管理
          _buildSectionTitle('数据管理'),
          _buildDataManagementOptions(),
        ],
      ),
    );
  }

  Widget _buildStorageOverview(int totalSize) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.storage,
            size: 48,
            color: Colors.white,
          ),
          const SizedBox(height: 12),
          const Text(
            '总存储使用',
            style: TextStyle(
              fontSize: 18,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _formatBytes(totalSize),
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.textDarkColor,
        ),
      ),
    );
  }

  Widget _buildStorageItem(
    String title,
    int size,
    IconData icon,
    LinearGradient gradient, {
    VoidCallback? onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            gradient: gradient,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 24),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(_formatBytes(size)),
        trailing: onTap != null
            ? const Icon(Icons.arrow_forward_ios, size: 16)
            : null,
        onTap: onTap,
      ),
    );
  }

  Widget _buildCleanupOptions() {
    return Column(
      children: [
        _buildActionCard(
          '清理缓存',
          '删除临时文件和缓存数据',
          Icons.cleaning_services,
          AppTheme.orangeGradient,
          () => _clearCache(),
        ),
        const SizedBox(height: 8),
        _buildActionCard(
          '清理临时文件',
          '删除处理过程中产生的临时文件',
          Icons.delete_sweep,
          AppTheme.redGradient,
          () => _clearTempFiles(),
        ),
      ],
    );
  }

  Widget _buildDataManagementOptions() {
    return Column(
      children: [
        _buildActionCard(
          '导出数据',
          '将应用数据导出到文件',
          Icons.file_download,
          AppTheme.greenGradient,
          () => _exportData(),
        ),
        const SizedBox(height: 8),
        _buildActionCard(
          '导入数据',
          '从文件导入应用数据',
          Icons.file_upload,
          AppTheme.blueGradient,
          () => _importData(),
        ),
        const SizedBox(height: 8),
        _buildActionCard(
          '重置应用数据',
          '清除所有数据并恢复默认设置',
          Icons.restore,
          AppTheme.redGradient,
          () => _resetAppData(),
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    IconData icon,
    LinearGradient gradient,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            gradient: gradient,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 24),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  Future<void> _clearCache() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
        await cacheDir.create();
      }
      _showSuccessSnackBar('缓存清理完成');
      _loadStorageInfo();
    } catch (e) {
      _showErrorSnackBar('清理缓存失败：$e');
    }
  }

  Future<void> _clearTempFiles() async {
    try {
      // 清理临时文件的逻辑
      _showSuccessSnackBar('临时文件清理完成');
      _loadStorageInfo();
    } catch (e) {
      _showErrorSnackBar('清理临时文件失败：$e');
    }
  }

  void _manageVoiceFiles() {
    // 导航到语音文件管理页面
    _showInfoSnackBar('语音文件管理功能开发中');
  }

  void _manageImageFiles() {
    // 导航到图片文件管理页面
    _showInfoSnackBar('图片文件管理功能开发中');
  }

  void _exportData() {
    // 导出数据功能
    _showInfoSnackBar('数据导出功能开发中');
  }

  void _importData() {
    // 导入数据功能
    _showInfoSnackBar('数据导入功能开发中');
  }

  void _resetAppData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置应用数据'),
        content: const Text('此操作将删除所有数据并恢复默认设置，无法撤销。确定要继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performReset();
            },
            child: const Text('确定', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _performReset() async {
    try {
      // 重置应用数据的逻辑
      _showSuccessSnackBar('应用数据重置完成');
      _loadStorageInfo();
    } catch (e) {
      _showErrorSnackBar('重置应用数据失败：$e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.greenDark,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.redDark,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.primaryColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

class StorageInfo {
  final int totalAppSize;
  final int cacheSize;
  final int contentDataSize;
  final int settingsDataSize;
  final int voiceDataSize;
  final int imageDataSize;

  StorageInfo({
    required this.totalAppSize,
    required this.cacheSize,
    required this.contentDataSize,
    required this.settingsDataSize,
    required this.voiceDataSize,
    required this.imageDataSize,
  });
}
