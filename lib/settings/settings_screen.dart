import 'dart:io';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../config/app_theme.dart';
import '../config/chinese_traditional_colors.dart';
import '../config/constants.dart';
import '../services/service_locator.dart';

// import '../subscription/subscription_settings_screen.dart'; // 暂时注释掉
import '../widgets/chinese_traditional_color_theme_selector.dart';
import 'help_center_screen.dart';
import 'feedback_screen.dart';
import 'storage_management_screen.dart';
import 'notification_settings_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;

  // 主题选择
  ThemeMode _themeMode = ThemeMode.system;

  // 中国传统色主题选择
  ChineseTraditionalColorTheme? _chineseTraditionalColorTheme;



  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat();

    _scrollController.addListener(_onScroll);
    _loadSettings();
  }

  // 加载设置
  Future<void> _loadSettings() async {
    if (ServiceLocator().isInitialized) {
      final settings = ServiceLocator().settingsService.settings;
      setState(() {
        _themeMode = settings.themeMode;
        _chineseTraditionalColorTheme = settings.chineseTraditionalColorTheme;
      });
    } else {
      setState(() {
        _themeMode = ThemeMode.system;
        _chineseTraditionalColorTheme = null;
      });
    }
  }

  // 保存主题模式
  Future<void> _saveThemeMode(ThemeMode themeMode) async {
    await ServiceLocator().settingsService.updateThemeMode(themeMode);
  }

  void _onScroll() {
    if (_scrollController.offset > 50 && !_isScrolled) {
      setState(() => _isScrolled = true);
    } else if (_scrollController.offset <= 50 && _isScrolled) {
      setState(() => _isScrolled = false);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // 背景动态效果
          _buildAnimatedBackground(),

          // 主内容
          _buildMainContent(),
        ],
      ),
    );
  }

  // 构建动态背景
  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Stack(
          children: [
            // 基础浅色背景
            Container(decoration: BoxDecoration(color: AppTheme.bgLightColor)),

            // 顶部渐变装饰
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: 220,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppTheme.primaryColor.withValues(alpha: 0.2),
                      AppTheme.primaryColor.withValues(alpha: 0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),

            // 顶部右侧装饰图案
            Positioned(
              top: -30,
              right: -30,
              child: Transform.rotate(
                angle: math.pi / 6,
                child: Container(
                  width: 180,
                  height: 180,
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(40),
                  ),
                ),
              ),
            ),

            // 轻量级浮动粒子效果
            Opacity(
              opacity: 0.1,
              child: CustomPaint(
                size: Size.infinite,
                painter: LightParticlesPainter(_animationController.value),
              ),
            ),
          ],
        );
      },
    );
  }

  // 构建主要内容
  Widget _buildMainContent() {
    return SafeArea(
      child: CustomScrollView(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(),
        slivers: [
          // 应用栏
          SliverAppBar(
            expandedHeight: 130,
            floating: false,
            pinned: true,
            stretch: true,
            elevation: _isScrolled ? 4 : 0,
            backgroundColor:
                _isScrolled
                    ? AppTheme.bgWhiteColor.withValues(alpha: 0.95)
                    : Colors.transparent,
            title:
                _isScrolled
                    ? const Text(
                      '设置',
                      style: TextStyle(
                        color: AppTheme.textDarkColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    )
                    : null,
            flexibleSpace: FlexibleSpaceBar(
              title:
                  !_isScrolled
                      ? const Text(
                        '设置',
                        style: TextStyle(
                          color: AppTheme.textDarkColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 28,
                        ),
                      )
                      : null,
              titlePadding: const EdgeInsets.only(left: 20, bottom: 16),
              collapseMode: CollapseMode.pin,
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // 轻微的波浪动画效果
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    height: 40,
                    child: AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return CustomPaint(
                          painter: LightWavePainter(
                            _animationController.value,
                            AppTheme.primaryColor.withValues(alpha: 0.1),
                          ),
                        );
                      },
                    ),
                  ),

                  // 设置图标装饰
                  Positioned(
                    right: 20,
                    bottom: 30,
                    child: Icon(
                      Icons.settings,
                      size: 60,
                      color: AppTheme.primaryColor.withValues(alpha: 0.15),
                    ),
                  ),
                ],
              ),
            ),
            leading: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.arrow_back,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ),

          // 设置内容部分
          SliverPadding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 100),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                const SizedBox(height: 8),

                // 个人设置部分
                _buildSectionHeader('个人设置', Icons.person_outline),
                _buildThemeSetting(),
                const SizedBox(height: 12),
                _buildChineseTraditionalColorSetting(),
                const SizedBox(height: 12),
                _buildLanguageSetting(),
                const SizedBox(height: 24),

                // 应用设置部分
                _buildSectionHeader('应用设置', Icons.tune),
                _buildCardItem(
                  '通知设置',
                  '管理推送通知和提醒',
                  Icons.notifications_outlined,
                  AppTheme.blueGradient,
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) => const NotificationSettingsScreen(),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 12),
                _buildCardItem(
                  '隐私设置',
                  '管理数据隐私和安全选项',
                  Icons.privacy_tip_outlined,
                  AppTheme.purpleGradient,
                  () {
                    _showPrivacySettings();
                  },
                ),
                const SizedBox(height: 12),
                _buildAutoSaveSetting(),
                const SizedBox(height: 24),

                // 数据管理部分
                _buildSectionHeader('数据管理', Icons.storage),
                _buildCardItem(
                  '存储管理',
                  '查看存储使用情况和清理缓存',
                  Icons.folder_outlined,
                  AppTheme.orangeGradient,
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const StorageManagementScreen(),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 12),
                _buildCardItem(
                  '数据导入导出',
                  '备份和恢复应用数据',
                  Icons.import_export,
                  AppTheme.greenGradient,
                  () {
                    _showDataManagement();
                  },
                ),
                const SizedBox(height: 24),

                // 支持与反馈
                _buildSectionHeader('支持与反馈', Icons.help_outline),
                _buildCardItem(
                  '帮助中心',
                  '查看常见问题和使用指南',
                  Icons.help_outline,
                  AppTheme.blueGradient,
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const HelpCenterScreen(),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 12),
                _buildCardItem(
                  '意见反馈',
                  '向我们提供您的建议和意见',
                  Icons.feedback_outlined,
                  AppTheme.purpleGradient,
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const FeedbackScreen(),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 12),
                _buildCardItem(
                  '评价应用',
                  '在应用商店中为我们评分',
                  Icons.star_outline,
                  AppTheme.orangeGradient,
                  () {
                    _showRatingDialog();
                  },
                ),
                const SizedBox(height: 12),
                _buildCardItem(
                  '关于应用',
                  '了解应用版本和开发者信息',
                  Icons.info_outline,
                  AppTheme.greenGradient,
                  () {
                    _showAboutDialog();
                  },
                ),
                const SizedBox(height: 24),

                // 高级设置
                _buildSectionHeader('高级设置', Icons.settings_applications),
                _buildCardItem(
                  '开发者选项',
                  '调试和开发者工具',
                  Icons.developer_mode,
                  AppTheme.redGradient,
                  () {
                    _showDeveloperOptions();
                  },
                ),
                const SizedBox(height: 32),

                // 版本信息
                _buildVersionInfo(),
                const SizedBox(height: 8),
              ]),
            ),
          ),
        ],
      ),
    );
  }

  // 构建部分标题
  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12, left: 4),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, size: 16, color: AppTheme.primaryColor),
          ),
          const SizedBox(width: 10),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
          ),
        ],
      ),
    );
  }

  // 构建卡片项
  Widget _buildCardItem(
    String title,
    String subtitle,
    IconData icon,
    LinearGradient gradient,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
      child: Ink(
        decoration: BoxDecoration(
          color: AppTheme.bgWhiteColor,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: gradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: Colors.white, size: 26),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textDarkColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textMediumColor,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppTheme.textLightColor,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建主题设置卡片
  Widget _buildThemeSetting() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.palette_outlined,
                  color: AppTheme.primaryColor,
                  size: 26,
                ),
              ),
              const SizedBox(width: 16),
              const Text(
                '应用主题',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textDarkColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildThemeOption(
                '浅色',
                Icons.light_mode_outlined,
                _themeMode == ThemeMode.light,
                () {
                  setState(() {
                    _themeMode = ThemeMode.light;
                  });
                  _saveThemeMode(ThemeMode.light);
                },
              ),
              _buildThemeOption(
                '深色',
                Icons.dark_mode_outlined,
                _themeMode == ThemeMode.dark,
                () {
                  setState(() {
                    _themeMode = ThemeMode.dark;
                  });
                  _saveThemeMode(ThemeMode.dark);
                },
              ),
              _buildThemeOption(
                '跟随系统',
                Icons.settings_brightness,
                _themeMode == ThemeMode.system,
                () {
                  setState(() {
                    _themeMode = ThemeMode.system;
                  });
                  _saveThemeMode(ThemeMode.system);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建主题选项
  Widget _buildThemeOption(
    String label,
    IconData icon,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? AppTheme.primaryColor.withValues(alpha: 0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : AppTheme.borderColor,
            width: 1.5,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color:
                  isSelected ? AppTheme.primaryColor : AppTheme.textMediumColor,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color:
                    isSelected
                        ? AppTheme.primaryColor
                        : AppTheme.textMediumColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建中国传统色设置卡片
  Widget _buildChineseTraditionalColorSetting() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _showChineseTraditionalColorDialog,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    gradient: AppTheme.chineseGradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.palette,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '中国传统色',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textDarkColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _chineseTraditionalColorTheme != null
                            ? ChineseTraditionalColors.getThemeName(
                              _chineseTraditionalColorTheme!,
                            )
                            : '跟随系统主题',
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppTheme.textMediumColor,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.chevron_right,
                  color: AppTheme.textMediumColor,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 显示中国传统色选择对话框
  void _showChineseTraditionalColorDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
              child: ChineseTraditionalColorThemeSelector(
                currentTheme: _chineseTraditionalColorTheme,
                onThemeChanged: (theme) {
                  setState(() {
                    _chineseTraditionalColorTheme = theme;
                  });
                  Navigator.of(context).pop();
                },
              ),
            ),
          ),
    );
  }

  // 构建语言设置卡片
  Widget _buildLanguageSetting() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.language,
                  color: AppTheme.primaryColor,
                  size: 26,
                ),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '语言',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textDarkColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getCurrentLanguageDisplayName(),
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textMediumColor,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              TextButton(
                onPressed: _showLanguageDialog,
                child: const Text(
                  '更改',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 获取当前语言显示名称
  String _getCurrentLanguageDisplayName() {
    // 暂时返回简体中文，将来可以从设置中读取
    return '简体中文';
  }

  // 显示语言选择对话框
  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('选择语言'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildLanguageOption('简体中文', 'zh_CN'),
                _buildLanguageOption('繁體中文', 'zh_TW'),
                _buildLanguageOption('English', 'en_US'),
                _buildLanguageOption('日本語', 'ja_JP'),
                _buildLanguageOption('Español', 'es_ES'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
            ],
          ),
    );
  }

  // 构建语言选项
  Widget _buildLanguageOption(String language, String localeCode) {
    final currentLanguage = _getCurrentLanguageDisplayName();
    return ListTile(
      title: Text(language),
      trailing:
          currentLanguage == language
              ? const Icon(Icons.check_circle, color: AppTheme.primaryColor)
              : null,
      onTap: () {
        // 这里可以保存语言设置到设置服务
        // 暂时只显示选择效果
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已选择语言：$language'),
            backgroundColor: AppTheme.primaryColor,
          ),
        );
        Navigator.pop(context);
      },
    );
  }

  // 显示评价对话框
  void _showRatingDialog() {
    int selectedRating = 0;

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('评价应用'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        '请为我们的应用评分',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // 星级评分
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(5, (index) {
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedRating = index + 1;
                              });
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              child: Icon(
                                index < selectedRating
                                    ? Icons.star
                                    : Icons.star_border,
                                color: Colors.amber,
                                size: 40,
                              ),
                            ),
                          );
                        }),
                      ),

                      const SizedBox(height: 16),

                      // 评分说明
                      Text(
                        selectedRating == 0
                            ? '点击星星进行评分'
                            : selectedRating >= 4
                            ? '感谢您的支持！'
                            : selectedRating >= 3
                            ? '我们会继续改进'
                            : '我们会努力做得更好',
                        style: TextStyle(
                          fontSize: 16,
                          color:
                              selectedRating >= 4
                                  ? AppTheme.greenDark
                                  : AppTheme.textMediumColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),

                      const SizedBox(height: 20),

                      // 按钮
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildRatingButton(
                            '稍后',
                            Icons.schedule,
                            AppTheme.textMediumColor,
                            () {
                              Navigator.pop(context);
                            },
                          ),
                          _buildRatingButton(
                            '提交评价',
                            Icons.star,
                            selectedRating >= 4
                                ? Colors.amber
                                : AppTheme.primaryColor,
                            () {
                              Navigator.pop(context);
                              if (selectedRating >= 4) {
                                // 高分评价，直接跳转到应用商店
                                _openAppStore();
                              } else {
                                // 低分评价，显示反馈对话框
                                _showFeedbackDialog();
                              }
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
          ),
    );
  }

  // 构建评价按钮
  Widget _buildRatingButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              text,
              style: TextStyle(color: color, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  // 打开应用商店
  Future<void> _openAppStore() async {
    String url;

    if (Platform.isIOS || Platform.isMacOS) {
      // iOS/macOS App Store URL - 使用通用的评分链接
      // 注意：需要替换为实际的应用ID
      url =
          'https://apps.apple.com/app/contentpal/id1234567890?action=write-review';
    } else if (Platform.isAndroid) {
      // Google Play Store URL
      url =
          'https://play.google.com/store/apps/details?id=com.example.contentpal';
    } else {
      // 其他平台
      url = 'https://example.com/app';
    }

    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);

        // 显示成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('正在打开应用商店...'),
            backgroundColor: AppTheme.greenDark,
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else {
        _showErrorSnackBar('无法打开应用商店');
      }
    } catch (e) {
      _showErrorSnackBar('打开应用商店时出现错误');
    }
  }

  // 显示反馈对话框
  void _showFeedbackDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('意见反馈'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.feedback_outlined,
                  color: AppTheme.primaryColor,
                  size: 48,
                ),
                SizedBox(height: 16),
                Text(
                  '感谢您的反馈！\n我们会认真考虑您的建议，\n努力改进应用体验。',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('关闭'),
              ),
            ],
          ),
    );
  }

  // 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.redDark,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // 显示关于应用对话框
  void _showAboutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(AppConstants.appName),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('版本: ${AppConstants.appVersion}'),
                const SizedBox(height: 8),
                const Text('一款强大的内容管理工具，帮助您更高效地创建和管理各种格式的内容。'),
                const SizedBox(height: 16),
                const Text(
                  '© 2023-2024 ContentPal团队',
                  style: TextStyle(
                    color: AppTheme.textLightColor,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('关闭'),
              ),
            ],
          ),
    );
  }

  // 构建自动保存设置
  Widget _buildAutoSaveSetting() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: SwitchListTile(
        secondary: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.save_outlined,
            color: AppTheme.primaryColor,
            size: 24,
          ),
        ),
        title: const Text(
          '自动保存',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: const Text('自动保存您的工作内容'),
        value:
            ServiceLocator().isInitialized
                ? ServiceLocator().settingsService.settings.autoSaveChat
                : true,
        onChanged: (value) async {
          if (ServiceLocator().isInitialized) {
            await ServiceLocator().settingsService.updateSettings(
              autoSaveChat: value,
            );
            setState(() {});
          }
        },
      ),
    );
  }

  // 构建版本信息
  Widget _buildVersionInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.info_outline,
            size: 48,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(height: 12),
          Text(
            AppConstants.appNameChinese,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'v${AppConstants.appVersion}',
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.textMediumColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppConstants.appDescription,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textLightColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // 显示隐私设置
  void _showPrivacySettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('隐私设置'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SwitchListTile(
                  title: const Text('数据分析'),
                  subtitle: const Text('帮助改进应用体验'),
                  value: false,
                  onChanged: (value) {
                    // 实现数据分析开关
                  },
                ),
                SwitchListTile(
                  title: const Text('错误报告'),
                  subtitle: const Text('自动发送崩溃报告'),
                  value: true,
                  onChanged: (value) {
                    // 实现错误报告开关
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('关闭'),
              ),
            ],
          ),
    );
  }

  // 显示数据管理选项
  void _showDataManagement() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  '数据管理',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                ListTile(
                  leading: const Icon(Icons.file_download),
                  title: const Text('导出数据'),
                  subtitle: const Text('将应用数据导出为文件'),
                  onTap: () {
                    Navigator.pop(context);
                    _exportAppData();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.file_upload),
                  title: const Text('导入数据'),
                  subtitle: const Text('从文件导入应用数据'),
                  onTap: () {
                    Navigator.pop(context);
                    _importAppData();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.backup),
                  title: const Text('创建备份'),
                  subtitle: const Text('创建完整的数据备份'),
                  onTap: () {
                    Navigator.pop(context);
                    _createBackup();
                  },
                ),
              ],
            ),
          ),
    );
  }

  // 显示开发者选项
  void _showDeveloperOptions() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('开发者选项'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.bug_report),
                  title: const Text('调试模式'),
                  subtitle: const Text('启用详细日志'),
                  trailing: Switch(
                    value: false,
                    onChanged: (value) {
                      // 实现调试模式开关
                    },
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.speed),
                  title: const Text('性能监控'),
                  subtitle: const Text('显示性能指标'),
                  trailing: Switch(
                    value: false,
                    onChanged: (value) {
                      // 实现性能监控开关
                    },
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.clear_all),
                  title: const Text('清除所有数据'),
                  subtitle: const Text('重置应用到初始状态'),
                  onTap: () {
                    Navigator.pop(context);
                    _showResetConfirmation();
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('关闭'),
              ),
            ],
          ),
    );
  }

  // 导出应用数据
  void _exportAppData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('数据导出功能开发中...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  // 导入应用数据
  void _importAppData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('数据导入功能开发中...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  // 创建备份
  void _createBackup() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('备份功能开发中...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  // 显示重置确认对话框
  void _showResetConfirmation() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('重置应用'),
            content: const Text('此操作将删除所有数据并恢复默认设置，无法撤销。确定要继续吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _resetApp();
                },
                child: const Text('确定', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }

  // 重置应用
  void _resetApp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('应用重置功能开发中...'),
        backgroundColor: AppTheme.redDark,
      ),
    );
  }
}

/// 轻量级粒子绘制器
class LightParticlesPainter extends CustomPainter {
  final double animationValue;
  final int particleCount = 20;
  final List<Offset> particles = [];
  final List<double> particleSizes = [];
  final List<Color> particleColors = [];

  LightParticlesPainter(this.animationValue) {
    final rnd = math.Random(42);
    for (int i = 0; i < particleCount; i++) {
      particles.add(Offset(rnd.nextDouble() * 500, rnd.nextDouble() * 800));
      particleSizes.add(rnd.nextDouble() * 4 + 1);
      particleColors.add(
        AppTheme.primaryColor.withValues(alpha: rnd.nextDouble() * 0.3 + 0.1),
      );
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    for (int i = 0; i < particleCount; i++) {
      final xOffset = math.sin(animationValue * 2 * math.pi + i) * 10;
      final yOffset = math.cos(animationValue * 2 * math.pi + i) * 10;

      final paint =
          Paint()
            ..color = particleColors[i]
            ..style = PaintingStyle.fill;

      final position = Offset(
        (particles[i].dx + xOffset) % size.width,
        (particles[i].dy + yOffset) % size.height,
      );

      canvas.drawCircle(position, particleSizes[i], paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// 轻量级波浪绘制器
class LightWavePainter extends CustomPainter {
  final double animationValue;
  final Color color;

  LightWavePainter(this.animationValue, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final path = Path();

    path.moveTo(0, size.height);

    for (double i = 0; i <= size.width; i++) {
      final x = i;
      final y =
          size.height -
          math.sin(
                (x / size.width * 4 * math.pi) + (animationValue * math.pi * 2),
              ) *
              10 -
          math.sin(
                (x / size.width * 2 * math.pi) + (animationValue * math.pi),
              ) *
              10;

      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
