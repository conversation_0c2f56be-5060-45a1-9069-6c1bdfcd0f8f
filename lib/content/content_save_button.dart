import 'package:flutter/material.dart';
import '../models/content_item.dart';
import 'save_content_page.dart';

/// 内容保存按钮 - 可以添加到任何内容页面，点击后打开保存对话框
class ContentSaveButton extends StatelessWidget {
  /// 内容标题
  final String title;

  /// 内容
  final dynamic content;

  /// 内容类型
  final ContentType contentType;

  /// 初始标签
  final List<String>? initialTags;

  /// 保存成功的回调
  final void Function(ContentItem savedItem)? onSaved;

  const ContentSaveButton({
    super.key,
    required this.title,
    required this.content,
    required this.contentType,
    this.initialTags,
    this.onSaved,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: () => _handleSave(context),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 20,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 4), // 控制图标与文字的间距
          const Text(
            '收藏',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleSave(BuildContext context) async {
    final result = await Navigator.push<ContentItem>(
      context,
      MaterialPageRoute(
        builder:
            (context) => SaveContentPage(
              initialTitle: title,
              content: content,
              contentType: contentType,
              initialTags: initialTags,
            ),
      ),
    );

    if (result != null && onSaved != null) {
      onSaved!(result);
    }
  }
}
