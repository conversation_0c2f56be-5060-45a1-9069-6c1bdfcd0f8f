import 'dart:io';
import 'package:flutter/material.dart';
import '../../config/app_theme.dart';
import '../../models/content_item.dart';
import '../../models/content_render_data.dart';

/// 现代化的内容卡片组件
/// 支持显示渲染结果，具有顶级UI/UX设计
class ModernContentCard extends StatelessWidget {
  final ContentItem item;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onShare;
  final bool showActions;

  const ModernContentCard({
    super.key,
    required this.item,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onShare,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 4),
            blurRadius: 20,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 内容预览区域
              _buildContentPreview(),

              // 内容信息区域
              _buildContentInfo(),

              // 操作按钮区域
              if (showActions) _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建内容预览区域
  Widget _buildContentPreview() {
    if (item.shouldShowRenderResult && item.hasRenderResult) {
      return _buildRenderPreview();
    } else {
      return _buildDefaultPreview();
    }
  }

  /// 构建渲染结果预览
  Widget _buildRenderPreview() {
    final renderData = item.renderData!;

    return Container(
      height: 200,
      width: double.infinity,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        child:
            renderData.isMultiImage
                ? _buildMultiImagePreview(renderData)
                : _buildSingleImagePreview(renderData),
      ),
    );
  }

  /// 构建多图预览（分块模式或合集模式）
  Widget _buildMultiImagePreview(ContentRenderData renderData) {
    final imagePaths = renderData.imagePaths;

    if (imagePaths.isEmpty) {
      return _buildEmptyPreview();
    }

    return Stack(
      children: [
        // 主图片
        Positioned.fill(child: _buildImageWidget(imagePaths.first)),

        // 多图指示器
        Positioned(
          top: 12,
          right: 12,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.collections, color: Colors.white, size: 14),
                const SizedBox(width: 4),
                Text(
                  '${imagePaths.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),

        // 渐变遮罩
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: 60,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.3),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建单图预览
  Widget _buildSingleImagePreview(ContentRenderData renderData) {
    final imagePath = renderData.displayImagePath;

    if (imagePath == null) {
      return _buildEmptyPreview();
    }

    return _buildImageWidget(imagePath);
  }

  /// 构建图片组件
  Widget _buildImageWidget(String imagePath) {
    return Image.file(
      File(imagePath),
      fit: BoxFit.cover,
      width: double.infinity,
      height: double.infinity,
      errorBuilder: (context, error, stackTrace) {
        return _buildEmptyPreview();
      },
    );
  }

  /// 构建默认预览（无渲染结果时）
  Widget _buildDefaultPreview() {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: _getTypeGradient(),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Stack(
        children: [
          // 背景图案
          Positioned.fill(
            child: CustomPaint(painter: _ContentPatternPainter()),
          ),

          // 类型图标和信息
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(_getTypeIcon(), size: 32, color: Colors.white),
                ),
                const SizedBox(height: 8),
                Text(
                  _getTypeName(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空预览
  Widget _buildEmptyPreview() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppTheme.bgLightColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported_outlined,
              size: 48,
              color: AppTheme.textLightColor,
            ),
            const SizedBox(height: 8),
            Text(
              '预览不可用',
              style: TextStyle(color: AppTheme.textLightColor, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建内容信息区域
  Widget _buildContentInfo() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和类型标签
          Row(
            children: [
              Expanded(
                child: Text(
                  item.title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textDarkColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 12),
              _buildTypeChip(),
            ],
          ),

          const SizedBox(height: 12),

          // 预览文本
          Text(
            item.previewText,
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textMediumColor,
              height: 1.4,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 16),

          // 元数据信息
          _buildMetadata(),
        ],
      ),
    );
  }

  /// 构建类型标签
  Widget _buildTypeChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getTypeGradient().colors.first.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getTypeGradient().colors.first.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        _getTypeName(),
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: _getTypeGradient().colors.first,
        ),
      ),
    );
  }

  /// 构建元数据信息
  Widget _buildMetadata() {
    return Row(
      children: [
        Icon(Icons.access_time, size: 14, color: AppTheme.textLightColor),
        const SizedBox(width: 4),
        Text(
          _formatDate(item.updatedAt),
          style: TextStyle(fontSize: 12, color: AppTheme.textLightColor),
        ),
        const SizedBox(width: 16),
        Icon(Icons.storage, size: 14, color: AppTheme.textLightColor),
        const SizedBox(width: 4),
        Text(
          item.formattedSize,
          style: TextStyle(fontSize: 12, color: AppTheme.textLightColor),
        ),
        if (item.hasRenderResult) ...[
          const SizedBox(width: 16),
          Icon(Icons.image, size: 14, color: AppTheme.primaryColor),
          const SizedBox(width: 4),
          Text(
            '已渲染',
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }

  /// 构建操作按钮区域
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: AppTheme.bgLightColor.withValues(alpha: 0.5),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(20)),
      ),
      child: Row(
        children: [
          if (onEdit != null)
            _buildActionButton(
              icon: Icons.edit_outlined,
              label: '编辑',
              onTap: onEdit!,
            ),
          if (onShare != null) ...[
            const SizedBox(width: 12),
            _buildActionButton(
              icon: Icons.share_outlined,
              label: '分享',
              onTap: onShare!,
            ),
          ],
          const Spacer(),
          if (onDelete != null)
            _buildActionButton(
              icon: Icons.delete_outline,
              label: '删除',
              onTap: onDelete!,
              isDestructive: true,
            ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final color = isDestructive ? Colors.red : AppTheme.textMediumColor;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取类型对应的渐变色
  LinearGradient _getTypeGradient() {
    switch (item.type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return AppTheme.blueGradient;
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return AppTheme.greenGradient;
      case ContentType.pdf:
        return AppTheme.redGradient;
      case ContentType.voice:
        return AppTheme.purpleGradient;
      case ContentType.html:
        return AppTheme.orangeGradient;
      case ContentType.svg:
        return AppTheme.yellowGradient;
      case ContentType.image:
        return AppTheme.primaryGradient;
    }
  }

  /// 获取类型对应的图标
  IconData _getTypeIcon() {
    switch (item.type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return Icons.description;
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return Icons.credit_card;
      case ContentType.pdf:
        return Icons.picture_as_pdf;
      case ContentType.voice:
        return Icons.mic;
      case ContentType.html:
        return Icons.web;
      case ContentType.svg:
        return Icons.code;
      case ContentType.image:
        return Icons.image;
    }
  }

  /// 获取类型名称
  String _getTypeName() {
    switch (item.type) {
      case ContentType.markdown:
        return 'Markdown';
      case ContentType.markdownBlocks:
        return 'Markdown分块';
      case ContentType.textCard:
        return '文本卡片';
      case ContentType.textCardCollection:
        return '卡片合集';
      case ContentType.pdf:
        return 'PDF';
      case ContentType.voice:
        return '语音';
      case ContentType.html:
        return 'HTML';
      case ContentType.svg:
        return 'SVG';
      case ContentType.image:
        return '图片';
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${date.month}/${date.day}';
    }
  }
}

/// 内容背景图案绘制器
class _ContentPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1)
          ..strokeWidth = 1;

    // 绘制简单的几何图案
    for (int i = 0; i < 5; i++) {
      for (int j = 0; j < 3; j++) {
        final x = (size.width / 5) * i + (size.width / 10);
        final y = (size.height / 3) * j + (size.height / 6);

        canvas.drawCircle(Offset(x, y), 2, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
