import 'dart:io';
import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import '../models/content_item.dart';
import '../services/content_service.dart';

/// 现代化的内容详情页面
class ContentDetailPage extends StatefulWidget {
  final ContentItem item;
  final VoidCallback? onItemUpdated;
  final VoidCallback? onItemDeleted;

  const ContentDetailPage({
    super.key,
    required this.item,
    this.onItemUpdated,
    this.onItemDeleted,
  });

  @override
  State<ContentDetailPage> createState() => _ContentDetailPageState();
}

class _ContentDetailPageState extends State<ContentDetailPage> {
  late ContentItem _item;
  final ContentService _contentService = ContentService();
  bool _isRendering = false;

  @override
  void initState() {
    super.initState();
    _item = widget.item;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(
            child: Column(
              children: [
                if (_shouldShowRenderResult()) _buildRenderResultSection(),
                _buildContentPreviewSection(),
                _buildActionSection(),
                _buildMetadataSection(),
                const SizedBox(height: 32),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios, color: AppTheme.textDarkColor),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: Icon(
            _item.isFavorite ? Icons.favorite : Icons.favorite_border,
            color: _item.isFavorite ? Colors.red : AppTheme.textDarkColor,
          ),
          onPressed: _toggleFavorite,
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: AppTheme.textDarkColor),
          onSelected: _handleMenuAction,
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'share',
                  child: Row(
                    children: [
                      Icon(Icons.share, size: 20),
                      SizedBox(width: 12),
                      Text('分享'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 20, color: Colors.red),
                      SizedBox(width: 12),
                      Text('删除', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          _item.title,
          style: const TextStyle(
            color: AppTheme.textDarkColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        titlePadding: const EdgeInsets.only(left: 56, bottom: 16, right: 120),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                _getTypeGradient().colors.first.withValues(alpha: 0.1),
                _getTypeGradient().colors.last.withValues(alpha: 0.05),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRenderResultSection() {
    final renderData = _item.renderData!;
    
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(Icons.image, color: AppTheme.primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  '渲染结果',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textDarkColor,
                  ),
                ),
                const Spacer(),
                if (renderData.isMultiImage)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${renderData.imagePaths.length} 张图片',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          if (renderData.displayImagePath != null)
            Container(
              height: 200,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.file(
                  File(renderData.displayImagePath!),
                  fit: BoxFit.cover,
                  width: double.infinity,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: AppTheme.bgLightColor,
                      child: Center(
                        child: Icon(
                          Icons.broken_image,
                          color: AppTheme.textLightColor,
                          size: 48,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildContentPreviewSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getTypeIcon(),
                color: _getTypeGradient().colors.first,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _getTypeName(),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textDarkColor,
                ),
              ),
              const SizedBox(width: 8),
              // 特殊标识
              if (_item.type == ContentType.markdownBlocks || 
                  _item.type == ContentType.textCardCollection)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getTypeGradient().colors.first.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    _item.type == ContentType.markdownBlocks ? '分块模式' : '卡片集合',
                    style: TextStyle(
                      fontSize: 10,
                      color: _getTypeGradient().colors.first,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          // 根据内容类型显示不同的预览
          _buildContentPreview(),
        ],
      ),
    );
  }

  /// 构建内容预览
  Widget _buildContentPreview() {
    switch (_item.type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return Text(
          _item.previewText,
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textMediumColor,
            height: 1.5,
          ),
        );
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return Text(
          _item.previewText,
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textMediumColor,
            height: 1.5,
          ),
        );
      case ContentType.html:
        return Text(
          _item.previewText,
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textMediumColor,
            height: 1.5,
            fontFamily: 'monospace',
          ),
        );
      case ContentType.pdf:
        return Row(
          children: [
            Icon(Icons.picture_as_pdf, color: _getTypeGradient().colors.first),
            const SizedBox(width: 8),
            Text(
              'PDF文档，共${_item.renderData?.allImagePaths.length ?? 0}页',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textMediumColor,
              ),
            ),
          ],
        );
      case ContentType.voice:
        return Row(
          children: [
            Icon(Icons.mic, color: _getTypeGradient().colors.first),
            const SizedBox(width: 8),
            Text(
              '语音文件，时长${_item.renderData?.voiceDuration ?? '未知'}',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textMediumColor,
              ),
            ),
          ],
        );
      case ContentType.image:
      case ContentType.svg:
        if (_item.content is String) {
          return Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppTheme.borderColor),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.file(
                File(_item.content as String),
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Icon(
                      Icons.broken_image,
                      color: AppTheme.textLightColor,
                      size: 48,
                    ),
                  );
                },
              ),
            ),
          );
        } else {
          return Container(
            height: 200,
            decoration: BoxDecoration(
              color: AppTheme.bgLightColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Icon(
                Icons.image_not_supported,
                size: 48,
                color: AppTheme.textLightColor,
              ),
            ),
          );
        }
      default:
        return Text(
          _item.previewText,
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textMediumColor,
            height: 1.5,
          ),
        );
    }
  }

  Widget _buildActionSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '操作',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _editContent,
                  icon: const Icon(Icons.edit),
                  label: const Text('编辑内容'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              if (_canReRender()) ...[
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isRendering ? null : _reRenderContent,
                    icon:
                        _isRendering
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : const Icon(Icons.refresh),
                    label: Text(_isRendering ? '渲染中...' : '重新渲染'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.greenGradient.colors.first,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetadataSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '详细信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildMetadataRow('类型', _getTypeName()),
          _buildMetadataRow('大小', _item.formattedSize),
          _buildMetadataRow('创建时间', _formatDateTime(_item.createdAt)),
          _buildMetadataRow('更新时间', _formatDateTime(_item.updatedAt)),
        ],
      ),
    );
  }

  Widget _buildMetadataRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textMediumColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 14, color: AppTheme.textDarkColor),
            ),
          ),
        ],
      ),
    );
  }

  void _toggleFavorite() {
    setState(() {
      _item = _item.copyWith(isFavorite: !_item.isFavorite);
    });
    widget.onItemUpdated?.call();
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        _showSnackBar('分享功能开发中');
        break;
      case 'delete':
        _deleteContent();
        break;
    }
  }

  void _editContent() {
    _showSnackBar('编辑功能开发中');
  }

  /// 检查是否可以重新渲染
  bool _canReRender() {
    switch (_item.type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
      case ContentType.textCard:
      case ContentType.textCardCollection:
      case ContentType.voice:
      case ContentType.pdf:
        return true;
      case ContentType.image:
      case ContentType.svg:
      case ContentType.html:
        return false;
    }
  }

  /// 检查是否应显示渲染结果
  bool _shouldShowRenderResult() {
    return _item.shouldShowRenderResult;
  }

  /// 重新渲染内容
  Future<void> _reRenderContent() async {
    if (_isRendering) return;

    setState(() {
      _isRendering = true;
    });

    try {
      final updatedItem = await _contentService.reRenderItem(_item.id);
      setState(() {
        _item = updatedItem;
        _isRendering = false;
      });

      _showSnackBar('重新渲染完成');
      widget.onItemUpdated?.call();
    } catch (e) {
      setState(() {
        _isRendering = false;
      });
      _showSnackBar('渲染失败: $e');
    }
  }

  void _deleteContent() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('确认删除'),
            content: Text('确定要删除"${_item.title}"吗？此操作无法撤销。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
          ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                  widget.onItemDeleted?.call();
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('删除'),
          ),
            ],
          ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  LinearGradient _getTypeGradient() {
    switch (_item.type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return AppTheme.blueGradient;
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return AppTheme.greenGradient;
      case ContentType.pdf:
        return AppTheme.redGradient;
      case ContentType.voice:
        return AppTheme.purpleGradient;
      case ContentType.html:
        return AppTheme.orangeGradient;
      case ContentType.svg:
        return AppTheme.yellowGradient;
      case ContentType.image:
        return AppTheme.primaryGradient;
    }
  }

  IconData _getTypeIcon() {
    switch (_item.type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return Icons.description;
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return Icons.credit_card;
      case ContentType.pdf:
        return Icons.picture_as_pdf;
      case ContentType.voice:
        return Icons.mic;
      case ContentType.html:
        return Icons.web;
      case ContentType.svg:
        return Icons.code;
      case ContentType.image:
        return Icons.image;
    }
  }

  String _getTypeName() {
    switch (_item.type) {
      case ContentType.markdown:
        return 'Markdown文档';
      case ContentType.markdownBlocks:
        return 'Markdown分块文档';
      case ContentType.textCard:
        return '文本卡片';
      case ContentType.textCardCollection:
        return '文本卡片合集';
      case ContentType.pdf:
        return 'PDF文档';
      case ContentType.voice:
        return '语音文件';
      case ContentType.html:
        return 'HTML文档';
      case ContentType.svg:
        return 'SVG图形';
      case ContentType.image:
        return '图片文件';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
