import 'package:flutter/material.dart';
import 'dart:ui';

import '../config/app_theme.dart';
import '../models/content_item.dart';
import '../services/content_service.dart';
import '../services/theme_manager.dart';
import 'content_detail_page.dart';
import 'text_card_detail_page.dart';

/// 现代化的内容库首页
class ModernContentHomePage extends StatefulWidget {
  const ModernContentHomePage({super.key});

  @override
  State<ModernContentHomePage> createState() => _ModernContentHomePageState();
}

class _ModernContentHomePageState extends State<ModernContentHomePage> {
  final ContentService _contentService = ContentService();
  List<ContentItem> _allItems = [];
  List<ContentItem> _filteredItems = [];
  bool _isLoading = true;
  String _searchQuery = '';
  ContentType? _selectedCategory;
  String? _filterTag;
  bool _showFavoritesOnly = false;
  final List<String> _allTags = [];

  // 批量管理相关状态
  bool _isSelectionMode = false;
  final Set<String> _selectedItems = <String>{};

  // 内容分类定义
  final List<ContentCategory> _categories = [
    ContentCategory(
      type: null,
      name: '全部',
      icon: Icons.apps,
      color: Colors.blue,
    ),
    ContentCategory(
      type: ContentType.textCard,
      name: '文本卡片',
      icon: Icons.credit_card,
      color: ThemeManager().getCategoryChipColor('textcard'),
    ),
    ContentCategory(
      type: ContentType.markdown,
      name: 'Markdown',
      icon: Icons.description,
      color: ThemeManager().getCategoryChipColor('markdown'),
    ),
    ContentCategory(
      type: ContentType.image,
      name: '图片',
      icon: Icons.image,
      color: ThemeManager().getCategoryChipColor('image'),
    ),
    ContentCategory(
      type: ContentType.html,
      name: 'HTML',
      icon: Icons.code,
      color: Colors.red,
    ),
    ContentCategory(
      type: ContentType.pdf,
      name: 'PDF',
      icon: Icons.picture_as_pdf,
      color: Colors.teal,
    ),
    ContentCategory(
      type: ContentType.voice,
      name: '语音',
      icon: Icons.mic,
      color: Colors.indigo,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeContent();
    _updateCategoryColors();
  }

  /// 更新分类颜色（当主题变化时调用）
  void _updateCategoryColors() {
    setState(() {
      // 更新分类颜色
      _categories[1] = ContentCategory(
        type: ContentType.textCard,
        name: '文本卡片',
        icon: Icons.credit_card,
        color: ThemeManager().getCategoryChipColor('textcard'),
      );
      _categories[2] = ContentCategory(
        type: ContentType.markdown,
        name: 'Markdown',
        icon: Icons.description,
        color: ThemeManager().getCategoryChipColor('markdown'),
      );
      _categories[3] = ContentCategory(
        type: ContentType.image,
        name: '图片',
        icon: Icons.image,
        color: ThemeManager().getCategoryChipColor('image'),
      );
    });
  }

  Future<void> _initializeContent() async {
    setState(() {
      _isLoading = true;
    });

    await _contentService.initialize();
    _loadContent();

    setState(() {
      _isLoading = false;
    });
  }

  void _loadContent() {
    _allItems = _contentService.getAllItems();
    _allTags.clear();
    _allTags.addAll(_contentService.getAllTags());
    _filterItems();
  }

  void _filterItems() {
    List<ContentItem> items = _allItems;

    // 按类型筛选
    if (_selectedCategory != null) {
      items =
          items
              .where((item) => _getItemCategory(item) == _selectedCategory)
              .toList();
    }

    // 按标签筛选
    if (_filterTag != null && _filterTag!.isNotEmpty) {
      items = items.where((item) => item.tags.contains(_filterTag)).toList();
    }

    // 按收藏筛选
    if (_showFavoritesOnly) {
      items = items.where((item) => item.isFavorite).toList();
    }

    // 按搜索关键词筛选
    if (_searchQuery.isNotEmpty) {
      items =
          items.where((item) {
            return item.title.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                item.tags.any(
                  (tag) =>
                      tag.toLowerCase().contains(_searchQuery.toLowerCase()),
                );
          }).toList();
    }

    setState(() {
      _filteredItems = items;
    });
  }

  /// 获取内容项的分类
  ContentType _getItemCategory(ContentItem item) {
    switch (item.type) {
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return ContentType.textCard; // 统一归类为文本卡片
      case ContentType.image:
      case ContentType.svg:
        return ContentType.image; // 统一归类为图片
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return ContentType.markdown; // 统一归类为Markdown
      default:
        return item.type;
    }
  }

  /// 检查是否是Markdown分块模式
  bool _isMarkdownBlockMode(ContentItem item) {
    return item.type == ContentType.markdownBlocks;
  }

  /// 检查是否是文本卡片集合
  bool _isTextCardCollection(ContentItem item) {
    return item.type == ContentType.textCardCollection;
  }

  void _filterByCategory(ContentType? category) {
    setState(() {
      _selectedCategory = category;
    });
    _filterItems();
  }

  Future<void> _refreshContent() async {
    await _initializeContent();
  }

  // 选择模式相关方法
  void _enterSelectionMode(String itemId) {
    setState(() {
      _isSelectionMode = true;
      _selectedItems.clear();
      _selectedItems.add(itemId);
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedItems.clear();
    });
  }

  void _toggleItemSelection(String itemId) {
    setState(() {
      if (_selectedItems.contains(itemId)) {
        _selectedItems.remove(itemId);
        // 如果没有选中项，退出选择模式
        if (_selectedItems.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedItems.add(itemId);
      }
    });
  }

  void _selectAllItems() {
    setState(() {
      _selectedItems.clear();
      _selectedItems.addAll(_filteredItems.map((item) => item.id));
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedItems.clear();
    });
  }

  // 删除相关方法
  Future<void> _deleteItem(String itemId) async {
    try {
      await _contentService.deleteItem(itemId);
      await _refreshContent();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('内容已删除'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('删除失败: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  Future<void> _batchDeleteItems() async {
    if (_selectedItems.isEmpty) return;

    try {
      for (final itemId in _selectedItems) {
        await _contentService.deleteItem(itemId);
      }

      final count = _selectedItems.length;
      _exitSelectionMode();
      await _refreshContent();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已删除 $count 个内容'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('批量删除失败: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  // 对话框方法
  Future<void> _showDeleteConfirmDialog(String itemId, String itemTitle) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: const Text('确认删除'),
            content: Text('确定要删除「$itemTitle」吗？此操作无法撤销。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('删除'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      await _deleteItem(itemId);
    }
  }

  Future<void> _showBatchDeleteConfirmDialog() async {
    final count = _selectedItems.length;
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: const Text('确认批量删除'),
            content: Text('确定要删除选中的 $count 个内容吗？此操作无法撤销。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('删除'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      await _batchDeleteItems();
    }
  }

  void _showItemActionSheet(ContentItem item) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    margin: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  ListTile(
                    leading: Icon(
                      item.isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: item.isFavorite ? Colors.red : Colors.grey,
                    ),
                    title: Text(item.isFavorite ? '取消收藏' : '添加收藏'),
                    onTap: () async {
                      Navigator.of(context).pop();
                      try {
                        await _contentService.toggleFavorite(item.id);
                        await _refreshContent();
                      } catch (e) {
                        if (mounted) {
                          ScaffoldMessenger.of(
                            context,
                          ).showSnackBar(SnackBar(content: Text('操作失败: $e')));
                        }
                      }
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.delete, color: Colors.red),
                    title: const Text(
                      '删除',
                      style: TextStyle(color: Colors.red),
                    ),
                    onTap: () {
                      Navigator.of(context).pop();
                      _showDeleteConfirmDialog(item.id, item.title);
                    },
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
    );
  }

  void _viewContentItem(ContentItem item) {
    // 检查是否是文本卡片（包括单个卡片和卡片集合）
    if (_isTextCard(item) || _isTextCardCollection(item)) {
      _openTextCardDetail(item);
    } else {
      // 普通内容项，打开详情页
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => ContentDetailPage(item: item)),
      ).then((_) => _refreshContent());
    }
  }

  /// 检查是否是文本卡片
  bool _isTextCard(ContentItem item) {
    // 通过标签判断
    if (item.tags.contains('text_card')) {
      return true;
    }

    // 通过类型判断
    if (item.type == ContentType.textCard) {
      return true;
    }

    return false;
  }

  /// 打开文本卡片详情页面
  void _openTextCardDetail(ContentItem item) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => TextCardDetailPage(item: item)),
    ).then((_) => _refreshContent());
  }

  /// 获取内容类型的显示名称
  String _getContentTypeName(ContentType type) {
    switch (type) {
      case ContentType.markdown:
        return 'Markdown文档';
      case ContentType.markdownBlocks:
        return 'Markdown分块文档';
      case ContentType.textCard:
        return '文本卡片';
      case ContentType.textCardCollection:
        return '文本卡片集合';
      case ContentType.image:
        return '图片';
      case ContentType.svg:
        return 'SVG图标';
      case ContentType.html:
        return 'HTML文档';
      case ContentType.pdf:
        return 'PDF文档';
      case ContentType.voice:
        return '语音文件';
      default:
        return '未知类型';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        ),
        slivers: [
          // 现代化的顶部区域
          _isSelectionMode ? _buildSelectionModeHeader() : _buildModernHeader(),

          // 分类筛选区域（选择模式下隐藏）
          if (!_isSelectionMode) _buildModernCategoryFilter(),

          // 内容区域
          if (_isLoading)
            const SliverFillRemaining(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.primaryColor,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      '正在加载内容...',
                      style: TextStyle(
                        color: AppTheme.textMediumColor,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else if (_filteredItems.isEmpty)
            SliverFillRemaining(child: _buildModernEmptyView())
          else
            _buildModernContentGrid(),

          // 底部安全区域
          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
      floatingActionButton: _isSelectionMode ? null : _buildModernFAB(),
      bottomNavigationBar: _isSelectionMode ? _buildBatchActionBar() : null,
    );
  }

  /// 选择模式的顶部区域
  Widget _buildSelectionModeHeader() {
    return SliverToBoxAdapter(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF667eea), // 柔和蓝紫色
              const Color(0xFF764ba2), // 深紫色
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部导航栏
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.close, color: Colors.white),
                        onPressed: _exitSelectionMode,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        '已选择 ${_selectedItems.length} 项',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.select_all, color: Colors.white),
                        onPressed: () {
                          if (_selectedItems.length == _filteredItems.length) {
                            _clearSelection();
                          } else {
                            _selectAllItems();
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 现代化的顶部区域
  Widget _buildModernHeader() {
    return SliverToBoxAdapter(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF667eea), // 柔和蓝紫色
              const Color(0xFF764ba2), // 深紫色
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部导航栏
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.arrow_back, color: Colors.white),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ),
                    const Spacer(),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        icon: Icon(
                          _showFavoritesOnly
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: Colors.white,
                        ),
                        onPressed: () {
                          setState(() {
                            _showFavoritesOnly = !_showFavoritesOnly;
                          });
                          _filterItems();
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // 标题和描述
                const Text(
                  '内容库',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    letterSpacing: -0.5,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '管理您的所有创作内容',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),

                const SizedBox(height: 24),

                // 现代化搜索框
                _buildModernSearchBar(),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 现代化搜索框
  Widget _buildModernSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _filterItems();
            },
            style: const TextStyle(color: Colors.black, fontSize: 16),
            decoration: InputDecoration(
              hintText: '搜索内容...',
              hintStyle: TextStyle(color: Colors.grey, fontSize: 16),
              prefixIcon: Icon(Icons.search, color: Colors.black, size: 20),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? IconButton(
                        icon: Icon(
                          Icons.clear,
                          color: Colors.white.withValues(alpha: 0.8),
                          size: 20,
                        ),
                        onPressed: () {
                          setState(() {
                            _searchQuery = '';
                          });
                          _filterItems();
                        },
                      )
                      : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 现代化分类筛选
  Widget _buildModernCategoryFilter() {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.fromLTRB(20, 32, 20, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '内容分类',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: AppTheme.textDarkColor,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                physics: const BouncingScrollPhysics(),
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  final isSelected = _selectedCategory == category.type;
                  final count = _getCategoryCount(category.type);

                  return Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: _buildModernCategoryChip(
                      category,
                      isSelected,
                      count,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 现代化分类芯片
  Widget _buildModernCategoryChip(
    ContentCategory category,
    bool isSelected,
    int count,
  ) {
    return GestureDetector(
      onTap: () => _filterByCategory(category.type),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? category.color : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? category.color : Colors.grey.shade300,
            width: 1.5,
          ),
          boxShadow: [
            if (isSelected)
              BoxShadow(
                color: category.color.withValues(alpha: 0.3),
                offset: const Offset(0, 4),
                blurRadius: 12,
                spreadRadius: 0,
              ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              category.icon,
              color: isSelected ? Colors.white : category.color,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              category.name,
              style: TextStyle(
                color: isSelected ? Colors.white : AppTheme.textDarkColor,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (count > 0) ...[
              const SizedBox(width: 6),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? Colors.white.withValues(alpha: 0.2)
                          : category.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '$count',
                  style: TextStyle(
                    color: isSelected ? Colors.white : category.color,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  int _getCategoryCount(ContentType? type) {
    if (type == null) return _allItems.length;
    return _allItems.where((item) => _getItemCategory(item) == type).length;
  }

  /// 现代化内容网格
  Widget _buildModernContentGrid() {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.75,
          crossAxisSpacing: 16,
          mainAxisSpacing: 20,
        ),
        delegate: SliverChildBuilderDelegate((context, index) {
          final item = _filteredItems[index];
          return TweenAnimationBuilder<double>(
            duration: Duration(milliseconds: 300 + (index * 100)),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.translate(
                offset: Offset(0, 20 * (1 - value)),
                child: Opacity(
                  opacity: value,
                  child: _buildModernContentCard(item, index),
                ),
              );
            },
          );
        }, childCount: _filteredItems.length),
      ),
    );
  }

  /// 现代化内容卡片
  Widget _buildModernContentCard(ContentItem item, int index) {
    final isSelected = _selectedItems.contains(item.id);

    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(opacity: value, child: child),
        );
      },
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border:
                _isSelectionMode && isSelected
                    ? Border.all(color: AppTheme.primaryColor, width: 2)
                    : null,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                offset: const Offset(0, 8),
                blurRadius: 24,
                spreadRadius: 0,
              ),
            ],
          ),
          child: InkWell(
            onTap: () {
              if (_isSelectionMode) {
                _toggleItemSelection(item.id);
              } else {
                _viewContentItem(item);
              }
            },
            onLongPress: () {
              if (!_isSelectionMode) {
                _enterSelectionMode(item.id);
              }
            },
            borderRadius: BorderRadius.circular(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 内容预览区域
                _buildContentPreview(item, isSelected),

                // 内容信息区域
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 标题
                        Flexible(
                          child: Text(
                            item.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textDarkColor,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        const SizedBox(height: 8),

                        // 类型标签
                        _buildTypeChip(item),

                        const Spacer(),

                        // 底部信息
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 12,
                              color: Colors.grey.shade500,
                            ),
                            const SizedBox(width: 4),
                            Flexible(
                              child: Text(
                                _formatDate(item.createdAt),
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.grey.shade500,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 8),
                            if (item.isFavorite)
                              Icon(
                                Icons.favorite,
                                size: 14,
                                color: Colors.red.shade400,
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建内容预览区域
  Widget _buildContentPreview(ContentItem item, bool isSelected) {
    return Container(
      height: 120,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        gradient: _getContentGradient(item.type),
      ),
      child: Stack(
        children: [
          // 背景图案
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
                color: Colors.black.withValues(alpha: 0.1),
              ),
            ),
          ),

          // 内容图标和特殊标识
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Icon(
                    _getContentIcon(item.type),
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(height: 4),
                // 特殊标识（如分块模式、卡片集合等）
                if (_isMarkdownBlockMode(item) || _isTextCardCollection(item))
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _isMarkdownBlockMode(item) ? '分块' : '集合',
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // 选择复选框（选择模式下显示）
          if (_isSelectionMode)
            Positioned(
              top: 12,
              left: 12,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.primaryColor : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        isSelected
                            ? AppTheme.primaryColor
                            : Colors.white.withValues(alpha: 0.7),
                    width: 2,
                  ),
                ),
                child:
                    isSelected
                        ? const Icon(Icons.check, color: Colors.white, size: 16)
                        : null,
              ),
            ),

          // 更多操作按钮（非选择模式下显示）
          if (!_isSelectionMode)
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.more_vert,
                    color: Colors.white,
                    size: 18,
                  ),
                  onPressed: () => _showItemActionSheet(item),
                  padding: const EdgeInsets.all(4),
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建类型标签
  Widget _buildTypeChip(ContentItem item) {
    final category = _categories.firstWhere(
      (cat) => cat.type == _getItemCategory(item),
      orElse: () => _categories.first,
    );

    // 特殊处理：为分块模式的Markdown和卡片集合添加额外标识
    String displayName = category.name;
    if (_isMarkdownBlockMode(item)) {
      displayName = 'Markdown(分块)';
    } else if (_isTextCardCollection(item)) {
      displayName = '卡片集合';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: category.color.withValues(alpha: 0.12),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: category.color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(category.icon, size: 12, color: category.color),
          const SizedBox(width: 4),
          Text(
            displayName,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: category.color,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取内容渐变色
  LinearGradient _getContentGradient(ContentType type) {
    // 使用主题管理器获取动态颜色
    return ThemeManager().getContentTypeGradient(
      type.toString().split('.').last,
    );
  }

  /// 获取内容图标
  IconData _getContentIcon(ContentType type) {
    switch (type) {
      case ContentType.textCard:
        return Icons.credit_card;
      case ContentType.markdown:
        return Icons.description;
      case ContentType.image:
        return Icons.image;
      case ContentType.html:
        return Icons.code;
      case ContentType.pdf:
        return Icons.picture_as_pdf;
      case ContentType.voice:
        return Icons.mic;
      default:
        return Icons.insert_drive_file;
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 现代化空状态视图
  Widget _buildModernEmptyView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 空状态插图
            Container(
              width: 160,
              height: 160,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme.primaryColor.withValues(alpha: 0.1),
                    AppTheme.primaryColor.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(80),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(40),
                      ),
                      child: Icon(
                        _searchQuery.isEmpty
                            ? Icons.inventory_2_outlined
                            : Icons.search_off,
                        size: 40,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                  // 装饰性元素
                  Positioned(
                    top: 20,
                    right: 30,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 30,
                    left: 25,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // 标题
            Text(
              _searchQuery.isEmpty ? '还没有保存任何内容' : '找不到匹配的内容',
              style: const TextStyle(
                fontSize: 24,
                color: AppTheme.textDarkColor,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // 描述
            Text(
              _searchQuery.isEmpty
                  ? '使用各种工具创建内容后，可以将其保存在这里\n开始您的创作之旅吧！'
                  : '尝试使用其他搜索关键词或清除筛选条件\n也许能找到您想要的内容',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textMediumColor,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 40),

            // 操作按钮
            if (_searchQuery.isEmpty)
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppTheme.primaryColor, AppTheme.primaryLightColor],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withValues(alpha: 0.3),
                      offset: const Offset(0, 8),
                      blurRadius: 24,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  icon: const Icon(Icons.add_circle_outline),
                  label: const Text('开始创作'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    shadowColor: Colors.transparent,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    textStyle: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              )
            else
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _searchQuery = '';
                    _selectedCategory = null;
                    _showFavoritesOnly = false;
                  });
                  _filterItems();
                },
                icon: const Icon(Icons.clear_all),
                label: const Text('清除所有筛选'),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 批量操作栏
  Widget _buildBatchActionBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, -2),
            blurRadius: 10,
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          child: Row(
            children: [
              // 选择状态信息
              Expanded(
                child: Text(
                  '已选择 ${_selectedItems.length} / ${_filteredItems.length} 项',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textMediumColor,
                  ),
                ),
              ),

              // 删除按钮
              Container(
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.red.withValues(alpha: 0.3),
                      offset: const Offset(0, 4),
                      blurRadius: 12,
                    ),
                  ],
                ),
                child: ElevatedButton.icon(
                  onPressed:
                      _selectedItems.isEmpty
                          ? null
                          : _showBatchDeleteConfirmDialog,
                  icon: const Icon(Icons.delete, size: 18),
                  label: const Text('删除'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    shadowColor: Colors.transparent,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 现代化浮动按钮
  Widget _buildModernFAB() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.primaryColor, AppTheme.primaryLightColor],
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.4),
            offset: const Offset(0, 8),
            blurRadius: 24,
            spreadRadius: 0,
          ),
        ],
      ),
      child: FloatingActionButton(
        heroTag: 'modern_content_home_page_fab',
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: const Icon(Icons.add, color: Colors.white, size: 28),
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }
}

/// 内容分类模型
class ContentCategory {
  final ContentType? type;
  final String name;
  final IconData icon;
  final Color color;

  ContentCategory({
    required this.type,
    required this.name,
    required this.icon,
    required this.color,
  });
}
