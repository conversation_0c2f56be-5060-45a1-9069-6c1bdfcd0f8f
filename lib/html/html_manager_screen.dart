import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

import '../config/app_theme.dart';
import '../content/content_home_page.dart';
import '../models/content_item.dart';
import '../services/content_service.dart';
import '../services/service_locator.dart';
import 'html_editor_screen.dart';

/// HTML管理屏幕
class HtmlManagerScreen extends StatefulWidget {
  /// 构造函数
  const HtmlManagerScreen({super.key});

  @override
  State<HtmlManagerScreen> createState() => _HtmlManagerScreenState();
}

class _HtmlManagerScreenState extends State<HtmlManagerScreen>
    with SingleTickerProviderStateMixin {
  /// HTML服务
  final _htmlService = ServiceLocator().htmlService;

  /// 内容服务
  final _contentService = ContentService();

  /// 动画控制器
  late final AnimationController _animationController;

  /// 透明度动画
  late final Animation<double> _fadeAnimation;

  /// 平移动画
  late final Animation<Offset> _slideAnimation;

  /// 是否正在导入
  bool _isImporting = false;

  /// 导入文件数量
  int _totalFiles = 0;

  /// 已导入文件数量
  int _importedFiles = 0;

  @override
  void initState() {
    super.initState();
    // 确保初始化内容服务
    _contentService.initialize();

    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
      ),
    );

    // 启动动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 创建新HTML
  void _createNewHtml() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const HtmlEditorScreen()),
    );
  }

  /// 导入HTML文件
  Future<void> _importHtmlFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['html', 'htm'],
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        // 显示加载指示器
        setState(() {
          _isImporting = true;
          _totalFiles = result.files.length;
          _importedFiles = 0;
        });

        for (final file in result.files) {
          if (file.path != null) {
            final htmlDoc = await _htmlService.importFromFile(File(file.path!));

            // 将导入的HTML保存到内容库
            await _contentService.createTextContent(
              title: htmlDoc!.title,
              type: ContentType.html,
              content: htmlDoc.content,
              tags: [],
            );

            setState(() {
              _importedFiles++;
            });
          }
        }

        // 完成导入
        setState(() {
          _isImporting = false;
        });

        // 显示成功提示
        if (mounted) {
          _showSuccessDialog(result.files.length);
        }
      }
    } catch (e) {
      // 重置导入状态
      setState(() {
        _isImporting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('导入HTML文件失败: $e'),
            backgroundColor: Colors.red.shade700,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// 显示导入成功对话框
  void _showSuccessDialog(int count) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green.shade500,
                  size: 28,
                ),
                const SizedBox(width: 8),
                const Text('导入成功'),
              ],
            ),
            content: Text('已成功导入 $count 个HTML文件到内容库'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ContentHomePage(),
                    ),
                  );
                },
                style: FilledButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: const Text('前往内容库'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;

    // 获取主题
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = isDarkMode ? Colors.white : Colors.black;
    final subtitleColor = isDarkMode ? Colors.white70 : Colors.grey[600];

    // 根据屏幕尺寸调整按钮宽度
    final buttonWidth = isSmallScreen ? 180.0 : 220.0;

    return Scaffold(
      appBar: AppBar(
        title: const Text('HTML 管理'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // 导入按钮
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: TextButton.icon(
              icon:
                  _isImporting
                      ? Container(
                        width: 20,
                        height: 20,
                        padding: const EdgeInsets.all(2),
                        child: const CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                      : const Icon(
                        Icons.file_download_outlined,
                        color: Colors.white,
                        size: 20,
                      ),
              label: Text(
                _isImporting ? '导入中...' : '导入',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onPressed: _isImporting ? null : _importHtmlFile,
              style: TextButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                backgroundColor: Colors.white.withValues(alpha: 0.15),
              ),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // 主内容
          Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 顶部图标
                      Container(
                        width: isSmallScreen ? 80 : 100,
                        height: isSmallScreen ? 80 : 100,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha:
                            isDarkMode ? 0.2 : 0.1,
                          ),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.html,
                          size: isSmallScreen ? 44 : 56,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      SizedBox(height: isSmallScreen ? 20 : 28),

                      // 标题和说明
                      Text(
                        'HTML 管理',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 22 : 24,
                          fontWeight: FontWeight.bold,
                          color: textColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 24 : 40,
                        ),
                        child: Text(
                          '创建和编辑HTML文档，所有内容将自动保存至内容库进行统一管理',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 14 : 16,
                            color: subtitleColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      SizedBox(height: isSmallScreen ? 32 : 40),

                      // 创建HTML按钮
                      SizedBox(
                        width: buttonWidth,
                        child: FilledButton.icon(
                          onPressed: _isImporting ? null : _createNewHtml,
                          icon: const Icon(Icons.add_circle_outline, size: 20),
                          label: const Text('创建新的HTML文档'),
                          style: FilledButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            padding: EdgeInsets.symmetric(
                              vertical: isSmallScreen ? 12 : 16,
                              horizontal: isSmallScreen ? 16 : 24,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // 导入HTML按钮
                      SizedBox(
                        width: buttonWidth,
                        child: OutlinedButton.icon(
                          onPressed: _isImporting ? null : _importHtmlFile,
                          icon: const Icon(Icons.file_download_outlined),
                          label: const Text('导入HTML文件'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppTheme.primaryColor,
                            side: BorderSide(
                              color: AppTheme.primaryColor.withValues(alpha:
                                isDarkMode ? 0.7 : 0.5,
                              ),
                            ),
                            padding: EdgeInsets.symmetric(
                              vertical: isSmallScreen ? 12 : 16,
                              horizontal: isSmallScreen ? 16 : 24,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: isSmallScreen ? 32 : 40),

                      // 前往内容库
                      InkWell(
                        onTap:
                            _isImporting
                                ? null
                                : () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) => const ContentHomePage(),
                                    ),
                                  );
                                },
                        borderRadius: BorderRadius.circular(12),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.library_books_outlined,
                                size: 20,
                                color: AppTheme.primaryColor,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '查看内容库',
                                style: TextStyle(
                                  color: AppTheme.primaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Icon(
                                Icons.arrow_forward_ios,
                                size: 14,
                                color: AppTheme.primaryColor,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // 导入进度指示
          if (_isImporting)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 16,
                ),
                decoration: BoxDecoration(
                  color: isDarkMode ? Colors.grey.shade800 : Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          '正在导入HTML文件 ($_importedFiles/$_totalFiles)',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: textColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: _totalFiles > 0 ? _importedFiles / _totalFiles : 0,
                      backgroundColor: Colors.grey.withValues(alpha: 0.2),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
