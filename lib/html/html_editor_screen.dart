import 'dart:io';
import 'dart:async';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../config/app_theme.dart';
import '../services/service_locator.dart';
import '../services/content_service.dart';
import '../models/content_item.dart';
import '../content/content_save_button.dart';
import 'html_document.dart';

/// HTML编辑器屏幕
class HtmlEditorScreen extends StatefulWidget {
  /// 初始HTML内容
  final String? initialHtmlContent;

  /// 初始标题
  final String? initialTitle;

  /// 文档ID（如果是编辑现有文档）
  final String? documentId;

  /// 构造函数
  const HtmlEditorScreen({
    super.key,
    this.initialHtmlContent,
    this.initialTitle,
    this.documentId,
  });

  @override
  State<HtmlEditorScreen> createState() => _HtmlEditorScreenState();
}

class _HtmlEditorScreenState extends State<HtmlEditorScreen> {
  /// HTML服务
  final _htmlService = ServiceLocator().htmlService;

  /// 编辑控制器
  late TextEditingController _htmlController;

  /// 标题控制器
  late TextEditingController _titleController;

  /// WebView控制器
  WebViewController? _webViewController;

  /// 当前文档
  HtmlDocument? _document;

  /// 是否正在处理
  bool _isProcessing = false;

  /// 是否有错误
  bool _hasError = false;

  /// 错误消息
  String _errorMessage = '';

  /// 是否显示预览
  bool _showPreview = false;

  /// 分屏比例
  double _splitRatio = 0.5;

  /// 是否为分屏模式
  bool _isSplitView = false;

  /// 拖动处理中
  bool _isDragging = false;

  /// WebView是否已初始化
  bool _isWebViewInitialized = false;

  /// 是否显示标题栏
  bool _isAppBarVisible = true;

  /// 标题栏隐藏定时器
  Timer? _appBarTimer;

  /// 内容更新防抖定时器
  Timer? _contentUpdateTimer;

  /// 是否正在处理大文本
  bool _isProcessingLargeText = false;

  @override
  void initState() {
    super.initState();

    _htmlController = TextEditingController(
      text: widget.initialHtmlContent ?? HtmlDocument.getBasicTemplate(),
    );
    _titleController = TextEditingController(
      text: widget.initialTitle ?? '新HTML文档',
    );

    _htmlController.addListener(_onHtmlChanged);

    // 如果有文档ID，加载文档
    if (widget.documentId != null) {
      _loadDocument(widget.documentId!);
    } else if (widget.initialHtmlContent != null) {
      // 如果有初始内容，创建新文档
      _createNewDocument();
    }

    // 启动标题栏自动隐藏定时器
    _resetAppBarTimer();
  }

  /// 初始化WebView
  void _initWebView() {
    // 避免重复初始化
    if (_isWebViewInitialized) {
      return;
    }

    try {
      _webViewController =
          WebViewController()
            ..setJavaScriptMode(JavaScriptMode.unrestricted)
            ..setBackgroundColor(Colors.white)
            ..enableZoom(true)
            ..setNavigationDelegate(
              NavigationDelegate(
                onPageFinished: (String url) {
                  debugPrint('页面加载完成: $url');
                  // 注入JavaScript以启用缩放和平移
                  _webViewController?.runJavaScript('''
                document.body.style.overflow = 'auto';
                document.body.style.touchAction = 'auto';
                document.documentElement.style.overflow = 'auto';
                document.documentElement.style.touchAction = 'auto';
              ''');

                  // 注入JavaScript来捕获点击事件并调用Flutter
                  _webViewController?.runJavaScript('''
                document.addEventListener('click', function(e) {
                  window.flutter_inappwebview.callHandler('onTap');
                });
              ''');
                },
                onWebResourceError: (WebResourceError error) {
                  debugPrint('WebView错误: ${error.description}');
                },
              ),
            )
            // 添加JavaScript通道处理WebView中的点击
            ..addJavaScriptChannel(
              'flutter_inappwebview',
              onMessageReceived: (JavaScriptMessage message) {
                _showAppBar();
              },
            );

      // 加载初始HTML
      _updateWebViewContent();
      _isWebViewInitialized = true;
    } catch (e) {
      debugPrint('WebView初始化失败: $e');
      _isWebViewInitialized = false;
    }
  }

  @override
  void dispose() {
    _htmlController.removeListener(_onHtmlChanged);
    _htmlController.dispose();
    _titleController.dispose();

    // 清理防抖定时器
    _contentUpdateTimer?.cancel();

    // 清理WebView资源
    _disposeWebView();

    // 取消定时器
    _appBarTimer?.cancel();

    super.dispose();
  }

  /// 释放WebView资源
  void _disposeWebView() {
    _webViewController = null;
    _isWebViewInitialized = false;
  }

  /// 加载文档
  Future<void> _loadDocument(String id) async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final document = _htmlService.getDocument(id);

      if (document != null) {
        setState(() {
          _document = document;
          _htmlController.text = document.content;
          _titleController.text = document.title;
          _isProcessing = false;
          _hasError = false;
        });

        _updateWebViewContent();
      } else {
        setState(() {
          _isProcessing = false;
          _hasError = true;
          _errorMessage = '找不到文档';
        });
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '加载文档失败: $e';
      });
    }
  }

  /// 创建新文档
  Future<void> _createNewDocument() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final document = await _htmlService.createFromString(
        _htmlController.text,
        title: _titleController.text,
      );

      setState(() {
        _document = document;
        _isProcessing = false;
        _hasError = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '创建文档失败: $e';
      });
    }
  }

  /// 保存文档
  Future<void> _saveDocument() async {
    // 如果是新文档或需要重命名，显示输入对话框
    if (_document == null || _document!.title == '新HTML文档') {
      final newTitle = await _showTitleInputDialog();
      if (newTitle == null) {
        // 用户取消了输入
        return;
      }
      _titleController.text = newTitle;
    }

    if (_document == null) {
      await _createNewDocument();
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      _document!.updateContent(_htmlController.text);
      _document!.updateTitle(_titleController.text);

      await _htmlService.updateDocument(_document!);

      setState(() {
        _isProcessing = false;
        _hasError = false;
      });

      _showSnackBar('保存成功');
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '保存文档失败: $e';
      });
    }
  }

  /// 显示标题输入对话框
  Future<String?> _showTitleInputDialog() async {
    return showDialog<String>(
      context: context,
      builder: (context) {
        final textController = TextEditingController(
          text: _titleController.text,
        );
        return AlertDialog(
          title: const Text('输入HTML文件名'),
          content: TextField(
            controller: textController,
            decoration: const InputDecoration(
              labelText: '文件名',
              hintText: '请输入HTML文件名',
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                if (textController.text.trim().isNotEmpty) {
                  Navigator.pop(context, textController.text.trim());
                } else {
                  _showSnackBar('文件名不能为空');
                }
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  /// 导入HTML文件
  Future<void> _importHtmlFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['html', 'htm'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        final document = await HtmlDocument.fromFile(file);

        setState(() {
          _htmlController.text = document.content;
          _titleController.text = document.title;
          _hasError = false;
        });

        _updateWebViewContent();
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = '导入HTML文件失败: $e';
      });
    }
  }

  /// 导出为图片
  Future<void> _exportAsImage() async {
    if (_document == null) {
      await _saveDocument();
    }

    if (_document == null) {
      _showSnackBar('请先保存文档');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // 先让用户选择保存位置
      String? outputPath = await FilePicker.platform.getDirectoryPath(
        dialogTitle: '选择保存位置',
      );

      if (outputPath == null) {
        // 用户取消了选择
        setState(() {
          _isProcessing = false;
        });
        return;
      }

      // 使用用户选择的路径保存图片
      final file = await _htmlService.saveAsImage(
        _document!,
        outputPath: outputPath,
      );

      setState(() {
        _isProcessing = false;
      });

      if (file != null) {
        _showSnackBar('导出图片成功: ${file.path}');
      } else {
        _showSnackBar('导出图片失败');
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '导出图片失败: $e';
      });
    }
  }

  /// 分享HTML
  Future<void> _shareHtml() async {
    if (_document == null) {
      await _saveDocument();
    }

    if (_document == null) {
      _showSnackBar('请先保存文档');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      await _document!.share();

      setState(() {
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '分享HTML失败: $e';
      });
    }
  }

  /// 分享为图片
  Future<void> _shareAsImage() async {
    if (_document == null) {
      await _saveDocument();
    }

    if (_document == null) {
      _showSnackBar('请先保存文档');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      await _htmlService.shareAsImage(_document!);

      setState(() {
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '分享图片失败: $e';
      });
    }
  }

  /// 重置标题栏隐藏定时器
  void _resetAppBarTimer() {
    _appBarTimer?.cancel();
    setState(() {
      _isAppBarVisible = true;
    });
    _appBarTimer = Timer(const Duration(seconds: 3), () {
      setState(() {
        _isAppBarVisible = false;
      });
    });
  }

  /// 显示标题栏
  void _showAppBar() {
    if (!_isAppBarVisible) {
      _resetAppBarTimer();
    }
  }

  /// HTML内容变更处理
  void _onHtmlChanged() {
    setState(() {
      // 通知UI刷新
    });

    // 使用防抖处理，避免频繁更新WebView
    _contentUpdateTimer?.cancel();

    // 检查是否是大文本
    if (_htmlController.text.length > 10000) {
      // 大文本需要延迟更长时间处理
      setState(() {
        _isProcessingLargeText = true;
      });

      _contentUpdateTimer = Timer(const Duration(milliseconds: 500), () {
        _updateWebViewContent();
        setState(() {
          _isProcessingLargeText = false;
        });
      });
    } else {
      // 小文本可以更快处理
      _contentUpdateTimer = Timer(const Duration(milliseconds: 200), () {
        _updateWebViewContent();
      });
    }

    _resetAppBarTimer();
  }

  /// 更新WebView内容
  void _updateWebViewContent() {
    if (_htmlController.text.isEmpty) return;

    // 使用异步处理WebView内容更新，避免阻塞UI线程
    Future.microtask(() {
      // 如果WebView未初始化，先初始化
      if (!_isWebViewInitialized) {
        _initWebView();
      }

      try {
        _webViewController?.loadHtmlString(_htmlController.text);
      } catch (e) {
        debugPrint('更新WebView内容失败: $e');
        // 如果更新失败，尝试重新初始化
        _disposeWebView();
        _initWebView();
      }
    });
  }

  /// 显示提示消息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// 切换视图模式
  void _toggleViewMode() {
    setState(() {
      _showPreview = !_showPreview;

      // 如果切换到预览模式并且WebView未初始化，则初始化WebView
      if (_showPreview && !_isWebViewInitialized) {
        _initWebView();
      }
    });
  }

  /// 切换分屏模式
  void _toggleSplitView() {
    setState(() {
      _isSplitView = !_isSplitView;
      if (!_isSplitView) {
        _showPreview = false;
      } else if (!_isWebViewInitialized) {
        // 如果开启分屏模式，确保WebView已初始化
        _initWebView();
      }
    });
  }

  /// 保存HTML到内容库
  Future<void> _saveToContentLibrary() async {
    // 如果文档未保存，先保存
    if (_document == null) {
      await _saveDocument();
    }

    if (_document == null) {
      _showSnackBar('请先保存文档');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final contentService = ContentService();
      await contentService.initialize();

      final contentItem = await contentService.createTextContent(
        title: _document!.title,
        type: ContentType.html,
        content: _document!.content,
        tags: [],
      );

      setState(() {
        _isProcessing = false;
      });

      _showSnackBar('已保存到内容库: ${contentItem.title}');
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = '保存到内容库失败: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final size = MediaQuery.of(context).size;
    final isLandscape = size.width > size.height;

    // 获取状态栏高度
    final statusBarHeight = MediaQuery.of(context).padding.top;
    // 计算标题栏总高度（状态栏 + 工具栏）
    final appBarTotalHeight = statusBarHeight + kToolbarHeight;

    final appBar = AppBar(
      elevation: 0,
      scrolledUnderElevation: 2,
      title: Row(
        children: [
          Expanded(
            child: Text(
              _titleController.text,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontSize: 18),
            ),
          ),
        ],
      ),
      actions: [
        // 保存到内容库按钮
        if (_document != null)
          ContentSaveButton(
            title: _titleController.text,
            content: _htmlController.text,
            contentType: ContentType.html,
            onSaved: (savedItem) {
              _showSnackBar('已保存到内容库: ${savedItem.title}');
            },
          ),
        // 视图切换按钮
        if (!_isSplitView)
          IconButton(
            icon: Icon(_showPreview ? Icons.code : Icons.visibility),
            tooltip: _showPreview ? '编辑模式' : '预览模式',
            onPressed: _toggleViewMode,
          ),
        // 分屏按钮
        IconButton(
          icon: Icon(_isSplitView ? Icons.fullscreen : Icons.splitscreen),
          tooltip: _isSplitView ? '单屏模式' : '分屏模式',
          onPressed: _toggleSplitView,
        ),
        // 更多菜单
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          tooltip: '更多操作',
          onSelected: (value) {
            switch (value) {
              case 'import':
                _importHtmlFile();
                break;
              case 'export_image':
                _exportAsImage();
                break;
              case 'share_image':
                _shareAsImage();
                break;
              case 'share_html':
                _shareHtml();
                break;
              case 'rename':
                _showTitleInputDialog().then((newTitle) {
                  if (newTitle != null) {
                    setState(() {
                      _titleController.text = newTitle;
                    });
                  }
                });
                break;
              case 'save_to_library':
                _saveToContentLibrary();
                break;
            }
          },
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'import',
                  child: Row(
                    children: [
                      Icon(Icons.add_box_outlined),
                      SizedBox(width: 12),
                      Text('导入HTML文件'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'export_image',
                  child: Row(
                    children: [
                      Icon(Icons.image_outlined),
                      SizedBox(width: 12),
                      Text('导出为图片'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share_image',
                  child: Row(
                    children: [
                      Icon(Icons.share_outlined),
                      SizedBox(width: 12),
                      Text('分享为图片'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share_html',
                  child: Row(
                    children: [
                      Icon(Icons.code),
                      SizedBox(width: 12),
                      Text('分享HTML'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'rename',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 12),
                      Text('重命名'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'save_to_library',
                  child: Row(
                    children: [
                      Icon(Icons.bookmark_add_outlined),
                      SizedBox(width: 12),
                      Text('保存到内容库'),
                    ],
                  ),
                ),
              ],
        ),
      ],
    );

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: GestureDetector(
        onTap: _showAppBar,
        child: Stack(
          children: [
            // 主内容区域
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: EdgeInsets.only(
                // 根据标题栏可见状态动态调整顶部边距
                top: _isAppBarVisible ? appBarTotalHeight : statusBarHeight,
              ),
              child:
                  _isProcessing
                      ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('处理中...'),
                          ],
                        ),
                      )
                      : Column(
                        children: [
                          // 错误提示
                          if (_hasError)
                            Container(
                              padding: const EdgeInsets.all(12),
                              margin: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: AppTheme.redLight,
                                borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusMD,
                                ),
                              ),
                              width: double.infinity,
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.error_outline,
                                    color: AppTheme.redDark,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      _errorMessage,
                                      style: TextStyle(color: AppTheme.redDark),
                                    ),
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      Icons.close,
                                      color: AppTheme.redDark,
                                    ),
                                    onPressed:
                                        () => setState(() => _hasError = false),
                                    iconSize: 18,
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                  ),
                                ],
                              ),
                            ),
                          // 主内容区域
                          Expanded(
                            child:
                                _isSplitView
                                    ? _buildSplitView(isDarkMode, isLandscape)
                                    : _showPreview
                                    ? _buildPreviewView(isDarkMode)
                                    : _buildEditorView(isDarkMode),
                          ),
                        ],
                      ),
            ),

            // 标题栏
            AnimatedPositioned(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              top: _isAppBarVisible ? 0 : -kToolbarHeight - statusBarHeight,
              left: 0,
              right: 0,
              child: appBar,
            ),
          ],
        ),
      ),
      floatingActionButton: AnimatedOpacity(
        opacity: _isAppBarVisible ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        // 当透明度为0时，禁用按钮功能
        child: IgnorePointer(
          ignoring: !_isAppBarVisible,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 保存到内容库按钮
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: FloatingActionButton(
                  heroTag: "save_to_library",
                  onPressed: _saveToContentLibrary,
                  tooltip: '保存到内容库',
                  backgroundColor: Colors.green,
                  child: const Icon(Icons.bookmark_add),
                ),
              ),
              // 保存按钮
              FloatingActionButton(
                heroTag: "save_html",
                onPressed: _saveDocument,
                tooltip: '保存',
                child: const Icon(Icons.save),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建分屏视图
  Widget _buildSplitView(bool isDarkMode, bool isLandscape) {
    final orientation = isLandscape ? Axis.horizontal : Axis.vertical;

    return LayoutBuilder(
      builder: (context, constraints) {
        final maxWidth = constraints.maxWidth;
        final maxHeight = constraints.maxHeight;

        return Stack(
          children: [
            // 分屏布局
            orientation == Axis.horizontal
                ? Row(
                  children: [
                    SizedBox(
                      width: maxWidth * _splitRatio,
                      child: _buildEditorView(isDarkMode),
                    ),
                    SizedBox(
                      width: maxWidth * (1 - _splitRatio),
                      child: _buildPreviewView(isDarkMode),
                    ),
                  ],
                )
                : Column(
                  children: [
                    SizedBox(
                      height: maxHeight * _splitRatio,
                      child: _buildEditorView(isDarkMode),
                    ),
                    SizedBox(
                      height: maxHeight * (1 - _splitRatio),
                      child: _buildPreviewView(isDarkMode),
                    ),
                  ],
                ),

            // 分隔线和拖动手柄
            orientation == Axis.horizontal
                ? Positioned(
                  left: maxWidth * _splitRatio - 5,
                  top: 0,
                  bottom: 0,
                  child: GestureDetector(
                    onHorizontalDragStart:
                        (_) => setState(() => _isDragging = true),
                    onHorizontalDragEnd:
                        (_) => setState(() => _isDragging = false),
                    onHorizontalDragUpdate: (details) {
                      setState(() {
                        _splitRatio += details.delta.dx / maxWidth;
                        _splitRatio = _splitRatio.clamp(0.2, 0.8);
                      });
                    },
                    child: Container(
                      width: 10,
                      color:
                          _isDragging
                              ? AppTheme.primaryColor.withValues(alpha: 0.5)
                              : Colors.transparent,
                      child: Center(
                        child: Container(
                          width: 4,
                          color:
                              isDarkMode
                                  ? AppTheme.darkBorderColor
                                  : AppTheme.borderColor,
                        ),
                      ),
                    ),
                  ),
                )
                : Positioned(
                  top: maxHeight * _splitRatio - 5,
                  left: 0,
                  right: 0,
                  child: GestureDetector(
                    onVerticalDragStart:
                        (_) => setState(() => _isDragging = true),
                    onVerticalDragEnd:
                        (_) => setState(() => _isDragging = false),
                    onVerticalDragUpdate: (details) {
                      setState(() {
                        _splitRatio += details.delta.dy / maxHeight;
                        _splitRatio = _splitRatio.clamp(0.2, 0.8);
                      });
                    },
                    child: Container(
                      height: 10,
                      color:
                          _isDragging
                              ? AppTheme.primaryColor.withValues(alpha: 0.5)
                              : Colors.transparent,
                      child: Center(
                        child: Container(
                          height: 4,
                          color:
                              isDarkMode
                                  ? AppTheme.darkBorderColor
                                  : AppTheme.borderColor,
                        ),
                      ),
                    ),
                  ),
                ),
          ],
        );
      },
    );
  }

  /// 构建编辑器视图
  Widget _buildEditorView(bool isDarkMode) {
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        side: BorderSide(
          color: isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
        ),
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(8),
            child: TextField(
              controller: _htmlController,
              maxLines: null,
              expands: true,
              keyboardType: TextInputType.multiline,
              decoration: InputDecoration(
                hintText: '输入HTML代码',
                border: InputBorder.none,
                fillColor:
                    isDarkMode ? AppTheme.darkBgColor : AppTheme.bgLightColor,
                filled: true,
                contentPadding: const EdgeInsets.all(12),
              ),
              style: TextStyle(
                fontFamily: 'monospace',
                fontSize: 14,
                color:
                    isDarkMode
                        ? AppTheme.darkTextColor
                        : AppTheme.textDarkColor,
              ),
            ),
          ),
          // 处理大文本时显示加载指示器
          if (_isProcessingLargeText)
            Positioned.fill(
              child: Container(
                color: Colors.black.withValues(alpha: 0.3),
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      Text(
                        '处理大文本...',
                        style: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建预览视图
  Widget _buildPreviewView(bool isDarkMode) {
    // 确保预览模式下WebView已初始化
    if (!_isWebViewInitialized && _htmlController.text.isNotEmpty) {
      _initWebView();
    }

    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        side: BorderSide(
          color: isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
        ),
      ),
      child:
          _htmlController.text.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.web_asset_off_outlined,
                      size: 80,
                      color:
                          isDarkMode
                              ? AppTheme.darkTextLightColor
                              : AppTheme.textLightColor,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '无HTML内容',
                      style: TextStyle(
                        fontSize: 18,
                        color:
                            isDarkMode
                                ? AppTheme.darkTextLightColor
                                : AppTheme.textLightColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '请输入HTML代码',
                      style: TextStyle(
                        color:
                            isDarkMode
                                ? AppTheme.darkTextLightColor
                                : AppTheme.textLightColor,
                      ),
                    ),
                  ],
                ),
              )
              : ClipRRect(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
                child:
                    _isWebViewInitialized && _webViewController != null
                        ? WebViewWidget(controller: _webViewController!)
                        : const Center(child: CircularProgressIndicator()),
              ),
    );
  }
}
