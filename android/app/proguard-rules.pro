# Flutter相关
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# PDF相关
-keep class com.syncfusion.** { *; }
-dontwarn com.syncfusion.**

# 语音相关
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**

# 网络请求
-keepattributes Signature
-keepattributes *Annotation*
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**

# 反射相关
-keepattributes InnerClasses
-keep class kotlin.reflect.** { *; }
-dontwarn kotlin.reflect.**

# 保持应用类
-keep class jk.lcc.contentpal.** { *; }