package com.example.contentpal

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.File

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example.contentpal/file_intent"
    private lateinit var channel: MethodChannel
    private var initialIntent: Map<String, Any>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 处理应用启动时的Intent
        handleIntent(intent)
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        channel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
        channel.setMethodCallHandler { call, result ->
            when (call.method) {
                "getInitialIntent" -> {
                    result.success(initialIntent)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        intent ?: return
        
        when (intent.action) {
            Intent.ACTION_VIEW -> {
                // 处理文件打开
                intent.data?.let { uri ->
                    handleFileUri(uri)
                }
            }
            Intent.ACTION_SEND -> {
                // 处理文件分享
                when (intent.type) {
                    "text/plain" -> {
                        val sharedText = intent.getStringExtra(Intent.EXTRA_TEXT)
                        if (!sharedText.isNullOrEmpty()) {
                            handleTextIntent(sharedText)
                        }
                    }
                    else -> {
                        val uri = intent.getParcelableExtra<Uri>(Intent.EXTRA_STREAM)
                        uri?.let { handleFileUri(it) }
                    }
                }
            }
        }
    }

    private fun handleFileUri(uri: Uri) {
        try {
            val filePath = when (uri.scheme) {
                "file" -> uri.path
                "content" -> {
                    // 对于content URI，需要复制到临时文件
                    copyContentToTempFile(uri)
                }
                else -> null
            }
            
            filePath?.let {
                val intentData = mapOf(
                    "type" to "file",
                    "filePath" to it
                )
                
                if (::channel.isInitialized) {
                    channel.invokeMethod("handleFileIntent", mapOf("filePath" to it))
                } else {
                    initialIntent = intentData
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun handleTextIntent(text: String) {
        val intentData = mapOf(
            "type" to "text",
            "text" to text
        )
        
        if (::channel.isInitialized) {
            channel.invokeMethod("handleTextIntent", mapOf("text" to text))
        } else {
            initialIntent = intentData
        }
    }

    private fun copyContentToTempFile(uri: Uri): String? {
        return try {
            val inputStream = contentResolver.openInputStream(uri)
            val fileName = getFileName(uri) ?: "temp_file"
            val tempFile = File(cacheDir, fileName)
            
            inputStream?.use { input ->
                tempFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }
            
            tempFile.absolutePath
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    private fun getFileName(uri: Uri): String? {
        return try {
            contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                val nameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                if (cursor.moveToFirst() && nameIndex >= 0) {
                    cursor.getString(nameIndex)
                } else {
                    null
                }
            }
        } catch (e: Exception) {
            null
        }
    }
}
