# 发布前检查清单

## 🚨 必须完成的任务

### 1. Android 签名配置
- [ ] 生成正式签名密钥：`keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload`
- [ ] 创建 `android/key.properties` 文件（参考 `key.properties.example`）
- [ ] 测试签名构建：`flutter build apk --release`

### 2. 代码质量修复
- [x] 修复非空断言警告
- [ ] 清理未使用的方法和代码
- [ ] 运行完整的代码分析：`flutter analyze`

### 3. 版本信息更新
- [ ] 更新 `pubspec.yaml` 中的版本号
- [ ] 更新应用描述和元数据
- [ ] 检查应用图标和启动画面

## 📋 建议完成的任务

### 4. 国际化完善
- [x] 完善英文翻译
- [x] 完善中文翻译
- [ ] 添加日语翻译（如需要）

### 5. 性能优化
- [ ] 运行性能测试
- [ ] 检查内存泄漏
- [ ] 优化应用启动时间

### 6. 测试验证
- [ ] 在真机上测试所有功能
- [ ] 测试文件导入导出
- [ ] 测试语音功能
- [ ] 测试PDF处理功能

### 7. 应用商店准备
- [ ] 准备应用截图
- [ ] 编写应用描述
- [ ] 准备隐私政策
- [ ] 设置应用分类和关键词

## 🔧 构建命令

### Android
```bash
# 调试版本
flutter build apk --debug

# 正式版本
flutter build apk --release --obfuscate --split-debug-info=./symbols

# AAB格式（Google Play推荐）
flutter build appbundle --release --obfuscate --split-debug-info=./symbols
```

### iOS
```bash
# 正式版本
flutter build ios --release --obfuscate --split-debug-info=./symbols

# 或者使用Xcode构建
open ios/Runner.xcworkspace
```

## 📱 测试设备要求

### Android
- 最低版本：Android 5.0 (API 21)
- 推荐测试设备：不同屏幕尺寸和Android版本

### iOS
- 最低版本：iOS 12.0
- 推荐测试设备：iPhone和iPad不同尺寸

## 🚀 发布流程

1. 完成所有必须任务
2. 构建正式版本
3. 在测试设备上验证
4. 提交到应用商店
5. 监控发布状态和用户反馈

## 📞 紧急联系

如果发现严重问题，立即：
1. 停止发布流程
2. 记录问题详情
3. 修复问题后重新测试
4. 更新版本号后重新发布