---
description:
globs:
alwaysApply: false
---
# Flutter 项目开发规范

## 架构与代码组织

### 文件结构与行数限制
- **单个源文件最多 600 行** - 将大文件拆分为更小、更专注的模块
- 遵循基于功能的目录结构，位于 [lib/](mdc:lib/) 下
- 将相关功能分组：模型、服务、视图和控制器放在一起
- 使用桶导出（`index.dart`）实现干净的导入

### SOLID 设计原则
- **单一职责原则**：每个类应该只有一个改变的理由
- **开闭原则**：对扩展开放，对修改封闭
- **里氏替换原则**：派生类必须可以替换其基类
- **接口隔离原则**：客户端不应该依赖它们不使用的接口
- **依赖倒置原则**：依赖抽象而非具体实现

### 状态管理
- 使用 `flutter_bloc` 进行复杂状态管理（已在 [pubspec.yaml](mdc:pubspec.yaml) 中配置）
- 简单本地状态优先使用 StatefulWidget
- 尽可能避免全局状态
- 将业务逻辑与 UI 组件分离

## 代码质量标准

### 分析与代码检查
- **每次代码修改后必须运行 `flutter analyze`** 以确保代码质量
- 遵循 [analysis_options.yaml](mdc:analysis_options.yaml) 中定义的代码检查规则
- 在认为修改完成之前修复所有警告和错误
- 维护或改善代码质量指标

### 代码风格
- 使用有意义的变量和函数名称
- 编写清晰、自文档化的代码
- 为公共 API 添加文档注释
- 遵循 Dart 命名约定（变量使用驼峰命名，类使用帕斯卡命名）

## 依赖库管理

### 禁用库列表
- **禁止使用代码生成库**（build_runner、json_annotation 等）
- **禁止使用 GetX** 或类似的响应式框架
- 坚持使用官方 Flutter 包和维护良好的社区包

### 当前允许的依赖
参考 [pubspec.yaml](mdc:pubspec.yaml) 中的已批准依赖：
- UI 相关：flutter_svg、shimmer、flutter_markdown
- 状态管理：flutter_bloc 用于复杂状态管理
- 存储：shared_preferences、hive、path_provider
- 音频：speech_to_text、flutter_tts、just_audio
- PDF：syncfusion_flutter_pdf、printing
- 文件处理：file_picker、share_plus

## 开发工作流程

### 应用运行
- **除非明确要求，否则不要运行 `flutter run`**
- 开发时使用热重载和热重启
- 进行 UI 更改时在多个平台上测试

### 测试策略
- 为业务逻辑编写单元测试
- 为 UI 组件编写小部件测试
- 为关键用户流程编写集成测试
- 保持测试覆盖率在 70% 以上

## 文件组织模式

### 基于功能的结构
```
lib/
├── common/           # 共享组件和工具
├── models/           # 数据模型和实体
├── services/         # 业务逻辑和 API 调用
├── config/           # 应用配置和常量
├── [功能名称]/        # 特定功能模块
│   ├── models/
│   ├── services/
│   ├── views/
│   └── widgets/
```

### 导入指南
- 在功能内使用相对导入
- 跨功能依赖使用包导入
- 分组导入：dart 核心、flutter、第三方、内部

## 性能考虑

### 组件优化
- 尽可能使用 `const` 构造函数
- 实现正确的 `shouldRepaint` 和 `shouldRebuild` 逻辑
- 避免不必要的组件重建
- 长列表使用 `ListView.builder`

### 内存管理
- 正确释放控制器和流
- 适当时使用弱引用
- 开发期间监控内存使用

## 错误处理

### 异常管理
- 为特定领域错误使用自定义异常类
- 在 UI 中实现正确的错误边界
- 适当记录错误以便调试
- 为用户提供有意义的错误消息

### 异步操作
- 使用 try-catch 块进行正确的错误处理
- 为网络调用实现超时机制
- 优雅处理网络连接问题

## 安全指南

### 数据保护
- 验证所有用户输入
- 对敏感数据使用安全存储
- 实现正确的身份验证流程
- 遵循平台安全指南

### 网络安全
- 所有网络通信使用 HTTPS
- 生产环境实现证书固定
- 验证服务器响应

## 文档要求

### 代码文档
- 使用 dartdoc 注释记录公共 API
- 为复杂功能包含使用示例
- 维护 README.md 文件，包含设置说明
- 记录架构决策

### 变更管理
- 编写清晰的提交消息
- 更改 API 时更新文档
- 在版本更新中包含破坏性变更说明

## 质量门禁

每次代码变更完成前的检查清单：
1. ✅ 文件行数少于 600 行
2. ✅ `flutter analyze` 通过，无问题
3. ✅ 代码遵循 SOLID 原则
4. ✅ 未添加禁用的依赖库
5. ✅ 测试通过（如适用）
6. ✅ 文档已更新（如需要）

## 常用设计模式

### 服务层模式
```dart
abstract class ApiService {
  Future<Result<T>> fetchData<T>();
}

class HttpApiService implements ApiService {
  // 具体实现
}
```

### 仓库模式
```dart
abstract class UserRepository {
  Future<User> getUser(String id);
}

class UserRepositoryImpl implements UserRepository {
  // 包含缓存和错误处理的实现
}
```

### BLoC 模式
```dart
class FeatureBloc extends Bloc<FeatureEvent, FeatureState> {
  FeatureBloc(this._repository) : super(FeatureInitial()) {
    on<LoadFeature>(_onLoadFeature);
  }
}
```

这些规范确保代码可维护、可扩展且高质量，同时遵循特定的要求和约束。
