---
description: 
globs: 
alwaysApply: false
---
# 代码重构指南

## 大文件检测与重构

### 当前问题
- [home.dart](mdc:lib/home.dart) 包含 697 行，**必须进行重构** 以符合 600 行限制

### 重构策略

#### 1. 提取组件
将大型 StatefulWidget 类拆分为更小、更专注的组件：
```dart
// 原来的一个大型 HomePage 类
class HomePage extends StatefulWidget { ... } // 300+ 行

// 拆分为多个组件
class HomePage extends StatefulWidget { ... } // <100 行
class HomeAppBar extends StatelessWidget { ... }
class HomeContent extends StatefulWidget { ... }
class HomeSidebar extends StatelessWidget { ... }
```

#### 2. 提取业务逻辑
将业务逻辑移动到单独的服务类中：
```dart
// 从组件中提取
class HomePageLogic {
  void handleUserAction() { ... }
  Future<Data> fetchData() { ... }
}

// 在组件中使用
class HomePage extends StatefulWidget {
  final HomePageLogic logic = HomePageLogic();
  // 仅包含 UI 代码
}
```

#### 3. 创建功能模块
将大文件拆分为基于功能的模块：
```dart
// home.dart (主入口) - <100 行
// home/
//   ├── widgets/
//   │   ├── home_app_bar.dart
//   │   ├── home_content.dart
//   │   └── home_sidebar.dart
//   ├── services/
//   │   └── home_service.dart
//   └── models/
//       └── home_state.dart
```

## 文件大小监控

### 修改前
1. 检查当前文件行数：`wc -l lib/**/*.dart`
2. 识别接近或超过 600 行的文件
3. 在添加新功能前制定重构策略

### 开发期间
- 当现有文件 >500 行时，将新功能添加到单独的文件
- 使用组合而非继承保持类的专注性
- 将常量和配置提取到单独文件

### 修改后
- 验证没有文件超过 600 行
- 运行 `flutter analyze` 确保重构未引入问题
- 根据需要更新导入和导出

## 大文件重构检查清单

### 步骤 1：分析
- [ ] 识别大文件中的不同职责
- [ ] 映射不同部分之间的依赖关系
- [ ] 制定提取策略

### 步骤 2：提取组件
- [ ] 为每个不同职责创建单独文件
- [ ] 将相关代码块放在一起
- [ ] 保持适当的封装

### 步骤 3：更新导入
- [ ] 添加必要的导入语句
- [ ] 删除未使用的导入
- [ ] 根据需要创建桶导出

### 步骤 4：验证
- [ ] 确保没有文件超过 600 行
- [ ] 运行 `flutter analyze` 无错误
- [ ] 测试功能保持不变
- [ ] 验证没有循环依赖

## 常见提取模式

### UI 组件提取
```dart
// 原始大文件
class HomePage extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(...), // 50 行
      body: Column(...),   // 200 行
      drawer: Drawer(...), // 100 行
    );
  }
}

// 提取后
class HomePage extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HomeAppBar(),
      body: HomeBody(),
      drawer: HomeDrawer(),
    );
  }
}
```

### 状态管理提取
```dart
// 将状态逻辑提取到 BLoC
class HomeBloc extends Bloc<HomeEvent, HomeState> {
  // 状态管理逻辑
}

// 组件变为仅展示
class HomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) => ...,
    );
  }
}
```

### 服务层提取
```dart
// 提取 API 调用和业务逻辑
class HomeService {
  Future<List<Item>> fetchItems() async { ... }
  Future<void> saveItem(Item item) async { ... }
}

// 在 BLoC 或组件中使用
class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final HomeService _service = HomeService();
  // 使用 _service 进行数据操作
}
```

## 优先重构文件

基于当前代码库分析：
1. **[home.dart](mdc:lib/home.dart)** - 697 行（需要立即处理）

## 重构收益
- 提高代码可维护性
- 更好的组件测试能力
- 减少阅读代码时的认知负担
- 更容易团队协作
- 更好的 IDE 性能表现
